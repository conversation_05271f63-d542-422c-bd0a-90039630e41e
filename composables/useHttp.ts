import type { AsyncDataRequestStatus, UseFetchOptions } from 'nuxt/app';
import { useFetch } from 'nuxt/app';

interface ApiResponse<T> {
    data: Ref<T | null>;
    error: Ref<Error | null>;
    pending: Ref<boolean>;
    status: Ref<AsyncDataRequestStatus>;
    refresh: () => Promise<void>;
    execute: () => Promise<void>;
}

interface ApiOptions<T = any> extends UseFetchOptions<T> {
    method?: 'GET' | 'POST';
    query?: Record<string, string>;
    body?: Record<string, any> | FormData;
    transform?: (response: T) => T;
}

export function useApiFetch<T extends {}>(
    url: string,
    options: ApiOptions<T> = {}
): ApiResponse<T> {
    const config = useRuntimeConfig();
    const baseUrl = config.public.apiBase;
    let fullUrl = `${baseUrl}${url}`;

    // Handle query parameters for GET requests
    // if (options.method === 'GET' && options.query) {
    //     const queryString = new URLSearchParams(options.query).toString();
    //     fullUrl = `${fullUrl}?${queryString}`;
    // }
    // Set up fetch options
    const fetchOptions: UseFetchOptions<T> = {
        ...options,
        method: options.method || 'GET',
        query: options.method === 'GET' ? options.query : undefined,
        body: options.method === 'POST' ? options.body : undefined,
        transform: async (response: any) => {
            if (typeof response === 'object' && response !== null) {
                // 为了兼容老的格式
                const aa = {
                    ...response,
                    ok: response?.success || false,
                    data: response?.result || null
                } as T;
                // console.log('response', aa);

                return Promise.resolve(aa as T);
            }
            throw new Error('Invalid response format');
        },
        onRequest({ request, options }: { request: Request; options: any }) {
            // Set default headers
            options.headers = new Headers({
                ...options.headers,
                'Content-Type': 'application/json',
                // 'bspId': '1'
            });
        },
        onRequestError({ request, options, error }: {
            request: Request;
            options: any;
            error: Error
        }) {
            console.error('Request error:', error);
        },
        onResponse({ request, response, options }: {
            request: Request;
            response: Response & { _data?: any };
            options: any
        }) {
            // console.log('Response:', response);
            // Handle successful responses
            if (response.status >= 200 && response.status < 300) {
                // Validate response structure
                if (response._data && typeof response._data === 'object') {
                    if (!response._data.success) {
                        throw new Error(response._data.message || 'API request failed');
                    }
                    return response._data;
                }
                throw new Error('Invalid API response format');
            }
            throw new Error(`HTTP Error: ${response.status}`);
        },
        onResponseError({ request, response, options }: {
            request: Request;
            response: Response & { _data?: any };
            options: any
        }) {
            console.error('Response error:', response._data);
        }
    };
    const { data, error, pending, status, refresh, execute } = useFetch<T>(fullUrl, fetchOptions);

    return {
        data: data as Ref<T | null>,
        error: error as Ref<Error | null>,
        pending,
        status,
        refresh,
        execute
    };
}

// Convenience methods
export function useApiGet<T extends {} = any>(
    url: string,
    query?: Record<string, string>,
    options: ApiOptions<T> = {}
): ApiResponse<T> {
    return useApiFetch<T>(url, {
        ...options,
        method: 'GET',
        query
    });
}

export function useApiPost<T extends {} = any>(
    url: string,
    body: Record<string, any> | FormData,
    options: ApiOptions<T> = {}
): ApiResponse<T> {
    return useApiFetch<T>(url, {
        ...options,
        method: 'POST',
        body
    });
}
