import { useChannelStore } from '@/stores/channelId';
import { useUserStore } from '@/stores/user';
import { UserService } from '~/services/user';

import { useVisitorIdStore } from '~/stores/visitorId';
import { dateFormatS, getPlatform } from '~/utils/utils';
// 定义 Web Worker 发送消息的接口
// 消息类型包括初始化成功、初始化失败、追踪成功、追踪失败
// 当出现错误时，会携带错误信息
interface TrackingWorkerMessage {
    type: 'INIT_SUCCESS' | 'INIT_ERROR' | 'TRACK_SUCCESS' | 'TRACK_ERROR';
    error?: string;
}

// 创建全局变量来存储 Worker 实例和初始化状态
// 这样在页面切换时 Worker 实例不会丢失
let globalWorker: Worker | null = null;
let isWorkerInitialized = false;

// 全局清理函数
const globalCleanup = () => {
    if (globalWorker) {
        console.log('应用关闭，清理 Worker');
        globalWorker.terminate();
        globalWorker = null;
        isWorkerInitialized = false;
    }
};

// 在浏览器环境中添加全局事件监听器
if (typeof window !== 'undefined') {
    window.addEventListener('beforeunload', globalCleanup);
    window.addEventListener('unload', globalCleanup);
}

// 导出一个组合式函数，用于处理事件追踪相关逻辑
export const useTracking = () => {
    // 获取 visitorIdStore 实例
    const visitorIdStore = useVisitorIdStore();

    const channelStore = useChannelStore()

    const store = useUserStore()

    // 模拟后端返回的时间
    const createTime = store.currentLoginInfo?.createTime;;
    // 获取今天的日期（格式化为 YYYY-MM-DD）
    const today = dateFormatS(new Date(), 'yyyy-MM-dd');
    // 判断用户类型
    const isNewUser = !createTime || dateFormatS(new Date(createTime), 'yyyy-MM-dd') === today ? true : false;
    // 清理函数，用于清理 Web Worker 和定时器等资源
    const cleanup = () => {
        if (globalWorker) {
            // 终止 Web Worker 实例
            globalWorker.terminate();
            // 将 Web Worker 实例置为 null
            globalWorker = null;
            // 重置初始化状态
            isWorkerInitialized = false;
        }
    };

    // 初始化 Web Worker
    const initWorker = () => {
        // 检查是否在浏览器环境中，若不在则直接返回
        if (typeof window === 'undefined') return;

        // 如果 Worker 已经初始化，直接返回
        if (globalWorker && isWorkerInitialized) {
            // console.log('打点服务已经初始化，无需重新初始化');
            return;
        }

        try {
            // 如果全局 Worker 实例不存在，则创建一个新的实例
            if (!globalWorker) {
                // console.log('创建新的 Worker 实例');
                // 使用 Vite 的动态导入来加载 Web Worker 脚本
                globalWorker = new Worker(
                    new URL(`${location.protocol}//${location.host}/workers/tracking.worker.js`),
                    { type: 'module' }
                );
                // 监听 Web Worker 发送的消息
                globalWorker.onmessage = (event: MessageEvent<TrackingWorkerMessage>) => {
                    const { type, error } = event.data;

                    // 根据消息类型进行不同处理
                    switch (type) {
                        case 'INIT_SUCCESS':
                            // 标记 Web Worker 初始化成功
                            isWorkerInitialized = true;
                            // 打印初始化成功的日志
                            // console.log('打点服务初始化成功');
                            break;
                        case 'INIT_ERROR':
                            // 打印初始化失败的错误信息
                            // console.error('打点服务初始化失败:', error);
                            // 调用清理函数
                            cleanup();
                            break;
                        case 'TRACK_SUCCESS':
                            // 打印事件记录成功的日志
                            // console.log('事件记录成功');
                            break;
                        case 'TRACK_ERROR':
                            // 打印事件记录失败的错误信息
                            // console.error('事件记录失败:', error);
                            break;
                    }
                };

                // 监听 Web Worker 的错误事件
                globalWorker.onerror = (error) => {
                    // 打印 Web Worker 错误信息
                    console.error('Worker 错误:', error);
                    // 调用清理函数
                    cleanup();
                };
            }
        } catch (error) {
            // 打印初始化 Web Worker 失败的错误信息
            console.error('初始化 Worker 失败:', error);
            // 调用清理函数
            cleanup();
        }
    };





    // 记录事件的函数
    const track = (type: string, targetId: string, targetName: string) => {
        // 如果 Web Worker 未初始化或未成功初始化，打印警告信息并初始化 Web Worker
        const store = useUserStore()
        // console.log('track', type, targetId, targetName)

        if (!globalWorker || !isWorkerInitialized) {
            console.warn('打点服务未初始化，初始化服务');
            initWorker();
        }

        try {
            // 创建事件对象
            const event = {
                event: type,
                time: dateFormatS(new Date(), 'yyyy-MM-dd hh:mm:ss'),
                platform: getPlatform(),
                channel: store.channel,
                is_new_user: isNewUser,
                target_id: targetId,

                user_id: UserService.getSelfUserId(),
                visitor_id: visitorIdStore.getVisitorId,
                channel_id: store.currentLoginInfo?.channelId || channelStore.getChannelId,
            };

            // 如果 Web Worker 已初始化，直接发送事件到 Web Worker
            if (globalWorker && isWorkerInitialized) {
                globalWorker.postMessage({
                    type: 'TRACK',
                    data: [event]
                });
            } else {
                // 如果 Web Worker 未初始化，先初始化 Web Worker
                initWorker();
                // 延迟发送事件，等待 Web Worker 初始化完成
                setTimeout(() => {
                    if (globalWorker && isWorkerInitialized) {
                        globalWorker.postMessage({
                            type: 'TRACK',
                            data: [event]
                        });
                    } else {
                        console.error('打点服务初始化失败，无法发送事件');
                    }
                }, 500);
            }
        } catch (error) {
            // 打印事件数据序列化失败的错误信息
            console.error('事件数据序列化失败:', error);
        }
    };

    // 处理组件卸载前的清理工作
    const dispose = () => {
        // 注意：我们不再在组件卸载时清理 Worker
        // 因为我们希望 Worker 在页面切换时保持存在
        // 只有在应用关闭时才需要清理
    };

    // 如果 Worker 尚未初始化，则初始化
    if (!globalWorker) {
        initWorker();
    }

    // 返回可供外部调用的函数
    return {
        track,
        initWorker,
        cleanup,
        dispose
    };
};