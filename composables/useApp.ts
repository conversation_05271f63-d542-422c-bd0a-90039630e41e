interface AppInfo {
    appCode: string,
    isMouse: boolean,
    platform: string,
    isDesktop?: boolean,
}
export const useApp = () => useState("app", () => null as AppInfo | null);
export const getPlatformNew = () => {
    const app = useApp();
    return app.value?.platform || 'web'
};
export async function useLoadAppInfo(userAgent: string) {
    const app = useApp();
    if (app.value == null) {
        // 此处需要判断当前访问url地址
        let url = useRequestURL().href;
        if (url && url.indexOf('xiaoin-mouse') > -1) {
            app.value = {
                appCode: 'mouse',
                isMouse: true,
                platform: 'mouse',
                isDesktop: true
            };
        } else {
            // userAgent:    QAiOffice, Customization_AiOffice
            const ua = userAgent.toLocaleLowerCase();
            //雷神ua
            //Mozilla/5.0 (Windows NT 10.0: Win64; x64)AppleWebKit/537.36 (KHTML, like Gecko)ThunderobotAl/3.0.2 Chrome/122.0.6261.156Electron/29.4.5 Safari/537.36
            if (ua) {
                if (ua.indexOf('qaioffice') > -1) {
                    app.value = {
                        appCode: 'mouse',
                        isMouse: true,
                        platform: 'mouse',
                        isDesktop: true
                    };
                } else if (ua.indexOf('thunderobotai') > -1) {
                    app.value = {
                        appCode: 'thunderobot',
                        isMouse: false,
                        platform: 'web',
                        isDesktop: true
                    };
                } else {
                    if (ua.indexOf('electron') > -1) {
                        app.value = {
                            appCode: 'qihu360',
                            isMouse: false,
                            platform: 'web',
                            isDesktop: true
                        };
                    }
                }
            }


        }
        //测试用,正常请注释，鼠标端本地开发请开启这个
        // app.value = {
        //     appCode: 'mouse',
        //     isMouse: true,
        //     platform: 'mouse',
        //     isDesktop: true
        // };
    }
}
