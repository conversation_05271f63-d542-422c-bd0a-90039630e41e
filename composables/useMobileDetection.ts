import { onMounted, onUnmounted, ref } from 'vue'

/**
 * 移动端检测composable
 * 提供统一的移动端检测逻辑和响应式状态
 */
export const useMobileDetection = () => {
  const isMobile = ref(false)
  const isTablet = ref(false)
  const screenWidth = ref(0)
  const screenHeight = ref(0)

  // 检测是否为移动设备
  const checkMobileDevice = () => {
    if (typeof window !== 'undefined') {
      const userAgent = navigator.userAgent
      const mobileRegex = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i
      return mobileRegex.test(userAgent)
    }
    return false
  }

  // 检测是否为平板设备
  const checkTabletDevice = () => {
    if (typeof window !== 'undefined') {
      const userAgent = navigator.userAgent
      const tabletRegex = /iPad|Android(?=.*Mobile)|Tablet/i
      return tabletRegex.test(userAgent)
    }
    return false
  }

  // 更新屏幕尺寸和设备类型
  const updateDeviceInfo = () => {
    if (typeof window !== 'undefined') {
      screenWidth.value = window.innerWidth
      screenHeight.value = window.innerHeight

      // 基于屏幕宽度判断移动端（优先级高于User-Agent）
      const isSmallScreen = screenWidth.value < 768
      const isMediumScreen = screenWidth.value >= 768 && screenWidth.value < 1024

      // 综合判断移动端
      isMobile.value = isSmallScreen || checkMobileDevice()
      isTablet.value = isMediumScreen || checkTabletDevice()
    }
  }

  // 监听窗口大小变化
  const handleResize = () => {
    updateDeviceInfo()
  }

  onMounted(() => {
    updateDeviceInfo()
    window.addEventListener('resize', handleResize)
  })

  onUnmounted(() => {
    if (typeof window !== 'undefined') {
      window.removeEventListener('resize', handleResize)
    }
  })

  return {
    isMobile: readonly(isMobile),
    isTablet: readonly(isTablet),
    screenWidth: readonly(screenWidth),
    screenHeight: readonly(screenHeight),
    isDesktop: computed(() => !isMobile.value && !isTablet.value),
    // 便捷方法
    isTouchDevice: computed(() => isMobile.value || isTablet.value),
    isSmallScreen: computed(() => screenWidth.value < 640),
    isMediumScreen: computed(() => screenWidth.value >= 640 && screenWidth.value < 1024),
    isLargeScreen: computed(() => screenWidth.value >= 1024)
  }
}
