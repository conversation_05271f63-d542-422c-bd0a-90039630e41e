import type { AppUserInfo, KnowledgeAssistantMemberInfo } from '~/services/types/loginMobileRes'
import { CookieAuthManager, type AuthCookieData } from '~/utils/cookie-auth'

/**
 * 认证 Cookies 的 Composable
 * 提供统一的认证状态管理接口，支持 SSR
 */
export const useAuthCookies = () => {
  /**
   * 获取登录状态
   */
  const isLoggedIn = computed(() => {
    return CookieAuthManager.getLoginStatus()
  })

  /**
   * 获取认证 token
   */
  const token = computed(() => {
    return CookieAuthManager.getToken()
  })

  /**
   * 获取用户信息
   */
  const userInfo = computed(() => {
    return CookieAuthManager.getUserInfo()
  })

  /**
   * 获取知识助手会员信息
   */
  const knowledgeAssistantInfo = computed(() => {
    return CookieAuthManager.getKnowledgeAssistantInfo()
  })

  /**
   * 设置登录状态和相关信息
   */
  const setAuthData = (data: AuthCookieData) => {
    CookieAuthManager.setAuthData(data)
  }

  /**
   * 设置登录状态
   */
  const setLoginStatus = (status: boolean) => {
    CookieAuthManager.setLoginStatus(status)
  }

  /**
   * 设置认证 token
   */
  const setToken = (tokenValue: string) => {
    CookieAuthManager.setToken(tokenValue)
  }

  /**
   * 设置用户信息
   */
  const setUserInfo = (info: AppUserInfo) => {
    CookieAuthManager.setUserInfo(info)
  }

  /**
   * 设置知识助手会员信息
   */
  const setKnowledgeAssistantInfo = (info: KnowledgeAssistantMemberInfo) => {
    CookieAuthManager.setKnowledgeAssistantInfo(info)
  }


  /**
   * 清理所有认证信息
   */
  const clearAuth = () => {
    CookieAuthManager.clearAll()
  }

  /**
   * 获取所有认证数据
   */
  const getAuthData = (): AuthCookieData => {
    return CookieAuthManager.getAuthData()
  }

  /**
   * 同步认证状态到 Pinia store
   */
  const syncToStore = () => {
    const userStore = useUserStore()
    const authData = getAuthData()

    if (authData.userInfo) {
      userStore.setLoginInfo(authData.userInfo)
    }
    if (authData.knowledgeAssistantInfo) {
      userStore.setKnowledgeAssistantMemberInfo(authData.knowledgeAssistantInfo)
    }
  }

  /**
   * 从 Pinia store 同步到 cookies
   */
  const syncFromStore = () => {
    const userStore = useUserStore()

    if (userStore.currentLoginInfo) {
      setUserInfo(userStore.currentLoginInfo)
      setLoginStatus(true)
    }
    if (userStore.knowledgeAssistantMemberInfo) {
      setKnowledgeAssistantInfo(userStore.knowledgeAssistantMemberInfo)
    }
  }

  return {
    // 计算属性
    isLoggedIn,
    token,
    userInfo,
    knowledgeAssistantInfo,

    // 设置方法
    setAuthData,
    setLoginStatus,
    setToken,
    setUserInfo,
    setKnowledgeAssistantInfo,


    // 工具方法
    clearAuth,
    getAuthData,
    syncToStore,
    syncFromStore
  }
}
