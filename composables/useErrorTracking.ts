
// 定义发送错误日志到阿里云的函数
export const useErrorTracking = () => {

    // 发送错误日志到阿里云
    const trackError = async (errorType: string, errorMessage: string) => {
        if (typeof window === 'undefined') return;

        try {
            // 简化的错误数据，只包含核心信息
            const errorData = {
                error_type: errorType,
                error_message: errorMessage,
            };
            const response = await fetch(`${location.protocol}//${location.host}/api/error-log`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(
                    [errorData]
                ),
            })
            // console.log('错误日志发送成功', response);
        } catch (error) {
            console.error('发送错误日志失败', error);
        }
    };

    // 返回可供外部调用的函数
    return {
        trackError,
    };
}; 