interface RuntimeConfig {
    apiBase?: string;
    bookApiBase?: string;
    // 其他可能的属性
}
declare global {
    interface Window {
        initAliyunCaptcha: (config: any) => void;
        uet_report_conversion: (config: any) => void;
        sendDeviceInfo: (data: string, cc: string) => void;
        CefViewQuery: any;
        webkit: any;
        onDeviceCallback: any;
        eruda: any;
        __RUNTIME_CONFIG__: RuntimeConfig
    }
}

export { };
