declare module 'simple-mind-map/src/parse/markdown.js' {
    const markdown: {
        transformMarkdownTo: (markdown: string) => any;
    };
    export default markdown;
}

declare module 'simple-mind-map/src/utils' {
    export function createUid(): string;
    export function handleInputPasteText(e: ClipboardEvent): void;
    export function htmlEscape(str: string): string;
    export function nodeRichTextToTextWithWrap(text: string): string;
    export function textToNodeRichTextWithWrap(text: string): string;
    export function simpleDeepClone<T>(obj: T): T;
}

declare module 'simple-mind-map' {
    export interface MindMapNode {
        uid: string;
        text: string;
        richText: boolean;
        isActive?: boolean;
        children?: MindMapNode[];
        data?: {
            uid: string;
            text: string;
            richText: boolean;
            isActive?: boolean;
        };
    }

    export interface MindMapData {
        data: MindMapNode;
        children?: MindMapData[];
    }

    export interface MindMapRenderer {
        findNodeByUid(uid: string): any;
        textEdit: {
            hideEditTextBox(): void;
        };
    }

    export interface MindMap {
        getData(richText?: boolean): MindMapData;
        renderer: MindMapRenderer;
        execCommand(command: string, ...args: any[]): void;
    }
}

declare module 'simple-mind-map/src/constants/constant' {
    export interface LayoutItem {
        name: string
        value: string
    }

    export const layoutList: LayoutItem[]
} 