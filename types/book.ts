import type { Defs, Element } from '@svgdotjs/svg.js'

export interface Chapter {
  key: string
  title: string
  order: number
  level: 1 | 2 | 3 | 4
  children: Chapter[]
  parentKey?: string
  content?: any
  parent?: string | null
  full_title?: string
  // opening?: string  // 章节开头内容
  // ending?: string   // 章节结尾内容
}

export interface Book {
  key: string
  title: string
  description?: string
  user_id: string
  // chapters: Chapter[]
  flattened_chapters: Chapter[]
  createdAt: string
  updatedAt: string
  type: 'book' | 'paper'
  submission_id: string
  is_generating: boolean,
  generating_chapter_key?: string,
  chapters: any,
  outline_model?: string,
  language?: string
}

export interface BookState {
  currentBook: Book | null
  books: Book[]
  conversationId: string
}

export interface CreateBookData {
  title: string
  type: 'book' | 'paper'
  description?: string
  content?: any
  submission_id?: string
  size?: string
  style?: string
  language?: string
  ask?: string
  status?: string
  user_id?: string
}

export interface Chapter {
  key: string
  title: string
  order: number
  level: 1 | 2 | 3 | 4
  children: Chapter[]
  parentKey?: string
  content?: any
  parent?: string | null
  full_title?: string
  is_generating: boolean

  // opening?: string  // 章节开头内容
  // ending?: string   // 章节结尾内容
}

export interface ReferenceUploadParams {
  book_key: string;
  attachments: Attachment[];
}

export interface Attachment {
  url: string;
  fileName: string;
}


export interface ReferenceResponse {
  success: boolean
  message?: string
  error?: string
  data?: ReferenceResponseData
}
export interface ReferenceResponseData {
  files: ReferenceItem[]
  total: number
  in_progress: number
  completed: number
}
// 参考资料列表项的类型
export interface ReferenceItem {
  reference_no: number
  key: string
  cloud_url: string
  title: string
  file_name: string
  status: string
  citation_line: string
}

export interface SvgObject {
  x: number
  y: number
  width: number
  height: number
  defs: Defs // 根据您的Defs类型进行调整
  uses: Element[] // 根据您的Element类型进行调整
  shadows: Element[] // 根据您的Element类型进行调整
}
