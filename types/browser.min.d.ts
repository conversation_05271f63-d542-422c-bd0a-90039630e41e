interface BrowserInfo {
    userAgent: string;
    browser: string;
    browserVersion: string;
    device: 'Desktop' | 'Mobile' | 'Tablet';
    system: string;
    systemVersion: string;
    platform: string;
    engine: string;
    architecture: string;
    gpu: string;
    gpuModel: string;
    network: string;
    language: string;
    isOnline: boolean;
    isWebview: boolean;
    isRobot: boolean;
}

declare global {
    const browser: (userAgent?: string) => BrowserInfo;
}

export { };

