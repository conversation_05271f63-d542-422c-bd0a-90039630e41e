#!/bin/bash

# https://xiaoin-web.oss-cn-shanghai.aliyuncs.com/
# 
# OSS 配置
OSS_BUCKET="xiaoin-web"
OSS_ENDPOINT="oss-cn-shanghai.aliyuncs.com"
ACCESS_KEY_ID="LTAI5t9nZSmWt47bitM4MDi3"
ACCESS_KEY_SECRET="******************************"

# 构建目录
BUILD_DIR=".output/public"

# 上传到 OSS
ossutil64 config -e ${OSS_ENDPOINT} -i ${ACCESS_KEY_ID} -k ${ACCESS_KEY_SECRET}

# 上传静态资源
# ossutil64 cp -rf ${BUILD_DIR}/ oss://${OSS_BUCKET}/ --meta "Cache-Control:max-age=31536000"

ossutil64 cp -rf ${BUILD_DIR}/ oss://${OSS_BUCKET}/prod/