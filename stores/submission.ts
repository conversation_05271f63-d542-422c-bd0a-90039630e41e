import { message } from 'ant-design-vue'
import { defineStore } from 'pinia'
import { getCreatorsDetail } from '~/api/appCategory'
import { getCreateSubmissionResult } from '~/api/create'
import type { CreatorsInfo } from '~/services/types/appMessage'
import type { SubmissionInfo } from '~/services/types/submission'

interface SubmissionState {
  currentCreator: CreatorsInfo | null
  currentSubmission: SubmissionInfo | null
}

export const useSubmissionStore = defineStore('submission', {
  state: (): SubmissionState => ({
    currentCreator: null,
    currentSubmission: null
  }),

  actions: {
    async loadSubmissionData(code: string, submissionId: string, callback?: (message: string) => void) {
      try {
        const [creatorRes, submissionRes] = await Promise.all([
          getCreatorsDetail({ code }),
          getCreateSubmissionResult({ submissionId })
        ])
        if (!creatorRes.ok || !submissionRes.ok) {
          const msg = creatorRes.message || submissionRes.message || '数据加载失败'
          callback && callback(msg)
          // message.error(msg)
          return false
        }

        if (!creatorRes.data || !submissionRes.data) {
          message.error('写作数据为空')
          return false
        }

        this.currentCreator = creatorRes.data
        this.currentSubmission = submissionRes.data
        return true
      } catch (error) {
        console.error('Data fetch error:', error)
        message.error('数据加载失败')
        return false
      }
    },

    updateSubmission(submission: SubmissionInfo) {
      this.currentSubmission = submission
    },

    clearData() {
      this.currentCreator = null
      this.currentSubmission = null
    },

    async loadSubmissionById(submissionId: string) {
      try {
        const submissionRes = await getCreateSubmissionResult({ submissionId })

        if (!submissionRes.ok || !submissionRes.data) {
          message.error('提交数据加载失败')
          return false
        }

        this.currentSubmission = submissionRes.data

        // 如果已经有对应的写作者数据，就不需要重新加载
        if (this.currentCreator?.creator.code === submissionRes.data.creatorCode) {
          return true
        }

        // 加载写作者数据
        const creatorRes = await getCreatorsDetail({
          code: submissionRes.data.creatorCode
        })

        if (!creatorRes.ok || !creatorRes.data) {
          message.error('写作者数据加载失败')
          return false
        }

        this.currentCreator = creatorRes.data
        return true
      } catch (error) {
        console.error('Data fetch error:', error)
        message.error('数据加载失败')
        return false
      }
    }
  }
}) 