import { defineStore } from 'pinia'

interface OutlineState {
    outlineContent: OutlineElement[]
    outlineCustomLength: number
}

export const useOutlineStore = defineStore('outline', {
    state: (): OutlineState => ({
        outlineContent: [],
        outlineCustomLength: 0
    }),

    actions: {
        updateOutlineContent(content: OutlineElement[],) {
            this.outlineContent = content
        },

        updateOutlineLength(length: number) {
            this.outlineCustomLength = length
        }
    },

    getters: {
        getOutlineContent: (state) => state.outlineContent,

        getOutlineLength: (state) => state.outlineCustomLength,
    }
}) 