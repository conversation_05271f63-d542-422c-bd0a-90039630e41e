import { defineStore } from 'pinia'

export const useUpgradePopoverStore = defineStore('upgradePopover', {
    state: () => ({
        isVisible: false,
        triggerRect: null as DOMRect | null,
        hideTimer: null as NodeJS.Timeout | null,
    }),
    actions: {
        show(rect: DOMRect) {
            if (this.hideTimer) {
                clearTimeout(this.hideTimer)
                this.hideTimer = null
            }
            this.isVisible = true
            this.triggerRect = rect
        },
        hide() {
            this.hideTimer = setTimeout(() => {
                this.isVisible = false
                this.triggerRect = null
                this.hideTimer = null
            }, 200)
        }
    }
}) 