import { defineStore } from 'pinia'

interface TimeState {
    hours: number
    minutes: number
    seconds: number
}

export const useDemoStore = defineStore('demo', {
    state: (): TimeState => ({
        hours: 0,
        minutes: 0,
        seconds: 0
    }),

    actions: {
        updateCountdown() {
            const now = new Date()
            const midnight = new Date()
            midnight.setHours(24, 0, 0, 0)

            const diff = midnight.getTime() - now.getTime()

            const hours = Math.floor(diff / (1000 * 60 * 60))
            const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
            const seconds = Math.floor((diff % (1000 * 60)) / 1000)

            this.hours = hours
            this.minutes = minutes
            this.seconds = seconds
        }
    }
}) 