import { defineStore } from 'pinia';
import { folderSearch, listAll, listAllFiles } from '~/api/repositoryFile';
import { UserService } from '~/services/user';

interface ProcessData {
    title: string
    file_id: string
    inspiration_status: number
    inspiration_data: string
    parse_status: number
    summary_data: string
    summary_status: number
    word_count: number
    green_status: number
    translate_status: number // 2完成3失败
    error: Error
}
// 定义知识文件的类型
interface KnowledgeFile {
    id: string;
    title: string;
    fileType: string;
    wordCount: number;
    createTime: string;
    isFolder?: boolean;
    level?: number;
    children?: KnowledgeFile[];
    isExpanded?: boolean;
    status: string;
    processData: ProcessData;
}

export const useKnowledgeStore = defineStore('knowledge', {
    state: () => ({
        allKnowledgeFileOptions: [] as KnowledgeFile[], // 设置类型
        knowledgeFileOptions: [] as KnowledgeFile[], // 设置类型
        // folderFiles: [] as KnowledgeFile[], // 存储特定文件夹的文件列表
        selectedFolderIds: [] as string[], // 存储选中的文件夹ID
        appCode: '',
        total: 0,
        totalPages: 0,
        currentPage: 1,
        pageSize: 10,
        isSearchMode: false, // 是否处于搜索模式
        hasSearchKeyword: false, // 是否有搜索关键词
        reqParams: { // 新增 reqParams 字段
            fileNames: '', //文件后缀：doc、pdf等
            // fileName: '',
            status: 'done',
            fileType: 'file', //文件类型：html、file
            folderId: 0,
            keywords: '', //搜索关键词
        },
        treeData: [] as KnowledgeFile[],
        // folderFilesMap: new Map(), // 存储文件夹ID和对应的文件列表
    }),
    actions: {

        setAllKnowledgeFileOptions(options: KnowledgeFile[]) {
            const existingIds = new Set(this.allKnowledgeFileOptions.map(file => file.id));
            const uniqueList = options.filter(file => !existingIds.has(file.id));
            this.allKnowledgeFileOptions.push(...uniqueList);
            // console.log('allKnowledgeFileOptions', this.allKnowledgeFileOptions)
        },

        async loadKnowledgeFileData(folderId: string | number = 0, defaultParams: any = {}) {
            const params: Record<string, any> = {
                pageNo: this.currentPage,
                pageSize: this.pageSize,
                ...this.reqParams,
                ...defaultParams,
            };
            console.log('loadKnowledgeFileData', params, defaultParams);
            if (this.reqParams.fileNames.includes('html')) {
                params.fileType = ''
            }
            // 更新 folderId
            params.folderId = folderId;

            params.spaceId = UserService.getCurrentLoginInfo()?.id


            const res = await listAll(params);

            if (res.ok && res.data && res.data.items) {
                const list: KnowledgeFile[] = (res.data.items.records || []).map((item: any) => ({
                    id: item.id,
                    title: item.name,
                    key: item.id,
                    fileId: item.id,
                    fileType: item.fileType,
                    type: item.type,
                    isFolder: item.type == 'folder',
                    wordCount: item.wordCount,
                    createTime: item.createTime,
                    status: item.status,
                    processData: item.processData,
                }));
                // 去重逻辑
                this.setAllKnowledgeFileOptions(list)

                // 更新当前显示的文件列表
                this.knowledgeFileOptions = list;

                this.total = res.data.items.total;
                this.totalPages = res.data.items.pages;
                this.currentPage = params.pageNo;
            } else {
                throw new Error(res?.message || '加载知识库列表错误');
            }
        },

        async loadKnowledgeSearchFileData() {

            const params: any = {
                ...this.reqParams,
                pageNo: 1,
                pageSize: 1000,
                keyword: this.reqParams.keywords,
                searchTypes: ['file', 'folder', 'block']
            }

            params.spaceId = UserService.getCurrentLoginInfo()?.id

            const res = await folderSearch(params);
            // console.log('loadKnowledgeSearchFileData', res.data)
            if (res.ok && res.data) {
                const list: KnowledgeFile[] = (res.data || []).map((item: any) => ({
                    id: item.id,
                    title: item.name,
                    key: item.id,
                    fileId: item.id,
                    fileType: item.fileType,
                    type: item.type,
                    isFolder: item.type == 'folder',
                    wordCount: item.wordCount,
                    createTime: item.createTime,
                    status: item.status,
                    processData: item.processData,
                }));

                this.knowledgeFileOptions = list;

                this.setAllKnowledgeFileOptions(list)
            }
        },

        async listAllFiles(folderId: string | number) {

            const params: Record<string, any> = {
                pageNo: 1,
                pageSize: 1000, // 设置较大的数值以获取所有文件
                folderId: folderId,
                fileNames: this.reqParams.fileNames,
                limit: 20,
                status: this.reqParams.status,
            };

            params.spaceId = UserService.getCurrentLoginInfo()?.id

            // console.log('listAllFiles', params)
            const res = await listAllFiles(params);
            // console.log('listAllFiles', res.data)
            if (res.ok && res.data) {
                const newList: KnowledgeFile[] = (res.data || []).map((item: any) => ({
                    id: item.id,
                    title: item.name,
                    key: item.id,
                    fileId: item.id,
                    fileType: item.fileType,
                    type: item.type,
                    isFolder: item.type == 'folder',
                    wordCount: item.wordCount,
                    createTime: item.createTime,
                    status: item.status,
                    processData: item.processData,
                }));
                return newList;
            }
            return [];
        },

        clearFolderFilesMap() {
            // this.folderFilesMap.clear();
        },

        resetData() {
            this.knowledgeFileOptions = [];
            this.allKnowledgeFileOptions = []; // 重置所有数据
            this.selectedFolderIds = []; // 重置选中的文件夹ID
            // this.folderFiles = [];
            this.total = 0;
            this.currentPage = 1;
            this.isSearchMode = false;
            this.clearFolderFilesMap();
        },

        nextPage() {
            if (this.currentPage < Math.ceil(this.total / this.pageSize)) {
                this.currentPage += 1;
            }
        },

        prevPage() {
            if (this.currentPage > 1) {
                this.currentPage -= 1;
            }
        },

        setOptions(newOptions: any) {
            // console.log("newOptions ==>", newOptions)
            if (!newOptions) {
                return
            }

            this.reqParams.fileNames = newOptions
        },

        setAppCode(code: string) {
            this.appCode = code
        },

        initKnowledgeFileData() {
            this.allKnowledgeFileOptions = [] as KnowledgeFile[]
            this.knowledgeFileOptions = [] as KnowledgeFile[]
            this.selectedFolderIds = [] as string[] // 初始化选中的文件夹ID
            // this.folderFiles = [] as KnowledgeFile[]
            this.total = 0
            this.totalPages = 0
            this.currentPage = 1
            this.pageSize = 10
            this.reqParams = {
                fileNames: '',
                keywords: '',
                status: 'done',
                fileType: 'file',
                folderId: 0,
            }
        },

        transformToTree(files: KnowledgeFile[]) {
            return files.map(file => ({
                ...file,
                level: 0,
                isExpanded: false,
                children: []
            }))
        },

        // 添加文件夹到选中列表
        addSelectedFolder(folderId: string) {
            if (!this.selectedFolderIds.includes(folderId)) {
                this.selectedFolderIds.push(folderId);
            }
        },

        // 从选中列表中移除文件夹
        removeSelectedFolder(folderId: string) {
            this.selectedFolderIds = this.selectedFolderIds.filter(id => id !== folderId);
        },

        // 检查文件夹是否被选中
        isFolderSelected(folderId: string) {
            return this.selectedFolderIds.includes(folderId);
        }
    },
}); 