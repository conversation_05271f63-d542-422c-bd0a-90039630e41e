import { defineStore } from 'pinia';
import { RechargeModalTab } from '~/utils/constants';

export enum RECHARGE_STATUS {
    SUCCESS = 'success',
    INIT = 'init',
    CANCEL = 'cancel',
}

export const useRechargeStore = defineStore('recharge', {
    state: () => ({
        rechargeModalVisible: false,

        currentTab: RechargeModalTab.vip,
        shouldShowSwitchAccoundModal: false,
        rechargeStatus: RECHARGE_STATUS.INIT,
        expendTokens: 0
    }),
    actions: {
        openRechargeModal(cTab?: RechargeModalTab, tokens?: number) {

            this.rechargeStatus = RECHARGE_STATUS.INIT
            if (tokens !== undefined) {
                this.expendTokens = tokens
            } else {
                this.expendTokens = 0
            }

            this.rechargeModalVisible = true
            this.shouldShowSwitchAccoundModal = false

            if (cTab) {
                this.currentTab = cTab
            } else {
                this.currentTab = RechargeModalTab.vip
            }
        },
        closeRechargeModal(rechargeStatus: RECHARGE_STATUS = RECHARGE_STATUS.CANCEL) {
            this.rechargeModalVisible = false
            this.rechargeStatus = rechargeStatus
            this.expendTokens = 0
        }
    }
}) 