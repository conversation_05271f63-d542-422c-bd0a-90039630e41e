import { defineStore } from 'pinia'
import { searchCategory } from '~/api/appCategory'
import type { SearchCategoryInfo } from '~/services/types/appMessage'

// export interface SearchResult {
//   title: string
//   id: string
//   items: {
//     code: string
//     name: string
//     description: string
//     avatar?: string
//   }[]
// }

export const useSearchStore = defineStore('search', {
  state: () => ({
    searchQuery: '',
    isSearching: false,
    searchResults: {} as SearchCategoryInfo,
    isShowSearch: false,
  }),

  actions: {
    async handleSearch() {
      console.log('this.searchQuery  ==>', this.searchQuery)
      if (!this.searchQuery.trim()) {
        this.clearSearch()
        return
      }

      this.isSearching = true

      // 模拟搜索结果数据
      // const mockResults = [
      //   {
      //     title: '搜索结果',
      //     id: 'search-results',
      //     items: [
      //       {
      //         code: 'paper',
      //         name: '论文写作',
      //         description: '专业论文写作助手',
      //         avatar: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/logo/paper.png'
      //       },
      //       {
      //         code: 'ppt',
      //         name: 'PPT制作',
      //         description: '智能PPT生成工具',
      //         avatar: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/logo/PPT.png'
      //       }
      //     ].filter(item =>
      //       item.name.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
      //       item.description.toLowerCase().includes(this.searchQuery.toLowerCase())
      //     )
      //   }
      // ]
      const res = await searchCategory({
        keyword: this.searchQuery,
        isDefaultKeyword: false,
      })
      console.log('res  ==>', res)
      if (!res.ok) {
        // toast.show(res.message || '加载失败')
        return
      }

      // let recommendList = res.data?.recommendList

      // if (recommendList) {
      //   recommendList = [...recommendList, {
      //     "type": "creator",
      //     "code": "need_more",
      //     "name": "我还想要",
      //     "avatar": "https://static-1256600262.file.myqcloud.com/xiaoin-h5/icons/new-pc/want-icon.png",
      //     "description": "没有搜到合适的应用？快来告诉我们你想要什么吧~",
      //     lastTime: '',
      //     content: '',
      //     mediaType: '',
      //     isPinned: '',
      //     isRead: '',
      //     buttonText: ''
      //   }]
      // }


      // this.searchResults = {
      //   ...res.data,
      //   recommendList,
      // } as SearchCategoryInfo

      this.searchResults = res.data as SearchCategoryInfo
      this.isSearching = false
      this.isShowSearch = true
    },

    clearSearch() {
      this.searchQuery = ''
      this.searchResults = {} as SearchCategoryInfo
      this.isShowSearch = false
    },
  },
})
