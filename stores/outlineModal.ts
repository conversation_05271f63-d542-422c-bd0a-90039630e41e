import { defineStore } from 'pinia'

interface OutlineModalState {
    isVisible: boolean
    content: string
    isLoading: boolean
    callback: {
        ok: Function | null
        cancel: Function | null
    },
    isCreateOutlineNow: boolean
}

export const useOutlineModalStore = defineStore('outlineModal', {
    state: (): OutlineModalState => ({
        isVisible: false,
        content: '',
        isLoading: false,
        callback: {
            ok: null,
            cancel: null
        },
        isCreateOutlineNow: false,
    }),

    actions: {
        /**
         * 打开大纲模态框
         * @param content 初始内容
         * @param okCallback 确认回调
         * @param cancelCallback 取消回调
         */
        openModal(content: string = '', okCallback: Function | null = null, cancelCallback: Function | null = null) {
            this.content = content
            this.isVisible = true
            this.callback.ok = okCallback
            this.callback.cancel = cancelCallback
        },

        /**
         * 关闭大纲模态框
         */
        closeModal() {
            this.isVisible = false
            this.content = ''
            this.isLoading = false
            this.callback.ok = null
            this.callback.cancel = null
        },

        /**
         * 更新内容
         * @param content 新内容
         */
        updateContent(content: string) {
            this.content = content
        },

        /**
         * 设置加载状态
         * @param loading 是否加载中
         */
        setLoading(loading: boolean) {
            this.isLoading = loading
        },

        /**
         * 确认操作
         */
        handleOk() {
            if (this.callback.ok) {
                this.callback.ok(this.content)
            }
        },

        /**
         * 取消操作
         */
        handleCancel() {
            if (this.callback.cancel) {
                this.callback.cancel()
            }
            this.closeModal()
        },
        setCreateOutlineNow(flag = false) {
            this.isCreateOutlineNow = flag
        }
    },

    getters: {
        getIsVisible: (state) => state.isVisible,
        getContent: (state) => state.content,
        getIsLoading: (state) => state.isLoading
    }
})