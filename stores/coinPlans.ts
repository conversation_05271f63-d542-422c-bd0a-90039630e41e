import { getCoinDetail, getGoodsList, getOrderDetail, testCreateOrder } from '@/api/recharge'
import type { CoinInfo } from '@/services/types/goods'
import type { CachedPayInfo, WebPayQrcode, XiaoinGoodInfo } from '@/services/types/recharge'
import { UserService } from '@/services/user'
import { GoodsListType, KnowledgeAssistantMemberVipCode, RechargeOrderStatus } from '@/utils/constants'
import { StarloveUtil } from "@/utils/util"
import { getPayChannel } from '@/utils/utils'
import { message } from 'ant-design-vue'
import { defineStore } from 'pinia'
import { useRechargeStore } from './recharge'
interface PurchaseRecord {
  userId: string
  amount: number
}
interface CoinPlansState {
  currentRechargeInfo: XiaoinGoodInfo | null
  firstRechargeGoodInfo: XiaoinGoodInfo | null
  goodsList: XiaoinGoodInfo[]
  firstGoodsList: XiaoinGoodInfo[]
  orderId: string
  coinDetail: CoinInfo | null
  webPayQrcodeLink: WebPayQrcode
  isQrcodeImageLoading: boolean
  isQrcodeImageError: boolean
  isLoadingQrcode: boolean
  productLoaded: boolean
  payChannel: string
  intervalId: NodeJS.Timeout | undefined
  timeoutId: NodeJS.Timeout | null
  purchaseRecords: PurchaseRecord[]
  cachedPayInfoMap: Record<string, CachedPayInfo>
  amount: number
}

export const useCoinPlansStore = defineStore('coinPlans', {
  state: (): CoinPlansState => ({
    currentRechargeInfo: null,
    firstRechargeGoodInfo: null,
    goodsList: [],
    firstGoodsList: [],
    orderId: '',
    coinDetail: null,
    webPayQrcodeLink: {
      linkUrl: '',
      isQrCode: false,
      isError: false
    },
    isQrcodeImageLoading: true,
    isQrcodeImageError: false,
    isLoadingQrcode: true,
    productLoaded: false,
    payChannel: '9',
    intervalId: undefined,
    timeoutId: null,
    purchaseRecords: [],
    cachedPayInfoMap: {},
    amount: 0
  }),

  getters: {
    allGoodsList(): XiaoinGoodInfo[] {
      let list = [...this.firstGoodsList, ...this.goodsList]
      if ((UserService.getCurrentLoginInfo()?.vipLevel || 0) > 0) {
        return list.filter(item => item.id != '13')
      }
      return list
    }
  },

  actions: {

    async loadGoodsData() {

      const params = {
        pageNo: 1,
        pageSize: 20,
        goodsGroupId: GoodsListType.normal,
        isFirstCharge: (UserService.getCurrentLoginInfo()?.vipLevel || 0) <= 0
      }

      const res = await getGoodsList(params)
      if (!res.ok || !res.data) {
        message.error(res.message || '加载失败')
        return
      }

      const data = (res.data.records || []).map((item: any) => {
        if (item.extraParams) {
          try {
            item.extraParams = JSON.parse(item.extraParams)
          } catch {
            item.extraParams = {}
          }
        }
        return item
      })
      this.setRechargeGoodInfo(data)
    },

    setRechargeGoodInfo(list: any[]) {
      const rechargeStore = useRechargeStore()
      const type = UserService.getKnowledgeAssistantMemberInfo()?.vipCode
      const differenceValue = (rechargeStore.expendTokens || 0)
      let firstList = []
      let otherList = []

      for (const item of list) {
        if (item.id == '13' || item.id == '100') {
          // 普通点击默认首冲显示9.9
          if (item.id == '13') {
            this.firstRechargeGoodInfo = item
          }
          firstList.push(item)
          this.firstGoodsList = firstList
        } else {
          otherList.push(item)
        }
      }

      if (differenceValue > 0) {
        // 硬币差值＜=10万，首充档位显示：10万硬币/¥9.9
        if (differenceValue <= 100000) {
          const list = firstList.filter(item => item.id == '13')
          this.firstGoodsList = list
          firstList = list
        }
        // 10万＜硬币差值＜=30万，首充档位为：30万硬币/¥29.9；
        if (differenceValue <= 300000 && differenceValue > 100000) {
          const list = firstList.filter(item => item.id == '100')
          this.firstGoodsList = list
          firstList = list
        }
        // 硬币差值＞30万，即使用户符合首充条件，也不再展示首充档位商品
        if (differenceValue > 300000) {
          this.firstGoodsList = []
          firstList = []
        }
        if (type != KnowledgeAssistantMemberVipCode.A) {
          this.firstGoodsList = []
          firstList = []
        }

        // 硬币差值＜=200万，默认定位118
        if (differenceValue <= 2000000) {
          this.goodsList = [...otherList]
          this.setCurrentRechargeInfo(otherList.filter(item => item.id == '2')[0])
          return
        } else {
          // 差值大于200万，直接选中600万
          this.goodsList = [...otherList]
          this.setCurrentRechargeInfo(otherList.filter(item => item.id == '4')[0])
          return
        }
      }

      if (type == KnowledgeAssistantMemberVipCode.A) {

        // 新用户，显示9.9，选中118
        this.firstGoodsList = firstList.filter(item => item.id == '13')
        this.goodsList = [...otherList]

        this.setCurrentRechargeInfo(otherList.filter(item => item.id == '2')[0])
        return
      }

      this.firstGoodsList = []
      this.goodsList = [...otherList]

      this.setCurrentRechargeInfo(otherList.filter(item => item.id == '2')[0])
    },

    setCurrentRechargeInfo(item: any) {
      // console.log('setCurrentRechargeInfo', item)
      this.currentRechargeInfo = item
      this.processPlan()
      this.centerCurrentRechargeInfo()
    },

    centerCurrentRechargeInfo() {
      const index = this.goodsList.findIndex((item) => item.id === this.currentRechargeInfo?.id)
      if (index === -1) {
        // console.warn('当前选中的对象不在 goodsList 中')
        return
      }

      const itemToCenter = this.goodsList.splice(index, 1)[0]
      const middleIndex = Math.floor(this.goodsList.length / 2)
      this.goodsList.splice(middleIndex, 0, itemToCenter)
    },

    async loadOrderData() {
      const rechargeStore = useRechargeStore()
      const res = await getOrderDetail({ id: this.orderId })
      if (!res.ok || !res.data) {
        return
      }
      if (res.data.status != RechargeOrderStatus.done) {
        return
      }
      // 充值成功将首冲设置为空
      this.firstGoodsList = []

      this.clearIntervals()
      await UserService.loadUserInfo()
      message.success('充值成功')
      rechargeStore.closeRechargeModal(RECHARGE_STATUS.SUCCESS)

      this.clearCache()
      //充值成功需要上报到必应
      try {
        if (window && typeof window.uet_report_conversion === 'function') {
          window.uet_report_conversion(this.currentRechargeInfo?.price)
        }
      } catch (error) {
        console.log(error)
      }
      return true
    },

    setupInterval(callback: () => void) {
      if (this.intervalId) {
        clearInterval(this.intervalId)
      }
      this.intervalId = setInterval(callback, 5000)
    },

    async processPlan() {
      // 根据环境判断payChannel的值
      this.payChannel = `${getPayChannel()}`
      // 清除之前的定时器
      this.clearIntervals()

      this.isQrcodeImageLoading = true
      this.isQrcodeImageError = false

      // 重置二维码错误状态
      if (this.webPayQrcodeLink) {
        this.webPayQrcodeLink.isError = false
      }

      const cacheKey = `${this.currentRechargeInfo?.id}_${this.payChannel}`

      try {
        // 检查缓存
        if (this.cachedPayInfoMap?.[cacheKey]) {
          const cachedInfo = this.cachedPayInfoMap[cacheKey]
          const createTime = cachedInfo.createTime
          const now = Date.now()
          const diff = now - createTime
          if (diff < 1 * 60 * 1000) {
            // console.log('从缓存中读取: ', cacheKey, cachedInfo)
            this.orderId = cachedInfo.orderId
            this.amount = cachedInfo.amount
            this.webPayQrcodeLink = cachedInfo.payInfo
            this.isQrcodeImageLoading = false
            this.isQrcodeImageError = false
            // console.log('从缓存中读取 webPayQrcodeLink: ', this.webPayQrcodeLink)
            this.setupInterval(this.loadOrderData)
            this.timeoutId = setTimeout(
              () => {
                this.clearIntervals()
                if (this.webPayQrcodeLink) {
                  this.webPayQrcodeLink.isError = true
                }
                // 删除过期的缓存
                delete this.cachedPayInfoMap[cacheKey]
              },
              StarloveUtil.isInTestServer() ? 1 * 60 * 1000 : 5 * 60 * 1000
            )
            return
          } else {
            console.log('缓存过期: ', cacheKey)
            delete this.cachedPayInfoMap[cacheKey]
            // this.isQrcodeImageError = true
            // return
            this.isQrcodeImageError = false
            this.isQrcodeImageLoading = true
          }
        }

        // console.log('缓存中没有: ', cacheKey)
        console.log('开始获取新的支付信息')

        // 如果缓存中没有，则请求新数据
        const paymentResponse = await processPayment(
          this.currentRechargeInfo!,
          this.payChannel
        )

        this.isQrcodeImageLoading = false
        this.isQrcodeImageError = false

        if (paymentResponse.result === PaymentResult.SUCCESS && paymentResponse.orderId) {
          this.orderId = paymentResponse.orderId
          this.amount = paymentResponse.amount || 0
          this.webPayQrcodeLink = paymentResponse.payInfo!

          // 添加到缓存
          if (!this.cachedPayInfoMap) {
            this.cachedPayInfoMap = {}
          }
          this.cachedPayInfoMap[cacheKey] = {
            orderId: paymentResponse.orderId,
            payInfo: paymentResponse.payInfo!,
            amount: paymentResponse.amount || 0,
            createTime: Date.now()
          }

          this.setupInterval(this.loadOrderData)
          this.timeoutId = setTimeout(
            () => {
              this.clearIntervals()
              if (this.webPayQrcodeLink) {
                this.webPayQrcodeLink.isError = true
              }
              // 删除过期的缓存
              delete this.cachedPayInfoMap[cacheKey]
            },
            StarloveUtil.isInTestServer() ? 1 * 60 * 1000 : 5 * 60 * 1000
          )
        } else {
          // TODO
          // 处理好绑定手机号
          // 处理好失败场景
        }
      } finally {
        //TODO
      }
    },

    async loadCoinDetail() {
      const res = await getCoinDetail({})
      if (!res.ok || !res.data) {
        message.error(res.message || '剩余硬币加载失败')
        return
      }
      this.coinDetail = res.data
    },

    async handleTestPay() {
      if (!this.currentRechargeInfo) {
        return
      }
      const res = await testCreateOrder({
        appid: getAppId(),
        orderId: this.orderId,
        payChannel: this.payChannel,
      })
      if (!res.ok) {
        message.error(res.message || '充值失败')
        return
      }
      setTimeout(async () => {
        this.firstGoodsList = []
        await this.loadCoinDetail()
        await UserService.loadUserInfo()
        message.success('测试充值成功')
        const rechargeStore = useRechargeStore()
        rechargeStore.closeRechargeModal(RECHARGE_STATUS.SUCCESS)

        this.clearCache()
      }, 1000)
      return true
    },

    clearIntervals() {
      if (this.timeoutId) {
        clearTimeout(this.timeoutId)
        this.timeoutId = null
      }
      if (this.intervalId) {
        clearInterval(this.intervalId)
        this.intervalId = undefined
      }
    },

    rotatePurchaseRecords() {
      if (this.purchaseRecords.length > 1) {
        const first = this.purchaseRecords.shift()
        if (first) {
          this.purchaseRecords.push(first)
        }
      }
      const type = UserService.getKnowledgeAssistantMemberInfo()?.vipCode
      if (type == KnowledgeAssistantMemberVipCode.A) {
        this.loadGoodsData()
      }
    },
    changeCurentPlan(plan: XiaoinGoodInfo) {
      // console.log('changeCurentPlan', plan)
      if (this.currentRechargeInfo?.id === plan?.id) {
        return // 如果选择的是同一个商品，不做处理
      }
      this.currentRechargeInfo = plan
      this.processPlan() // 重新生成订单和二维码
    },

    clearCache(goodsId?: string) {
      if (goodsId && this.cachedPayInfoMap) {
        // 清除指定商品的缓存
        const cacheKey = `${goodsId}_${this.payChannel}`;
        delete this.cachedPayInfoMap[cacheKey];
      } else {
        // 清除所有缓存
        this.cachedPayInfoMap = {};
      }
    }
  }
}) 
