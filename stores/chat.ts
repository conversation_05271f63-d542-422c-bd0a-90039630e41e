import { storage } from '@/utils/local-storage'
import type { AgentStreamData } from '~/services/types/agent'
import type { AppMessageRecord } from '~/services/types/appMessage'

export const useChatStore = defineStore('chat', {
  state: () => ({
    isNewSessionId: false,
    messageRecordList: [] as AppMessageRecord[],
    homeChatQestionData: null, //首页搜索的数据，在
    replyMessage: [] as AgentStreamData[], //消息回复的数据
    openReferenceSource: false, //是否显示参考来源
  }),
  actions: {
    setIsNewSessionId(isFlag = false) {
      this.isNewSessionId = isFlag
    },
    setMessageRecordList(arr: AppMessageRecord[] = []) {
      this.messageRecordList = arr
    },
    setHomeChatQestionData(_homeChatQestionData: any) {
      if (_homeChatQestionData == null) {
        storage.remove(StarloveConstants.keyOflocalStorage.homeChatQuestion)
      } else {
        storage.set(StarloveConstants.keyOflocalStorage.homeChatQuestion, _homeChatQestionData)
      }
      this.homeChatQestionData = _homeChatQestionData
    },
    getHomeChatQestionData() {
      let _homeChatQestionData = null
      try {
        const homeChatQuestion = storage.get(StarloveConstants.keyOflocalStorage.homeChatQuestion)
        if (homeChatQuestion &&
          typeof homeChatQuestion === 'object') {
          _homeChatQestionData = homeChatQuestion
        }
      } catch (error) {
        console.error(error)
      }
      this.homeChatQestionData = _homeChatQestionData
      return _homeChatQestionData
    },
    setReplyMessage(_replyMessage: any) {
      this.replyMessage = _replyMessage
    }
  },
  // persist: {
  //   storage: persistedState.sessionStorage,
  // },
})
