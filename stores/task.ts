import type { EventSourceMessage } from '@microsoft/fetch-event-source';
import { defineStore } from 'pinia';
import { computed, ref, watch } from 'vue';
import { editorBus } from '~/utils/book/editorBus';
import { useChapterStore } from './chapter';

import { useRouter } from 'vue-router';

export const useTaskStore = defineStore('task', () => {
    // 基础消息接口
    interface Message {
        type: 'normal';
        content: string;
        id?: number; // 添加ID以确保Vue能正确识别每个消息
    }

    // 章节状态消息
    interface ChapterStatusMessage {
        id: number;
        content: string;
        timestamp: number;
    }

    // 消息容器基础接口
    interface MessageContainer {
        id: number;
        timestamp: number;
        bookKey: string;
        type: 'book_status' | 'chapter_group';
    }

    // 书籍状态消息接口
    interface BookStatusMessage extends MessageContainer {
        type: 'book_status';
        content: string;
    }

    // 章节消息组接口
    interface ChapterMessageGroup extends MessageContainer {
        type: 'chapter_group';
        title: string;
        chapterKey: string;
        messages: ChapterStatusMessage[];
        isExpanded: boolean;
        isCompleted: boolean;
    }

    // 消息容器列表
    const messageContainers = ref<(BookStatusMessage | ChapterMessageGroup)[]>([]);

    // 当前活跃的章节消息组
    const currentChapterMessageGroup = ref<ChapterMessageGroup | null>(null);

    // 旧的消息字段，保留以兼容现有代码
    const messages = ref<Message[]>([]);

    const isGenerating = ref<boolean>(false);
    const isTraversing = ref<boolean>(false);
    const traverseProgress = ref<number>(0);
    const autoCreatingIndicatorVisible = ref<boolean>(false);

    // ID计数器
    let messageCounter = 0;
    let chapterStatusCounter = 0;
    let containerCounter = 0;

    const router = useRouter();

    // 监听 chapterStore 中 currentBook 的变化
    const setupWatchers = () => {
        const chapterStore = useChapterStore();
        watch(() => chapterStore.currentBook, () => {
            console.log('检测到 currentBook 变化，清空消息');
            clearMessages();
        }, { deep: false }); // 使用浅层监听
    }

    // 初始化监听器
    setupWatchers();

    // 折叠所有章节消息组
    const collapseAllChapterGroups = () => {
        console.log('折叠所有章节消息组');
        // 遍历所有消息容器
        const updatedContainers = messageContainers.value.map(container => {
            // 只处理章节消息组
            if (container.type === 'chapter_group') {
                return {
                    ...container,
                    isExpanded: false
                } as ChapterMessageGroup;
            }
            return container;
        });
        console.log("updatedContainers ==>", updatedContainers)
        messageContainers.value = updatedContainers;

        // 如果当前有活跃的章节消息组，也要更新其状态
        if (currentChapterMessageGroup.value) {
            currentChapterMessageGroup.value.isExpanded = false;
        }
    };

    // 添加书籍状态消息
    const addBookStatusMessage = (content: string, bookKey: string) => {
        console.log('添加书籍状态消息:', content);
        // 确保消息不为空
        if (!content || content.trim() === '') {
            console.warn('尝试添加空书籍消息，已跳过');
            return;
        }

        // 折叠所有章节消息组
        collapseAllChapterGroups();

        // 创建新的书籍状态消息
        const newBookMessage: BookStatusMessage = {
            id: ++containerCounter,
            type: 'book_status',
            content,
            timestamp: Date.now(),
            bookKey
        };

        // 添加到消息容器列表
        messageContainers.value = [...messageContainers.value, newBookMessage];

        console.log('书籍状态消息已添加，当前消息容器数量:', messageContainers.value);
    };

    // 添加章节状态消息
    const addChapterStatusMessage = (content: string, chapterKey: string, chapterTitle: string, status?: string) => {
        console.log('添加章节状态消息:', content);
        // 确保消息不为空
        if (!content || content.trim() === '') {
            console.warn('尝试添加空章节消息，已跳过');
            return;
        }

        const chapterStore = useChapterStore();
        const bookKey = chapterStore.currentBook?.key || '';

        // 创建新的章节状态消息
        const newChapterMessage: ChapterStatusMessage = {
            id: ++chapterStatusCounter,
            content,
            timestamp: Date.now()
        };

        // 同时更新旧的messages引用，保持兼容
        const newMessage: Message = {
            type: 'normal',
            content,
            id: ++messageCounter
        };
        messages.value = [...messages.value, newMessage];

        // 检查是否有当前章节消息组
        if (!currentChapterMessageGroup.value) {
            // 创建新的章节消息组前，折叠所有现有章节消息组
            collapseAllChapterGroups();

            // 创建新的章节消息组
            const newGroup: ChapterMessageGroup = {
                id: ++containerCounter,
                type: 'chapter_group',
                title: `生成${chapterTitle || '章节'}`,
                chapterKey,
                bookKey,
                messages: [newChapterMessage],
                timestamp: Date.now(),
                isExpanded: true,
                isCompleted: false
            };

            currentChapterMessageGroup.value = newGroup;
            messageContainers.value = [...messageContainers.value, newGroup];
            console.log(`messageContainers.value: `, messageContainers.value);
            console.log(`创建新章节消息组: ${newGroup.title}, ID: ${newGroup.id}`);
        } else {
            // 向现有章节消息组添加消息
            currentChapterMessageGroup.value.messages.push(newChapterMessage);

            // 更新消息容器列表
            const updatedContainers = [...messageContainers.value];
            const index = updatedContainers.findIndex(c =>
                c.type === 'chapter_group' &&
                (c as ChapterMessageGroup).id === currentChapterMessageGroup.value!.id
            );

            if (index >= 0) {
                updatedContainers[index] = { ...currentChapterMessageGroup.value };
                messageContainers.value = updatedContainers;
            }
        }

        // 如果消息表示完成，设置章节消息组为已完成并清除当前引用
        if (status === 'completed') {
            if (currentChapterMessageGroup.value) {
                currentChapterMessageGroup.value.isCompleted = true;

                // 更新消息容器列表
                const updatedContainers = [...messageContainers.value];
                const index = updatedContainers.findIndex(c =>
                    c.type === 'chapter_group' &&
                    (c as ChapterMessageGroup).id === currentChapterMessageGroup.value!.id
                );

                if (index >= 0) {
                    updatedContainers[index] = { ...currentChapterMessageGroup.value };
                    messageContainers.value = updatedContainers;
                }

                // 清空当前章节消息组引用
                currentChapterMessageGroup.value = null;
                autoCreatingIndicatorVisible.value = false;
                isGenerating.value = false;

            }
        } else {
            isGenerating.value = true;
        }

        if (status == 'start_content') {
            const chapterStore = useChapterStore();
            // console.log('start_content', chapterKey, chapterStore.currentChapter?.key)
            if (chapterStore.currentChapter && chapterStore.currentChapter.key == chapterKey) {
                autoCreatingIndicatorVisible.value = true;
            }
        }

        // console.log('章节状态消息已添加，当前容器数量:', messageContainers.value.length);
    };

    // 切换章节消息组的展开/折叠状态
    const toggleChapterGroupExpansion = (groupId: number) => {
        const index = messageContainers.value.findIndex(c =>
            c.type === 'chapter_group' && c.id === groupId
        );

        if (index >= 0) {
            const group = messageContainers.value[index] as ChapterMessageGroup;
            const isExpanding = !group.isExpanded;

            // 如果是展开操作，先折叠其他所有章节消息组
            if (isExpanding) {
                collapseAllChapterGroups();
            }

            const updatedGroup = {
                ...group,
                isExpanded: isExpanding
            };

            const updatedContainers = [...messageContainers.value];
            updatedContainers[index] = updatedGroup;
            messageContainers.value = updatedContainers;

            // 如果这是当前活跃的章节消息组，也更新引用
            if (currentChapterMessageGroup.value && currentChapterMessageGroup.value.id === groupId) {
                currentChapterMessageGroup.value = updatedGroup;
            }
        }
    };

    const setIsGenerating = (newIsGenerating: boolean) => {
        isGenerating.value = newIsGenerating;
    };

    const clearMessages = () => {
        console.log('清空所有消息');
        messages.value = [];
        messageContainers.value = [];
        currentChapterMessageGroup.value = null;
        isGenerating.value = false;
    };

    const setIsTraversing = (newIsTraversing: boolean) => {
        isTraversing.value = newIsTraversing;
    };

    const setTraverseProgress = (newTraverseProgress: number) => {
        traverseProgress.value = newTraverseProgress;
    };

    // 获取按时间戳排序的消息容器
    const sortedMessageContainers = computed(() => {
        return [...messageContainers.value].sort((a, b) => a.timestamp - b.timestamp);
    });

    // 处理SSE消息
    const processSSEMessage = (eventSourceMessage: EventSourceMessage) => {
        try {
            const data = JSON.parse(eventSourceMessage.data);
            console.log('SSE消息 data:', eventSourceMessage.event, data);

            if (eventSourceMessage.event === 'book_status') {
                processBookStatusMessage(data);
                return;
            }

            if (eventSourceMessage.event === 'chapter_status') {
                processChapterStatusMessage(data);
                return;
            }

            if (eventSourceMessage.event === 'book_progress') {
                processBookProgressMessage(data);
                return;
            }

            if (eventSourceMessage.event === 'content') {
                processContentMessage(data);
                return;
            }

            if (eventSourceMessage.event === 'error') {
                processErrorMessage(data);
                return;
            }
        } catch (error) {
            console.error('处理SSE消息出错:', error, eventSourceMessage);
        }
    };

    const processBookStatusMessage = (data: any) => {
        console.log('处理书籍状态消息:', data);

        // 提取消息文本和书籍key - 兼容不同格式
        let messageText = '';
        let bookKey = '';

        if (data.message) {
            messageText = data.message;
        } else if (typeof data === 'string') {
            messageText = data;
        } else if (data.content) {
            messageText = data.content;
        }

        if (data.book_key) {
            bookKey = data.book_key;
        } else {
            const chapterStore = useChapterStore();
            bookKey = chapterStore.currentBook?.key || '';
        }

        // 保存书籍状态消息
        if (messageText && bookKey) {
            addBookStatusMessage(messageText, bookKey);
        } else {
            console.warn('书籍状态消息为空或缺少bookKey，跳过添加');
        }
    }

    const processErrorMessage = (data: any) => {
        console.log('处理错误消息:', data);

        const messageText = data.message;
        addBookStatusMessage(messageText, data.bookKey);
    }

    // 处理章节状态消息
    const processChapterStatusMessage = (data: any) => {
        // console.log('处理章节状态消息:', data);

        // {
        //     "message": "联网搜索资料",
        //     "current_chapter_full_title": "第四章 国际贸易与市场动态",
        //     "current_chapter_title": "国际贸易与市场动态",
        //     "current_chapter_key": "726e9d4f-fbb8-4703-b958-f763017fd52c"
        // }

        if (data.message) {
            addChapterStatusMessage(data.message, data.current_chapter_key, data.current_chapter_full_title, data.status);
        } else {
            console.warn('章节状态消息为空，跳过添加');
        }
    };

    // 处理内容消息
    const processContentMessage = (data: any) => {
        /**
         * {
            "content_type": "block",
            "data": {
                "type": "paragraph",
                "content": [
                    {
                        "text": "xxx",
                        "type": "text"
                    }
                ]
            },
         *     "chapter_key": "3f6bbd05-e178-43bc-97d5-4cd67696cece"
         * }
         */

        const chapterStore = useChapterStore();
        if (!data) return;
        if (data.chapter_key != chapterStore.currentChapter?.key) return;
        if (data.content_type == 'block') {
            const fullContent = [
                data.data
            ];
            editorBus.emit('editor:appendGeneratedContent', { content: fullContent });
            console.log('已通过eventBus更新编辑器块内容');
        } else {
            editorBus.emit('editor:appendGeneratedContent', { content: data.data });
            console.log('已通过eventBus更新编辑器块内容');
        }
    };

    const processBookProgressMessage = (data: any) => {
        console.log('处理书籍进度消息:', data);

        traverseProgress.value = data.progress

        const chapterStore = useChapterStore();

        if (chapterStore.currentBook) {
            chapterStore.currentBook.generating_chapter_key = data.current_chapter_key
        }

        // 思考要不要跳转
        if (data.is_from_history) {
            return
        }

        if (data.current_chapter_key == chapterStore.currentChapter?.key) {
            return
        }

        //TODO 思考还有没有更好的方式
        chapterStore.loadChapter(data.current_chapter_key)
    };


    const resetData = () => {
        autoCreatingIndicatorVisible.value = false;
        messageContainers.value = [];
        currentChapterMessageGroup.value = null;
        messages.value = [];
        isGenerating.value = false;
        isTraversing.value = false;
        traverseProgress.value = 0;
        messageCounter = 0;
        chapterStatusCounter = 0;
        containerCounter = 0;
    }


    return {
        // 旧接口保持兼容
        messages,
        isGenerating,
        // 消息容器相关接口
        messageContainers,
        sortedMessageContainers,
        currentChapterMessageGroup,
        isTraversing,
        traverseProgress,
        autoCreatingIndicatorVisible,
        setIsGenerating,
        clearMessages,
        setIsTraversing,
        setTraverseProgress,
        toggleChapterGroupExpansion,
        collapseAllChapterGroups,
        addChapterStatusMessage,
        // SSE消息处理接口
        processSSEMessage,
        resetData,
    };
}); 