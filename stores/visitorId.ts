export const useVisitorIdStore = defineStore('visitorId', {
    state: () => ({
        visitorId: '',
    }),
    getters: {
        getVisitorId(): string {
            return this.visitorId
        },
    },
    actions: {
        setVisitorId() {
            // 首先尝试从本地存储获取访客ID
            const storedVisitorId = localStorage.getItem('visitorId')

            // 如果本地存储中已有访客ID，则直接使用
            if (storedVisitorId) {
                this.visitorId = storedVisitorId
                return
            }

            // 否则生成新的访客ID
            const fpPromise = import('https://static-1256600262.file.myqcloud.com/lib/fingerprintjs/v4.js')
                .then(FingerprintJS => FingerprintJS.load())

            // Get the visitor identifier when you need it.
            fpPromise
                .then(fp => fp.get())
                .then(result => {
                    // This is the visitor identifier:
                    this.visitorId = result.visitorId
                    // 将访客ID存储到本地存储
                    localStorage.setItem('visitorId', result.visitorId)
                })
        },
    },
})