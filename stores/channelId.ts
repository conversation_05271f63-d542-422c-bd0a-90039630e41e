import { StarloveConstants } from '@/utils/starloveConstants';
import { defineStore } from 'pinia';
import { channelType } from '~/utils/constants';
import { storage } from '~/utils/local-storage';
import { sessionStore } from '~/utils/session-storage';

interface channelState {
    channelId: number
}

function getReferrerType() {
    const referrer = document.referrer;
    if (!referrer) {
        return 'none'; // 没有 referer 时返回 none
    }
    const searchEngines = [
        'baidu',
        'google',
        'bing',
        'yahoo',
        'so', // 360 搜索
        'soso', // 腾讯 SOSO 搜索
        'uc', // UC 搜索
        'firefox', // 火狐搜索
        'yandex', // Yandex 搜索
        'duckduckgo', // DuckDuckGo 搜索
        'quark', // 夸克搜索
    ];
    for (const engine of searchEngines) {
        if (referrer.includes(engine)) {
            return 'search'; // 返回 'search' 表示来自搜索引擎
        }
    }
    return 'other'; // 返回 'other' 表示来自其他网站
}

export const useChannelStore = defineStore('channel', {
    state: (): channelState => ({
        channelId: 0
    }),
    getters: {
        getChannelId(): number {
            return this.channelId;
        },
    },
    actions: {
        calculateChannelId(currentPath: string) {
            try {
                const referrerType = getReferrerType();


                if (!currentPath) {
                    return channelType.UNKNOWN;
                }

                // 如果包含 manual，直接返回 SEO
                if (currentPath.includes('manual')) {
                    return channelType.SEO; // SEO
                }

                // 计算层级：统计 `/` 出现的次数
                const routeLevels = (currentPath.match(/\//g) || []).length;


                // 如果层级大于等于2
                if (routeLevels >= 2) {
                    if (referrerType == 'search') {
                        return channelType.SEO; // SEO
                    } else if (referrerType == 'other') {
                        return channelType.UNKNOWN; // 其他
                    } else if (referrerType == 'none') {
                        return channelType.ORGANIC; // 自然流量
                    }
                    return channelType.SEO; // SEO
                }

                // 如果层级小于等于1
                if (routeLevels <= 1) {
                    if (referrerType == 'search') {
                        const mediaPaths = ['/chat', '/create', '/news', '/library', '/download'];
                        if (mediaPaths.includes(currentPath)) {
                            return channelType.SOCIALMEDIA; // 自媒体
                        }

                        if (currentPath == '/') {
                            return channelType.SOCIALMEDIA; // 自媒体
                        }

                        return channelType.ORGANIC; // 自然流量
                    }

                    // 如果不是搜索引擎，返回自然流量
                    return channelType.ORGANIC; // 自然流量
                }

                // 默认返回自然流量
                return channelType.UNKNOWN; // 未知
            }
            catch {
                return channelType.UNKNOWN; // 未知
            }
        },

        remove() {
            if (import.meta.client) {
                sessionStore.remove(StarloveConstants.keyOfsessionStorage.sessionChannelData)
                storage.remove(StarloveConstants.keyOflocalStorage.localChannelData)
            }
        }
    },
});