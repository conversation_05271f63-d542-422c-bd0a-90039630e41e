import { defineStore } from 'pinia'
import { computed, ref } from 'vue'

// 弹窗类型枚举
export enum DialogType {
    SHORTCUT_KEY = 'shortcutKey',
    THEME = 'theme',
    BASE_STYLE = 'baseStyle',
    OUTLINE = 'outline'
}

// 类型定义
interface DialogState {
    [DialogType.SHORTCUT_KEY]: boolean
    [DialogType.THEME]: boolean
    [DialogType.BASE_STYLE]: boolean
    [DialogType.OUTLINE]: boolean
}

interface MindMapState {
    isDark: boolean
    activeSidebar: string | null
    sidebarVisible: boolean
    sidebarZIndex: number
    isOutlineEdit: boolean
    themeName: string
    dialogs: DialogState
    notHandleDataChange: boolean
    isHandleNodeTreeRenderEnd: boolean
    beInsertNodeUid: string
    insertType: string
    currentData: any | null
    isDragOutlineTreeNode: boolean
}

export const useMindMapStore = defineStore('mindMap', () => {
    // 状态定义
    const state = ref<MindMapState>({
        isDark: false,
        activeSidebar: null,
        sidebarVisible: false,
        sidebarZIndex: 1,
        isOutlineEdit: false,
        themeName: 'default',
        dialogs: {
            [DialogType.SHORTCUT_KEY]: false,
            [DialogType.THEME]: false,
            [DialogType.BASE_STYLE]: false,
            [DialogType.OUTLINE]: false
        },
        notHandleDataChange: false,
        isHandleNodeTreeRenderEnd: false,
        beInsertNodeUid: '',
        insertType: '',
        currentData: null,
        isDragOutlineTreeNode: false
    })

    // Getters
    const isDark = computed(() => state.value.isDark)
    const activeSidebar = computed(() => state.value.activeSidebar)
    const sidebarVisible = computed(() => state.value.sidebarVisible)
    const isOutlineEdit = computed(() => state.value.isOutlineEdit)
    const themeName = computed(() => state.value.themeName)
    const dialogs = computed(() => state.value.dialogs)
    const currentData = computed(() => state.value.currentData)
    const notHandleDataChange = computed(() => state.value.notHandleDataChange)
    const isHandleNodeTreeRenderEnd = computed(() => state.value.isHandleNodeTreeRenderEnd)
    const beInsertNodeUid = computed(() => state.value.beInsertNodeUid)
    const insertType = computed(() => state.value.insertType)
    const isDragOutlineTreeNode = computed(() => state.value.isDragOutlineTreeNode)

    // Actions
    const setThemeName = (value: string) => {
        state.value.themeName = value
    }

    const setIsDark = (value: boolean) => {
        state.value.isDark = value
    }

    const setActiveSidebar = (value: string | null) => {
        if (!state.value.sidebarVisible) {
            state.value.sidebarVisible = true
        }
        state.value.activeSidebar = value
    }

    const showSidebar = (type: string) => {
        if (!type) return
        state.value.sidebarVisible = true
        state.value.activeSidebar = type
        state.value.sidebarZIndex++
    }

    const hideSidebar = () => {
        state.value.sidebarVisible = false
        state.value.activeSidebar = null
    }

    const setIsOutlineEdit = (value: boolean) => {
        state.value.isOutlineEdit = value
    }

    const setNotHandleDataChange = (value: boolean) => {
        state.value.notHandleDataChange = value
    }

    const setIsHandleNodeTreeRenderEnd = (value: boolean) => {
        state.value.isHandleNodeTreeRenderEnd = value
    }

    const setBeInsertNodeUid = (value: string) => {
        state.value.beInsertNodeUid = value
    }

    const setInsertType = (value: string) => {
        state.value.insertType = value
    }

    const setCurrentData = (value: any) => {
        state.value.currentData = value
    }

    const setIsDragOutlineTreeNode = (value: boolean) => {
        state.value.isDragOutlineTreeNode = value
    }

    const handleDataChange = () => {
        if (state.value.notHandleDataChange) {
            state.value.notHandleDataChange = false
            return
        }
    }

    const handleNodeTreeRenderEnd = () => {
        if (state.value.insertType) {
            state.value.insertType = ''
            return
        }
        if (state.value.isHandleNodeTreeRenderEnd) {
            state.value.isHandleNodeTreeRenderEnd = false
        }
    }

    const hideTextEdit = () => {
        if (state.value.notHandleDataChange) {
            state.value.notHandleDataChange = false
        }
    }

    const toggleDialog = (type: DialogType, value?: boolean) => {
        state.value.dialogs[type] = value !== undefined ? value : !state.value.dialogs[type]
    }

    return {
        // State
        isDark,
        activeSidebar,
        sidebarVisible,
        isOutlineEdit,
        themeName,
        dialogs,
        currentData,
        notHandleDataChange,
        isHandleNodeTreeRenderEnd,
        beInsertNodeUid,
        insertType,
        isDragOutlineTreeNode,
        // Actions
        setThemeName,
        setIsDark,
        setActiveSidebar,
        showSidebar,
        hideSidebar,
        setIsOutlineEdit,
        setNotHandleDataChange,
        setIsHandleNodeTreeRenderEnd,
        setBeInsertNodeUid,
        setInsertType,
        setCurrentData,
        setIsDragOutlineTreeNode,
        handleDataChange,
        handleNodeTreeRenderEnd,
        hideTextEdit,
        toggleDialog
    }
}) 