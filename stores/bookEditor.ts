import { Editor } from '@tiptap/vue-3'

interface BookEditorState {
  editor: Editor | null | undefined
  mathModalVisible: boolean,
  mathEditingData: {
    formula: string
    type: string
  },
  isLoadingSave: boolean
}
export const useBookEditorStore = defineStore('bookEditor', {
  state: (): BookEditorState => ({
    editor: null,
    mathModalVisible: false,
    mathEditingData: {
      formula: '',
      type: 'inline' as 'inline' | 'block'
    },
    isLoadingSave: false,
  }),
})

