import { getGoodsList, getOrderDetail, testGiftCreateOrder } from '@/api/recharge'
import type { CachedPayInfo, WebPayQrcode, XiaoinGoodInfo } from '@/services/types/recharge'
import { UserService } from '@/services/user'
import { getAppId, KnowledgeAssistantMemberVipCode, RechargeOrderStatus, VipLevelNumber } from '@/utils/constants'
import { PaymentResult, processPayment } from '@/utils/payment'
import { StarloveUtil } from '@/utils/util'
import { getPayChannel } from '@/utils/utils'
import { message } from 'ant-design-vue'
import { defineStore } from 'pinia'
import { useRechargeStore } from './recharge'


interface VipPlansState {
  goodsList: XiaoinGoodInfo[]
  experienceMemberGoodList: XiaoinGoodInfo[]
  currentRechargeInfo: XiaoinGoodInfo | null
  isLoading: boolean
  orderId: string
  timestamp: number
  webPayQrcodeLink?: WebPayQrcode
  isQrcodeImageLoading: boolean
  isQrcodeImageError: boolean
  payChannel: string
  timeoutId: NodeJS.Timeout | null
  intervalId: NodeJS.Timeout | undefined
  cachedPayInfoMap?: Record<string, CachedPayInfo>
  from: string
  amount: number
}

export const useVipPlansStore = defineStore('vipPlans', {
  state: (): VipPlansState => ({
    goodsList: [],
    experienceMemberGoodList: [],
    currentRechargeInfo: null,
    isLoading: false,
    orderId: '',
    timestamp: 0,
    webPayQrcodeLink: {
      linkUrl: '',
      isQrCode: false,
      isError: false
    },
    isQrcodeImageLoading: true,
    isQrcodeImageError: false,
    payChannel: '9',
    timeoutId: null,
    intervalId: undefined,
    cachedPayInfoMap: {},
    from: '',
    amount: 0
  }),

  getters: {
    knowledgeAssistantMemberInfo() {
      return UserService.getKnowledgeAssistantMemberInfo()
    },

    isMaxLevel(): boolean {
      return this.knowledgeAssistantMemberInfo?.vipCode === KnowledgeAssistantMemberVipCode.C3
    }
  },

  actions: {
    isItemDisabled(item: XiaoinGoodInfo): boolean {
      if (item.extraParams.vipLevel != VipLevelNumber.level4 && (this.knowledgeAssistantMemberInfo?.vipLevel || 0) >= item.extraParams.vipLevel) {
        return true
      }
      if ((this.knowledgeAssistantMemberInfo?.vipLevel || 0) >= VipLevelNumber.level4 && item.extraParams.vipLevel == VipLevelNumber.level4) {
        return false
      }
      return false
    },

    initGoodsList() {
      if (this.goodsList.length > 0) {
        // if ((this.knowledgeAssistantMemberInfo?.vipLevel || 0) > 0) {
        this.goodsList = this.handleGoodsListOrder(this.goodsList)
        // }
        return true
      }
      return false
    },

    handleGoodsListOrder(list: XiaoinGoodInfo[]): XiaoinGoodInfo[] {

      const type = this.knowledgeAssistantMemberInfo?.vipCode || ''

      // console.log('type  ==>', type)
      // 此处判断是否鼠标过来的弹出窗，如果是选择至尊会员
      if (this.from === 'mouse') {
        // const reversedList = [...list].reverse()
        this.changeCurentPlan(list[list.length - 1])
        return list
      }
      // 已经是至尊会员
      if (type === KnowledgeAssistantMemberVipCode.C3) {
        this.changeCurentPlan(list.filter(item => item.extraParams.vipLevel == VipLevelNumber.level4)[0])
        return list
      }

      // 定位至尊会员
      if ([
        KnowledgeAssistantMemberVipCode.C1,
        KnowledgeAssistantMemberVipCode.C2,
        KnowledgeAssistantMemberVipCode.C3,
        KnowledgeAssistantMemberVipCode.C4,
        KnowledgeAssistantMemberVipCode.C6,
        KnowledgeAssistantMemberVipCode.C5,
        KnowledgeAssistantMemberVipCode.E,
        KnowledgeAssistantMemberVipCode.C0EX,
        KnowledgeAssistantMemberVipCode.C1EX,
        KnowledgeAssistantMemberVipCode.C2EX,
        KnowledgeAssistantMemberVipCode.C3EX,
        KnowledgeAssistantMemberVipCode.C4EX,
      ].includes(type)) {
        this.changeCurentPlan(list[list.length - 1])
        return list
      }
      // 定位标准会员，并计算最合适硬币
      if ([
        KnowledgeAssistantMemberVipCode.A,
        KnowledgeAssistantMemberVipCode.B1,
        KnowledgeAssistantMemberVipCode.B2,
        KnowledgeAssistantMemberVipCode.B3,
        KnowledgeAssistantMemberVipCode.C0,
      ].includes(type)) {
        const rechargeStore = useRechargeStore()
        // console.log('differenceValue==>', differenceValue)
        const differenceValue = rechargeStore.expendTokens //* 10000

        if (differenceValue > 0) {
          if (differenceValue <= 1000000) {
            this.changeCurentPlan(list.filter(item => item.extraParams.vipLevel === 1)[0])
            return list
          }
          const filteredList = list.filter(item => item.description.coinNum > differenceValue)
          // console.log('filteredList==>', filteredList)
          // 如果找不到大于目标值的对象，则选择最后一个，最后一个是至尊会员
          if (filteredList.length === 0) {
            this.changeCurentPlan(list[list.length - 1])
            console.warn('没有找到大于目标值的对象')
            return list
          }
          this.changeCurentPlan(filteredList[0])
          return list
        }
        this.changeCurentPlan(list.filter(item => item.extraParams.vipLevel === 1)[0])
        return list
      }
      return list
    },

    async loadGoodsData() {
      if (this.initGoodsList()) {
        return
      }

      this.isLoading = true
      await UserService.loadKnowledgeAssistantMemberInfo()
      this.currentRechargeInfo = null

      const params = {
        pageNo: 1,
        pageSize: 20,
        goodsGroupId: GoodsListType.knowledgeAssistant,
        isFirstCharge: (this.knowledgeAssistantMemberInfo?.vipLevel || 0) <= 0
      }

      const res = await getGoodsList(params)
      if (!res.ok || !res.data) {
        this.isLoading = false
        throw new Error(res.message || '充值类型加载失败')
      }

      this.timestamp = res.timestamp
      const data = res.data.records || []

      const list = data.map((item: any) => {
        try {
          if (item.description) {
            item.description = JSON.parse(item.description)
          }
          if (item.extraParams) {
            item.extraParams = JSON.parse(item.extraParams)
          }
          return item as XiaoinGoodInfo
        } catch (error) {
          return {
            ...item,
            description: {},
            extraParams: {}
          } as XiaoinGoodInfo
        }
      })

      let newList = list.filter((item: any) => item.extraParams.vipLevel > 0)
      // console.log('remainingItems ==>', remainingItems)
      if (this.knowledgeAssistantMemberInfo?.isVip) {
        newList = newList.filter((item: any) => item.extraParams.vipLevel != 0.5)
        this.experienceMemberGoodList = list.filter((item: any) => item.extraParams.vipLevel == 0.5)
      }

      this.goodsList = this.handleGoodsListOrder(newList)
      this.isLoading = false
    },

    setupInterval(callback: () => void) {
      if (this.intervalId) {
        clearInterval(this.intervalId)
      }
      this.intervalId = setInterval(callback, 5000)
    },

    clearTimers() {
      if (this.timeoutId) {
        clearTimeout(this.timeoutId)
        this.timeoutId = null
      }
      if (this.intervalId) {
        clearInterval(this.intervalId)
        this.intervalId = undefined
      }
      // this.currentRechargeInfo = null
    },

    async processPlan() {
      // 添加防护检查
      const currentPlan = this.currentRechargeInfo
      if (!currentPlan?.id) {
        console.warn('No recharge plan selected')
        return
      }

      // 根据环境判断payChannel的值
      this.payChannel = `${getPayChannel()}`
      // 清除之前的定时器
      this.clearTimers()

      this.isQrcodeImageLoading = true
      this.isQrcodeImageError = false

      // 重置二维码错误状态
      if (this.webPayQrcodeLink) {
        this.webPayQrcodeLink.isError = false
      }

      const cacheKey = `${currentPlan.id}_${this.payChannel}`

      try {
        // 检查缓存
        const cachedPayInfo = this.cachedPayInfoMap?.[cacheKey]
        if (cachedPayInfo) {
          const createTime = cachedPayInfo.createTime
          const now = Date.now()
          const diff = now - createTime
          if (diff < 1 * 60 * 1000) {
            // console.log('从缓存中读取: ', cacheKey, cachedPayInfo)
            this.orderId = cachedPayInfo.orderId
            this.amount = cachedPayInfo.amount
            this.webPayQrcodeLink = cachedPayInfo.payInfo
            this.isQrcodeImageLoading = false
            this.isQrcodeImageError = false
            // console.log('从缓存中读取 webPayQrcodeLink: ', this.webPayQrcodeLink)
            this.setupInterval(this.loadOrderData)
            this.timeoutId = setTimeout(
              () => {
                this.clearTimers()
                if (this.webPayQrcodeLink) {
                  this.webPayQrcodeLink.isError = true
                }
                // 删除过期的缓存
                if (this.cachedPayInfoMap) {
                  delete this.cachedPayInfoMap[cacheKey]
                }
              },
              StarloveUtil.isInTestServer() ? 1 * 60 * 1000 : 5 * 60 * 1000
            )
            return
          } else {
            console.log('缓存过期: ', cacheKey)
            if (this.cachedPayInfoMap) {
              delete this.cachedPayInfoMap[cacheKey]
            }
            this.isQrcodeImageError = false
            this.isQrcodeImageLoading = true
          }
        }

        console.log('开始获取新的支付信息')

        // 如果缓存中没有，则请求新数据
        const paymentResponse = await processPayment(
          currentPlan,
          this.payChannel
        )

        // console.log('paymentResponse==>', paymentResponse)

        this.isQrcodeImageLoading = false
        this.isQrcodeImageError = false

        if (paymentResponse.result === PaymentResult.SUCCESS && paymentResponse.orderId) {
          this.orderId = paymentResponse.orderId
          this.amount = paymentResponse.amount || 0
          this.webPayQrcodeLink = paymentResponse.payInfo!

          // 添加到缓存
          this.cachedPayInfoMap = this.cachedPayInfoMap || {}
          this.cachedPayInfoMap[cacheKey] = {
            orderId: paymentResponse.orderId,
            payInfo: paymentResponse.payInfo!,
            createTime: Date.now(),
            amount: paymentResponse.amount || 0
          }

          this.setupInterval(this.loadOrderData)
          this.timeoutId = setTimeout(
            () => {
              this.clearTimers()
              if (this.webPayQrcodeLink) {
                this.webPayQrcodeLink.isError = true
              }
              // 删除过期的缓存
              if (this.cachedPayInfoMap) {
                delete this.cachedPayInfoMap[cacheKey]
              }
            },
            StarloveUtil.isInTestServer() ? 1 * 60 * 1000 : 5 * 60 * 1000
          )
        } else {
          // 处理失败场景
          this.isQrcodeImageError = true
        }
      } catch (error) {
        console.error('处理支付信息时出错:', error)
        this.isQrcodeImageLoading = false
        this.isQrcodeImageError = true
      }
    },

    async loadOrderData() {
      if (!this.orderId) {
        this.clearTimers()
        return
      }
      const rechargeStore = useRechargeStore()
      const res = await getOrderDetail({ id: this.orderId })
      if (!res.ok || !res.data) return
      if (res.data.status !== RechargeOrderStatus.done) return

      this.clearTimers()

      await UserService.loadUserInfoAndAssistantMemberInfo()
      message.success('充值成功')

      rechargeStore.closeRechargeModal(RECHARGE_STATUS.SUCCESS)

      this.clearCache()
      //充值成功需要上报到必应
      try {
        if (window && typeof window.uet_report_conversion === 'function') {
          window.uet_report_conversion(this.currentRechargeInfo?.price)
        }
      } catch (error) {
        console.log(error)
      }
      return true
    },

    async handleTestPay() {
      this.payChannel = `${getPayChannel()}`
      const res = await testGiftCreateOrder({
        appid: getAppId(),
        orderId: this.orderId,
        payChannel: this.payChannel
      })

      if (!res.ok) {
        message.error(res.message || '充值失败')
        return false
      }
      setTimeout(async () => {
        await UserService.loadUserInfoAndAssistantMemberInfo()
        message.success('测试充值成功')
        const rechargeStore = useRechargeStore()
        rechargeStore.closeRechargeModal(RECHARGE_STATUS.SUCCESS)

        this.clearCache()
      }, 1000)

      return true
    },

    changeCurentPlan(plan: XiaoinGoodInfo) {

      // if (this.currentRechargeInfo?.id === plan?.id) {
      //   return 
      // }
      // console.log('changeCurentPlan 111==>', plan)
      // console.log('changeCurentPlan 222==>', this.currentRechargeInfo)
      this.currentRechargeInfo = plan
      this.processPlan()
    },

    clearCache(goodsId?: string) {
      if (goodsId && this.cachedPayInfoMap) {
        // 清除指定商品的缓存
        const cacheKey = `${goodsId}_${this.payChannel}`;
        delete this.cachedPayInfoMap[cacheKey];
      } else {
        // 清除所有缓存
        this.cachedPayInfoMap = {};
      }
    }
  }
}) 
