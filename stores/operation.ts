import { defineStore } from 'pinia';
import { getPlatform } from '~/utils/utils';

interface OperationGroup {
    id: string
    title: string
    items: any[]
}

interface Result {
    success: boolean
    code: number
    message: string
    result: OperationGroup[]
}

interface State {
    operationGroups: OperationGroup[]
}

export const useOperationStore = defineStore('operation', {
    state: (): State => ({
        operationGroups: []
    }),

    actions: {
        async loadOperationGroups() {
            if (this.operationGroups.length > 0) return

            const config = useRuntimeConfig()
            try {
                const controller = new AbortController()
                const timeout = setTimeout(() => controller.abort(), 8000) // 8秒超时

                const res: any = await $fetch(`${config.public.apiBase}/operationGroup/listCreator?platform=${getPlatform()}`)
                clearTimeout(timeout)
                if (res && res.success) {
                    this.operationGroups = res.result
                }
                return res
            } catch (error) {
                console.error('获取首页数据失败:', error)
            }
        }
    }
}) 