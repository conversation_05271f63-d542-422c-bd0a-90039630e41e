import { defineStore } from 'pinia'
import { getListLastUse } from '~/api/recent'
import { UserService } from '~/services/user'

interface RecentApp {
    code: string
    name: string
    avatar: string
}

export const useRecentAppsStore = defineStore('recentApps', {
    state: () => ({
        recentList: [] as RecentApp[]
    }),

    actions: {
        // 添加应用到最近使用列表
        addToRecentList(app: RecentApp) {
            // 如果已存在，先移除旧的
            this.recentList = this.recentList.filter(item => item.code !== app.code)
            // 添加到开头
            this.recentList.unshift(app)
            // 限制数量为10个
            if (this.recentList.length > 10) {
                this.recentList = this.recentList.slice(0, 10)
            }
        },

        // 从服务器加载最近使用列表
        async loadFromServer() {
            if (!UserService.isLogined()) {
                return
            }
            const result = await getListLastUse()
            if (result.ok && result.data) {
                this.recentList = result.data
            }
        }
    }
}) 