实现handlePressGenerateChapterSSE方法，通过fetch-event-source订阅@http://127.0.0.1:8000/api/batch/generate_content_sse/ 这个接口，
参数类似：
{
  "book_key": "f871a5d2-c4c5-450f-acd6-65716a148c97",
  "user_prompt": ""
}
阶段性返回内容：{
    "status": "processing",
    "current_chapter_key": "1a453cdb-2e46-49ef-ad94-3cda05e4b343",
    "current_chapter_title": "辅助系统集成设计",
    "current_chapter_index": 18,
    "total_chapters": 18,
    "progress": 97.22,
    "message": "正在生成章节 18/18: 辅助系统集成设计"
}

最后完成的内容：
{
    "status": "completed",
    "progress": 100,
    "total_chapters": 18,
    "completed_chapters": 18,
    "message": "《北京市某电厂15万Nm3/h烟气脱硫工艺设计》的所有 18 个章节生成完成"
}

期望：过程中通过message显示toast（持续显示），文案为返回值中的message。
完成后，显示toast并消失。
