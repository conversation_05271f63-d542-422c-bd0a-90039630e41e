FROM registry.cn-shanghai.aliyuncs.com/xiaoin/node:18-alpine

RUN mkdir -p /home/<USER>/app/node_modules && chown -R node:node /home/<USER>/app

WORKDIR /home/<USER>/app


# 复制当前目录下的所有文件到工作目录
COPY . /home/<USER>/app
COPY --chown=node:node .env.pre /home/<USER>/app/.env

# COPY --chown=node:node . .
# RUN npm config set registry https://registry.npmmirror.com && npm install pm2 -g

# # USER node

RUN npm config set registry https://registry.npmmirror.com && npm install pm2 -g && npm install -g pnpm@9.15.0 && pnpm install

# # 构建生产版本
RUN pnpm build:pre

# # 添加执行权限
# RUN chmod +x /home/<USER>/app/upload-static-pre.sh

EXPOSE 3001

# 替换 .env 文件并启动应用
# ENTRYPOINT ["/bin/sh", "-c", "cp -f .env.pre .env && npm run deploydocker:pre"]
CMD npm run deploydocker:pre
