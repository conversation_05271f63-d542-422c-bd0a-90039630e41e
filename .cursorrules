这是一个使用 Vue 3 和 Nuxt 3 构建的前端项目。项目使用 TypeScript 进行类型检查，TailwindCSS 进行样式管理，确保代码的可维护性和可读性。

## 技术栈
- **前端框架**: Vue 3, Nuxt 3
- **编程语言**: TypeScript, JavaScript
- **样式框架**: TailwindCSS
- **UI 组件库**: UI.Nuxt.com

## 代码实现指南
1. **使用 Composition API**: 所有组件都应使用 Vue 3 的 Composition API。
2. **使用 TailwindCSS**: 所有样式都应使用 TailwindCSS 类，避免使用内联样式或自定义 CSS。
3. **事件处理**: 事件处理函数应以 `handle` 前缀命名，例如 `handleClick`。
4. **常量和类型**: 使用 `const` 定义常量，必要时定义类型。
5. **代码简洁性**: 代码应简洁易读，避免重复代码（DRY 原则）。

## 注意事项
- 确保代码完整，无待办事项或占位符。
- 验证所有功能，确保其正常工作。
- 使用描述性变量和函数名称。

# API注意
- 开发接口时，优先用axios。尽量跟已经有的调用服务器接口的地方保持一致。
