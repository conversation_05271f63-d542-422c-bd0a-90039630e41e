@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* 全局样式文件 */
.ant-message {
  z-index: 99999 !important; /* 强制覆盖 */
}

.ant-btn-primary {
  background-color: #2551b5 !important;
}

/* Popconfirm 确保显示在 Modal 上层 */
.ant-popover {
  z-index: 100000 !important; /* 高于 Modal */
}

/* Cascader 弹出层确保显示在 Modal 上层 */
.ant-cascader-dropdown {
  z-index: 10001 !important; /* 高于 Modal 和 Popover */
}

/* Modal.success 图标和内容单行显示优化 */
.ant-modal-confirm .ant-modal-confirm-body {
  display: flex !important;
  align-items: center !important;
  gap: 12px !important;
}

.ant-modal-confirm .ant-modal-confirm-body .ant-modal-confirm-content {
  flex: 1 !important;
  min-width: 0 !important;
}

/* 确保内容可滚动到底部，防止被底部工具栏遮挡 */
@media (max-width: 1023px) {
  html,
  body {
    height: 100%;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    /* 增加iOS滚动流畅度 */
  }

  .h-screen {
    min-height: 100vh;
    /* 使用视口高度 */
    min-height: -webkit-fill-available;
    /* 适配iOS */
    /* height: auto; */
    /* 允许内容扩展 */
  }
}
