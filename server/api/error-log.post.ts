import { createRequire } from 'module';
const require = createRequire(import.meta.url);

const ALY = require('aliyun-sdk');

const sls = new ALY.SLS({
    accessKeyId: "LTAI5t6c5DkyKeNVcEsh12Rd",
    secretAccessKey: "******************************",
    endpoint: 'http://cn-shanghai.log.aliyuncs.com',
    apiVersion: '2015-06-01'
});

let logStoreName = 'user-error'

export default defineEventHandler(async (event) => {
    try {
        const body = await readBody(event);

        if (!body) {
            return { code: 400, message: "无效的请求体" };
        }

        const log = {
            time: Math.floor(new Date().getTime() / 1000),
            contents: [
                { key: 'error_type', value: body[0].error_type || 'unknown' },
                { key: 'error_message', value: body[0].error_message || '' },
                { key: 'timestamp', value: String(new Date().valueOf()) }
            ]
        };

        const param = {
            projectName: "xiaoin-frontend",
            logStoreName: logStoreName,
            logGroup: {
                logs: [log],
                topic: 'error',
                source: event.node.req.headers['x-forwarded-for'] || '127.0.0.1'
            }
        };

        await new Promise((resolve, reject) => {
            sls.putLogs(param, (err: any, data: any) => {
                if (err) reject(err);
                else resolve(data);
            });
        });
        return { code: 200, message: "错误日志写入成功" };
    } catch (error: any) {
        console.error("错误日志写入失败:", error);
        return { code: 500, message: error.message || "错误日志写入失败" };
    }
}); 