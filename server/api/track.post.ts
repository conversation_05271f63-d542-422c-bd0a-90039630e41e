import { createRequire } from 'module';
const require = createRequire(import.meta.url);

const ALY = require('aliyun-sdk');

const sls = new ALY.SLS({
    accessKeyId: "LTAI5t6c5DkyKeNVcEsh12Rd",
    secretAccessKey: "******************************",
    endpoint: 'http://cn-shanghai.log.aliyuncs.com',
    apiVersion: '2015-06-01'
});

let logStoreName = 'user-action-dev'

if (process.env.DEPLOY_ENV === 'prod') {
    logStoreName = 'user-action'
} else if (process.env.DEPLOY_ENV === 'pre') {
    logStoreName = 'user-action'
}

export default defineEventHandler(async (event) => {
    const body = await readBody(event);

    if (!body || !Array.isArray(body)) {
        return { code: 400, message: "无效的请求体" };
    }

    const logs = body.map((item: any) => {
        return {
            time: Math.floor(new Date().getTime() / 1000),
            contents: [
                { key: 'event', value: item.event || '' },
                { key: 'time', value: item.time || '' },
                { key: 'platform', value: item.platform },
                { key: 'timestamp', value: String(new Date().valueOf()) },
                { key: 'channel', value: item.channel || '' },
                { key: 'is_new_user', value: item.is_new_user.toString() || '' },
                { key: 'user_id', value: item.user_id || '' },
                { key: 'team_id', value: item.team_id || '' },
                { key: 'visitor_id', value: item.visitor_id || '' },
                { key: 'target_id', value: item.target_id || '' },
                { key: 'channel_id', value: item.channel_id.toString() || '' },
            ]
        };
    })
    if (logs.length === 0) {
        return { code: 400, message: "无有效日志数据" };
    }
    const param = {
        projectName: "xiaoin-frontend",
        logStoreName: logStoreName,
        logGroup: {
            logs: logs,
            topic: 'vv',
            source: event.node.req.headers['x-forwarded-for'] || '127.0.0.1'
        }
    };
    try {
        await new Promise((resolve, reject) => {
            sls.putLogs(param, (err: any, data: any) => {
                if (err) reject(err);
                else resolve(data);
            });
        });
        return { code: 200, message: "日志写入成功" };
    } catch (error: any) {
        console.error("日志写入失败:", error);
        return { code: 500, message: error.message || "日志写入失败" };
    }
});
