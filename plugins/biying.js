// 微软uet事件转化
export default defineNuxtPlugin(() => {
  if (import.meta.client) {
    ;(function (w, d, t, r, u) {
      var f, n, i
      ;(w[u] = w[u] || []),
        (f = function () {
          var o = { ti: '187127708', enableAutoSpaTracking: true }
          ;(o.q = w[u]), (w[u] = new UET(o)), w[u].push('pageLoad')
        }),
        (n = d.createElement(t)),
        (n.src = r),
        (n.async = 1),
        (n.onload = n.onreadystatechange =
          function () {
            var s = this.readyState
            ;(s && s !== 'loaded' && s !== 'complete') ||
              (f(), (n.onload = n.onreadystatechange = null))
          }),
        (i = d.getElementsByTagName(t)[0]),
        i?.parentNode?.insertBefore(n, i)
    })(window, document, 'script', '//bat.bing.com/bat.js', 'uetq')

    function uet_report_conversion(price) {
      window.uetq = window.uetq || []
      window.uetq.push('event', 'onPaySuccess', { revenue_value: price, currency: 'CNY' })
    }
    window.uet_report_conversion = uet_report_conversion
  }
})
