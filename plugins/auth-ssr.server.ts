/**
 * SSR 认证状态预初始化插件
 * 在服务端渲染阶段从 cookies 读取认证状态并预填充 Pinia store
 */
export default defineNuxtPlugin(async (_nuxtApp) => {
  // 只在服务端执行
  if (!import.meta.server) return

  try {
    const { CookieAuthManager } = await import('~/utils/cookie-auth')
    const { useUserStore } = await import('~/stores/user')

    // 获取认证数据
    const authData = CookieAuthManager.getAuthData()

    // 预填充用户 store
    const userStore = useUserStore()

    if (authData.userInfo) {
      userStore.setLoginInfo(authData.userInfo)

      // console.log("authData ==>", authData)
    }
    if (authData.knowledgeAssistantInfo) {
      userStore.setKnowledgeAssistantMemberInfo(authData.knowledgeAssistantInfo)
    }

  } catch (error) {
    // console.warn('SSR 认证状态预初始化失败:', error)
  }
})
