import { useErrorTracking } from '@/composables/useErrorTracking';
const sendMessage = async (text: string | undefined = '') => {
    if (!text) {
        return
    }
    const { trackError } = useErrorTracking();
    trackError('nuxt_error', text)
}

export default defineNuxtPlugin((nuxtApp) => {
    // Vue 错误处理
    nuxtApp.vueApp.config.errorHandler = (error: any) => {
        // console.error('Vue Error:', error)
        // 这里可以添加错误上报逻辑

        sendMessage(error?.message || '')
    }
    // Nuxt 错误处理
    nuxtApp.hook('vue:error', (error: any) => {
        // console.error('Nuxt Error:', error)
        // 这里可以添加错误上报逻辑
        sendMessage(`${error?.message || ''}\n${error?.stack || ''}`)
    })
    nuxtApp.hook('app:error', (error) => {
        // 处理错误，例如记录日志或修改状态码等
        // console.error('捕获到错误:', error);
        sendMessage(`${error?.message || ''}\n${error?.stack || ''}`)
        // error.code = 400;
        // // 可以添加更多自定义属性
        // error.customMessage = '这是自定义的错误信息';
        throw createError({ statusCode: 400, message: '内部服务器错误', fatal: true })

    });
}) 