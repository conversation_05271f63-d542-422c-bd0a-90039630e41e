/**
 * 客户端认证状态快速同步插件
 * 在客户端水合阶段快速同步认证状态，避免状态闪烁
 */
export default defineNuxtPlugin(async (nuxtApp) => {
  // 只在客户端执行
  if (!import.meta.client) return

  try {
    const { useAuthCookies } = await import('~/composables/useAuthCookies')
    const { useUserStore } = await import('~/stores/user')
    // 不再需要导入团队身份 store，避免重复初始化

    const authCookies = useAuthCookies()
    const userStore = useUserStore()

    // 快速同步认证状态到 store（如果 SSR 阶段没有正确初始化）
    if (!userStore.isLogined && authCookies.isLoggedIn.value) {
      // console.log('客户端快速同步认证状态')
      authCookies.syncToStore()
    }

  } catch (error) {
    // console.warn('客户端认证状态同步失败:', error)
  }
})
