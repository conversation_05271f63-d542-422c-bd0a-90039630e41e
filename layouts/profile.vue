<template>
    <NuxtLayout name="default">
        <div class="flex flex-col flex-1 h-screen min-h-0 md:flex-row bg-slate-50">
            <!-- 左侧菜单 - 桌面端显示 -->
            <div class="hidden w-64 h-full p-4 border-r md:flex bg-white/80 backdrop-blur-sm border-gray-200/70">
                <div class="flex flex-col flex-1 h-full">
                    <!-- 菜单项容器 - 添加滚动 -->
                    <div class="flex flex-col flex-1 min-h-0 space-y-2 overflow-y-auto">
                        <NuxtLink v-for="(item, index) in menuItems" :key="index" :to="`/profile/${item.key}`"
                            class="flex items-center px-4 py-3 rounded-lg cursor-pointer hover:bg-blue-50 whitespace-nowrap"
                            :class="{ 'bg-blue-50 text-blue-700': isCurrentRoute(item.key) }">
                            <component :is="item.icon" theme="outline" size="18" class="mr-3" />
                            <span class="text-base text-gray-600">{{ item.label }}</span>
                        </NuxtLink>
                    </div>

                    <!-- 退出按钮固定在底部 -->
                    <div class="flex-shrink-0 pt-4 mt-auto">
                        <a-popconfirm title="确定退出登录吗?" ok-text="确定" cancel-text="取消" @confirm="handlePressExit">
                            <div
                                class="flex items-center w-full px-4 py-3 rounded-lg cursor-pointer hover:bg-blue-50 whitespace-nowrap">
                                <component :is="Logout" theme="outline" size="18" class="mr-3" />
                                <span class="text-base text-gray-600">退出登录</span>
                            </div>
                        </a-popconfirm>
                    </div>
                </div>
            </div>

            <!-- 右侧内容区 -->
            <div class="flex-1 min-h-0">
                <div class="flex flex-col h-full">
                    <!-- 页面标题区 -->
                    <div
                        class="px-3 py-2 m-3 mb-3 border bg-white/80 backdrop-blur-sm rounded-xl sm:px-5 border-blue-700/20 md:mb-4">
                        <div class="flex items-center justify-between w-full ">
                            <div class="flex items-center">
                                <div
                                    class="flex items-center justify-center w-10 h-10 mr-3 bg-blue-700 shadow-inner rounded-xl md:mr-4">
                                    <component :is="getCurrentMenuIcon" theme="outline" size="24" md:size="32"
                                        fill="#FFFFFF" />
                                </div>
                                <div>
                                    <h1 class="text-lg font-bold text-blue-700">
                                        {{ getCurrentMenuTitle }}
                                    </h1>
                                    <p class="text-sm text-gray-500">{{ getCurrentMenuDescription }}</p>
                                </div>
                            </div>
                            <div class="hidden md:block ">
                                <div
                                    class="flex items-center justify-end flex-1 transition-all duration-200 bg-white border shadow-md sm:mx-4 md:flex-none border-blue-200/50 rounded-xl focus-within:border-blue-300/50 focus-within:ring-2 focus-within:ring-blue-200/50">
                                    <!-- 搜索图标 -->
                                    <div class="px-3 text-blue-500">
                                        <search theme="outline" size="24" fill="#3b82f6" />
                                    </div>

                                    <!-- 输入框 -->
                                    <input v-model="searchQuery" type="text" placeholder="搜索提问记录"
                                        class="flex-1 h-9 bg-transparent text-gray-800 text-sm placeholder-[#999] focus:outline-none text-base "
                                        @keyup.enter="handleSearch()" />
                                    <!-- 搜索按钮 -->
                                    <button @click="handleSearch()"
                                        class="bg-blue-700 h-full py-1.5 px-4 rounded-lg text-white text-sm font-medium transition-all duration-200 hover:bg-blue-600">
                                        <span>搜索</span>
                                    </button>
                                </div>
                            </div>

                        </div>
                    </div>

                    <!-- 主要内容区 - 添加滚动 -->
                    <div class="flex-1 min-h-0 overflow-y-auto">
                        <!-- <div class="p-4 border bg-white/80 backdrop-blur-sm rounded-xl md:p-6 border-gray-200/70"> -->
                        <slot />
                        <!-- </div> -->
                    </div>
                </div>
            </div>

            <!-- 移动端底部菜单 - 改为可滚动 -->
            <div class="fixed bottom-0 left-0 right-0 z-10 bg-white border-t md:hidden border-gray-200/70">
                <div class="flex px-2 py-1 overflow-x-auto scrollbar-hide">
                    <!-- 主要菜单项 -->
                    <div class="flex flex-shrink-0 space-x-2">
                        <NuxtLink v-for="(item, index) in menuItems.slice(0, 4)" :key="index"
                            :to="`/profile/${item.key}`" class="flex flex-col items-center p-2 rounded-lg min-w-[4rem]"
                            :class="{ 'text-blue-700': isCurrentRoute(item.key) }">
                            <component :is="item.icon" theme="outline" size="24" />
                            <span class="mt-1 text-xs whitespace-nowrap ">{{ item.label }}</span>
                        </NuxtLink>
                    </div>

                    <!-- 更多按钮 -->
                    <div class="flex items-center px-2">
                        <a-dropdown placement="top">
                            <div class="flex flex-col items-center p-2 rounded-lg min-w-[4rem]">
                                <More theme="outline" size="24" />
                                <span class="mt-1 text-xs">更多</span>
                            </div>
                            <template #overlay>
                                <a-menu>
                                    <a-menu-item v-for="(item, index) in menuItems.slice(4)" :key="index">
                                        <NuxtLink :to="`/profile/${item.key}`" class="flex items-center">
                                            <component :is="item.icon" theme="outline" size="20" class="mr-2" />
                                            <span>{{ item.label }}</span>
                                        </NuxtLink>
                                    </a-menu-item>
                                    <a-menu-divider />
                                    <a-menu-item>
                                        <a-popconfirm title="确定退出登录吗?" ok-text="确定" cancel-text="取消"
                                            @confirm="handlePressExit">
                                            <div class="flex items-center text-red-500">
                                                <component :is="Logout" theme="outline" size="20" class="mr-2" />
                                                <span>退出登录</span>
                                            </div>
                                        </a-popconfirm>
                                    </a-menu-item>
                                </a-menu>
                            </template>
                        </a-dropdown>
                    </div>
                </div>
            </div>
        </div>
    </NuxtLayout>
</template>

<script setup lang="ts">
import { useChannelStore } from '@/stores/channelId';
import { deleteStoredDate } from '@/utils/uvTracker';
import {
    Customer,
    Edit,
    Funds,
    Info,
    Lock,
    Logout,
    Message,
    MessageOne,
    More,
    Search,
    Share,
    ShoppingCart,
    TransactionOrder,
    User
} from '@icon-park/vue-next';
import { computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { deleteAuthentication } from '~/utils/request';
import { resetAllStores } from '~/utils/resetAllStores';
const { $eventBus } = useNuxtApp();

const route = useRoute()
const router = useRouter()
const channelStore = useChannelStore()
const searchQuery = ref('')
const handleSearch = () => {
    $eventBus.emit(StarloveConstants.keyOfEventBus.myQuestionSearch, { keyword: searchQuery.value })
}
// 菜单配置
const menuItems = [
    { key: 'basic', label: '编辑资料', icon: User },
    { key: 'questions', label: '提问记录', icon: MessageOne },
    { key: 'creations', label: '写作记录', icon: Edit },
    { key: 'coins', label: '硬币明细', icon: Funds },
    { key: 'orders', label: '我的订单', icon: TransactionOrder },
    { key: 'invite', label: '邀请赚硬币', icon: Share },
    { key: 'purchasehistory', label: '购买记录', icon: ShoppingCart },
    // { key: 'share', label: '分享赚硬币', icon: Share },
    // { key: 'guide', label: '使用说明', icon: Book },
    { key: 'security', label: '账号与安全', icon: Lock },
    { key: 'support', label: '联系客服', icon: Customer },
    { key: 'feedback', label: '意见反馈', icon: Message },
    { key: 'about', label: '关于我们', icon: Info },
]

// 判断当前路由
const isCurrentRoute = (key: string) => {
    return route.path === `/profile/${key}`
}
const handlePressExit = async () => {

    // 原有的退出登录逻辑
    deleteAuthentication()
    resetAllStores()
    // 删除UV日期记录
    await deleteStoredDate();
    channelStore.remove()
    router.replace({
        path: '/'
    })
}

// 获取当前菜单信息
const currentMenuItem = computed(() => {
    const path = route.path.split('/').pop()
    return menuItems.find(item => item.key === path) || menuItems[0]
})

const getCurrentMenuTitle = computed(() => currentMenuItem.value.label)
const getCurrentMenuIcon = computed(() => currentMenuItem.value.icon)

// 获取当前菜单的描述
const getCurrentMenuDescription = computed(() => {
    const descriptions: Record<string, string> = {
        basic: '管理您的个人信息和账户设置',
        questions: '查看您的历史提问记录',
        creations: '管理您写作的所有内容',
        coins: '查看硬币收支明细',
        orders: '查看您的订单记录',
        invite: '邀请好友获得硬币奖励',
        share: '分享内容赚取硬币',
        guide: '了解平台使用指南',
        security: '管理账号安全设置',
        support: '获取客服支持',
        feedback: '您的反馈将帮助我们做得更好！我们将在5-15个工作日内给予回复',
        about: '了解我们的故事'
    }
    return descriptions[currentMenuItem.value.key] || ''
})
onMounted(() => {

})
// 路由变化的时候恢复搜索框
watch(() => route.path, () => {
    searchQuery.value = ''
})

</script>

<style>
.scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
    display: none;
}
</style>