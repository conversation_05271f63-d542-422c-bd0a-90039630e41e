<template>
    <div class="flex h-screen overflow-auto bg-gray-50">
        <!-- 移动端汉堡菜单按钮 -->
        <button
            class="fixed z-50 p-2 border border-blue-100 rounded-lg shadow-lg lg:hidden top-4 left-4 bg-white/80 backdrop-blur-sm"
            @click="isSidebarOpen = !isSidebarOpen">
            <HamburgerButton :class="{ 'rotate-90': isSidebarOpen }"
                class="w-6 h-6 text-blue-600 transition-transform duration-300" />
        </button>

        <!-- 左侧导航栏 - 添加响应式类 -->
        <div :class="[
            'fixed lg:relative lg:translate-x-0 transition-transform duration-300',
            isSidebarOpen ? 'translate-x-0' : '-translate-x-full'
        ]"
            class="w-[240px] lg:w-[180px] z-40 h-screen bg-gradient-to-b from-blue-100 to-indigo-50 backdrop-blur-sm border-r border-blue-200/50 flex flex-col overflow-auto ">
            <!-- Logo部分 -->
            <div class="flex flex-row items-center justify-center p-4 space-x-2 align-middle">
                <img src="/logo.png" alt="logo" title="万能小in logo"
                    class="h-12 transition-all duration-300 lg:h-10 rounded-xl hover:shadow-xl hover:scale-105" />

                <h1 class="text-xl font-semibold text-blue-800">
                    万能小in
                </h1>
            </div>

            <!-- 导航菜单 -->
            <nav class="px-3 lg:px-2 py-6 lg:py-4 space-y-2 lg:space-y-1.5">
                <NavItem v-for="item in navItems" :key="item.path" :to="item.path" :title="item.title" :icon="item.icon"
                    :is-active="isRouteActive(item.path)" :hot="item.hot" class="text-base lg:text-sm" />
            </nav>

            <div class="flex flex-col justify-end flex-1 px-2 pb-2 space-y-2">
                <NuxtLink to="/download" v-if="isShowDowload">
                    <div
                        class="relative flex items-center px-4 py-2 transition-all duration-300 border rounded-lg bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200/50 hover:shadow-md hover:border-blue-300/70">
                        <download theme="filled" size="20" class="mr-2 text-blue-500" />
                        <span class="text-sm text-blue-600 bg-gradient-to-r">下载APP</span>
                    </div>
                </NuxtLink>

                <a :href="creationGuide" target="_blank">
                    <div
                        class="relative flex items-center px-4 py-2 transition-all duration-300 border rounded-lg bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200/50 hover:shadow-md hover:border-blue-300/70">
                        <file-success theme="filled" size="20" class="mr-2 text-blue-500" />
                        <span class="text-sm text-blue-600 bg-gradient-to-r">使用攻略</span>
                    </div>
                </a>

                <!-- 登录按钮区域 -->
                <div class="w-full">
                    <!-- 未登录状态 -->
                    <button v-if="!isLoggedIn" @click="handlePressLogin" class="w-full">
                        <div
                            class="flex items-center justify-center px-4 py-2 text-white rounded-lg bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 hover:shadow-md">
                            登录
                        </div>
                    </button>

                    <!-- 已登录状态 - 用户信息和会员区域 -->
                    <div class="my-4" v-else>
                        <UserInfoArea :showExpireDate="false" :showCoinBalanceArea="true" />
                        <div class="flex items-center justify-between w-full mt-3 cursor-pointer" ref="userProfileRef"
                            @click="handleUserProfileClick" @mouseenter="handleUserProfileMouseEnter"
                            @mouseleave="handleUserProfileMouseLeave">
                            <UserProfileArea />
                        </div>

                        <!-- <button v-if="isMobile" class="flex items-center justify-between w-full mt-3 cursor-pointer"
                            ref="userProfileRef" @click="handleUserProfileClick">
                            <UserProfileArea />
                        </button> -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 遮罩层 - 移动端侧边栏打开时显示 -->
        <div v-if="isSidebarOpen" class="fixed inset-0 z-30 bg-black/20 backdrop-blur-sm lg:hidden"
            @click="isSidebarOpen = false">
        </div>

        <!-- 右侧内容区域 -->
        <div class="flex flex-col flex-1 pl-0 overflow-auto lg:pl-0 ">
            <!-- 页面内容插槽 -->
            <slot />
        </div>

        <!-- 登录弹窗 -->
        <ClientOnly>
            <LoginModal v-if="userStore.showLoginModal" v-model="userStore.showLoginModal" />
        </ClientOnly>

        <!-- 充值弹窗 -->
        <RechargeModal v-if="rechargeStore.rechargeModalVisible" v-model:modelValue="rechargeStore.rechargeModalVisible"
            @recharge="handleRecharge" />


        <ReceivingModal v-if="receivingVisible" :isShowHint="false" v-model:open-visible="receivingVisible"
            @add-success="onAddRessSuccess" :containerId="'default-container'">
        </ReceivingModal>

        <user-bind-phone />

        <!-- 全局升级 Popover -->
        <Teleport to="body">
            <Transition name="fade">
                <div v-if="upgradePopoverStore.isVisible" class="fixed z-[1112] shadow-xl" :style="popoverStyle"
                    @mouseenter="handleUserProfileMouseEnter" @mouseleave="handleUserProfileMouseLeave">
                    <UpgradePopover :showShouldSwitchAccount="true" @onOpenUserCenter="onOpenUserCenter" />
                </div>
            </Transition>
        </Teleport>

        <!-- 添加专属客服弹窗 -->
        <ExclusiveServiceModal v-model="exclusiveStore.isModalOpen" />

        <!-- 移动端升级 Popover -->
        <MobileUpgradePopover v-if="mobileUpgradePopoverVisible" v-model="mobileUpgradePopoverVisible"
            @onOpenUserCenter="onOpenUserCenter" />

        <!-- 兑换搜索次数模态弹窗 -->
        <ExchangeSearchModal v-if="exchangeSearchStore.modalVisible"
            v-model:visible="exchangeSearchStore.modalVisible" />
    </div>
</template>

<script setup lang="ts">
import ExclusiveServiceModal from '@/components/Auth/ExclusiveServiceModal.vue';
import { useVipPlansStore } from '@/stores/vipPlans';
import { creationGuide } from '@/utils/constants';
import {
    Brain,
    DegreeHat,
    Download,
    FileSuccess,
    HamburgerButton,
    Home,
    MessageOne,
    NewspaperFolding,
    Write
} from '@icon-park/vue-next';
import { markRaw, onMounted, onUnmounted, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import LoginModal from '~/components/Auth/LoginModal.vue';
import RechargeModal from '~/components/Auth/RechargeModal.vue';
import UserBindPhone from '~/components/Auth/UserBindPhone.vue';
import ExchangeSearchModal from '~/components/Common/ExchangeSearchModal.vue';
import MobileUpgradePopover from '~/components/Common/MobileUpgradePopover.vue';
import UpgradePopover from '~/components/Common/UpgradePopover.vue';
import NavItem from '~/components/layout/NavItem.vue';
import ReceivingModal from '~/components/Profile/ReceivingModal.vue';
import { useApp } from '~/composables/useApp';
import { useExclusiveStore } from '~/stores/exclusiveStore';
import { RECHARGE_STATUS, useRechargeStore } from '~/stores/recharge';

import { useUpgradePopoverStore } from '~/stores/upgradePopover';
import { useUserStore } from '~/stores/user';
import { RechargeModalTab, UTM_SOURCE_VALUE } from '~/utils/constants';

const { isMobile } = useMobileDetection()

const app = useApp()
const route = useRoute()
const router = useRouter()
const { $eventBus } = useNuxtApp();

const receivingVisible = ref(false)
const vipPlansStore = useVipPlansStore()

const userStore = useUserStore()
const { isLogined: isLoggedIn } = storeToRefs(userStore)


const popoverStyle = {
    left: `170px`,
    bottom: `10px`,
    background: 'linear-gradient( 104deg, #E5F2FF 0%, #DEDBFF 100%)',
    borderRadius: '15px',
    boxShadow: '0px 1px 20px 0px #C7D6FE'
}

const isShowDowload = ref(app.value?.appCode != UTM_SOURCE_VALUE.THUNDEROBOT)
useHead({
    link: [
        {
            href: '//at.alicdn.com/t/c/font_4853281_u0u6jlfrt1g.css',
            rel: 'stylesheet',
        },
    ],
    script: [
        {
            src: '//o.alicdn.com/captcha-frontend/aliyunCaptcha/AliyunCaptcha.js', // 替换为你的外部 JS 文件 URL
            async: false, // 可选，是否异步加载
            defer: false, // 可选，是否延迟加载
        },
        {
            src: '//at.alicdn.com/t/c/font_4853281_u0u6jlfrt1g.js'
        }
    ],
})
// 导航菜单配置
const navItems = [
    {
        path: '/',
        title: '首页',
        icon: markRaw(Home)
    },
    {
        path: `/chat`,
        title: 'AI提问',
        icon: markRaw(MessageOne)
    },
    {
        path: '/create',
        title: 'AI写作',
        icon: markRaw(Write),
        hot: true
    },
    // {
    //     path: '/news',
    //     title: '资讯',
    //     icon: markRaw(NewspaperFolding)
    // },
    {
        path: '/book',
        title: 'AI写书',
        icon: markRaw(NewspaperFolding)
    },
    {
        path: '/library',
        title: '知识库',
        icon: markRaw(Brain)
    },
    {
        path: '/scholar',
        title: '学术搜索',
        icon: markRaw(DegreeHat)
    }
]


const exclusiveStore = useExclusiveStore();

// const handlePressNav = (element: any) => {
//     let path = element.path;
//     // if (element.title == '提问') {
//     //     const chat = useChatStore()
//     //     chat.setIsNewSessionId(true)
//     //     // path = `/chat/${getCurrentTimestampString()}`
//     // }
//     router.push(path)
// }
// 路由匹配方法
const isRouteActive = (path: string) => {
    let currentPath = route.path;
    if (path === '/') {
        return route.path === '/'
    }
    // if (currentPath === '/profile/questions') {
    //   currentPath = '/chat/questions'
    // }
    if (currentPath.startsWith('/chat')) {
        return path.startsWith('/chat')
    }
    return currentPath.startsWith(path)
}

const rechargeStore = useRechargeStore()
const upgradePopoverStore = useUpgradePopoverStore()
const exchangeSearchStore = useExchangeSearchStore()

const { currentTab } = storeToRefs(rechargeStore)

// 使用 storeToRefs 获取响应式的认证状态 - 更高效且简洁


const showLoginModal = ref(false)
const showRechargeModal = ref(false)


const handleShowRecharge = () => {
    showRechargeModal.value = true
}
const handleRecharge = (data: any) => {
    console.log('充值信息：', data)
    // 处理充值逻辑
    rechargeStore.closeRechargeModal()
}

const onOpenUserCenter = (event: Event) => {
    // 阻止事件冒泡
    event?.stopPropagation();

    // 先关闭 popover
    upgradePopoverStore.hide();

    // 使用 nextTick 确保状态更新后再跳转
    nextTick(() => {
        router.push('/profile');
    });
}

const handlePressLogin = () => {
    const userStore = useUserStore();
    userStore.openLoginModal();
};


const onAddRessSuccess = () => { }

// 添加侧边栏状态控制
const isSidebarOpen = ref(false)

// 监听路由变化，在移动端自动关闭侧边栏
watch(() => route.path, () => {
    isSidebarOpen.value = false
})

watch(() => rechargeStore.rechargeStatus, (_newValue) => {
    // 充值至尊会员成功
    if (currentTab.value === RechargeModalTab.vip && _newValue == RECHARGE_STATUS.SUCCESS && vipPlansStore.currentRechargeInfo?.extraParams.vipLevel == 3) {
        receivingVisible.value = true
    }

})

watch(() => rechargeStore.rechargeModalVisible, (_newValue) => {
    if (_newValue) {
        upgradePopoverStore.hide()
    }
})


const handleLoginEvent = () => {
    showLoginModal.value = true
}

// 监听窗口尺寸变化
onMounted(async () => {
    const handleResize = () => {
        if (window.innerWidth >= 1024) { // lg breakpoint
            isSidebarOpen.value = false
        }
    }

    $eventBus.on(StarloveConstants.keyOfEventBus.goToLogin, handleLoginEvent);
    $eventBus.on(StarloveConstants.keyOfEventBus.rechargeModalOpen, handleShowRecharge);

    window.addEventListener('resize', handleResize)
    handleResize() // 初始化时执行一次



    onUnmounted(() => {
        window.removeEventListener('resize', handleResize)
    })

    // 在客户端初始化 PWA 相关功能
    // if (import.meta.client) {
    //     //setupPWAInstallation()
    //     //inRunningInPWA()
    //     //UserService.beforeInstallPromptListener()


    // if (isLoggedIn.value) {
    //     watch(() => UserService.getKnowledgeAssistantMemberInfo(), (_newValue) => {
    //         knowledgeAssistantMemberInfo.value = _newValue
    //     }, { immediate: true })
    // }


    // 判断浏览器地址 ?action=login&redirectUrl=${href}` 带这个标识， 弹出登录框
    if (window.location.search.indexOf('action=login') > -1) {
        userStore.openLoginModal()
        try {
            // 解析redirectUrl的值
            const urlParams = new URLSearchParams(window.location.search);
            const redirectUrl = urlParams.get('redirectUrl');
            if (redirectUrl) {
                // 将redirectUrl存储到sessionStorage
                sessionStorage.setItem(StarloveConstants.keyOflocalStorage.redirectUrl, redirectUrl)
            }
        } catch (error) {
            console.error('解析redirectUrl失败:', error);
        }
    }

})
onBeforeUnmount(() => {
    $eventBus.off(StarloveConstants.keyOfEventBus.goToLogin, handleLoginEvent);
    $eventBus.off(StarloveConstants.keyOfEventBus.rechargeModalOpen, handleShowRecharge);
});


const userProfileRef = ref<HTMLElement | null>(null)

const handleUserProfileMouseEnter = () => {
    if (isMobile.value) {
        return
    }
    if (userProfileRef.value instanceof HTMLElement) {
        const rect = userProfileRef.value.getBoundingClientRect()
        upgradePopoverStore.show(rect)
    }
}

const handleUserProfileMouseLeave = () => {
    if (isMobile.value) {
        return
    }
    upgradePopoverStore.hide()
}


const mobileUpgradePopoverVisible = ref(false)
const handleUserProfileClick = () => {
    if (!isMobile.value) {
        return
    }
    mobileUpgradePopoverVisible.value = true
}

</script>

<style>
/* 自定义滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(241, 245, 249, 0.8);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(to bottom, rgba(59, 130, 246, 0.3), rgba(147, 197, 253, 0.3));
    border-radius: 4px;
    transition: all 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(to bottom, rgba(59, 130, 246, 0.5), rgba(147, 197, 253, 0.5));
}

/* Firefox滚动条样式 */
* {
    scrollbar-width: thin;
    scrollbar-color: rgba(59, 130, 246, 0.3) rgba(241, 245, 249, 0.8);
}

/* Fade 动画 */
.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
}
</style>