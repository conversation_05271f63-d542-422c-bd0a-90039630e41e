<template>
  <div class="flex h-screen bg-gray-50">
    <!-- 右侧内容区域 -->
    <div class="flex-1">
      <slot />
    </div>

    <!-- 充值弹窗 -->
    <RechargeModal v-if="rechargeStore.rechargeModalVisible" v-model="rechargeStore.rechargeModalVisible"
      @recharge="handleRecharge" />

    <ClientOnly>
      <LoginModal v-if="userStore.showLoginModal" v-model="userStore.showLoginModal" />
    </ClientOnly>

    <ReceivingModal v-if="receivingVisible" :isShowHint="false" v-model:open-visible="receivingVisible"
      @add-success="onAddRessSuccess" :containerId="'book-container'">
    </ReceivingModal>

    <user-bind-phone />

    <!-- 添加专属客服弹窗 -->
    <ExclusiveServiceModal v-model="exclusiveStore.isModalOpen" />

    <!-- 兑换搜索次数模态弹窗 -->
    <ExchangeSearchModal v-if="exchangeSearchStore.modalVisible" v-model:visible="exchangeSearchStore.modalVisible" />
  </div>
</template>

<script setup lang="ts">
import { useVipPlansStore } from '@/stores/vipPlans';
import { VipLevelNumber } from '@/utils/constants';
import LoginModal from '~/components/Auth/LoginModal.vue';
import RechargeModal from '~/components/Auth/RechargeModal.vue';
import ExchangeSearchModal from '~/components/Common/ExchangeSearchModal.vue';
import { useExclusiveStore } from '~/stores/exclusiveStore';
import { useRechargeStore } from '~/stores/recharge';
import { useUserStore } from '~/stores/user.js';


const exclusiveStore = useExclusiveStore();
const exchangeSearchStore = useExchangeSearchStore()

const rechargeStore = useRechargeStore()

const userStore = useUserStore()
const vipPlansStore = useVipPlansStore()

const { currentTab } = storeToRefs(rechargeStore)

const receivingVisible = ref(false)



watch(() => rechargeStore.rechargeStatus, (_newValue) => {
  // 充值至尊会员成功
  if (currentTab.value === RechargeModalTab.vip && _newValue == RECHARGE_STATUS.SUCCESS && vipPlansStore.currentRechargeInfo?.extraParams.vipLevel == VipLevelNumber.level4) {
    receivingVisible.value = true
  }
})

const handleRecharge = () => {
  rechargeStore.closeRechargeModal()
}

const onAddRessSuccess = () => { }

// 页面加载时获取数据
onMounted(() => {

  watch(() => userStore.showLoginModal, (_newValue) => {
    if (_newValue) {
      rechargeStore.rechargeModalVisible = false
      receivingVisible.value = false
    }
  })

})
</script>

<style scoped></style>