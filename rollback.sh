#!/bin/bash

BASE_PATH="/Users/<USER>/deploy"
APP_NAME="xiaoin_nuxt"
CURRENT_LINK="${BASE_PATH}/current"
BLUE_DIR="${BASE_PATH}/${APP_NAME}_blue"
GREEN_DIR="${BASE_PATH}/${APP_NAME}_green"

# 确定当前运行的是哪个目录
if [ "$(readlink ${CURRENT_LINK})" = "${BLUE_DIR}" ]; then
    ROLLBACK_DIR="${GREEN_DIR}"
    CURRENT_DIR="${BLUE_DIR}"
    PORT="3002"
else
    ROLLBACK_DIR="${BLUE_DIR}"
    CURRENT_DIR="${GREEN_DIR}"
    PORT="3001"
fi

echo "回滚到目录: ${ROLLBACK_DIR}"

# 启动旧版本
cd ${ROLLBACK_DIR}
pm2 start ecosystem.config.cjs --name "${APP_NAME}_$(basename ${ROLLBACK_DIR})"

# 等待启动
sleep 5

# 更新符号链接
ln -sfn ${ROLLBACK_DIR} ${CURRENT_LINK}

# 关闭当前版本
pm2 delete "${APP_NAME}_$(basename ${CURRENT_DIR})"

echo "回滚完成! 应用运行在端口 ${PORT}" 