<template>
  <div>
    <a-spin :spinning="isNeedGetTheWordCount" tip="读取中...">
      <a-table rowKey="id" :showHeader="false" :columns="columns" :data-source="attachmentsList" :row-selection="{
        type: 'checkbox',
        selectedRowKeys: selectedFileIds,
        onChange: handleChangeSelect,
        getCheckboxProps: (record) => ({
          disabled:
            record.wordCount == -1 ||
            record.wordCount == 0 ||
            submission.status == SubmissionStatus.done,
          name: record.fileName
        })
      }" :scroll="{ y: 120 }" :pagination="false" size="small" :border="false">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'fileName'">
            <div class="file-name">
              <span v-if="record.fileName">{{ record.fileName }}</span>
              <span v-else>-</span>
            </div>
          </template>
          <template v-if="column.key === 'wordCount' && submission.status != SubmissionStatus.done">
            <span v-if="record.wordCount < 0">
              {{ isNeedGetTheWordCount ? '' : '字数读取错误，请稍后再试' }}
            </span>
            <span style="min-width: 80px" v-else-if="record.wordCount < MIN_WORDS_COUNT">
              {{ isNeedGetTheWordCount ? '' : '文档字数不足100字' }}
            </span>
            <span v-else>共{{ record.wordCount || 0 }}字</span>
          </template>
          <template v-if="column.key === 'coinNum' && submission.status != SubmissionStatus.done">
            <span v-if="record.wordCount < 0"></span>
            <span style="min-width: 295px" v-else-if="record.wordCount < MIN_WORDS_COUNT">
              {{ isNeedGetTheWordCount ? '' : '不会学习及产生学习费用，请重新上传文件' }}
            </span>
            <span v-else-if="submission.creatorCode != 'ppt'" style="min-width: 295px">
              <span v-if="selectedFileIds.findIndex((item) => item == record.id) > -1">
                <span v-if="record.coinNum >= 0">学习费用：{{ studyingFees(record) }}万硬币</span>
                <span v-else>-</span>
              </span>
              <span style="min-width: 295px" v-else>
                {{ isNeedGetTheWordCount ? '读取中...' : '未选择该文献，不会学习及产生学习费用' }}
              </span>
            </span>
          </template>
        </template>

      </a-table>
    </a-spin>
  </div>
</template>

<script setup lang="ts">
import { getCreateSubmissionResult } from '@/api/create';
import type { SubmissionInfo } from '@/services/types/submission';
import { MIN_WORDS_COUNT } from '@/utils/constants';
import { StarloveConstants } from '@/utils/starloveConstants';
import { message } from 'ant-design-vue';
import {
  computed,
  onBeforeUnmount,
  onMounted,
  onUnmounted,
  ref,
  watch,
  watchEffect
} from 'vue';
import type { KnowledgeAssistantMemberInfo } from '~/services/types/loginMobileRes';
import { useSubmissionStore } from '~/stores/submission';
import { SubmissionStatus } from '~/utils/constants';
const { $eventBus } = useNuxtApp();
const columns = [
  {
    name: 'fileName',
    dataIndex: 'fileName',
    key: 'fileName',
    width: '50%'
  },
  {
    title: 'wordCount',
    dataIndex: 'wordCount',
    key: 'wordCount',
    width: '20%'
  },
  {
    title: 'coinNum',
    dataIndex: 'coinNum',
    key: 'coinNum',
    width: '30%'
  }
]

interface Props {
  submission: SubmissionInfo
  isNeedGetTheWordCount: boolean
  knowledgeAssistantMemberInfo?: KnowledgeAssistantMemberInfo | null
  // attachmentsList: AttachmentsInfo[]
  // isCanSelect: boolean
}
// const isPc = ref(isPcWeb())

const props = withDefaults(defineProps<Props>(), {})
const emit = defineEmits(['update:isNeedGetTheWordCount', 'getSelectedFileIdsAndAttachments'])
const isNeedGetTheWordCount = computed({
  get: () => props.isNeedGetTheWordCount,
  set: (val) => {
    emit('update:isNeedGetTheWordCount', val)
  }
})

let intervalId: NodeJS.Timeout | undefined = undefined

onMounted(() => {
  console.log('onMounted ==>', props.submission)
  checkAttachmentsList()
  $eventBus.emit(StarloveConstants.keyOfEventBus.updateTheSubmissionAttachmentInformation, props.isNeedGetTheWordCount)

})
onUnmounted(() => {
  clearTheInterval()
})

onBeforeUnmount(() => {
  clearTheInterval()
})

watch(() => isNeedGetTheWordCount.value, (newValue, _oldValue) => {
  $eventBus.emit(StarloveConstants.keyOfEventBus.updateTheSubmissionAttachmentInformation, newValue)
})
const attachmentsList = ref(props.submission.attachments || [])

const ids = computed(() => {
  const list: string[] = []
  attachmentsList.value.map((item) => {
    if (item.wordCount && item.wordCount != 0 && item.wordCount != -1) {
      list.push(item.id)
    }
  })
  return list
})

const selectedFileIds = ref<string[]>(ids.value)



const studyingFees = (record: any) => {
  if (props.knowledgeAssistantMemberInfo && props.knowledgeAssistantMemberInfo.vipLevel >= 1) {
    return 0
  }
  const fees = Math.floor((record.coinNum / 10000) * 10) / 10
  if (fees <= 0) {
    return 0
  }
  return fees
}


watchEffect(() => {
  const list = selectedFileIds.value.map((item) => {
    return {
      fileId: item
    }
  })
  const params = {
    list: list || [],
    isNeedWordCount: isNeedGetTheWordCount.value,
    attachmentsList: attachmentsList.value,
    selectedRowKeys: selectedFileIds.value
  }
  // if (!getSelectedFileIds) {
  //   return
  // }
  // getSelectedFileIds(params)
  // getSelectedFileIdsAndAttachments(params)
  emit('getSelectedFileIdsAndAttachments', params)
})

const handleChangeSelect = (selectedRowKeys: (string | number)[], selectedRows: any[]) => {
  if (selectedRowKeys.length <= 0) {
    message.warning('至少需要选择一个文件')
    return
  }
  selectedFileIds.value = selectedRowKeys.map(key => String(key))
  emit('getSelectedFileIdsAndAttachments', { selectedRowKeys, attachmentsList: [], list: [] })
}

const submissionStore = useSubmissionStore()
const loadResult = async () => {
  try {

    const res = await getCreateSubmissionResult({ submissionId: props.submission.id })
    if (!res.ok || !res.data) {
      return
    }
    submissionStore.updateSubmission(res.data)
    attachmentsList.value = res.data.attachments || []
    const list = getTheWordCountList(res.data.attachments || [])
    if (list.length == 0) {
      clearTheInterval()
      isNeedGetTheWordCount.value = false
      selectedFileIds.value = ids.value
      $eventBus.emit(StarloveConstants.keyOfEventBus.updateSubmissionInfo, res.data)

    }
  } catch (error: any) {
    if (error.statusText == '网络错误') {
      clearTheInterval()
      intervalId = setInterval(loadResult, 3000)
    }
  }
}

const getTheWordCountList = (list: any[]) => {
  return list.filter((item) => item.wordCount == null || item.wordCount == undefined)
}

const checkAttachmentsList = () => {
  clearTheInterval()

  const list = getTheWordCountList(attachmentsList.value)
  console.log('list ==>', list)
  if (list.length > 0) {
    isNeedGetTheWordCount.value = true
    intervalId = setInterval(loadResult, 3000)
  } else {
    isNeedGetTheWordCount.value = false
  }
}

const clearTheInterval = () => {
  if (intervalId) {
    clearInterval(intervalId)
    intervalId = undefined
  }
}
</script>

<style lang="scss" scoped>
.file-name {
  // max-width: 302px;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  overflow: hidden;
  word-break: break-all;
}

// .file-name-h5 {
//   max-width: 102px;
// }</style>