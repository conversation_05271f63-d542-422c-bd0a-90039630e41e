<template>
    <div class="layoutList" :class="{ isDark: store.isDark }">
        <div class="layoutItem" v-for="item in mindMapLayoutList" :key="item.value" @click="useLayout(item)"
            :class="{ active: item.value === layout }">
            <div class="imgBox">
                <img :src="mindMapLayoutImageList[item.value]" alt="" />
            </div>
            <div class="name">{{ item.name }}</div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useMindMapStore } from '@/stores/mindMapStore'
import { mindMapLayoutImageList, mindMapLayoutList } from '@/utils/constants'
import { ref, watch } from 'vue'

// 声明props类型
interface Props {
    mindMap: {
        getLayout: () => string
        setLayout: (layout: string) => void
    }
}

const props = defineProps<Props>()
const store = useMindMapStore()
const layout = ref('')

// 监听侧边栏状态
watch(() => store.activeSidebar, (val) => {
    if (val === 'structure') {
        layout.value = props.mindMap.getLayout()
    }
})

// 切换布局方法
const useLayout = (layoutItem: { name: string; value: string }) => {
    layout.value = layoutItem.value
    props.mindMap.setLayout(layoutItem.value)
    // 如果需要持久化存储布局设置，可以通过其他方式实现
    // 比如：localStorage.setItem('mindmap-layout', layoutItem.value)
}
</script>

<style lang="scss" scoped>
.layoutList {
    padding: 20px;

    &.isDark {
        .name {
            color: #fff;
        }
    }

    .layoutItem {
        width: 100%;
        cursor: pointer;
        border-bottom: 1px solid #e9e9e9;
        margin-bottom: 20px;
        padding-bottom: 20px;
        transition: all 0.2s;
        border: 1px solid transparent;

        &:last-of-type {
            border: none;
        }

        &:hover {
            box-shadow: 0 1px 2px -2px rgba(0, 0, 0, 0.16),
                0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 5px 12px 4px rgba(0, 0, 0, 0.09);
        }

        &.active {
            border: 1px solid #67c23a;
        }

        .imgBox {
            width: 100%;

            img {
                width: 100%;
            }
        }

        .name {
            text-align: center;
            font-size: 14px;
        }
    }
}
</style>