<template>
    <!-- :class="{ isDark }" -->
    <div class="outlineEditContainer" ref="outlineEditContainer" v-if="isOutlineEdit">
        <div class="closeBtn" @click="onClose">
            <CloseOutlined style="font-size: 20px;" />
        </div>
        <div class="outlineEditBox" ref="outlineEditBox">
            <div class="outlineEdit">
                <a-tree ref="tree" class="outlineTree" :tree-data="treeData" :field-names="fieldNames" draggable
                    :default-expand-all="true" :selectable="true" :show-line="true" @drop="onNodeDrop"
                    @select="(selectedKeys, info) => onCurrentChange(selectedKeys, info)">
                    <template #title="node">
                        <span class="customNode" :data-id="node.key">
                            <span class="nodeEdit" :contenteditable="!isReadonly" :key="node.uid"
                                @blur="(event) => onBlur(event, node)" @input="onInput"
                                @keydown.stop="(event) => onNodeInputKeydown(event, node)" @keyup.stop @paste="onPaste"
                                v-html="node?.title"></span>
                        </span>
                    </template>
                </a-tree>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { CloseOutlined } from '@ant-design/icons-vue';

import { useMindMapStore } from '@/stores/mindMapStore';
import type { TreeProps } from 'ant-design-vue';
import type { DataNode, EventDataNode } from 'ant-design-vue/es/tree';
import { storeToRefs } from 'pinia';
import type { MindMapData } from 'simple-mind-map';
import {
    createUid,
    handleInputPasteText,
    htmlEscape,
    nodeRichTextToTextWithWrap,
    simpleDeepClone,
    textToNodeRichTextWithWrap
} from 'simple-mind-map/src/utils';
import { nextTick, onBeforeUnmount, onMounted, ref, watch } from 'vue';

type Key = string | number

interface ExtendedMindMapData extends MindMapData {
    root?: boolean;
}

interface NodeData {
    uid: string
    data: {
        text: string
        richText: boolean
        uid: string
        isActive?: boolean
    }
    children?: NodeData[]
    textCache?: string
    label?: string
    title?: string
    root?: boolean
}

interface TreeNode extends EventDataNode {
    data: NodeData
    uid: string
    textCache: string
    label: string
    title: string
    parent?: TreeNode
}

const props = defineProps<{
    mindMap: any
    isReadonly?: boolean
}>()

const mindMapStore = useMindMapStore()
const { isDark, isOutlineEdit } = storeToRefs(mindMapStore)

const outlineEditContainer = ref<HTMLElement>()
const outlineEditBox = ref<HTMLElement>()
const tree = ref()
const data = ref<NodeData[]>([])
const treeData = ref<DataNode[]>([])
const currentData = ref<NodeData | null>(null)

const fieldNames: TreeProps['fieldNames'] = {
    key: 'uid',
    title: 'label',
    children: 'children'
}

onMounted(() => {
    window.addEventListener('keydown', onKeyDown)
})

onBeforeUnmount(() => {
    window.removeEventListener('keydown', onKeyDown)
})

watch(isOutlineEdit, (val) => {
    if (val) {
        refresh()
        nextTick(() => {
            if (outlineEditContainer.value) {
                document.body.appendChild(outlineEditContainer.value)
            }
        })
    }
})

const refresh = () => {
    let data = props.mindMap.getData(false) as ExtendedMindMapData
    data.root = true
    // 转换数据结构的递归函数
    const transformData = (node: any) => {
        // 处理文本内容
        let text = node.data.title
            ? nodeRichTextToTextWithWrap(node.data.text)
            : node.data.text
        text = htmlEscape(text)
        text = text.replaceAll(/\n/g, '<br>')
        // 构造新的节点结构
        const newNode = {
            label: text,
            title: text,
            uid: node.data.uid,
            key: node.data.uid,
            children: []
        }
        // 递归处理子节点
        if (node.children && node.children.length > 0) {
            newNode.children = node.children.map((child: any) => transformData(child))
        }
        return newNode
    }
    // 转换整个树结构
    const transformedData = transformData(data)
    treeData.value = [transformedData]
}

const convertToTreeData = (nodes: NodeData[]): DataNode[] => {
    return nodes.map(node => ({
        key: node.uid,
        title: node.label,
        children: node.children ? convertToTreeData(node.children) : undefined,
        data: node
    }))
}

const onNodeDrop = () => {
    save()
}

const onCurrentChange = (selectedKeys: Key[], info: { event: 'select', selected: boolean, node: EventDataNode, selectedNodes: DataNode[], nativeEvent: MouseEvent }) => {
    const node = info.node as unknown as TreeNode
    currentData.value = node.data
}

const onInput = (e: any) => {
    console.log('onInput  eeee ', e)
}

const onBlur = (e: any, node: TreeNode) => {
    console.log('onBlur', node)
    console.log('onBlur  eeee ', e)
    const target = e.target as HTMLElement
    if (node.title === target.innerHTML) {
        return
    }
    // const richText = node.data.data.richText
    // const text = richText ? target.innerHTML : target.innerText
    node.data.title = target.innerText
    node.data.label = target.innerText//richText ? textToNodeRichTextWithWrap(text) : text
    node.textCache = target.innerHTML
    save()
}

const onNodeInputKeydown = (e: KeyboardEvent, node: TreeNode) => {
    const richText = !!node.data.title
    const uid = createUid()
    const text = '新节点'
    const newData = {
        key: uid,
        title: text,
        data: {
            textCache: text,
            uid,
            title: text,
            label: text,
            data: {
                text: richText ? textToNodeRichTextWithWrap(text) : text,
                uid,
                richText
            },
            children: []
        }
    }
    if (e.keyCode === 13 && !e.shiftKey) {
        e.preventDefault()
        if (node.data.root) {
            return
        }
        tree.value.insertAfter(newData, node)
    }
    if (e.keyCode === 9) {
        e.preventDefault()
        if (e.shiftKey) {
            if (node.parent) {
                tree.value.insertAfter(node.data, node.parent)
                tree.value.remove(node)
            }
        } else {
            tree.value.append(newData, node)
        }
    }
    save()
    nextTick(() => {
        tree.value.setSelectedKeys([uid])
        const el = document.querySelector(
            `.customNode[data-id="${uid}"] .nodeEdit`
        ) as HTMLElement
        if (el) {
            let selection = window.getSelection()
            if (selection) {
                let range = document.createRange()
                range.selectNodeContents(el)
                selection.removeAllRanges()
                selection.addRange(range)
                scrollTo(el.offsetTop)
            }
        }
    })
}

const onKeyDown = (e: KeyboardEvent) => {
    if (!isOutlineEdit.value) return
    if ([46, 8].includes(e.keyCode) && currentData.value) {
        e.stopPropagation()
        tree.value.remove(currentData.value)
        currentData.value = null
        save()
    }
}

const onPaste = (e: ClipboardEvent) => {
    handleInputPasteText(e)
}

const getKey = () => {
    return Math.random()
}

const onClose = () => {
    mindMapStore.setIsOutlineEdit(false)
    mindMapStore.handleDataChange()
}

const scrollTo = (y: number) => {
    const container = outlineEditBox.value
    if (container) {
        const height = container.offsetHeight
        const top = container.scrollTop
        y += 50
        if (y > top + height) {
            container.scrollTo(0, y - height / 2)
        }
    }
}

const getData = () => {
    let newNode = {} as NodeData
    let node = data.value[0]
    let walk = (root: NodeData, newRoot: NodeData) => {
        newRoot.data = root.data
        newRoot.children = []
            ; (root.children || []).forEach(child => {
                const newChild = {} as NodeData
                newRoot.children!.push(newChild)
                walk(child, newChild)
            })
    }
    walk(node, newNode)
    return simpleDeepClone(newNode)
}

const save = () => {
    mindMapStore.handleDataChange()
}
</script>

<style lang="scss" scoped>
[contenteditable]>span {
    display: inline;
}

[contenteditable] {
    -webkit-user-select: text;
    user-select: text;
}

.outlineEditContainer {
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
    background-color: #fff;
    overflow: hidden;

    &.isDark {
        background-color: #262a2e;

        .closeBtn {
            .icon {
                color: #fff;
            }
        }
    }

    .closeBtn {
        position: absolute;
        right: 40px;
        top: 20px;
        cursor: pointer;

        .icon {
            font-size: 28px;
        }
    }

    .outlineEditBox {
        width: 100%;
        height: 100%;
        overflow-y: auto;
        padding: 50px 0;

        .outlineEdit {
            width: 1000px;
            height: 100%;
            height: max-content;
            margin: 0 auto;

            :deep(.customNode) {
                .nodeEdit {
                    max-width: 800px;
                }
            }
        }
    }
}

.customNode {
    width: 100%;
    color: rgba(0, 0, 0, 0.85);
    font-weight: bold;

    .nodeEdit {
        outline: none;
        white-space: normal;
        padding-right: 20px;
    }
}

.outlineTree {
    &.isDark {
        background-color: #262a2e;

        .customNode {
            color: #fff;
        }

        :deep(.ant-tree-node-content-wrapper.ant-tree-node-selected) {
            background-color: hsla(0, 0%, 100%, 0.05) !important;
        }

        :deep(.ant-tree-node-content-wrapper:hover) {
            background-color: hsla(0, 0%, 100%, 0.02) !important;
        }

        :deep(.ant-tree-switcher) {
            color: #fff;
        }
    }

    :deep(.ant-tree-node-content-wrapper) {
        height: auto;
        margin: 5px 0;
    }

    :deep(.ant-tree-switcher) {
        color: #262a2e;
    }
}
</style>