<template>
    <div class="changeBtn" @click="onChangeToOutlineEdit">
        <FullscreenOutlined style="font-size: 18px;" />
    </div>

    <OutlineEdit v-show="isOutlineEdit" :mindMap="mindMap" :isReadonly="true" @scrollTo="onScrollTo"></OutlineEdit>
    <Outline v-show="!isOutlineEdit" :mindMap="mindMap" :isReadonly="true" :wnMindMap="wnMindMap"
        @scrollTo="onScrollTo"></Outline>
</template>

<script setup lang="ts">
import { useMindMapStore } from '@/stores/mindMapStore';
import { ref, watch } from 'vue';

interface Props {
    mindMap: any // 这里需要定义具体的MindMap类型
    wnMindMap: any

}

const props = defineProps<Props>()
const sidebar = ref()
const mindMapStore = useMindMapStore()

const { isDark, isOutlineEdit } = storeToRefs(mindMapStore)

// Methods
const onChangeToOutlineEdit = () => {
    mindMapStore.setActiveSidebar('outline')
    mindMapStore.setIsOutlineEdit(true)

}

const onScrollTo = (y: number) => {
    let container = sidebar.value.getEl()
    let height = container.offsetHeight
    let top = container.scrollTop
    if (y > top + height) {
        container.scrollTo(0, y - height / 2)
    }
}

// Watch
watch(() => mindMapStore.activeSidebar, (val) => {
    if (val === 'outline') {
        mindMapStore.showSidebar('outline')
    } else {
        mindMapStore.hideSidebar()
    }
})
</script>

<style lang="scss" scoped>
.changeBtn {
    position: absolute;
    right: 20px;
    top: 0px;
    padding: 15px 10px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;

    &.isDark {
        color: #fff;
    }
}
</style>