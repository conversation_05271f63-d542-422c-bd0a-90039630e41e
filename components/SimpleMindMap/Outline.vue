<template>
    <div class="outline-wrapper" @mouseenter="isInTreArea = true" @mouseleave="isInTreArea = false">
        <a-tree ref="tree" class="outlineTree" v-if="treeData.length > 0" :tree-data="treeData" block-node
            :field-names="fieldNames" draggable :defaultExpandAll="true" :selectable="true" :show-line="true"
            @drop="onNodeDrop" @dragstart="onNodeDragStart" @dragend="onNodeDragEnd" @select="onCurrentChange">
            <template #title="node">
                <span class="customNode" :data-id="node.key">
                    <span class="nodeEdit" :contenteditable="!isReadonly" @click="(event) => onNodeClick(event, node)"
                        :key="node.uid" @keydown="onNodeInputKeydown" @blur="(event) => onBlur(event, node)"
                        @select="handleSelectTreeNode(node)" @paste="onPaste" v-html="node?.title">
                    </span>
                </span>
            </template>
        </a-tree>
    </div>
</template>

<script setup lang="ts">
import { useMindMapStore } from '@/stores/mindMapStore'
import type { TreeProps } from 'ant-design-vue'
import type { Key } from 'ant-design-vue/es/table/interface'
import type { DataNode, EventDataNode } from 'ant-design-vue/es/tree'
import { storeToRefs } from 'pinia'
import type { MindMap, MindMapData } from 'simple-mind-map'
import {
    createUid,
    handleInputPasteText,
    htmlEscape,
    nodeRichTextToTextWithWrap,
    textToNodeRichTextWithWrap,
} from 'simple-mind-map/src/utils'
import { nextTick, onBeforeUnmount, onMounted, ref } from 'vue'

interface TreeNode extends DataNode {
    uid: string
    key: string | number
    data: {
        richText?: boolean
        text: string
        uid: string
    }
    nodeData?: {
        data: {
            isActive: boolean
        }
    }
    title?: string
    textCache?: string
}

interface ExtendedMindMapData extends MindMapData {
    root?: boolean
    textCache?: string
    label?: string
    uid?: string
    children?: ExtendedMindMapData[]
}

interface Props {
    mindMap: MindMap
    isReadonly?: boolean
    wnMindMap?: any
}

const props = defineProps<Props>()
const emit = defineEmits<{
    (e: 'scrollTo', offset: number): void
}>()

const tree = ref()
const treeData = ref<any[]>([])
const currentData = ref<TreeNode | null>(null)
const notHandleDataChange = ref(false)
const isHandleNodeTreeRenderEnd = ref(false)
const beInsertNodeUid = ref('')
const insertType = ref('')
const isInTreArea = ref(false)
const isAfterCreateNewNode = ref(false)

const mindMapStore = useMindMapStore()
const { isDark } = storeToRefs(mindMapStore)

const fieldNames: TreeProps['fieldNames'] = {
    key: 'uid',
    title: 'label',
    children: 'children',
}

// Methods
const handleHideTextEdit = () => {
    if (notHandleDataChange.value) {
        notHandleDataChange.value = false
        refresh()
    }
}

const handleDataChange = () => {
    if (notHandleDataChange.value) {
        notHandleDataChange.value = false
        isAfterCreateNewNode.value = false
        return
    }
    if (isAfterCreateNewNode.value) {
        isAfterCreateNewNode.value = false
        return
    }
    refresh()
}

const handleNodeTreeRenderEnd = () => {
    if (insertType.value) {
        const method = insertType.value
        if (typeof methods[method] === 'function') {
            methods[method]()
        }
        insertType.value = ''
        return
    }
    if (isHandleNodeTreeRenderEnd.value) {
        isHandleNodeTreeRenderEnd.value = false
        refresh()
        nextTick(() => {
            afterCreateNewNode()
        })
    }
}

const refresh = () => {
    let data = props.mindMap.getData(false) as ExtendedMindMapData
    data.root = true
    // 转换数据结构的递归函数
    const transformData = (node: any) => {
        // 处理文本内容
        let text = node.data.richText ? nodeRichTextToTextWithWrap(node.data.text) : node.data.text
        text = htmlEscape(text)
        text = text.replaceAll(/\n/g, '<br>')
        // 构造新的节点结构
        const newNode = {
            label: text,
            title: text,
            uid: node.data.uid,
            key: node.data.uid,
            children: [],
        }
        // 递归处理子节点
        if (node.children && node.children.length > 0) {
            newNode.children = node.children.map((child: any) => transformData(child))
        }
        return newNode
    }
    // 转换整个树结构
    const transformedData = transformData(data)
    treeData.value = [transformedData]
}

const afterCreateNewNode = () => {
    const id = beInsertNodeUid.value
    if (id && tree.value) {
        try {
            isAfterCreateNewNode.value = true
            tree.value.setSelectedKeys([id])
            const node = tree.value.getNode(id)
            onCurrentChange([id], {
                event: 'select',
                selected: true,
                node: node,
                selectedNodes: [node],
                nativeEvent: new MouseEvent('click'),
            })
            handleNodeClick(node.dataRef as TreeNode, node)
            const el = document.querySelector(`.customNode[data-id="${id}"] .nodeEdit`) as HTMLElement
            if (el) {
                let selection = window.getSelection()
                if (selection) {
                    let range = document.createRange()
                    range.selectNodeContents(el)
                    selection.removeAllRanges()
                    selection.addRange(range)
                    let offsetTop = el.offsetTop
                    emit('scrollTo', offsetTop)
                }
            }
        } catch (error) {
            console.log(error)
        }
    }
    beInsertNodeUid.value = ''
}

const onBlur = (e: Event, node: any) => {
    const target = e.target as HTMLElement
    if (node.textCache === target.innerHTML) {
        if (insertType.value) {
            const method = insertType.value
            if (typeof methods[method] === 'function') {
                methods[method]()
            }
            insertType.value = ''
        }
        return
    }
    const richText = node.data.richText
    const text = richText ? target.innerHTML : target.innerText
    const targetNode = props.mindMap.renderer.findNodeByUid(node.uid)
    if (!targetNode) return
    notHandleDataChange.value = true
    if (richText) {
        targetNode.setText(textToNodeRichTextWithWrap(text), true)
    } else {
        targetNode.setText(text)
    }
}

const onPaste = (e: ClipboardEvent) => {
    handleInputPasteText(e)
}

const getKey = () => {
    return Math.random()
}

const handleSelectTreeNode = (node: TreeNode) => {

}

const onNodeInputKeydown = (e: KeyboardEvent) => {
    console.log("onNodeInputKeydown keyCode ==>", e.keyCode)
    if (e.keyCode === 13 && !e.shiftKey) {
        e.preventDefault()
        insertType.value = 'insertNode'
        const target = e.target as HTMLElement
        target.blur()

        insertNode()
        handleHideTextEdit()
        handleNodeTreeRenderEnd()
        handleDataChange()
    }
    if (e.keyCode === 9) {
        e.preventDefault()
        const target = e.target as HTMLElement
        if (e.shiftKey) {
            insertType.value = 'moveUp'
            target.blur()
        } else {
            insertType.value = 'insertChildNode'
            target.blur()
        }
    }
}

const moveUp = () => {
    props.mindMap.execCommand('MOVE_UP_ONE_LEVEL')
}

const insertNode = () => {
    notHandleDataChange.value = true
    isHandleNodeTreeRenderEnd.value = true
    beInsertNodeUid.value = createUid()
    console.log('props.mindMap ==>', props.mindMap)
    props.mindMap.execCommand('INSERT_NODE', false, [], {
        uid: beInsertNodeUid.value,
    })
}

const insertChildNode = () => {
    notHandleDataChange.value = true
    isHandleNodeTreeRenderEnd.value = true
    beInsertNodeUid.value = createUid()
    props.mindMap.execCommand('INSERT_CHILD_NODE', false, [], {
        uid: beInsertNodeUid.value,
    })
}

const handleNodeClick = (data: TreeNode) => {
    // console.log('data ==>', data)
    notHandleDataChange.value = true
    const targetNode = props.mindMap.renderer.findNodeByUid(data.uid)
    if (targetNode && targetNode.nodeData.data.isActive) return
    props.mindMap.execCommand('GO_TARGET_NODE', data.uid, () => {
        notHandleDataChange.value = false
    })
}

const onNodeDragStart = () => {
    mindMapStore.setIsDragOutlineTreeNode(true)
}

const onNodeDragEnd = () => {
    mindMapStore.setIsDragOutlineTreeNode(false)
}

const onNodeDrop = (info: any) => {
    notHandleDataChange.value = true
    const dropKey = info.node.key
    const dragKey = info.dragNode.key
    const dropPos = info.node.pos.split('-')
    const dropPosition = info.dropPosition - Number(dropPos[dropPos.length - 1])

    const node = props.mindMap.renderer.findNodeByUid(dragKey)
    const targetNode = props.mindMap.renderer.findNodeByUid(dropKey)

    if (!node || !targetNode) return

    if (dropPosition === 0) {
        props.mindMap.execCommand('MOVE_NODE_TO', node, targetNode)
    } else if (dropPosition === 1) {
        props.mindMap.execCommand('INSERT_AFTER', node, targetNode)
    } else {
        props.mindMap.execCommand('INSERT_BEFORE', node, targetNode)
    }
}

const onCurrentChange = (
    selectedKeys: Key[],
    info: {
        event: 'select'
        selected: boolean
        node: EventDataNode
        selectedNodes: DataNode[]
        nativeEvent: MouseEvent
    }
) => {
    if (info && info.node) {
        currentData.value = info.node.dataRef as TreeNode
    }
}

const onKeyDown = (e: KeyboardEvent) => {
    if (!isInTreArea.value) return
    if ([46, 8].includes(e.keyCode) && currentData.value) {
        e.stopPropagation()
        props.mindMap.renderer.textEdit.hideEditTextBox()
        const node = props.mindMap.renderer.findNodeByUid(currentData.value.uid)
        if (node && !node.isRoot) {
            notHandleDataChange.value = true
            // console.log("currentData.value ==>", currentData.value)
            // console.log("treeData.value ==>", treeData.value)

            const parentNode = node.parent; // 获取父节点
            // console.log("parentNode ==>", parentNode)
            const children = parentNode ? parentNode.children : treeData.value; // 获取子节点列表
            // console.log("children ==>", children)
            const index = children.findIndex((child: any) => child.uid === node.uid); // 找到当前节点的索引
            // console.log("index ==>", index)
            if (index !== -1) {
                children.splice(index, 1); // 删除节点
                treeData.value = [...treeData.value]; // 更新 treeData
            }

            // tree.value.remove(currentData.value)
            props.mindMap.execCommand('REMOVE_NODE', [node])
        }
    }
}

const onNodeClick = async (e: Event, node: any) => {
    // console.log('onNodeClick ==>', e)
    // console.log('node ==>', node)

    handleNodeClick(node.data)

    setTimeout(() => {


        e.preventDefault()

        const target = e.target as HTMLElement
        target.focus()

        const range = document.createRange()
        const selection = window.getSelection()
        if (selection) {
            range.selectNodeContents(target)
            range.collapse(false)
            selection.removeAllRanges()
            selection.addRange(range)
        }
    }, 1000)
}

const methods: Record<string, () => void> = {
    moveUp,
    insertNode,
    insertChildNode,
}

// Lifecycle hooks
onMounted(() => {
    window.addEventListener('keydown', onKeyDown)
    refresh()

    // console.log('mindMapStore.isDark ==>', isDark)
})

onBeforeUnmount(() => {
    window.removeEventListener('keydown', onKeyDown)
})
</script>

<style>
.ant-tree .ant-tree-treenode {
    align-items: center;
}
</style>
<style lang="scss" scoped>
.outline-wrapper {
    height: 100%;
    overflow: auto;
}

.customNode {
    width: 100%;
    color: rgba(0, 0, 0, 0.85);
    font-weight: bold;

    .nodeEdit {
        outline: none;
        white-space: normal;
        padding-right: 20px;
        font-size: 14px;
        font-weight: 400;
    }
}

.outlineTree {
    &.isDark {
        background-color: #262a2e;
        color: #fff;

        .customNode {
            color: #fff;
        }

        :deep(.ant-tree-node-content-wrapper.ant-tree-node-selected) {
            background-color: hsla(0, 0%, 100%, 0.05) !important;
        }

        :deep(.ant-tree-node-content-wrapper:hover) {
            background-color: hsla(0, 0%, 100%, 0.02) !important;
        }

        :deep(.ant-tree-switcher) {
            color: #fff;
        }
    }

    :deep(.ant-tree-node-content-wrapper) {
        height: auto;
        margin: 5px 0;
    }

    :deep(.ant-tree-switcher) {
        color: #262a2e;
    }
}
</style>
