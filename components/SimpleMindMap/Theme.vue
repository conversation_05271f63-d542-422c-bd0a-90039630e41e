<template>
    <div class="themeList" :class="{ isDark: mindMapStore.isDark }">
        <!-- tabs 标签区域 -->
        <div class="tabs-wrapper">
            <a-tabs v-model:activeKey="activeName" style="margin-bottom: 0 !important;">
                <a-tab-pane v-for="group in groupList" :key="group.name" :tab="group.name" />
            </a-tabs>
        </div>
        <div class="themeItem" v-for="item in currentList" :key="item.value" @click="useTheme(item)"
            :class="{ active: item.value === theme }">
            <div class="imgBox">
                <img :src="themeImgMap[item.value]" alt="" />
            </div>
            <div class="name">{{ item.name }}</div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useMindMapStore } from '@/stores/mindMapStore'
import themeImgMap from 'simple-mind-map-plugin-themes/themeImgMap'
import themeList from 'simple-mind-map-plugin-themes/themeList'
import { computed, onBeforeUnmount, onMounted, ref } from 'vue'

// 类型定义
interface ThemeItem {
    name: string
    value: string
    dark: boolean
}

interface ThemeGroup {
    name: string
    list: ThemeItem[]
}

interface Props {
    mindMap: any
}

const props = withDefaults(defineProps<Props>(), {
})

const emit = defineEmits(['selectTheme'])

const mindMapStore = useMindMapStore()

// 响应式状态
const theme = ref('')
const activeName = ref('')
const groupList = ref<ThemeGroup[]>([])

// 主题列表
const fullThemeList = [
    {
        name: '默认主题',
        value: 'default',
        dark: false
    },
    ...themeList
].reverse()

// 计算属性
const currentList = computed(() => {
    return groupList.value.find(item => item.name === activeName.value)?.list || []
})

// 监听侧边栏状态
watch(() => mindMapStore.activeSidebar, (val) => {
    if (val === 'theme') {
        theme.value = props.mindMap.getTheme()
        mindMapStore.showSidebar('theme')
    } else {
        mindMapStore.hideSidebar()
    }
})


// 方法
const handleViewThemeChange = () => {
    theme.value = props.mindMap.getTheme()
    handleDark()
}

const initGroup = () => {
    const baiduThemes = [
        'default', 'skyGreen', 'classic2', 'classic3', 'classicGreen',
        'classicBlue', 'blueSky', 'brainImpairedPink', 'earthYellow',
        'freshGreen', 'freshRed', 'romanticPurple', 'pinkGrape', 'mint'
    ]

    const baiduList: ThemeItem[] = []
    const classicsList: ThemeItem[] = []

    fullThemeList.forEach(item => {
        if (baiduThemes.includes(item.value)) {
            baiduList.push(item)
        } else if (!item.dark) {
            classicsList.push(item)
        }
    })

    groupList.value = [
        {
            name: '经典',
            list: classicsList
        },
        {
            name: '深色',
            list: fullThemeList.filter(item => item.dark)
        },
        {
            name: '简约',
            list: baiduList
        }
    ]

    activeName.value = groupList.value[0].name
}

const handleDark = () => {
    const target = fullThemeList.find(item => item.value === theme.value)
    if (target) {
        mindMapStore.setIsDark(target.dark)
    }
}

const useTheme = async (themeItem: ThemeItem) => {
    if (themeItem.value === theme.value) return
    theme.value = themeItem.value
    mindMapStore.setThemeName(themeItem.value)
    try {
        console.log('Theme change failed:', themeItem.value)
        // 先清除已有的主题配置
        props.mindMap.setThemeConfig({}, true)
        // 处理暗色主题
        handleDark()
        // 存储配置
        // mindMapStore.storeConfig({
        //     theme: {
        //         template: themeItem.value,
        //         config: {}
        //     }
        // })
        props.mindMap.setTheme(themeItem.value)
    } catch (error) {
        console.error('Theme change failed:', error)
    }
}

onMounted(() => {
    initGroup()
    if (props.mindMap) {
        theme.value = props.mindMap.getTheme()
    }

    // 重新启用主题变化监听
    props.mindMap.on('view_theme_change', handleViewThemeChange)

    // 初始化时也要处理暗色主题
    handleDark()
})

onBeforeUnmount(() => {
    // 重新启用事件清理
    props.mindMap.off('view_theme_change', handleViewThemeChange)
})
</script>

<style lang="scss" scoped>
.themeList {

    padding-top: 0;

    &.isDark {
        .name {
            color: #fff;
        }
    }

    .tabs-wrapper {
        position: sticky;
        top: 0;
        z-index: 10;
        padding: 0 20px;
        background: #fff; // 继承父元素背景色
        // padding: 10px 0;
        // margin: -10px 0;
    }

    .themeItem {
        // width: 100%;
        cursor: pointer;
        border-bottom: 1px solid #e9e9e9;
        // margin-bottom: 20px;
        // padding-bottom: 20px;
        margin: 20px;
        transition: all 0.2s;
        border: 1px solid transparent;

        &:last-of-type {
            border: none;
        }

        &:hover {
            box-shadow: 0 1px 2px -2px rgba(0, 0, 0, 0.16),
                0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 5px 12px 4px rgba(0, 0, 0, 0.09);
        }

        &.active {
            border: 1px solid #67c23a;
        }

        .imgBox {
            width: 100%;

            img {
                width: 100%;
            }
        }

        .name {
            text-align: center;
            font-size: 14px;
        }
    }
}
</style>