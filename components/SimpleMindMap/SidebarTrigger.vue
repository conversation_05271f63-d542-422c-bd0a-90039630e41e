<template>
    <div class="sidebarTriggerContainer" @click.stop :class="{ hasActive: show && activeSidebar, show: show, }">
        <!-- isDark: mindMapStore.isDark -->
        <div class="toggleShowBtn" :class="{ hide: !show }" @click="show = !show">
            <RightOutlined />
        </div>

        <div class="trigger">
            <div class="triggerItem" v-for="item in triggerList" :key="item.value"
                :class="{ active: activeSidebar === item.value }" @click="trigger(item)">
                <!-- <div class="triggerIcon" :class="[item.icon]"></div> -->
                <!-- 快捷键 -->
                <LaptopOutlined v-if="item.icon == 'laptopOutlined'" />
                <!-- 大纲 -->
                <UnorderedListOutlined v-else-if="item.icon == 'unorderedList'" />
                <!-- 结构 -->
                <ClusterOutlined v-else-if="item.icon == 'cluster'" />
                <!-- 风格 -->
                <SkinOutlined v-else-if="item.icon == 'skin'" />
                <div class="triggerName" :class="{ isDark: mindMapStore.isDark }">{{ item.name }}</div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ClusterOutlined, LaptopOutlined, SkinOutlined, UnorderedListOutlined } from '@ant-design/icons-vue';

import { useMindMapStore } from '@/stores/mindMapStore';
import { sidebarTriggerList } from '@/utils/constants';
import { RightOutlined } from '@ant-design/icons-vue';
import { computed, ref, watch } from 'vue';

interface TriggerItem {
    name: string
    value: string
    icon: string
}

// Props
interface Props {
    isDark?: boolean
    isReadonly?: boolean
    activeSidebar?: string | null
}

const props = withDefaults(defineProps<Props>(), {
    isDark: false,
    isReadonly: false,
    activeSidebar: null
})

// Emits
const emit = defineEmits<{
    (e: 'update:activeSidebar', value: string | null): void
}>()

// Store
const mindMapStore = useMindMapStore()

// State
const show = ref(true)

const activeSidebar = ref('')

// Computed
const triggerList = computed(() => {
    let list = sidebarTriggerList
    if (props.isReadonly) {
        list = list.filter((item: TriggerItem) => {
            return ['outline', 'shortcutKey'].includes(item.value)
        })
    }
    return list
})

// Methods
const trigger = (item: TriggerItem) => {
    emit('update:activeSidebar', item.value)
    mindMapStore.setActiveSidebar(item.value)
}

// Watch
watch(() => props.isReadonly, (val) => {
    if (val) {
        emit('update:activeSidebar', null)
        mindMapStore.setActiveSidebar(null)
    }
})

watch(() => mindMapStore.activeSidebar, (val) => {
    activeSidebar.value = val || ''
})

</script>

<style lang="scss" scoped>
.sidebarTriggerContainer {
    position: fixed;
    right: -60px;
    margin-top: 110px;
    transition: all 0.3s;
    top: 50%;
    transform: translateY(-50%);

    &.isDark {
        .trigger {
            background-color: #262a2e;

            .triggerItem {
                color: hsla(0, 0%, 100%, 0.6);

                &:hover {
                    background-color: hsla(0, 0%, 100%, 0.05);
                }
            }
        }
    }

    &.show {
        right: 0;
    }

    &.hasActive {
        right: 305px;
    }

    .toggleShowBtn {
        position: absolute;
        left: -6px;
        width: 35px;
        height: 60px;
        background: #409eff;
        top: 50%;
        transform: translateY(-50%);
        cursor: pointer;
        transition: left 0.1s linear;
        z-index: 0;
        border-top-left-radius: 10px;
        border-bottom-left-radius: 10px;
        display: flex;
        align-items: center;
        padding-left: 4px;

        &.hide {
            left: -8px;

            span {
                transform: rotateZ(180deg);
            }
        }

        &:hover {
            left: -18px;
        }

        span {
            color: #fff;
            transition: all 0.1s;
        }
    }

    .trigger {
        position: relative;
        width: 60px;
        border-color: #eee;
        background-color: #fff;
        box-shadow: 0 2px 16px 0 rgba(0, 0, 0, 0.06);
        border-radius: 6px;
        overflow: hidden;

        .triggerItem {
            height: 60px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            color: #464646;
            user-select: none;
            white-space: nowrap;

            &:hover {
                background-color: #ededed;
            }

            &.active {
                color: #409eff;
                font-weight: bold;
            }

            .triggerIcon {
                font-size: 18px;
                margin-bottom: 5px;
            }

            .triggerName {
                font-size: 13px;
            }
        }
    }
}
</style>