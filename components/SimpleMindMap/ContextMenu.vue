<template>
  <div class="contextmenuContainer listBox" v-if="isShow" ref="contextmenuRef"
    :style="{ left: left + 'px', top: top + 'px' }" :class="{ isDark: false }">
    <template v-if="type === 'node'">
      <div class="item" @click="exec('COPY_NODE', isGeneralization)" :class="{ disabled: isGeneralization }">
        <span class="name">复制节点</span>
        <span class="desc">Ctrl + C</span>
      </div>
    </template>
    <template v-if="type === 'svg'">
      <div class="item">
        <span class="name">复制到剪贴板</span>
        <RightOutlined />
        <div class="subItems listBox" :class="{ isDark: false, showLeft: subItemsShowLeft }" style="top: -35px">
          <div class="item" v-for="item in copyList" :key="item.value" @click="copyContentToClipboard(item.value)">
            {{ item.name }}
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
// import { useState } from '#app'
import { copyTextToClipboard, copyToClipboard } from '@/utils/copyToClipboard'
import { RightOutlined } from '@ant-design/icons-vue'
import { transformToMarkdown } from 'simple-mind-map/src/parse/toMarkdown'
import { transformToTxt } from 'simple-mind-map/src/parse/toTxt'
import { computed, nextTick, onBeforeUnmount, onMounted, ref } from 'vue'

interface Props {
  mindMap: any // 这里可以根据实际的 MindMap 类型来定义
}

const props = defineProps<Props>()

// 状态管理
// const localConfig = useState('localConfig', () => ({
//   isZenMode: false,
//   isDark: false
// }))

// const supportFeatures = useState('supportFeatures', () => ({
//   supportNumbers: false,
//   supportCheckbox: false
// }))

// 响应式状态
const isShow = ref(false)
const left = ref(0)
const top = ref(0)
const node = ref<any>(null)
const type = ref('')
const isMousedown = ref(false)
const mousedownX = ref(0)
const mousedownY = ref(0)
const enableCopyToClipboardApi = ref(!!navigator.clipboard)
const numberType = ref('')
const numberLevel = ref('')
const subItemsShowLeft = ref(false)
const contextmenuRef = ref<HTMLElement | null>(null)

// 计算属性
// const isZenMode = computed(() => localConfig.value.isZenMode)
// const isDark = computed(() => localConfig.value.isDark)


const copyList = computed(() => [
  {
    name: 'Markdown',
    value: 'md'
  },
  {
    name: 'Text',
    value: 'txt'
  }
])


const isGeneralization = computed(() => node.value?.isGeneralization)

// 方法
// const setLocalConfig = (config: Partial<typeof localConfig.value>) => {
//   Object.assign(localConfig.value, config)
// }

const getShowPosition = (x: number, y: number) => {
  if (!contextmenuRef.value) return { x, y }

  const rect = contextmenuRef.value.getBoundingClientRect()
  if (x + rect.width > window.innerWidth) {
    x = x - rect.width - 20
  }
  subItemsShowLeft.value = x + rect.width + 150 > window.innerWidth
  if (y + rect.height > window.innerHeight) {
    y = window.innerHeight - rect.height - 10
  }
  return { x, y }
}

const show = (e: MouseEvent, nodeData: any) => {
  type.value = 'node'
  isShow.value = true
  node.value = nodeData
  const number = node.value.getData('number')
  if (number) {
    numberType.value = number.type || 1
    numberLevel.value = number.level === '' ? 1 : number.level
  }
  nextTick(() => {
    const { x, y } = getShowPosition(e.clientX + 10, e.clientY + 10)
    left.value = x
    top.value = y
  })
}

const onMousedown = (e: MouseEvent) => {
  if (e.which !== 3) return
  mousedownX.value = e.clientX
  mousedownY.value = e.clientY
  isMousedown.value = true
}

const onMouseup = (e: MouseEvent) => {
  if (!isMousedown.value) return
  isMousedown.value = false
  if (
    Math.abs(mousedownX.value - e.clientX) > 3 ||
    Math.abs(mousedownY.value - e.clientY) > 3
  ) {
    hide()
    return
  }
  show2(e)
}

const show2 = (e: MouseEvent) => {
  type.value = 'svg'
  isShow.value = true
  nextTick(() => {
    const { x, y } = getShowPosition(e.clientX + 10, e.clientY + 10)
    left.value = x
    top.value = y
  })
}

const hide = () => {
  isShow.value = false
  left.value = -9999
  top.value = -9999
  type.value = ''
  node.value = null
  numberType.value = ''
  numberLevel.value = ''
}

const exec = (key: string, disabled?: boolean, ...args: any[]) => {
  if (disabled) return

  switch (key) {
    case 'COPY_NODE':
      props.mindMap.renderer.copy()
      break
    // case 'TOGGLE_ZEN_MODE':
    //   setLocalConfig({
    //     isZenMode: !isZenMode.value
    //   })
    //   break
    case 'FIT_CANVAS':
      props.mindMap.view.fit()
      break
    case 'UNEXPAND_ALL':
      const uid = node.value ? node.value.uid : ''
      props.mindMap.execCommand(key, !uid, uid)
      break
    case 'EXPAND_ALL':
      props.mindMap.execCommand(key, node.value ? node.value.uid : '')
      break
    default:
      props.mindMap.execCommand(key, ...args)
      break
  }
  hide()
}

const copyContentToClipboard = async (type: string) => {
  try {
    hide()
    let data
    let str
    switch (type) {
      case 'md':
        data = props.mindMap.getData()
        str = transformToMarkdown(data)
        break
      case 'txt':
        data = props.mindMap.getData()
        str = transformToTxt(data)
        break
      default:
        break
    }
    if (str) {
      if (enableCopyToClipboardApi.value) {
        await copyToClipboard(str)
      } else {
        copyTextToClipboard(str)
      }
    }
    console.log('复制成功')
  } catch (error) {
    console.error(error)
    console.log('复制失败')
  }
}

// 生命周期钩子
onMounted(() => {

  props.mindMap.on('node_contextmenu', show)
  props.mindMap.on('node_click', hide)
  props.mindMap.on('draw_click', hide)
  props.mindMap.on('expand_btn_click', hide)
  props.mindMap.on('svg_mousedown', onMousedown)
  props.mindMap.on('mouseup', onMouseup)
  props.mindMap.on('translate', hide)
})

onBeforeUnmount(() => {
  props.mindMap.off('node_contextmenu', show)
  props.mindMap.off('node_click', hide)
  props.mindMap.off('draw_click', hide)
  props.mindMap.off('expand_btn_click', hide)
  props.mindMap.off('svg_mousedown', onMousedown)
  props.mindMap.off('mouseup', onMouseup)
  props.mindMap.off('translate', hide)
})
</script>

<style lang="scss" scoped>
.listBox {
  width: 150px;
  background: #fff;
  box-shadow: 0 4px 12px 0 hsla(0, 0%, 69%, 0.5);
  border-radius: 4px;
  padding-top: 8px;
  padding-bottom: 8px;

  &.isDark {
    background: #363b3f;
  }
}

.contextmenuContainer {
  position: fixed;
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #1a1a1a;

  &.isDark {
    color: #fff;

    .item {
      &:hover {
        background: hsla(0, 0%, 100%, 0.05);
      }
    }
  }

  .splitLine {
    width: 95%;
    height: 1px;
    background-color: #e9edf2;
    margin: 2px auto;
  }

  .item {
    position: relative;
    height: 28px;
    padding: 0 16px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;

    &.danger {
      color: #f56c6c;
    }

    &:hover {
      background: #f5f5f5;

      .subItems {
        visibility: visible;
      }
    }

    &.disabled {
      color: grey;
      cursor: not-allowed;
      pointer-events: none;

      &:hover {
        background: #fff;
      }
    }

    .name {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 14px;
    }

    .desc {
      color: #999;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .subItems {
      position: absolute;
      left: 100%;
      visibility: hidden;
      width: 150px;
      cursor: auto;


      &.showLeft {
        left: -150px;
      }
    }
  }
}
</style>