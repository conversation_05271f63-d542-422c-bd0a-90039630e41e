<template>
    <!-- <Sidebar ref="sidebar" title="快捷键"> -->
    <!-- :class="{ isDark: mindMapStore.isDark }" -->
    <div class="box">
        <div v-for="item in shortcutKeyList" :key="item.type">
            <div class="title">{{ item.type }}</div>
            <div class="list" v-for="item2 in item.list" :key="item2.value">
                <div class="item">
                    <!-- <span v-if="item2.icon" class="icon iconfont" :class="[item2.icon]"></span> -->
                    <span class="name" :title="item2.name">{{ item2.name }}</span>
                    <div class="value" :title="item2.value">{{ item2.value }}</div>
                </div>
            </div>
        </div>
    </div>
    <!-- </Sidebar> -->
</template>

<script setup lang="ts">
import { useMindMapStore } from '@/stores/mindMapStore';
import { shortcutKeyList } from '@/utils/constants';
import { ref, watch } from 'vue';

const mindMapStore = useMindMapStore()
const sidebar = ref()

watch(() => mindMapStore.activeSidebar, (val) => {
    console.log("mindMapStore.activeSidebar ==>", val)
    if (val === 'shortcutKey') {
        mindMapStore.showSidebar('shortcutKey')
    } else {
        mindMapStore.hideSidebar()
    }
})
</script>

<style lang="scss" scoped>
.box {
    padding: 0 10px;

    &.isDark {
        .title {
            color: #fff;
        }

        .list {
            .item {
                .icon {
                    color: hsla(0, 0%, 100%, 0.6);
                }

                .name {
                    color: hsla(0, 0%, 100%, 0.6);
                }

                .value {
                    color: hsla(0, 0%, 100%, 0.3);
                }
            }
        }
    }

    .title {
        font-size: 16px;
        font-weight: 500;
        color: #333;
        margin: 10px 0 10px;
    }

    .list {


        .item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            font-size: 14px;

            .icon {
                font-size: 16px;
                margin-right: 16px;
            }

            .name {
                color: #333;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .value {
                color: #909090;
                margin-left: auto;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                font-size: 14px;
            }
        }
    }
}
</style>