<template>
  <a-modal v-model:open="openVisible" @cancel="handleCancel" :footer="null" :width="800">
    <a-spin :spinning="isLoading">
      <div class="material-flow-modal">
        <div class="header-title">{{ receivedData?.logisticsStatusDesc }}</div>
        <div class="flex-between">
          <div class="expressage">
            {{ receivedData?.expressCompanyName }} {{ receivedData?.number }}
          </div>
          <div class="expressage copy" @click="handleOrderCopy">复制物流单号</div>
        </div>
        <div class="steps">
          <a-steps direction="vertical" size="small" :current="0" progress-dot :items="logisticsTraceDetails"></a-steps>
        </div>
        <div v-if="addressList.length > 0">
          <div class="expressage user-name">
            <span class="recipients">收</span>
            {{ addressList[0].receiverName }} {{ addressList[0].receiverMobile }}
          </div>
          <div class="expressage addressee">
            {{ addressList[0].provinceName }}{{ addressList[0].cityName
            }}{{ addressList[0].districtName }}{{ addressList[0].addressDetail }}
          </div>
        </div>
      </div>
    </a-spin>
  </a-modal>
</template>

<script lang="ts" setup>
import { getAddressList, getLogistics } from '@/api/user'
import { type ExpressInfo, type ReceiverInfo } from '@/services/types/order'
import { dateFormat } from '@/utils/utils'
import { Modal as AModal, Spin as ASpin, Steps as ASteps, message } from 'ant-design-vue'
import copy from 'copy-text-to-clipboard'
import { computed, onMounted, ref } from 'vue'

interface Props {
  openVisible: boolean
  orderId: string
}
const props = withDefaults(defineProps<Props>(), {})
const emit = defineEmits(['update:openVisible'])
const openVisible = computed({
  get: () => props.openVisible,
  set: (val) => {
    emit('update:openVisible', val)
  }
})

const isLoading = ref(true)
const receivedData = ref<ExpressInfo>()
const addressList = ref<ReceiverInfo[]>([])

const handleCancel = () => {
  openVisible.value = false
}

const logisticsTraceDetails = computed(() => {
  const expressInfo = receivedData.value?.logisticsTraceDetails
  if (!expressInfo) {
    return []
  }
  const list: any = []
  for (const item of expressInfo) {
    list.push({
      title: `${item.areaName}   ${dateFormat(item.time)}`,
      description: item.desc
    })
  }
  return list.reverse()
})

const handleOrderCopy = () => {
  const text = receivedData.value?.number
  if (!text) {
    message.error('物流单号复制失败')
    return
  }
  copy(text)
  message.success('复制成功')
}

const loadData = async () => {
  const res = await getLogistics({ orderId: props.orderId })
  // console.log('loadData res ==>', res)
  isLoading.value = false
  if (!res.ok) {
    return
  }
  receivedData.value = res.data
}

const getAddressData = async () => {
  const res = await getAddressList()
  if (!res.ok || !res.data) {
    return []
  }
  return res.data || []
}

onMounted(async () => {
  loadData()

  addressList.value = await getAddressData()
})
</script>

<style lang="scss" scoped>
.material-flow-modal {
  padding: 0 30px 30px 30px;

  .header-title {
    font-weight: 500;
    font-size: 18px;
    color: #333333;
    line-height: 29px;
    text-align: center;
    margin-bottom: 15px;
  }

  .expressage {
    font-weight: 400;
    font-size: 15px;
    color: #333333;
    line-height: 23px;
  }

  .copy {
    color: #1e99ff;
    cursor: pointer;
  }

  .user-name {
    font-weight: bold;
  }

  .recipients {
    font-weight: bold;
    font-size: 12px;
    color: #ffffff;
    line-height: 17px;
    background: #1e99ff;
    padding: 3px 4px;
    border-radius: 50%;
  }

  .addressee {
    margin-left: 22px;
    margin-top: 7px;
  }

  .steps {
    padding: 20px 30px;
    margin: 20px 0;
    background: #f9f9f9;
    height: 300px;
    overflow-y: auto;
  }

  .flex-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
</style>
