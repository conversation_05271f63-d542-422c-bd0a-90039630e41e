<template>
  <div class="space-y-6">
    <!-- 实名认证 -->
    <div class="p-4 border border-gray-200 rounded-lg hover:border-blue-300 transition-colors cursor-pointer"
      @click="showVerificationModal = true">
      <div class="flex justify-between items-center">
        <div>
          <h3 class="text-base font-medium">实名认证</h3>
          <p class="text-gray-500 text-sm mt-1">完成实名认证，保障账号安全</p>
        </div>
        <div class="flex items-center text-gray-500">
          <span class="text-sm">未认证</span>
          <right-one theme="outline" size="20" class="ml-2" />
        </div>
      </div>
    </div>

    <!-- 账号注销 -->
    <div class="p-4 border border-gray-200 rounded-lg hover:border-blue-300 transition-colors cursor-pointer"
      @click="handleDeleteAccount">
      <div class="flex justify-between items-center">
        <div>
          <h3 class="text-sm font-medium">账号注销</h3>
          <p class="text-gray-500 text-sm mt-1">注销后账号将无法恢复</p>
        </div>
        <div class="flex items-center text-gray-500">
          <right-one theme="outline" size="20" class="ml-2" />
        </div>
      </div>
    </div>

    <!-- 使用 Teleport 将 Modal 传送到 body -->
    <Teleport to="body">
      <Modal v-model="showVerificationModal" title="实名认证" subtitle="请填写您的实名认证信息" :width="500"
        @confirm="handleVerificationSubmit">
        <VerificationModal @submit="handleVerificationSubmit" />
      </Modal>
    </Teleport>

    <!-- 注销确认弹窗 -->
    <Teleport to="body">
      <Modal v-model="showDeleteModal" title="账号注销" :width="480" :show-footer="false">
        <DeleteAccountModal @close="showDeleteModal = false" @confirm="handleConfirmDelete" />
      </Modal>
    </Teleport>
  </div>
</template>

<script setup>
import { RightOne } from '@icon-park/vue-next'
import { ref } from 'vue'
import Modal from '~/components/Common/Modal.vue'
import DeleteAccountModal from './DeleteAccountModal.vue'
import VerificationModal from './VerificationModal.vue'

const showVerificationModal = ref(false)
const showDeleteModal = ref(false)

const handleVerificationSubmit = (formData) => {
  console.log('提交的认证信息：', formData)
  // 这里处理提交逻辑
  showVerificationModal.value = false
}

const handleDeleteAccount = () => {
  showDeleteModal.value = true
}

const handleConfirmDelete = () => {
  console.log('执行账号注销操作')
  // 这里添加实际的注销逻辑
  showDeleteModal.value = false
}
</script>