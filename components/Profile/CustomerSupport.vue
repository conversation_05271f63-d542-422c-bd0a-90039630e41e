<template>
  <div class="max-w-2xl mx-auto space-y-6">
    <!-- 客服信息卡片 -->
    <div class="bg-blue-50 rounded-lg p-6">
      <div class="flex items-center gap-3 mb-4">
        <div class="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center">
          <customer theme="outline" size="28" fill="#FFFFFF" />
        </div>
        <div>
          <h3 class="text-lg font-bold text-blue-700">联系客服</h3>
          <p class="text-blue-600 mt-1">工作时间：10:00-19:00</p>
        </div>
      </div>
    </div>

    <!-- 微信客服 -->
    <div class="bg-white rounded-lg p-6 border border-gray-200">
      <h3 class="text-base font-medium mb-4 flex items-center">
        <wechat theme="outline" size="24" class="mr-2" />
        微信客服
      </h3>
      <div class="flex items-center justify-center flex-col">
        <div class="w-48 h-48 mb-4">
          <img src="~/assets/images/qrcode.png" alt="微信客服二维码" class="w-full h-full object-contain">
        </div>
        <p class="text-gray-500 text-sm">扫描二维码添加客服微信</p>
      </div>
    </div>

    <!-- 用户ID信息 -->
    <div class="bg-white rounded-lg p-6 border border-gray-200">
      <h3 class="text-lg font-medium mb-4">您的用户ID</h3>
      <div class="flex items-center justify-between bg-gray-50 p-4 rounded-lg">
        <span class="text-gray-600">164240457162198630</span>
        <button class="text-blue-600 hover:text-blue-700 flex items-center" @click="copyUserId">
          <copy theme="outline" size="20" class="mr-1" />
          复制
        </button>
      </div>
      <p class="text-gray-500 text-sm mt-2">
        反馈问题时，请告知客服您的用户ID，以便更好地为您服务
      </p>
    </div>

    <!-- 常见问题 -->
    <div class="bg-white rounded-lg p-6 border border-gray-200">
      <h3 class="text-lg font-medium mb-4">常见问题</h3>
      <div class="space-y-4">
        <div v-for="(qa, index) in faqs" :key="index" class="border-b border-gray-100 last:border-0 pb-4 last:pb-0">
          <h4 class="font-medium text-gray-800 mb-2">{{ qa.question }}</h4>
          <p class="text-gray-600">{{ qa.answer }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Copy, Customer, Wechat } from '@icon-park/vue-next';

// 常见问题列表
const faqs = [
  {
    question: "如何充值硬币？",
    answer: "点击右上角的硬币图标，选择充值金额，支持微信和支付宝支付。"
  },
  {
    question: "硬币有效期是多久？",
    answer: "硬币长期有效，无使用期限。"
  },
  {
    question: "如何修改手机号？",
    answer: "请联系客服协助您修改绑定手机号。"
  }
]

// 复制用户ID
const copyUserId = () => {
  navigator.clipboard.writeText('164240457162198630')
    .then(() => {
      // TODO: 显示复制成功提示
      console.log('用户ID已复制')
    })
    .catch(err => {
      console.error('复制失败:', err)
    })
}
</script>