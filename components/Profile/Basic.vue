<template>

    <XLoading v-if="!isClientLoaded"></XLoading>
    <div v-else class="m-3 bg-white/80 backdrop-blur-sm rounded-xl p-4 md:p-6 border border-gray-200/70">
        <div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
            <!--头像区域 -->
            <div class="flex flex-col sm:flex-row items-center">
                <div class="relative group p-2 flex items-center">
                    <div class="relative w-20 h-20 sm:w-24 sm:h-24">
                        <div class="w-full h-full rounded-full overflow-hidden border-4 border-white shadow-lg">
                            <img :src="avatar" alt="用户头像" class="w-full h-full object-cover" />
                        </div>
                        <x-upload-avatar v-model:avatar="userInfo.avatar">
                            <div
                                class="absolute inset-0 flex items-center justify-center bg-black/50 opacity-0 group-hover:opacity-100 rounded-full transition-opacity cursor-pointer">
                                <camera theme="outline" size="24" fill="#FFFFFF" />
                            </div>
                        </x-upload-avatar>
                    </div>
                </div>
                <div class="sm:mt-0 sm:ml-6 text-center sm:text-left flex flex-col justify-center p-2">
                    <h3 class="text-sm text-gray-800">{{ nickname }}</h3>
                    <p class="text-sm text-gray-500 mt-1">更换头像</p>
                </div>
            </div>

            <div class="space-y-6 mt-6">
                <!-- 昵称 -->
                <div class="flex flex-col sm:flex-row sm:items-center">
                    <label class="text-sm w-full sm:w-24 text-gray-600 mb-2 sm:mb-0">昵称</label>
                    <input v-model="userInfo.nickname" type="text"
                        class="text-sm w-full flex-1 px-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:border-blue-500"
                        placeholder="请输入昵称" maxlength="10" />
                </div>

                <!-- 用户ID -->
                <div class="flex flex-col sm:flex-row sm:items-center">
                    <label class="text-sm w-full sm:w-24 text-gray-600 mb-2 sm:mb-0">用户ID</label>
                    <div class="flex-1 flex items-center">
                        <input v-model="userInfo.userId" type="text"
                            class="text-sm w-full flex-1 px-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:border-blue-500"
                            readonly />
                        <button class="ml-2 p-2 text-gray-500 hover:text-blue-600" @click="copyUserId(userInfo.userId)">
                            <copy theme="outline" size="20" />
                        </button>
                    </div>
                </div>
                <!-- 绑定手机号 -->
                <div class="flex flex-col sm:flex-row sm:items-center">
                    <label class="text-sm w-full sm:w-24 text-gray-600 mb-2 sm:mb-0">绑定手机号</label>
                    <template v-if="isClientLoaded">
                        <input v-if="isBindPhone" v-model="phone" type="text"
                            class="text-sm w-full flex-1 px-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:border-blue-500"
                            readonly />
                        <div v-else class="text-sm w-full flex-1 px-4 py-2 text-red-500 cursor-pointer"
                            @click="handlePressBindPhone">
                            绑定成功送5000硬币
                        </div>
                    </template>
                    <div v-else class="text-sm w-full flex-1 px-4 py-2 border border-gray-200 rounded-lg">
                        <!-- 加载中占位 -->
                    </div>
                </div>
                <!-- 保存按钮 -->
                <div class="flex justify-center pt-6">
                    <button @click="saveProfile"
                        class="text-sm w-full sm:w-auto px-8 py-2 bg-blue-700 text-white rounded-full hover:bg-blue-800 transition-colors">
                        保存
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { updateUserInfo } from '@/api/user'
import XLoading from '@/components/XLoading.vue'
import { ToastService } from '@/services/toast'
import { UserService } from '@/services/user'
import { useUserStore } from '@/stores/user'
import { defaultAvatar } from '@/utils/constants'
import { copyToClipboard } from '@/utils/copyToClipboard'
import { Camera, Copy } from '@icon-park/vue-next'
import { message } from 'ant-design-vue'
import { computed, reactive, ref } from 'vue'

const userStore = useUserStore()

const { isBindPhone } = storeToRefs(userStore)
// 用户信息
const userInfo = reactive({
    avatar: defaultAvatar,
    nickname: '',
    userId: '',
    phone: '',
})

// 使用 ref 来追踪客户端加载状态
const isClientLoaded = ref(false)

const avatar = computed(() => {
    if (!isClientLoaded.value) {
        return defaultAvatar
    }

    if (userInfo.avatar && userInfo.avatar !== defaultAvatar) {
        return userInfo.avatar
    }
    if (userStore?.currentLoginInfo?.avatar) {
        return userStore.currentLoginInfo.avatar
    }
    return defaultAvatar
})

const nickname = computed(() => {
    if (!isClientLoaded.value) {
        return ''
    }
    return userStore?.currentLoginInfo?.nickname || ''
})

const phone = computed(() => {
    return userStore?.currentLoginInfo?.account || ''
})

// 复制用户ID
const copyUserId = (id) => {
    copyToClipboard(id)
    message.success('复制成功')

}
const handlePressBindPhone = () => {
    //显示绑定手机号弹窗
    userStore.setShowPhoneBoundModal({
        status: BindPhoneModal.SHOW_BINDING,
    })
}

// 保存个人信息
const saveProfile = async () => {
    // TODO: 实现保存团队中个人昵称信息的逻辑
    const params = {
        nickname: userInfo.nickname,
    }
    if (userInfo.avatar) {
        params['avatar'] = userInfo.avatar
    }
    ToastService.loading()
    const res = await updateUserInfo(params)
    console.log('updateUserInfo res', res)
    if (!res.ok) {
        ToastService.error('保存失败')
        return
    }
    ToastService.success('保存成功')
    await UserService.loadUserInfo()
}

// 格式化用户ID，每4位添加一个空格
// const formatUserId = (id) => {
//   return id.replace(/(.{4})/g, '$1 ').trim()
// }

// 在客户端挂载后更新用户信息
onMounted(async () => {
    if (!UserService.isLogined()) {
        return
    }
    await UserService.loadUserInfo()
    isClientLoaded.value = true
    userInfo.userId = userStore?.currentLoginInfo?.id || ''
    userInfo.nickname = userStore?.currentLoginInfo?.nickname || ''
    userInfo.avatar = userStore?.currentLoginInfo?.avatar || defaultAvatar
})
</script>

<style scoped>
.bg-gradient-to-r {
    background-size: 100% 100%;
}
</style>