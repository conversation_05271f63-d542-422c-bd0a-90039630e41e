<template>
  <div class="max-w-3xl mx-auto space-y-8">
    <!-- 公司介绍 -->
    <section class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-6 shadow-sm">
      <div class="flex items-center gap-4 mb-4">
        <div class="w-16 h-16 rounded-lg overflow-hidden shadow-sm">
          <img src="/logo.png" alt="万能小in" class="w-full h-full object-cover">
        </div>
        <div>
          <h1 class="text-xl font-bold text-blue-800">星云爱店科技</h1>
          <p class="text-blue-600 mt-1 text-sm font-medium">让AI赋能每个写作者</p>
        </div>
      </div>
      <p class="text-gray-700 leading-relaxed text-sm">
        星云爱店科技专注于AI应用开发，致力于为写作者提供智能高效的AI工具。
        通过持续创新和技术突破，我们正在重新定义写作方式，让每位用户都能轻松释放创意潜能。
      </p>
    </section>

    <!-- 产品特色 -->
    <section>
      <h2 class="text-base font-bold mb-4 text-gray-800">产品特色</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div v-for="(feature, index) in features" :key="index" class="p-4 bg-white border border-gray-100 rounded-lg hover:border-blue-300 
                 transition-all duration-300 hover:shadow-sm">
          <div class="flex items-center gap-3 mb-2">
            <div class="w-8 h-8 rounded-lg bg-blue-50 flex items-center justify-center">
              <component :is="feature.icon" theme="outline" size="20" class="text-blue-600" />
            </div>
            <h3 class="text-sm font-semibold text-gray-800">{{ feature.title }}</h3>
          </div>
          <p class="text-gray-600 text-sm leading-relaxed">{{ feature.description }}</p>
        </div>
      </div>
    </section>

    <!-- 联系方式 -->
    <section class="bg-gray-50 rounded-lg p-6">
      <h2 class="text-base font-bold mb-4 text-gray-800">联系我们</h2>
      <div class="space-y-3">
        <div v-for="(contact, index) in contactInfo" :key="index"
          class="flex items-center gap-3 text-gray-700 hover:text-blue-600 transition-colors text-sm">
          <component :is="contact.icon" theme="outline" size="18" />
          <span>{{ contact.text }}</span>
        </div>
      </div>
    </section>

    <!-- 版权信息 -->
    <footer class="text-center text-gray-500 space-y-1.5 pt-4 border-t border-gray-100">
      <p class="text-xs">小in大模型算法-网信备案31011512433440124013号</p>
      <div class="text-xs space-y-1">
        <p>© 2023 xiaoin.com.cn ©上海星云爱店科技有限公司</p>
        <p>沪ICP备20022513号-6 | 沪公网安备：31010402333815号</p>
      </div>
    </footer>
  </div>
</template>

<script setup>
import {
  Mail, Phone,
  Protect,
  Robot, RobotOne, Time,
  World
} from '@icon-park/vue-next';

const features = [
  {
    icon: RobotOne,
    title: '智能写作',
    description: '强大的AI模型支持，让写作更加智能高效'
  },
  {
    icon: Robot,
    title: '对话体验',
    description: '自然流畅的对话交互，让使用更加简单直观'
  },
  {
    icon: Protect,
    title: '安全可靠',
    description: '严格的数据安全保护，确保用户信息安全'
  },
  {
    icon: Time,
    title: '持续更新',
    description: '定期更新优化，不断提升用户体验'
  }
]

const contactInfo = [
  {
    icon: Mail,
    text: '商务合作：<EMAIL>'
  },
  {
    icon: Phone,
    text: '联系电话：021-61734090'
  },
  {
    icon: World,
    text: '公司地址：上海市徐汇区丰谷路24号1-3层'
  }
]
</script>