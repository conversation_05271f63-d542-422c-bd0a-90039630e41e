<template>
  <div class="space-y-6">
    <!-- 反馈说明 -->
    <div class="bg-blue-50 rounded-lg p-6">
      <div class="flex items-center gap-3 mb-4">
        <div class="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center">
          <message theme="outline" size="28" fill="#FFFFFF" />
        </div>
        <div>
          <h3 class="text-lg font-bold text-blue-700">意见反馈</h3>
          <p class="text-blue-600 mt-1">您的反馈将帮助我们做得更好！我们将在5-15个工作日内给予回复</p>
        </div>
      </div>
    </div>

    <!-- 反馈表单 -->
    <div class="space-y-6">
      <!-- 反馈类型 -->
      <div>
        <label class="block text-gray-700 mb-2">反馈类型</label>
        <div class="flex flex-wrap gap-3">
          <button
            v-for="type in feedbackTypes"
            :key="type.value"
            class="px-4 py-2 rounded-full border"
            :class="[
              selectedType === type.value
                ? 'bg-blue-50 border-blue-500 text-blue-700'
                : 'border-gray-200 hover:border-blue-500'
            ]"
            @click="selectedType = type.value"
          >
            {{ type.label }}
          </button>
        </div>
      </div>

      <!-- 反馈内容 -->
      <div>
        <label class="block text-gray-700 mb-2">反馈内容</label>
        <textarea
          v-model="content"
          rows="5"
          class="w-full px-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:border-blue-500"
          placeholder="请详细描述您的问题或建议..."
        ></textarea>
        <p class="text-gray-500 text-sm mt-1">还可以输入 {{ 500 - content.length }} 字</p>
      </div>

      <!-- 联系方式 -->
      <div>
        <label class="block text-gray-700 mb-2">联系方式</label>
        <input
          v-model="contact"
          type="text"
          class="w-full px-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:border-blue-500"
          placeholder="请留下您的手机号，方便我们及时回复"
        >
      </div>

      <!-- 提交按钮 -->
      <div class="flex justify-center pt-4">
        <button
          @click="submitFeedback"
          class="px-8 py-2 bg-blue-700 text-white rounded-full hover:bg-blue-800 transition-colors"
        >
          提交反馈
        </button>
      </div>
    </div>

   
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { Message } from '@icon-park/vue-next'

const feedbackTypes = [
  { label: '功能建议', value: 'feature' },
  { label: '体验问题', value: 'experience' },
  { label: 'Bug反馈', value: 'bug' },
  { label: '其他', value: 'other' }
]

const selectedType = ref('feature')
const content = ref('')
const contact = ref('')

const feedbackRecords = ref([
  {
    type: '功能建议',
    time: '2024-03-15 14:30',
    content: '希望能增加批量导出功能',
    status: '已回复',
    reply: '感谢您的建议，我们会在下个版本中考虑添加该功能。'
  },
  {
    type: 'Bug反馈',
    time: '2024-03-14 09:15',
    content: '页面加载偶尔出现卡顿现象',
    status: '处理中'
  }
])

const getStatusClass = (status) => {
  const statusClasses = {
    '已回复': 'text-green-600',
    '处理中': 'text-orange-600',
    '待处理': 'text-gray-500'
  }
  return statusClasses[status] || 'text-gray-600'
}

const submitFeedback = () => {
  if (!content.value.trim()) {
    // TODO: 显示错误提示
    return
  }

  // TODO: 提交反馈到服务器
  console.log({
    type: selectedType.value,
    content: content.value,
    contact: contact.value
  })

  // 清空表单
  content.value = ''
  contact.value = ''
  selectedType.value = 'feature'
}
</script> 