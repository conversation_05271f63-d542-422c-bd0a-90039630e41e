<template>
  <div class="p-6">
    <form @submit.prevent="handleSubmit" class="space-y-6">
      <p class="text-gray-600">请完善以下信息</p>
      <p class="text-gray-500 text-sm">请提交个人实名信息，并确保信息真实有效</p>

      <div class="space-y-4">
        <div>
          <label class="block mb-1.5 text-gray-700">
            <span class="text-red-500">*</span> 真实姓名：
          </label>
          <input v-model="form.name" type="text" class="w-full px-4 py-2.5 bg-gray-50 border border-gray-200 rounded-lg 
            focus:bg-white focus:border-blue-400 focus:ring-2 focus:ring-blue-50 
            outline-none transition-colors duration-200" placeholder="请输入您的姓名" required>
        </div>

        <div>
          <label class="block mb-1.5 text-gray-700">
            <span class="text-red-500">*</span> 身份证号：
          </label>
          <input v-model="form.idNumber" type="text" maxlength="18" class="w-full px-4 py-2.5 bg-gray-50 border 
            border-gray-200 rounded-lg focus:bg-white focus:border-blue-400 focus:ring-2 focus:ring-blue-50 
            outline-none transition-colors duration-200" placeholder="请输入您的身份证号" required>
          <p v-if="idNumberError" class="mt-1.5 text-sm text-red-500">{{ idNumberError }}</p>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="mt-6 flex justify-end space-x-4">
        <button @click="$emit('close')" type="button"
          class="px-6 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors">
          取消
        </button>
        <button type="submit" :disabled="isSubmitDisabled"
          class="px-6 py-2 bg-blue-500 text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
          确认
        </button>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
import { saveId2MetaVerify } from '@/api/user';
import { UserService } from '@/services/user';
import { useUserStore } from '@/stores/user';
import { checkBirthday, checkParity, checkProvince, isCardNo } from '@/utils/utils';
import { message } from 'ant-design-vue';
import { computed, ref } from 'vue';



const emit = defineEmits(['submit', 'close']);

const form = ref({
  name: '',
  idNumber: '',
  phone: ''
});
const store = useUserStore();

// 身份证号校验错误信息
const idNumberError = computed(() => {
  const idNumber = form.value.idNumber.trim();

  if (!idNumber) {
    return '请输入身份证号';
  }

  if (idNumber.length !== 18) {
    return '身份证号必须为18位';
  }

  if (!isCardNo(idNumber)) {
    return '身份证号格式错误';
  }

  if (!checkProvince(idNumber)) {
    return '身份证号省份校验错误';
  }

  if (!checkBirthday(idNumber)) {
    return '身份证号生日校验错误';
  }

  if (!checkParity(idNumber)) {
    return '身份证号校验错误, 请重新输入';
  }

  return ''; // 没有错误
});

// 确定按钮是否禁用的逻辑
const isSubmitDisabled = computed(() => {
  // 只检查身份证号是否有错误
  return !!idNumberError.value;
});

// 校验和提交
const handleSubmit = async () => {
  // 如果有错误，直接返回
  if (idNumberError.value) {
    message.warning(idNumberError.value);
    return;
  }

  const params = {
    userName: form.value.name,
    identifyNum: form.value.idNumber
  };

  // 提交请求
  const res = await saveId2MetaVerify(params);
  if (!res.ok) {
    message.error('认证失败');
    emit('close');
    return;
  }

  localStorage.setItem('authenticationStatus', 'done');
  await UserService.loadUserInfo();

  if (!UserService.isAuthentication()) {
    const userInfo = store.currentLoginInfo;
    if (!userInfo) return;
    userInfo.authenticationStatus = 'done';
    store.currentLoginInfo = userInfo;
  }

  message.success('认证成功');
  emit('close');
};
</script>
