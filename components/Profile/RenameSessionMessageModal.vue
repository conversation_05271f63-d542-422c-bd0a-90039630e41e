<template>
    <UModal :model-value="modelValue" @update:model-value="emit('update:modelValue', $event)">
        <div class="p-6">
            <div class="space-y-4">
                <div class="space-y-2">
                    <label class="text-sm font-medium text-gray-700">修改提问记录名称</label>

                    <UInput v-model="sessionTitle" :placeholder="`请输入新的标题`" size="lg" color="gray" variant="outline"
                        class="w-full">
                    </UInput>

                </div>
                <div class="flex justify-end space-x-3">
                    <button @click="handleCancel"
                        class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
                        取消
                    </button>
                    <button @click="handleConfirm" :disabled="!sessionTitle.trim()"
                        class="px-4 py-2 text-sm font-medium text-white transition-colors rounded-lg bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 disabled:from-gray-400 disabled:to-gray-500">
                        确定 {{ isLoading ? '...' : '' }}
                    </button>
                </div>
            </div>
        </div>
    </UModal>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { messageSessionUpdate } from '~/api/appMessage';
import { ToastService } from '~/services/toast';
interface Props {
    modelValue: boolean
    item: any
    type: string
}

const props = defineProps<Props>()
const emit = defineEmits(['update:modelValue', 'confirm'])

const sessionTitle = ref(props.item?.title || '')

const handleCancel = () => {
    sessionTitle.value = ''
    emit('update:modelValue', false)
}
const isLoading = ref(false)
const handleConfirm = async () => {
    const title = sessionTitle.value.trim()
    if (!title) {
        ToastService.error('请输入提问记录名称')
    }
    isLoading.value = true
    const res = await messageSessionUpdate({
        id: props.item.id,
        title: `${title}`
    })
    if (!res.success) {
        isLoading.value = false
        return
    }
    isLoading.value = false
    emit('confirm', {
        title: `${title}`,
        id: props.item.id
    })
    handleCancel()
    ToastService.success('操作成功')
}
</script>