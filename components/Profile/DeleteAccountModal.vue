<template>
  <div class="p-6">
    <!-- 警告图标 -->
    <div class="flex justify-center mb-4">
      <attention theme="filled" size="48" fill="#EF4444" />
    </div>

    <!-- 警告文本 -->
    <div class="text-center mb-6">
      <h3 class="text-xl font-medium text-gray-800 mb-2">确认注销账号？</h3>
      <p class="text-gray-500">注销后，您的账号将被永久删除且无法恢复</p>
    </div>

    <!-- 注销影响说明 -->
    <div class="bg-red-50 rounded-lg p-4 mb-6">
      <h4 class="text-red-600 font-medium mb-2">注销后将失去：</h4>
      <ul class="space-y-2 text-gray-600">
        <li class="flex items-center">
          <dot theme="filled" size="16" fill="#EF4444" class="mr-2" />
          所有已创建的知识库内容
        </li>
        <li class="flex items-center">
          <dot theme="filled" size="16" fill="#EF4444" class="mr-2" />
          账号相关的所有数据和记录
        </li>
        <li class="flex items-center">
          <dot theme="filled" size="16" fill="#EF4444" class="mr-2" />
          与该账号相关的所有权益
        </li>
      </ul>
    </div>

    <!-- 确认复选框 -->
    <div class="mb-6">
      <label class="flex items-center">
        <input type="checkbox" v-model="confirmed" class="w-4 h-4 text-red-500 rounded border-gray-300 
          focus:ring-2 focus:ring-red-50 focus:ring-offset-0" required>
        <span class="ml-2 text-sm text-gray-600">
          我已知晓注销后果，并确认注销账号
        </span>
      </label>
    </div>

    <!-- 操作按钮 -->
    <div class="flex justify-end space-x-3">
      <button @click="$emit('close')" class="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors">
        取消
      </button>
      <button @click="handleConfirm" :disabled="!confirmed" class="px-6 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 
        disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
        确认注销
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { deleteAccount } from '@/api/user';
import { Attention, Dot } from '@icon-park/vue-next';
import { message } from 'ant-design-vue';
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { deleteAuthentication } from '~/utils/request';

const emit = defineEmits(['close', 'confirm'])
const confirmed = ref(false)
const router = useRouter()


const handleConfirm = async () => {
  if (!confirmed.value) {
    return
  }

  const res = await deleteAccount()
  if (!res.ok) {
    // message.error(res.message || '注销失败')
    message.error(res.message || '注销失败')
    return
  }

  localStorage.clear()
  deleteAuthentication()
  router.push({ path: '/' })

  emit('confirm')
}
</script>