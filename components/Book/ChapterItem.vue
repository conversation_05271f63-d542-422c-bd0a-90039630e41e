<template>
  <div :class="[
    'flex items-center gap-3 p-3 rounded-lg group hover:bg-gray-100 transition-colors',
    getIndentClass()
  ]">
    <!-- 章节序号 -->
    <div class="text-gray-400 w-16 text-right">
      {{ getChapterNumber() }}
    </div>

    <!-- 章节标题 -->
    <div class="flex-1 text-lg">
      {{ chapter.title }}
    </div>

    <!-- 操作按钮 -->
    <div class="opacity-0 group-hover:opacity-100 flex gap-2">
      <a-button v-if="chapter.level < 4" type="text" size="small" @click.stop="$emit('add-subchapter', chapter.key)"
        class="hover:text-blue-500">
        <template #icon>
          <PlusOutlined />
        </template>
      </a-button>
      <a-button type="text" size="small" @click.stop="handleEdit" class="hover:text-blue-500">
        <template #icon>
          <EditOutlined />
        </template>
      </a-button>
      <a-button type="text" size="small" @click.stop="handleMoveUp" :disabled="index === 0" class="hover:text-blue-500">
        <template #icon>
          <UpOutlined />
        </template>
      </a-button>
      <a-button type="text" size="small" @click.stop="handleMoveDown" :disabled="index === total - 1"
        class="hover:text-blue-500">
        <template #icon>
          <DownOutlined />
        </template>
      </a-button>
      <a-button type="text" size="small" @click.stop="handleDelete" class="hover:text-red-500">
        <template #icon>
          <DeleteOutlined />
        </template>
      </a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Chapter } from '@/types/book';
import {
  DeleteOutlined,
  DownOutlined,
  EditOutlined,
  PlusOutlined,
  UpOutlined
} from '@ant-design/icons-vue';
import { Modal } from 'ant-design-vue';

const props = defineProps<{
  chapter: Chapter
  index: number
  total: number
  isSubChapter?: boolean
  parentIndex?: string | number
}>()

const emit = defineEmits<{
  (e: 'edit', key: string, title: string): void
  (e: 'delete', key: string): void
  (e: 'move-up', parentKey: string | null, index: number): void
  (e: 'move-down', parentKey: string | null, index: number): void
  (e: 'add-subchapter', key: string): void
}>()

const handleEdit = () => {
  emit('edit', props.chapter.key, props.chapter.title)
}

const handleMoveUp = () => {
  const parentKey = props.chapter.parent || null
  emit('move-up', parentKey, props.index)
}

const handleMoveDown = () => {
  const parentKey = props.chapter.parent || null
  emit('move-down', parentKey, props.index)
}

const handleDelete = () => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除章节 "${props.chapter.title}" 吗？${props.chapter.children?.length ? '该操作将同时删除所有子章节。' : ''}`,
    okText: '确认',
    cancelText: '取消',
    okType: 'danger',
    onOk() {
      emit('delete', props.chapter.key)
    }
  })
}

const getIndentClass = () => {
  switch (props.chapter.level) {
    case 2:
      return 'ml-8'
    case 3:
      return 'ml-16'
    case 4:
      return 'ml-24'
    default:
      return ''
  }
}

const getChapterNumber = () => {
  switch (props.chapter.level) {
    case 1:
      return `第${props.index + 1}章`
    case 2:
    case 3:
    case 4:
      return `${props.parentIndex}.${props.index + 1}`
    default:
      return `${props.index + 1}`
  }
}
</script>

<style scoped></style>