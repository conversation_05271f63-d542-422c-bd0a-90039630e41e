<template>
  <a-card class="hover:shadow-md transition-shadow" :bordered="true">
    <div class="flex justify-between items-start">
      <div>
        <h3 class="text-lg font-medium text-gray-900">{{ book.title }}</h3>
        <p class="text-sm text-gray-500 mt-1">{{ book.description || '暂无描述' }}</p>
      </div>
      <a-button 
        type="primary"
        @click="emit('edit', book)"
      >
        继续编辑
      </a-button>
    </div>
    <div class="mt-2 flex justify-end items-center">
      <!-- <p v-if="book.chapters" class="text-sm text-gray-600">
        已创建 {{ book.chapters.length }} 个章节
      </p> -->
      <a-dropdown :trigger="['click']">
        <template #overlay>
          <a-menu>
            <a-menu-item @click="emit('editInfo', book)">
              <template #icon><EditOutlined /></template>
              编辑信息
            </a-menu-item>
            <a-menu-item @click="emit('delete', book)">
              <template #icon><DeleteOutlined /></template>
              删除
            </a-menu-item>
          </a-menu>
        </template>
        <a-button type="text">
          <MoreOutlined />
        </a-button>
      </a-dropdown>
    </div>
  </a-card>
</template>

<script setup lang="ts">
import { EditOutlined, DeleteOutlined, MoreOutlined } from '@ant-design/icons-vue'
import type { Book } from '~/types/book'

const props = defineProps<{
  book: Book
}>()

const emit = defineEmits<{
  edit: [book: Book]
  editInfo: [book: Book]
  delete: [book: Book]
}>()
</script> 