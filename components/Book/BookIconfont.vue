<!-- src/components/Iconfont.vue -->
<template>
  <i :class="iconClass" :style="iconStyle"></i>
</template>

<script>
export default {
  name: 'IconFont',
  props: {
    name: {
      type: String,
      required: true
    },
    size: {
      type: Number,
      default: 12 // 默认字体大小
    },
    color: {
      type: String,
      default: null // 可为null，不设置颜色时使用默认颜色
    }
  },
  computed: {
    iconClass() {
      return `iconfont icon-${this.name}`
    },
    iconStyle() {
      const style = { fontSize: `${this.size}px` }
      if (this.color) {
        style.color = this.color
      }
      return style
    }
  }
}
</script>
