<template>
  <div class="p-6 bg-white rounded-xl min-h-[400px]">
    <!-- Tab 切换 -->
    <div class="flex border-b border-gray-200 mb-6">
      <button 
        v-for="tab in tabs" 
        :key="tab.key"
        class="px-4 py-2 text-sm font-medium transition-colors -mb-[2px]"
        :class="currentTab === tab.key ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 border-b-2 border-transparent hover:text-gray-700'"
        @click="currentTab = tab.key"
      >
        {{ tab.name }}
      </button>
    </div>

    <!-- 知识库选择 -->
    <div v-if="currentTab === 'repos'" class="space-y-3 min-h-[300px]">
      <label
        v-for="repo in repositories"
        :key="repo.id"
        class="flex items-start gap-3 p-3 rounded-lg hover:bg-gray-50 cursor-pointer group transition-colors"
      >
        <input
          type="checkbox"
          :value="repo.id"
          v-model="selectedRepos"
          class="mt-1 h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
        >
        <div class="flex-1">
          <div class="text-gray-900">{{ repo.name }}</div>
          <div class="text-sm text-gray-500">{{ repo.description }}</div>
        </div>
      </label>
    </div>

    <!-- 文件选择 -->
    <div v-else-if="currentTab === 'files'" class="space-y-4">
      <div class="flex gap-3 items-center">
        <div class="relative flex-1">
          <input
            v-model="searchQuery"
            type="text"
            class="w-full h-10 bg-gray-50 border border-gray-200 rounded-lg px-4 py-2 pr-10 text-gray-900 text-sm
            placeholder:text-gray-400
            hover:border-gray-300
            focus:bg-white focus:border-blue-500 focus:ring-1 focus:ring-blue-500/20 focus:outline-none
            transition-colors"
            placeholder="搜索文件..."
          >
          <i class="i-heroicons-magnifying-glass absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 pointer-events-none"></i>
        </div>
        <button 
          class="h-10 px-4 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors text-sm font-medium flex items-center gap-2"
          @click="handleUpload"
        >
          <i class="i-heroicons-arrow-up-tray"></i>
          上传文件
        </button>
      </div>
      
      <div class="space-y-2 h-[500px] overflow-y-auto">
        <label
          v-for="file in filteredFiles"
          :key="file.id"
          class="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-50 cursor-pointer group transition-colors"
        >
          <input
            type="checkbox"
            :value="file.id"
            v-model="selectedFiles"
            class="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          >
          <div>
            <div class="text-gray-900 text-sm">{{ file.name }}</div>
            <div class="text-xs text-gray-500">{{ file.type }} · {{ file.size }}</div>
          </div>
        </label>
      </div>
    </div>

    <!-- 搜索引擎选项 -->
    <div v-else-if="currentTab === 'search'" class="space-y-4">
      <label class="flex items-start gap-3 p-4 rounded-lg border border-gray-200 hover:bg-gray-50 cursor-pointer group transition-colors">
        <input
          type="checkbox"
          v-model="enableSearchEngine"
          class="mt-1 h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
        >
        <div class="flex-1">
          <div class="text-gray-900 font-medium">启用搜索引擎</div>
          <div class="text-sm text-gray-500 mt-1">
            开启后，AI 助手会先通过搜索引擎获取相关的背景知识，以提供更全面和准确的回答。这可能会略微增加响应时间，但能够获得更好的答案质量。
          </div>
        </div>
      </label>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useKnowledgeBaseStore } from '@/stores/knowledge-base'

const knowledgeStore = useKnowledgeBaseStore()

const currentTab = ref('repos')
const searchQuery = ref('')
const selectedRepos = ref<number[]>([])
const selectedFiles = ref<number[]>([])
const enableSearchEngine = ref(false)

const tabs = [
  { key: 'repos', name: '选择知识库' },
  { key: 'files', name: '选择文件' },
  { key: 'search', name: '搜索引擎' }
]

const repositories = computed(() => knowledgeStore.repositories)

// 模拟文件数据
const files = [
  { id: 1, name: '自然灾害应急预案.docx', type: 'Word文档', size: '2.5MB' },
  { id: 2, name: '消防演练流程.pdf', type: 'PDF文件', size: '1.8MB' },
  { id: 3, name: '应急物资清单.xlsx', type: 'Excel表格', size: '1.2MB' },
  { id: 4, name: '山东省防汛应急预案2024版.pdf', type: 'PDF文件', size: '3.1MB' },
  { id: 5, name: '山东省地震应急响应机制.docx', type: 'Word文档', size: '2.8MB' },
  { id: 6, name: '危险化学品事故处置方案.pdf', type: 'PDF文件', size: '4.2MB' },
  { id: 7, name: '山东省应急指挥中心工作规范.docx', type: 'Word文档', size: '1.9MB' },
  { id: 8, name: '应急救援队伍建设指南.pdf', type: 'PDF文件', size: '2.3MB' },
  { id: 9, name: '山东省重点企业应急管理清单.xlsx', type: 'Excel表格', size: '1.7MB' },
  { id: 10, name: '突发公共卫生事件应急预案.pdf', type: 'PDF文件', size: '3.5MB' },
  { id: 11, name: '山东省应急避难场所分布图.pdf', type: 'PDF文件', size: '5.2MB' }
]

const filteredFiles = computed(() => {
  if (!searchQuery.value) return files
  return files.filter(file => 
    file.name.toLowerCase().includes(searchQuery.value.toLowerCase())
  )
})

// 初始化选中所有知识库
selectedRepos.value = repositories.value.map(repo => repo.id)

const handleUpload = () => {
  // 处理文件上传逻辑
  console.log('处理文件上传')
}

defineExpose({
  selectedRepos,
  selectedFiles,
  enableSearchEngine
})
</script> 