<template>
  <a-modal :open="modelValue" :centered="isModalCentered" title="学术搜索" @cancel="handleCancel" :footer="null" width="70%"
    class="academic-search-modal">

    <!-- 顶部引用文本块 -->
    <div class="flex justify-start items-center mb-6 p-4 bg-[#EEF3FF] border-[#ECF1FF] rounded-r-lg">
      <!-- 引导条区域 -->
      <div class="px-[2px] h-[40px] bg-[#2551B5] mr-[12px] ml-[10px] rounded-sm"></div>
      <div class="text-gray-700 text-sm leading-relaxed line-clamp-2">
        {{ chapterStore.referceText }}
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="flex gap-6 mb-6 h-[388px]">
      <!-- 左侧学术文献列表 -->
      <div class="w-[65%] flex-1 bg-[#F1F2F9] border border-[#E7E9F5] p-5 rounded-lg max-h-[590px] overflow-y-auto">
        <!-- 文献条目循环 -->
        <div v-for="item in literatureList" :key="item.id"
          class="mb-4 p-4 border rounded-lg cursor-pointer transition-all duration-200"
          :class="selectedPaper === item.id ? 'border-[#2551B5] bg-[#E7EDFE]' : 'border-[#EEEEEE] bg-[#FFFFFF] hover:border-gray-300'"
          @click="selectPaper(item.id)">
          <div class="flex items-start">
            <div class="text-blue-600 font-bold text-lg mr-3 mt-1">{{ item.index }}.</div>
            <div class="flex-1">
              <div class="text-gray-800 text-sm leading-relaxed mb-2">
                {{ item.content }}
              </div>
              <div class="flex items-center text-xs text-gray-500 mb-2">
                <div :class="['w-4 h-4 rounded mr-2 flex items-center justify-center', item.tagColor]">
                  <span class="text-white text-xs">{{ item.tagText }}</span>
                </div>
                <span>{{ item.author }}.{{ item.title }}[J]. {{ item.journal }},{{ item.year }},{{ item.issue }}:{{
                  item.pages }}.</span>
              </div>
              <div class="mt-[15px] flex items-center justify-end">
                <a-button type="primary" size="small" style="border: none; box-shadow: none; font-size: 13px;">
                  <template #icon>
                    <span class="text-xs">👁</span>
                  </template>
                  加入知识库
                </a-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧详细信息面板 -->
      <div class="w-[35%]  max-w-[400px] bg-[#ffffff] px-[20px] py-[16px] rounded-lg border-[#E7E9F5]">
        <h3 class="text-gray-800 font-medium mb-4">详细信息</h3>
        <div class="mb-4">
          <div class="text-sm text-gray-600 mb-1">论文观点：</div>
          <div class="text-sm text-gray-800 leading-relaxed">
            可解释 AI 在医疗诊断中的重要性日益凸显，有助于提高医生对 AI 系统的信任度。
          </div>
        </div>

        <div>
          <div class="text-sm text-gray-600 mb-1">论文信息：</div>
          <div class="text-sm text-gray-800 leading-relaxed">
            标题为《可解释 AI 在医疗中的应用》，作者是 Davis,R.,刊载期刊为《New England Journal of Medicine》（《新英格兰医学杂志》），发表时间 2023 年2月。
          </div>
        </div>
      </div>
    </div>

    <!-- 文本润色区域 -->
    <div>
      <h3 class="text-gray-800 font-medium">文本润色</h3>
      <div class="mb-4 py-3">
        <a-textarea class=" text-gray-500 text-sm border-0" :rows="4" placeholder="结合上述搜索结果对当前文本内容进行内容润色及学术优化..." />
      </div>

      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <a-checkbox v-model:checked="includeReferences">
            插入上述及相关学术文献
          </a-checkbox>
        </div>

        <div class="flex gap-3">
          <a-button type="primary" @click="handleInsert">插入</a-button>
          <a-button type="primary" @click="handleTransform">转换</a-button>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { useChapterStore } from '@/stores/chapter';
import { message } from 'ant-design-vue';
import { computed, onBeforeUnmount, onMounted, ref, watch } from 'vue';
const props = defineProps<{
  modelValue: boolean
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm', value: { action: string, includeReferences: boolean, selectedPaper?: number }): void
}>()

const chapterStore = useChapterStore()

// 移动端检测
const isMobile = ref(false)
const isModalCentered = computed(() => !isMobile.value)

const checkIsMobile = () => {
  if (typeof window !== 'undefined') {
    isMobile.value = window.innerWidth <= 768
  }
}

// 假数据数组
const literatureList = ref([
  {
    id: 1,
    index: 1,
    content: '技术应用与操作能力构成了数字素养的实践层面，包括对各类数字设备的熟练使用、软件工具的灵活操作以及网络平台的功能掌握。',
    tagText: '科',
    tagColor: 'bg-red-500',
    author: '科方超',
    title: '教育数字化转型背景下师范生数字素养评测研究',
    journal: '《科教导刊》',
    year: '2024',
    issue: '(15)',
    pages: '153-155'
  },
  {
    id: 2,
    index: 2,
    content: '研究显示，家长的数字参与程度与子女的数字素养呈正相关，城市家庭倾向于开展共同数字活动，如在线学习辅导，信息检索协作等',
    tagText: '教',
    tagColor: 'bg-red-500',
    author: '黄璐',
    title: '我国体育公共服务数字化转型的成效、问题及对策研究',
    journal: '《南京体育学院学报》',
    year: '2025',
    issue: '(2)',
    pages: '16-20'
  },
  {
    id: 3,
    index: 3,
    content: '多模态人工智能技术在医疗诊断中的应用，通过整合CT、MRI等影像数据与临床文本信息，显著提升了诊断准确率和效率。',
    tagText: '医',
    tagColor: 'bg-blue-500',
    author: '张明',
    title: '多模态AI在医疗影像诊断中的应用研究',
    journal: '《中华医学杂志》',
    year: '2024',
    issue: '(8)',
    pages: '45-52'
  },
  {
    id: 4,
    index: 4,
    content: '深度学习算法在自然语言处理领域的突破，为智能问答系统和机器翻译技术的发展提供了强有力的技术支撑。',
    tagText: '计',
    tagColor: 'bg-green-500',
    author: '李华',
    title: '基于深度学习的自然语言处理技术研究',
    journal: '《计算机学报》',
    year: '2024',
    issue: '(12)',
    pages: '78-85'
  }
])

// 响应式数据
const selectedPaper = ref<number | null>(null)
const includeReferences = ref(false)

// 选择学术文献
const selectPaper = (paperIndex: number) => {
  selectedPaper.value = paperIndex
}

// 监听弹窗打开状态，重置表单
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    // 重置状态
    selectedPaper.value = null
    includeReferences.value = false
  }
})

// 插入操作
const handleInsert = () => {
  emit('confirm', {
    action: 'insert',
    includeReferences: includeReferences.value,
    selectedPaper: selectedPaper.value || undefined
  })

  emit('update:modelValue', false)
  message.success('已插入学术内容')
}

// 转换操作
const handleTransform = () => {
  emit('confirm', {
    action: 'transform',
    includeReferences: includeReferences.value,
    selectedPaper: selectedPaper.value || undefined
  })

  emit('update:modelValue', false)
  message.success('已转换文本内容')
}

// 取消操作
const handleCancel = () => {
  emit('update:modelValue', false)
}

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && props.modelValue) {
    handleCancel()
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
  checkIsMobile()
  window.addEventListener('resize', checkIsMobile)
})

onBeforeUnmount(() => {
  document.removeEventListener('keydown', handleKeydown)
  window.removeEventListener('resize', checkIsMobile)
})
</script>

<style lang="scss">
.academic-search-modal {
  max-width: 1200px;
  min-width: 600px;

  .ant-modal-header {
    background: #F4F6FF !important;
  }

  .ant-modal-content {
    background: #F4F6FF !important;
  }
}

/* 文献条目样式 */
.academic-search-modal .border-blue-400 {
  border-color: #60a5fa !important;
  background-color: #eff6ff !important;
}

/* 详细信息面板样式 */
.academic-search-modal .bg-gray-50 {
  background-color: #f9fafb;
}

/* 按钮样式 */
.academic-search-modal :deep(.ant-btn-primary) {
  background-color: #3b82f6;
  border-color: #3b82f6;
}

.academic-search-modal :deep(.ant-btn-primary:hover) {
  background-color: #2563eb;
  border-color: #2563eb;
}

/* 复选框样式 */
.academic-search-modal :deep(.ant-checkbox-wrapper) {
  font-size: 14px;
  color: #374151;
}

/* 滚动条样式 */
.academic-search-modal :deep(.ant-modal-body)::-webkit-scrollbar {
  width: 6px;
}

.academic-search-modal :deep(.ant-modal-body)::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.academic-search-modal :deep(.ant-modal-body)::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.academic-search-modal :deep(.ant-modal-body)::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* textarea 无边框样式 */
.academic-search-modal :deep(.ant-input) {
  border: none !important;
  box-shadow: none !important;
}

.academic-search-modal :deep(.ant-input:focus) {
  border: none !important;
  box-shadow: none !important;
}

/* 加入知识库按钮样式 */
.academic-search-modal :deep(.ant-btn-link) {
  background-color: #FFFFFF !important;
}

/* 顶部引用文本块2行限制样式 */
.academic-search-modal .line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
