<template>
  <a-modal :open="modelValue" title="选择文献格式" @ok="handleOk" @cancel="handleCancel" :confirmLoading="loading"
    width="600px" cancelText="取消" okText="确认" :zIndex="48">
    <!-- 副标题 -->
    <div class="text-[#777777] text-[15px] mb-[20px] mt-[10px]">
      使用专业文献格式，一键优化文末参考文献
    </div>

    <!-- 选项卡区域 -->
    <div class="flex gap-3 mb-5 sm:flex-row flex-col">
      <button v-for="format in formatOptions" :key="format.value"
        class="relative flex-1 px-4 py-3 border rounded-md text-sm font-medium cursor-pointer transition-all duration-200 text-center"
        :class="formState.format === format.value
          ? 'border-[#2551B5] bg-[#E7EDFE] text-[#2551B5]'
          : 'border-[#E3EAFB] bg-[#F5F7FF] text-[#333333] hover:border-[#2551B5]'" @click="selectFormat(format.value)">
        {{ format.label }}
      </button>
    </div>

    <!-- 示例区域 -->
    <div class="relative bg-gray-50 rounded-lg p-4 border border-gray-100">

      <div class="text-sm font-medium text-gray-800 mb-2">示例:</div>
      <div class="text-xs leading-relaxed text-gray-600">
        [1]邹安民.人工智能辅助头颈部CTA在急性脑卒中影像诊断中的临床应用[J].《中国医学创新》,2025,(9):137-141.<br />
        [2]卞佳,人工智能辅助诊断系统在医学影像诊断学实践教学中的应用研究[J].《中国高等医学教育》,2024,(11):103-104.<br />
        [3]鲍远杰.磁共振成像及人工智能影像组学在肝纤维化诊断中的应用进展[J].《中国现代医生》,2025,(3):98-101.
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { useChapterStore } from '@/stores/chapter';
import { onBeforeUnmount, onMounted, ref, watch } from 'vue';

const { $eventBus } = useNuxtApp();

const chapterStore = useChapterStore()

const props = defineProps<{
  modelValue: boolean
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm', value: { format: string }): void
}>()

const loading = ref(false)

const formState = ref({
  format: 'gbt7714',
})

// 格式选项配置
const formatOptions = ref([
  { value: 'gbt7714', label: 'GB/T 7714', badge: '20px' },
  { value: 'mla', label: 'MLA', badge: '493px' },
  { value: 'apa', label: 'APA', badge: null }
])

// 选择格式方法
const selectFormat = (format: string) => {
  formState.value.format = format
}



// 监听 modelValue 变化，当弹窗打开时重置表单
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    formState.value = {
      format: 'gbt7714',
    }
  }
})

const handleOk = () => {
  emit('update:modelValue', false)
}

const handleCancel = () => {
  emit('update:modelValue', false)
}

onMounted(() => {
})

onBeforeUnmount(() => {
})
</script>
