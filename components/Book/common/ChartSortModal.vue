<template>
  <a-modal :open="modelValue" @cancel="handleCancel" :confirmLoading="loading" width="540px" cancelText="取消" okText="确定"
    :zIndex="48" :footer="false">

    <div class="text-center">
      <div class="text-[18px] text-[#333333] mb-[10px] mt-2 font-bold">一键图标表排序</div>
      <div class="text-[15px] text-[#999999] mb-[20px]">对整篇文件图表、表格等按正确顺序标注序号</div>
      <div class="text-[16px] text-[#333333] mb-[30px] font-bold">标注中<a-spin :spinning="true" /></div>
      <div class="mb-3">
        <button class="bg-[#2551B5] text-[14px] text-[#ffffff] px-[60px] py-[6px] border-none rounded-[7px]"
          @click="handleOk">
          确认
        </button>
      </div>
    </div>

  </a-modal>
</template>

<script setup lang="ts">
import { useChapterStore } from '@/stores/chapter';
import { message } from 'ant-design-vue';
import { onBeforeUnmount, onMounted, ref } from 'vue';

const chapterStore = useChapterStore()

defineProps<{
  modelValue: boolean
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm', value: { sortType: string, customDescription?: string, additionalRequirements?: string }): void
}>()

const loading = ref(false)

// 递归遍历内容节点
const traverseContent = (content: any[], callback: (node: any, path: number[]) => void, currentPath: number[] = []) => {
  if (!content || !Array.isArray(content)) return

  content.forEach((node, index) => {
    const nodePath = [...currentPath, index]
    callback(node, nodePath)

    // 递归遍历子节点
    if (node.content && Array.isArray(node.content)) {
      traverseContent(node.content, callback, nodePath)
    }
  })
}

// 收集所有图表和表格节点
const collectChartsAndTables = (content: any[]) => {
  const items: Array<{
    type: 'chart' | 'table' | 'image'
    node: any
    path: number[]
    position: number
  }> = []

  let position = 0

  traverseContent(content, (node, path) => {
    if (node.type === 'chart' || node.type === 'table' || node.type === 'image') {
      items.push({
        type: node.type,
        node,
        path,
        position: position++
      })
    }
  })

  return items
}

// 为内容添加编号标识
const addNumberingToContent = (content: any[]) => {
  const items = collectChartsAndTables(content)

  // 分别计数图表和表格
  let figureCount = 0 // 图（包括图表和图片）
  let tableCount = 0

  // 按位置排序确保正确的顺序，然后反向处理以避免索引偏移问题
  items.sort((a, b) => b.position - a.position)

  // 为每个项目添加编号（需要重新正向计数）
  const sortedItems = [...items].reverse()

  sortedItems.forEach((item, index) => {
    let numberText = ''

    if (item.type === 'chart' || item.type === 'image') {
      figureCount++
      numberText = `图${figureCount}`
    } else if (item.type === 'table') {
      tableCount++
      numberText = `表${tableCount}`
    }

    // 在节点前添加编号段落
    const numberingNode = {
      type: 'paragraph',
      content: [
        {
          type: 'text',
          text: numberText,
          marks: [{ type: 'bold' }]
        }
      ]
    }

    // 获取对应的反向项目（用于插入位置）
    const reverseItem = items[index]
    const parentPath = reverseItem.path.slice(0, -1)
    const nodeIndex = reverseItem.path[reverseItem.path.length - 1]

    if (parentPath.length === 0) {
      // 顶级节点，直接在content数组中插入
      content.splice(nodeIndex, 0, numberingNode)
    } else {
      // 嵌套节点，需要找到父容器
      let parent = content
      for (const pathIndex of parentPath) {
        parent = parent[pathIndex].content
      }
      parent.splice(nodeIndex, 0, numberingNode)
    }
  })

  return { figureCount, tableCount }
}

// 主要排序函数
const sortChartsAndTables = async () => {
  try {
    // 检查当前章节是否存在
    if (!chapterStore.currentChapter) {
      message.error('当前章节不存在，无法进行排序')
      return false
    }

    // 检查章节内容结构
    if (!chapterStore.currentChapter.content || !chapterStore.currentChapter.content.content) {
      message.error('章节内容结构异常，无法进行排序')
      return false
    }

    // 创建内容的深拷贝以避免直接修改原数据
    const contentCopy = JSON.parse(JSON.stringify(chapterStore.currentChapter.content))

    // 执行编号添加
    const result = addNumberingToContent(contentCopy.content)

    // 更新章节内容
    chapterStore.currentChapter.content = contentCopy

    // 保存内容到服务器
    const saveResult = await chapterStore.saveContent()
    if (!saveResult) {
      message.error('保存失败，请重试')
      return false
    }

    message.success(`排序完成！共处理了 ${result.figureCount} 个图表和 ${result.tableCount} 个表格`)
    return true

  } catch (error) {
    console.error('图表排序失败:', error)
    message.error('排序过程中发生错误，请重试')
    return false
  }
}

const handleOk = async () => {
  loading.value = true

  try {
    const success = await sortChartsAndTables()
    if (success) {
      emit('update:modelValue', false)
    }
  } catch (error) {
    console.error('处理图表排序时发生错误:', error)
    message.error('操作失败，请重试')
  } finally {
    loading.value = false
  }
}

const handleCancel = () => {
  emit('update:modelValue', false)
}


onMounted(() => {
})

onBeforeUnmount(() => {
})
</script>

<style scoped>
.text-ellipsis-wrapper {
  max-height: 100px;
  overflow: hidden;
}

.text-ellipsis {
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

:deep(.ant-radio-wrapper) {
  display: flex !important;
  align-items: flex-start !important;
  padding: 12px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  margin-bottom: 8px;
  transition: all 0.3s;
}

:deep(.ant-radio-wrapper:hover) {
  border-color: #1890ff;
  background-color: #f6ffed;
}

:deep(.ant-radio-wrapper-checked) {
  border-color: #1890ff;
  background-color: #e6f7ff;
}

:deep(.ant-radio) {
  margin-top: 2px;
}
</style>
