<template>
    <div class="flex items-center" v-if="editor">
        <span class="my-1.5 px-2.5 text-[#6D7EA5] whitespace-nowrap text-[13px]">{{ isLoadingSave ? '保存中' : '已保存'
            }}</span>
        <a-tooltip placement="bottom" title="撤销">
            <div @click="editor?.chain().focus().undo().run()"
                class="mx-1  px-1.25 w-7 h-7 border-0 rounded-md cursor-pointer hover:bg-white flex justify-center items-center text-gray-400"
                :class="{ 'text-gray-700': editor?.can().chain().focus().undo().run() }">
                <BookIconfont name="undo"></BookIconfont>
            </div>
        </a-tooltip>
        <a-tooltip placement="bottom" title="重做">
            <div @click="editor?.chain().focus().redo().run()"
                class="mx-1 px-1.25 w-7 h-7 border-0 rounded-md cursor-pointer hover:bg-white flex justify-center items-center text-gray-400"
                :class="{ 'text-gray-700': editor?.can().chain().focus().redo().run() }">
                <BookIconfont name="redo"></BookIconfont>
            </div>
        </a-tooltip>

        <a-tooltip placement="bottom" title="加粗">
            <div @click="editor?.chain().focus().toggleBold().run()"
                class="mx-1 my-0 px-1.25 w-7 h-7 border-0 rounded-md cursor-pointer hover:bg-white flex justify-center items-center"
                :class="{ 'text-blue-600': editor?.isActive('bold') }">
                <BookIconfont name="jiacu1"></BookIconfont>
            </div>
        </a-tooltip>
        <a-tooltip placement="bottom" title="斜体">
            <div @click="editor?.chain().focus().toggleItalic().run()"
                class="mx-1 my-0 px-1.25 w-7 h-7 border-0 rounded-md cursor-pointer hover:bg-white flex justify-center items-center"
                :class="{ 'text-blue-600': editor?.isActive('italic') }">
                <BookIconfont name="xieti"></BookIconfont>
            </div>
        </a-tooltip>
        <a-tooltip placement="bottom" title="下划线">
            <div @click="editor?.chain().focus().toggleUnderline().run()"
                class="mx-1 my-0 px-1.25 w-7 h-7 border-0 rounded-md cursor-pointer hover:bg-white flex justify-center items-center"
                :class="{ 'text-blue-600': editor?.isActive('underline') }">
                <BookIconfont name="xiahuaxian"></BookIconfont>
            </div>
        </a-tooltip>
        <a-tooltip placement="bottom" title="上传图片">
            <div @click="handleUploadImage"
                class="mx-1 my-0 px-1.25 w-7 h-7 border-0 rounded-md cursor-pointer hover:bg-white flex justify-center items-center">
                <BookIconfont name="tupian"></BookIconfont>
            </div>
        </a-tooltip>
        <a-tooltip placement="bottom" title="行内公式">
            <div @click="handleInsertInlineMath"
                class="mx-1 my-0 px-1.25 w-7 h-7 border-0 rounded-md cursor-pointer hover:bg-white flex justify-center items-center">
                <block theme="outline" size="13" />
            </div>
        </a-tooltip>
        <a-tooltip placement="bottom" title="块公式">
            <div @click="handleInsertBlockMath"
                class="mx-1 my-0 px-1.25 w-7 h-7 border-0 rounded-md cursor-pointer hover:bg-white flex justify-center items-center">
                <inline theme="outline" size="13" />
            </div>
        </a-tooltip>
        <TablePopover v-if="editor" :editor="editor"></TablePopover>
    </div>
</template>
<script setup lang="ts">
import BookIconfont from '@/components/Book/BookIconfont.vue';
import TablePopover from '@/components/Book/tiptap/TablePopover/index.vue';
import { Block, Inline } from '@icon-park/vue-next';
import { message } from 'ant-design-vue';
import { useBookEditorStore } from '~/stores/bookEditor';
import { checkEditorFocus } from '~/utils/editor';
import { uploadFn } from '../tiptap/extensions/Image/upload';

const { editor, mathModalVisible, mathEditingData, isLoadingSave } = storeToRefs(useBookEditorStore())


const handleInsertInlineMath = () => {
    if (!checkEditorFocus(editor.value)) {
        message.warning('请选择插入位置')
        return
    }
    mathEditingData.value = {
        formula: 'E = mc^2',
        type: 'inline'
    }
    mathModalVisible.value = true
}

const handleInsertBlockMath = () => {
    if (!checkEditorFocus(editor.value)) {
        message.warning('请选择插入位置')
        return
    }
    mathEditingData.value = {
        formula: '\\sum_{i=1}^n i = \\frac{n(n+1)}{2}',
        type: 'block'
    }
    mathModalVisible.value = true
}


const handleUploadImage = () => {
    if (!checkEditorFocus(editor.value)) {
        message.warning('请选择插入位置')
        return
    }
    if (!editor.value) return;
    // 在这里调用uploadFn或触发原生文件上传
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.onchange = async (event) => {
        const file = (event.target as HTMLInputElement).files?.[0];
        if (file && editor.value) {
            try {
                const url = await uploadFn(file);
                if (url && typeof url === 'string') {

                    // if (isFocused.value) {
                    editor.value.chain().focus().setImage({ src: url }).run();
                    // } else {
                    //     const { state, view } = editor.value;
                    //     const end = state.doc.content.size;
                    //     view.dispatch(state.tr.insert(end, state.schema.nodes.image.create({ src: url })));
                    // }
                }
            } catch (error) {
                console.error('上传图片失败:', error);
                message.error('上传图片失败');
            }
        }
    };
    input.click();
};

</script>