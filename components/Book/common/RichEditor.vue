<template>
  <div class="book-editor flex h-full w-full justify-between">
    <div class="flex flex-1 flex-col h-full text-gray-700">
      <!-- 编辑器工具栏区域 -->
      <div class="flex p-4">
        <div @click="handlePressPreviousChapter"
          class="w-[26px] h-[26px] bg-[#F5F7FF] flex justify-center items-center rounded-full"
          :class="[!chapterStore.previousChapter ? 'text-gray-400 cursor-not-allowed' : 'text-gray-700 cursor-pointer']">
          <left theme="outline" size="20" />
        </div>
        <div class="flex-1 space-x-2 text-center">
          <div class="flex items-center justify-center">
            <span class="ml-4">{{ currentChapter?.full_title || currentChapter?.title || '' }}</span>
          </div>
        </div>
        <div @click="handlePressNextChapter"
          class="w-[26px] h-[26px] bg-[#F5F7FF] flex justify-center items-center rounded-full"
          :class="[!chapterStore.nextChapter ? 'text-gray-400 cursor-not-allowed' : 'text-gray-700 cursor-pointer']">
          <right theme="outline" size="20" />
        </div>
      </div>

      <!-- 可滚动的内容区域 -->
      <div class="flex-1 overflow-auto rounded-lg px-4" ref="editorContainer">
        <!-- 边框区域 -->
        <div class="min-h-ful">
          <editor-content v-if="editor" :editor="editor" />

          <ClientOnly>
            <!-- AI编辑菜单组件 -->
            <SafeAIBubbleMenu :editor="editor" :submissionId="currentBook?.submission_id" :book="currentBook"
              :chapter="currentChapter" :editor-width="editorWidth" @insert-inline-math="handleInsertInlineMath"
              @insert-block-math="handleInsertBlockMath" @upload-image="handleUploadImage" />
          </ClientOnly>
        </div>
      </div>
    </div>
  </div>

  <!-- 图表数据编辑弹窗 -->
  <ChartEditorModal v-model:visible="modalVisible" :chart-type="currentChartType" :editing-data="editingData"
    @save="handleChartSave" @cancel="handleModalCancel" />

  <!-- 数学公式编辑弹窗 -->
  <MathEditorModal v-model:visible="mathModalVisible" :form-data="mathEditingData" @save="handleMathSave"
    @cancel="handleMathModalCancel" />

  <SvgEditorModal v-model:visible="schematicSvgStore.visible" @save="handleSvgSave"></SvgEditorModal>
</template>

<script lang="ts" setup>
import SvgEditorModal from '@/components/Book/tiptap/SvgEditorModal.vue';
import { useChapterStore } from '@/stores/chapter';
import { editorBus } from '@/utils/book/editorBus';
import { Left, Right } from '@icon-park/vue-next';
import Highlight from '@tiptap/extension-highlight';
import Table from '@tiptap/extension-table';
import TableCell from '@tiptap/extension-table-cell';
import TableHeader from '@tiptap/extension-table-header';
import TableRow from '@tiptap/extension-table-row';
import TextAlign from '@tiptap/extension-text-align';
import { Underline } from '@tiptap/extension-underline';
import StarterKit from '@tiptap/starter-kit';
import type { EditorOptions } from '@tiptap/vue-3';
import { Editor, EditorContent } from '@tiptap/vue-3';
import { message } from 'ant-design-vue';
import 'katex/dist/katex.min.css';
import ImageResize from 'tiptap-extension-resize-image';
import { nextTick, onBeforeUnmount, onMounted, ref, watch } from 'vue';
import { Citation } from '~/components/Book/tiptap/extensions/CitationExtension';
import { GeneratingIndicator } from '~/components/Book/tiptap/extensions/GeneratingIndicatorExtension';
import { Image as UploadImage } from '~/components/Book/tiptap/extensions/Image/Image';
import { uploadFn } from '~/components/Book/tiptap/extensions/Image/upload';
import { MathBlock, MathInline } from '~/components/Book/tiptap/extensions/MathExtension';
import { useBookEditorStore } from '~/stores/bookEditor';
import { useSchematicSvgStore } from '~/stores/schematicSvgStore';
import type { Book, Chapter } from '~/types/book';
import ChartEditorModal from '../tiptap/ChartEditorModal.vue';
import MathEditorModal from '../tiptap/MathEditorModal.vue';
import SafeAIBubbleMenu from '../tiptap/components/SafeAIBubbleMenu.vue';

import {
  ArcElement,
  BarElement,
  CategoryScale,
  Chart as ChartJS,
  Legend,
  LinearScale,
  LineElement,
  PointElement,
  Title,
  Tooltip
} from 'chart.js';

import { Extension } from '@tiptap/core';
import { Chart } from '../tiptap/extensions/ChartExtension';


const props = defineProps({
  modelValue: {
    type: Object,
    default: {},
  }
});

const emit = defineEmits(['update:modelValue', 'change']);

const chapterStore = useChapterStore()
const taskStore = useTaskStore()
const editorWidth = ref(0); // 新增：用于存储编辑器宽度

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  PointElement,
  LineElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
)

const { editor, mathModalVisible, mathEditingData } = storeToRefs(useBookEditorStore())
const modalVisible = ref(false)
const currentChartType = ref<'bar' | 'line' | 'pie'>('bar')

interface Dataset {
  label: string;
  data: string;
}

interface EditingData {
  labels: string;
  datasets: Dataset[];
}

const editingData = ref<EditingData>({
  labels: '',
  datasets: [{
    label: '',
    data: ''
  }]
})

const router = useRouter()

const currentBook = useState<Book | null>('currentBook', () => null)
const currentChapter = useState<Chapter | null>('currentChapter', () => null)

const editorContainer = ref<HTMLElement | null>(null)

const editingNode = ref<any>(null);
const mathEditingNode = ref<any>(null);

const schematicSvgStore = useSchematicSvgStore()

const handleModalCancel = () => {
  modalVisible.value = false;
  editingNode.value = null;
}

const handleMathSave = (data: any) => {
  if (editor.value && data.formula) {
    if (data.type === 'inline') {
      editor.value.chain().focus().setMathInline(data.formula).run()
    } else {
      editor.value.chain().focus().setMathBlock(data.formula).run()
    }
  }
  mathModalVisible.value = false
}

const handleMathModalCancel = () => {
  mathModalVisible.value = false;
  mathEditingNode.value = null;
};

const handlePressPreviousChapter = () => {
  const taskStore = useTaskStore()
  if (taskStore.isGenerating) {
    message.warning('正在生成章节内容，请等待完成后再切换章节')
    return
  }

  if (taskStore.isTraversing) {
    message.warning('正在批量生成章节内容，请等待完成后再切换章节')
    return
  }
  if (chapterStore.previousChapter && chapterStore.bookValue) {
    chapterStore.loadChapter(chapterStore.previousChapter.key)
  }
}

const handlePressNextChapter = () => {
  const taskStore = useTaskStore()
  if (taskStore.isGenerating) {
    message.warning('正在生成章节内容，请等待完成后再切换章节')
    return
  }

  if (taskStore.isTraversing) {
    message.warning('正在批量生成章节内容，请等待完成后再切换章节')
    return
  }

  if (chapterStore.nextChapter && chapterStore.bookValue) {
    chapterStore.loadChapter(chapterStore.nextChapter.key)
  }
}

const handleInsertInlineMath = () => {
  mathEditingData.value = {
    formula: 'E = mc^2',
    type: 'inline'
  }
  mathModalVisible.value = true
}

const handleInsertBlockMath = () => {
  mathEditingData.value = {
    formula: '\\sum_{i=1}^n i = \\frac{n(n+1)}{2}',
    type: 'block'
  }
  mathModalVisible.value = true
}

const handleChartSave = (data: any) => {
  if (editor.value) {
    editor.value.chain().focus().setChart({
      type: data.chartType,
      data: {
        labels: data.labels.split(',').map((item: string) => item.trim()),
        datasets: data.datasets
      }
    }).run()
  }
  modalVisible.value = false
}


const handleUploadImage = () => {
  if (!editor.value) return;
  // 在这里调用uploadFn或触发原生文件上传
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = 'image/*';
  input.onchange = async (event) => {
    const file = (event.target as HTMLInputElement).files?.[0];
    if (file && editor.value) {
      try {
        const url = await uploadFn(file);
        if (url && typeof url === 'string') {
          editor.value.chain().focus().setImage({ src: url }).run();
        }
      } catch (error) {
        console.error('上传图片失败:', error);
        message.error('上传图片失败');
      }
    }
  };
  input.click();
};

/** 回调区域 */
const onUpdate = ({ editor: _editor }: any) => {
  // console.log('onUpdate', taskStore.isGenerating)
  // sse生成中，关闭自动保存的功能
  if (taskStore.isGenerating) {
    return
  }

  const content = _editor.getJSON()
  emit('update:modelValue', content);
  emit('change', content);
}

// 创建防抖函数
const createDebounce = (fn: Function, delay: number) => {
  let timer: number | null = null;
  return function (...args: any[]) {
    if (timer) {
      clearTimeout(timer);
    }
    timer = window.setTimeout(() => {
      fn(...args);
      timer = null;
    }, delay);
  };
};

// 滚动到底部方法
const scrollToBottom = () => {
  nextTick(() => {
    if (editorContainer.value) {
      editorContainer.value.scrollTop = editorContainer.value.scrollHeight;
    }
  });
};

watch(() => props.modelValue, (newValue) => {
  if (JSON.stringify(newValue) != JSON.stringify(editor.value?.getJSON() || {})) {
    editor.value?.commands.setContent(newValue, false);
  }
});

// 监听生成状态变化，更新编辑器可编辑状态
watch(() => taskStore.isGenerating, (newValue) => {
  if (editor.value) {
    editor.value.setEditable(!newValue, false);
    console.log(`编辑器可编辑状态已更新: ${!newValue}`);
  }
});

// 监听指示器显示状态，自动滚动到底部
watch(() => taskStore.autoCreatingIndicatorVisible, (newValue) => {
  if (newValue) {
    scrollToBottom();
  }
});

// 更新编辑器宽度的函数
const updateEditorWidth = () => {
  if (editorContainer.value) {
    editorWidth.value = editorContainer.value.clientWidth;
    console.log('Editor width updated:', editorWidth.value);
  }
};

// 创建自定义扩展来处理键盘事件和选区变化
const CustomExtension = Extension.create({
  name: 'customExtension',

  addKeyboardShortcuts() {
    return {
      Backspace: ({ editor }) => {
        if (!editor.state.selection.empty) {
          setTimeout(() => {
            const pos = editor.state.selection.to;
            editor.commands.setTextSelection(pos);
          }, 10);
        }
        return false;
      },
      Delete: ({ editor }) => {
        if (!editor.state.selection.empty) {
          setTimeout(() => {
            const pos = editor.state.selection.to;
            editor.commands.setTextSelection(pos);
          }, 10);
        }
        return false;
      },
    }
  },
});

const handleOpenSchematicEditor = (svgData: any) => {
  console.log('编辑示意图:', svgData)

  // 如果有需要，可以在这里转换svgData为SchematicInfo格式
  // 这里我们假设svgData中包含了必要的信息
  if (svgData) {
    try {
      // 检查是否有schematicData（新的存储方式）
      if (svgData.schematicData) {
        // 设置示意图数据
        schematicSvgStore.setSchematicData(svgData.schematicData)
        // 设置编辑模式
        schematicSvgStore.setIsEdit(true)
        // 打开编辑器模态框
        schematicSvgStore.openModal()
      } else if (svgData.svgData) {
        // 兼容旧的数据格式
        schematicSvgStore.setSchematicData(svgData.svgData)
        schematicSvgStore.setIsEdit(true)
        schematicSvgStore.openModal()
      } else {
        console.warn('未找到有效的SVG数据')
      }
    } catch (error) {
      console.error('处理SVG数据时出错:', error)
    }
  }
}


const handleSvgSave = (data: any) => {
  console.log('handleSvgSave 接收数据 ==>', data)

  try {
    // 准备SVG数据用于存储在图片节点属性中
    const svgDataForStorage = {
      schematicData: {
        title: data.title,
        items: data.items,
        fileName: data.fileName,
        count: data.count,
        type: data.type,
        nums: data.nums
      },
      imageUrl: data.svgUrl,
      svgWidth: data.svgWidth,
      svgHeight: data.svgHeight,
      docId: chapterStore.bookValue?.submission_id
    }

    // 创建图片节点，包含SVG数据
    const imageNode = {
      type: 'image',
      attrs: {
        src: data.svgUrl,
        alt: data.title || '示意图',
        title: data.title || '示意图',
        width: data.svgWidth,
        height: data.svgHeight,
        svgData: svgDataForStorage
      }
    }

    if (schematicSvgStore.isEdit) {
      console.log('编辑模式：更新现有图片的SVG数据')
      // 编辑模式：更新现有图片的SVG数据
      const success = editor.value?.commands?.updateImageSvgData(data.svgUrl, svgDataForStorage)
      if (!success) {
        console.warn('更新SVG数据失败，尝试插入新图片')
        editor.value?.chain().focus().insertContent(imageNode).run()
      }
    } else {
      // 新增模式：插入新的示意图
      console.log('新增模式：插入新的示意图')

      if (editor.value) {
        try {
          // 获取当前选区
          const { from, to } = editor.value.state.selection
          console.log('当前选区位置 ==>', { from, to })

          // 确保编辑器有焦点
          if (!editor.value.isFocused) {
            editor.value.commands.focus()
          }

          // 检查是否有选中的内容
          const hasSelection = from !== to
          console.log('是否有选中内容:', hasSelection)

          // 使用专门的 setImageWithSvgData 命令来插入带有SVG数据的图片
          console.log('使用 setImageWithSvgData 命令插入图片')
          if (hasSelection) {
            // 有选中内容时，将光标移动到选区末尾，不替换内容
            console.log('有选中内容，将图片插入到选区末尾')
            editor.value.commands.setTextSelection(to)

            const contentToInsert = [
              { type: 'paragraph', content: [] }, // 添加一个空段落作为分隔
              imageNode
            ]

            const success = editor.value.commands.insertContentAt(to, contentToInsert, {
              updateSelection: true,
              parseOptions: {
                preserveWhitespace: 'full'
              }
            })

            if (!success) {
              console.warn('setImageWithSvgData 失败，使用备用方案')
              editor.value.chain().focus().insertContent(imageNode).run()
            }
          } else {
            // 没有选中内容时，在当前光标位置插入
            console.log('没有选中内容，在当前位置插入')

            const success = editor.value.commands.insertContentAt(to, imageNode, {
              updateSelection: true,
              parseOptions: {
                preserveWhitespace: 'full'
              }
            })

            if (!success) {
              console.warn('setImageWithSvgData 失败，使用备用方案')
              editor.value.chain().focus().insertContent(imageNode).run()
            }
          }
        } catch (error) {
          console.error('插入内容时出错:', error)
          // 最终备用方案：简单插入
          try {
            editor.value.chain().focus().insertContent(imageNode).run()
          } catch (fallbackError) {
            console.error('备用插入方案也失败:', fallbackError)
            throw new Error('无法插入示意图内容')
          }
        }
      } else {
        console.error('编辑器实例不存在')
        throw new Error('编辑器未初始化')
      }
    }

    schematicSvgStore.setIsEdit(false)

    // 成功提示
    message.success(schematicSvgStore.isEdit ? '示意图更新成功' : '示意图插入成功')
  } catch (error: any) {
    console.error('处理SVG内容时出错:', error)

    // 根据错误类型给出不同的提示
    if (error?.message?.includes('编辑器未初始化')) {
      message.error('编辑器未准备就绪，请稍后重试')
    } else if (error?.message?.includes('无法插入示意图内容')) {
      message.error('插入示意图失败，请检查光标位置后重试')
    } else {
      message.error('处理示意图时出现错误，但内容可能已成功插入')
    }

    // 重置编辑状态
    schematicSvgStore.setIsEdit(false)
  }
}

const clearEditorSelection = () => {
  try {
    // 1. 清除浏览器DOM选区
    const selection = window.getSelection();
    if (selection) {
      selection.removeAllRanges();
    }
    // 2. 清除编辑器内部选区
    if (editor?.value) {
      // 获取当前文档的结尾位置
      const docEnd = editor.value.state.doc.content.size;
      // 创建一个新的光标位置（不是选区范围）
      // 使用简单的方法设置光标位置到文档末尾
      editor.value.commands.setTextSelection(docEnd);
      // 强制编辑器更新视图
      editor.value.view.updateState(editor.value.view.state);
    }
    console.log('选区已完全清空');
  } catch (error) {
    console.error('清空选区时出错:', error);
  }
};

onMounted(() => {
  // console.log('editor onMounted', !taskStore.isGenerating)

  // 获取初始编辑器宽度
  nextTick(() => {
    updateEditorWidth();

    // 监听窗口大小变化，更新编辑器宽度
    window.addEventListener('resize', updateEditorWidth);
  });

  const editorConfig: Partial<EditorOptions> = {
    extensions: [
      Highlight.configure({ multicolor: true }),
      StarterKit as any,
      Underline,
      ImageResize,
      CustomExtension,
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      Table.configure({
        resizable: true,
      }),
      TableRow,
      TableHeader,
      TableCell,
      MathInline.configure({
        onEdit: ({ formula, type }) => {
          mathEditingData.value = {
            formula,
            type: 'inline'
          }
          mathModalVisible.value = true
        }
      }),
      Chart.configure({
        onEditChart: ({ type, data }) => {
          currentChartType.value = type as 'bar' | 'line' | 'pie'
          const labels = data.labels
          const datasets = data.datasets
          editingData.value = {
            labels: labels.join(', '),
            datasets: datasets.map((dataset: any) => ({
              label: dataset.label || '',
              data: dataset.data.join(', ')
            }))
          }
          modalVisible.value = true
        }
      }),
      MathBlock.configure({
        onEdit: ({ formula, type }) => {
          mathEditingData.value = {
            formula,
            type: 'block'
          }
          mathModalVisible.value = true
        }
      }),
      Citation.configure({
        onEditCitation: ({ id }) => {
        },
        onClickCitation: ({ id }) => {
        },
      }),
      UploadImage.configure({
        uploadFn: uploadFn,
        openSchematicEditor: handleOpenSchematicEditor
      }),
      // 添加AI选择菜单插件
      // AISelectionMenu,
      // 添加生成指示器插件
      GeneratingIndicator,
    ],
    content: props.modelValue.content.length > 0 ? props.modelValue : "<p></p>",
    editable: !taskStore.isGenerating,
    onUpdate: createDebounce(onUpdate, 800),
    onCreate: () => {
      console.log('editor onCreate')
    },
    onDestroy: () => {
      console.log('editor onDestroy')
    },
    onPaste: async (event: any) => {
      // 获取DataTransfer对象
      const dataTransfer = event.clipboardData || event.originalEvent?.clipboardData;
      if (!dataTransfer) {
        console.log('无法获取DataTransfer对象');
        return;
      }
      const fileList = dataTransfer.files;
      for (let i = 0; i < fileList.length; i++) {
        const file = fileList[i];
        if (file.type.startsWith('image/')) {
          // 阻止默认的图片插入行为
          event.preventDefault();
          try {
            // 上传图片到阿里云 OSS
            const url = await uploadFn(file);
            if (url && typeof url === 'string') {
              editor.value?.chain().focus().setImage({ src: url }).run();
            }
          } catch (error) {
            console.error('图片上传失败:', error);
          }
        }
      }
    }
  };

  editor.value = new Editor(editorConfig);

  editorBus.on('editor:setContent', ({ content, emitUpdate }) => {
    // console.log('editor:setContent content = ', content)
    editor.value?.commands.setContent(content, emitUpdate, {
      preserveWhitespace: 'full'
    }, {
      errorOnInvalidContent: false // 忽略无效内容 但目前没什么效果 保留false以便最大程度兼容
    })
  })
  editorBus.on('editor:insertContent', (content) => {
    if (editor.value) {
      const selection = editor.value.state.selection
      if (selection) {
        const to = selection.to
        editor.value.commands.insertContent(content, {
          updateSelection: true,
          parseOptions: {
            preserveWhitespace: 'full'
          }
        })
      }
    }

  })
  editorBus.on('editor:insertContentAt', (content) => {
    // console.log(content, 'editor:insertContentAt')
    if (editor.value) {
      const selection = editor.value.state.selection
      if (selection) {
        const to = selection.to
        editor.value.commands.insertContentAt(to, content, {
          updateSelection: true,
          parseOptions: {
            preserveWhitespace: 'full'
          }
        })
        clearEditorSelection()
      }
    }
  })
  editorBus.on('editor:insertContentAtEnd', (content) => {
    // console.log('editor:insertContentAtEnd content = ', content)
    if (editor.value) {
      // 获取文档大小（即文档末尾的位置）
      const docSize = editor.value.state.doc.content.size;

      // 在文档末尾插入内容，而不是在光标位置
      editor.value.commands.insertContentAt(docSize, content.content, {
        updateSelection: false,
        parseOptions: {
          preserveWhitespace: 'full'
        }
      });

      // 滚动到底部
      scrollToBottom();
    }
  })
  // 最主要的插入内容的方法，当sse返回block内容时，调用此方法
  editorBus.on('editor:appendGeneratedContent', (content) => {
    if (editor.value) {
      // console.log('editor:appendGeneratedContent content = ', content.content)

      // 检查编辑器是否只有一个空段落
      const docContent = editor.value.getJSON();
      const hasOnlyEmptyParagraph =
        docContent.content &&
        docContent.content.length === 1 &&
        docContent.content[0].type === 'paragraph' &&
        (!docContent.content[0].content || docContent.content[0].content.length === 0);

      if (hasOnlyEmptyParagraph) {
        // 如果只有一个空段落，则替换整个内容
        editor.value.commands.setContent(content.content, false, {
          preserveWhitespace: 'full'
        });
      } else {
        // 否则，按原来的方式在末尾追加内容
        const docSize = editor.value.state.doc.content.size;
        editor.value.commands.insertContentAt(docSize, content.content, {
          updateSelection: false,
          parseOptions: {
            preserveWhitespace: 'full'
          }
        });
      }

      // 滚动到底部
      scrollToBottom();
    }
  })
})


onBeforeUnmount(() => {
  editorBus.off('editor:insertContentAt')
  editorBus.off('editor:insertContent')
  editorBus.off('editor:setContent')
  editorBus.all.clear()
  editor.value?.destroy()

  // 移除窗口大小变化监听器
  window.removeEventListener('resize', updateEditorWidth);
})


</script>

<style scoped>
:deep(.ProseMirror) {
  position: relative;
  min-height: 560px;
  padding: 1rem;
  line-height: 200%;
  font-family: '宋体', sans-serif;
  outline: none;

  /* 编辑器内容区域的标题样式 */
  .ProseMirror h1 {
    margin-top: 30px;
    margin-bottom: 0;
    font-size: 22px;
    line-height: 37px;
  }

  .ProseMirror h2 {
    margin-top: 15px;
    margin-bottom: 0;
    font-size: 20px;
    line-height: 33px;
  }

  .ProseMirror h3 {
    margin-top: 15px;
    margin-bottom: 0;
    font-size: 19px;
    line-height: 31px;
  }

  .ProseMirror h1+.no-indent {
    margin-top: 15px;
  }

  p {
    text-indent: 2em;
    margin-bottom: 0.5em;
  }

  /* 居中文本段落不缩进 */
  p[style*="text-align: center"] {
    text-indent: 0;
  }

  :focus {
    outline: none;
  }
}

/* 生成指示器的样式 */
:deep(.generating-indicator-widget) {
  margin-top: 1rem;
  margin-bottom: 1.5rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  background-color: rgba(249, 250, 251, 0.8);
  display: inline-block;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

:deep(.ProseMirror table) {
  border-collapse: collapse;
  margin: 0;
  overflow: hidden;
  table-layout: fixed;
  width: 100%;
  margin-top: 12px;
}

:deep(.ProseMirror td),
:deep(.ProseMirror th) {
  border: 2px solid #ced4da;
  box-sizing: border-box;
  min-width: 1em;
  padding: 3px 5px;
  position: relative;
  vertical-align: top;
}

:deep(.ProseMirror th) {
  background-color: #f8f9fa;
  font-weight: bold;
  text-align: left;
}

:deep(.ProseMirror .selectedCell:after) {
  background: rgba(200, 200, 255, 0.4);
  content: "";
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  pointer-events: none;
  position: absolute;
  z-index: 2;
}

:deep(.ProseMirror h4) {
  font-size: 1.1em;
  font-weight: bold;
  margin: 1em 0;
}

:deep(.math-block) {
  margin: 1em 0;
  text-align: center;
}

:deep(.katex-display) {
  margin: 0;
}

.preview-math {
  @apply p-4 bg-gray-50 rounded-lg min-h-[100px] flex items-center justify-center;
}

:deep(.citation-mark) {
  vertical-align: super;
  font-size: 0.8em;
}

:deep(.ProseMirror li),
:deep(.ProseMirror blockquote),
:deep(.ProseMirror .math-block),
:deep(.ProseMirror table) {
  text-indent: 0;
}

:deep(.ProseMirror td p),
:deep(.ProseMirror th p) {
  text-indent: 0;
}
</style>