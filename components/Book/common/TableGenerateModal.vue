<template>
    <a-modal :open="modelValue" title="AI生成表格" width="60%" @ok="handleOk" @cancel="handleCancel"
        :confirmLoading="loading" cancelText="取消" okText="生成" :zIndex="48">
        <a-form layout="vertical" class="space-y-4">
            <!-- <div class="space-y-4"> -->
            <div v-if="chapterStore.referceText" class="my-5 px-5 py-3 bg-blue-50 rounded-lg text-gray-700">
                <div class="text-ellipsis-wrapper">
                    <div class="text-ellipsis">{{ chapterStore.referceText }}</div>
                </div>
            </div>
            <!-- 添加标签显示区域 -->
            <div v-else class="flex gap-2 my-2">
                上下文：
                <a-tag color="blue" class="flex items-center">
                    <template #icon>
                        <book-outlined />
                    </template>
                    已包含著作信息
                </a-tag>
                <a-tag color="green" class="flex items-center">
                    <template #icon>
                        <file-text-outlined />
                    </template>
                    已包含本章内容
                </a-tag>
            </div>


            <a-form-item required>
                <a-textarea v-model:value="prompt" :rows="8" placeholder="请输入你想要生成的表格内容或补充要求" :maxlength="1000"
                    showCount />
            </a-form-item>

            <!-- </div> -->
        </a-form>
    </a-modal>
</template>

<script setup lang="ts">
import { useChapterStore } from '@/stores/chapter';
import { generateTable } from '@/utils/api/generate_table';
import { BOOK_PAY_TRIGGER_TYPE } from '@/utils/constants';
import { message } from 'ant-design-vue';
import { onBeforeUnmount, onMounted, ref, watch } from 'vue';
import { UserService } from '~/services/user';
const { $eventBus } = useNuxtApp();
const chapterStore = useChapterStore()

const props = defineProps<{
    modelValue: boolean
}>()

const emit = defineEmits<{
    (e: 'update:modelValue', value: boolean): void
    (e: 'confirm', content: string): void
}>()

// const visible = ref(props.modelValue)
const prompt = ref('')
const loading = ref(false)

// watch(() => props.modelValue, (val) => {
//     visible.value = val
// })

watch(() => props.modelValue, (val) => {

    if (!val) {
        prompt.value = ''
    }
})

const sumbitData = async () => {
    if (!chapterStore.currentChapter?.key) {
        message.warning('请先选择章节')
        return
    }
    loading.value = true

    try {
        let userContext = ''
        if (chapterStore.referceText) {
            userContext = `${chapterStore.referceText}, \n 请结合上下文生成表格`
        }
        const response = await generateTable({
            chapter_key: chapterStore.currentChapter.key,
            user_prompt: prompt.value,
            user_context: userContext,
            teamId: chapterStore.teamId
        })
        // loading.value = false
        if (response.content) {
            emit('confirm', response.content)
            // visible.value = false
            chapterStore.referceText = ''
            emit('update:modelValue', false)
            prompt.value = ''
        }
        await UserService.loadUserInfoAndAssistantMemberInfo()
    } catch (error) {
        console.error('生成表格失败:', error)
        message.error('生成失败，请重试')

    } finally {
        loading.value = false
    }
}
const handleOk = async () => {
    // if (!prompt.value) {
    //     message.warning('请输入表格描述')
    //     return
    // }

    if (!chapterStore.currentChapter?.key) {
        message.warning('请先选择章节')
        return
    }
    chapterStore.showPayModal = true

}


const handleCancel = () => {
    if (loading.value) {
        message.warning('正在创作中，请勿关闭弹窗')
        return
    }
    chapterStore.referceText = ''
    emit('update:modelValue', false)
}
const handleEventConfirmCreate = (payType: string) => {
    if (payType == BOOK_PAY_TRIGGER_TYPE.GENERATE_TABLE) {
        sumbitData()
    }
}

onMounted(() => {
    $eventBus.on(StarloveConstants.keyOfEventBus.bookEditorCreateAfterConfirmPayModal, handleEventConfirmCreate)
})
onBeforeUnmount(() => {
    $eventBus.off(StarloveConstants.keyOfEventBus.bookEditorCreateAfterConfirmPayModal, handleEventConfirmCreate);
});
</script>

<style scoped>
.text-ellipsis-wrapper {
    position: relative;
    overflow: hidden;
    height: 3em;
}

.text-ellipsis {
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    overflow: hidden;
    word-break: break-all;
    padding: 0;
    line-height: 1.5;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
}
</style>
