<template>
    <a-modal v-model:open="visible" title="添加文件" width="60%" @cancel="handleCancel" :footer="null" destroy-on-close>
        <div class="space-y-4">
            <FieldUpload :ref="(el) => {
                if (el) uploadFilesRefs = el
            }
                " v-model:referenceType="fileValue" :isShowLabel="false" :field-item-info="fieldItem"
                :references-list="[]" :app-code="'book'"
                :knowledge-upload-list="chapterStore.chapterUploadFileModal == BookUploadFileType.bookDataBase ? knowledgeUploadList : []">
            </FieldUpload>
        </div>
        <div class="flex justify-center items-center">
            <a-button class="px-10 bg-[#2551B5]" type="primary"
                :loading="fileUploadProgress > 0 && fileUploadProgress < 100" @click="handleOk">{{ fileUploadProgress >
                    0 ? `上传中${fileUploadProgress.toFixed(0)}%` : '确定' }}</a-button>
        </div>
    </a-modal>
</template>

<script setup lang="ts">
import FieldUpload from '@/components/Create/component/FieldUpload.vue';
import { useChapterStore } from '@/stores/chapter';
import { BookUploadFileType } from '@/utils/constants';
import { message } from 'ant-design-vue';
import axios from 'axios';
import { ref } from 'vue';
import { checkFileHash } from '~/api/repositoryFile';
import { generatePutUrl, uploadByUrl } from '~/api/upload';
import type { SelectRequireInfo } from '~/services/types/appMessage';
import { getFileSha256, removeQuestionMarkText } from '~/utils/utils';

const chapterStore = useChapterStore()

const props = defineProps<{
    modelValue: boolean,
    uploadFileCount: number,
}>()

const emit = defineEmits<{
    (e: 'update:modelValue', value: boolean): void
    // (e: 'update:fileList', value: any[]): void
    (e: 'confirm', list: any[], type: BookUploadFileType): void
}>()

const visible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
})

const fieldItem = ref<SelectRequireInfo>({
    maxLength: props.uploadFileCount,
    fieldType: 'upload',
    fieldCode: 'file',
    fieldName: '文件',
    fieldValue: '',
    isRequired: 'N',
    description: '请上传文件',
    placeholder: '需上传文献原文、非文献标题',
    isShowField: true,
    options: '.pdf,doc,docx,ppt,pptx,xls,xlsx,txt,md,csv'
})

const knowledgeUploadList = ref<SelectRequireInfo[]>([
    {
        fieldCode: 'knowledge-upload',
        fieldName: '文件',
        fieldType: 'attachment',
        isRequired: 'N',
        fieldValue: '',
        defaultValue: '',
        options: '.pdf,doc,docx,ppt,pptx,xls,xlsx,txt,md,csv',
        description: '请上传文件',
        placeholder: '需上传文献原文、非文献标题',
        maxLength: props.uploadFileCount,
        isShowField: true,
        id: '',
    }
])

const fileValue = ref('')
const uploadFilesRefs = ref()

const fileIdList = ref<any[]>([])
const fileList = ref<any[]>([])



const fileUploadProgress = ref(0)
const fileProgressList = ref<number[]>([])

const handleOk = async () => {

    const list = uploadFilesRefs.value?.fileList
    const selectedKnowledgeFileList = uploadFilesRefs.value?.selectedKnowledgeList
    console.log('fileList', list)
    console.log('selectedKnowledgeFileIdList', selectedKnowledgeFileList)
    try {

        // console.log("fileList ==>", fileList.value)
        fileList.value = []
        await handleUploadFile()
        const type = chapterStore.chapterUploadFileModal
        emit('confirm', fileList.value, type)

    } catch (error) {
        console.log('error ==》', error)
    } finally {
        visible.value = false
        fileUploadProgress.value = 0
    }


}

const handleUploadFile = async () => {
    const fileIdList: Array<{ fileId: string; repositoryFileId?: string }> = []
    const list = uploadFilesRefs.value?.fileList

    if (list && list.length > 0) {
        // 初始化进度数组
        fileProgressList.value = new Array(list.length).fill(0)

        for (let i = 0; i < list.length; i++) {
            const item = list[i]
            if (item.fileUrl) {
                fileIdList.push({ fileId: item.uid })
                fileProgressList.value[i] = 100
                continue
            }

            try {
                const sha256 = await getFileSha256(item.file)
                const checkFileShaResult = await checkFileHash({
                    sha256: `${sha256}`,
                });

                if (!checkFileShaResult.ok) {
                    message.error(item.name + '上传失败')
                    continue
                }

                let params

                if (checkFileShaResult.data != null) {
                    params = {
                        fileName: checkFileShaResult.data.fileName,
                        fileUrl: checkFileShaResult.data.fileUrl,
                        fileSha256: checkFileShaResult.data.fileSha256
                    }
                } else {
                    const result = await generatePutUrl({ filename: item.name })
                    if (!result.ok || !result.data) {
                        message.error(item.name + '上传失败')
                        continue
                    }

                    const response = await axios.put(result.data.url, item.file, {
                        headers: {
                            'Content-Type': result.data.contentType
                        }
                    })

                    if (response.status !== 200) {
                        message.error(item.name + '上传失败')
                        continue
                    }

                    params = {
                        fileName: item.name,
                        fileUrl: removeQuestionMarkText(result.data.url),
                        fileSha256: `${sha256}`
                    }
                }

                console.log("params ==>", params)
                const uploadByUrlResult = await uploadByUrl(params)

                if (!uploadByUrlResult.ok) {
                    message.error(item.name + '上传失败')
                    continue
                }

                fileIdList.push({ fileId: uploadByUrlResult.data.id })
                fileList.value.push({
                    ...item,
                    id: uploadByUrlResult.data.id,
                    fileUrl: uploadByUrlResult.data.fileUrl,
                })

            } catch (error) {
                console.error('Upload error:', error)
                message.error('文件上传失败，请重试')
            }
        }
    }

    const selectedKnowledgeList = uploadFilesRefs.value?.selectedKnowledgeList
    if (selectedKnowledgeList && selectedKnowledgeList.length > 0) {
        fileList.value.push(...selectedKnowledgeList)
    }

    return fileIdList
}

const handleCancel = () => {
    visible.value = false
    chapterStore.chapterUploadFileModal = ''
}

onMounted(() => {
    console.log("props.uploadFileCount  ==>", props.uploadFileCount)
})
</script>
