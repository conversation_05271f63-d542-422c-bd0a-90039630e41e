<template>
  <a-modal :open="modelValue" title="AI生成数学公式" @ok="handleOk" @cancel="handleCancel" :confirmLoading="loading"
    width="800px" cancelText="取消" okText="生成" :zIndex="48">
    <a-form :model="formState" layout="vertical">
      <div v-if="chapterStore.referceText" class="my-5 px-5 py-3 bg-blue-50 rounded-lg text-gray-700">
        <div class="text-ellipsis-wrapper">
          <div class="text-ellipsis">{{ chapterStore.referceText }}</div>
        </div>
      </div>
      <div v-else class="flex gap-2 mb-4">
        上下文：
        <a-tag color="blue" class="flex items-center">
          <template #icon>
            <book-outlined />
          </template>
          已包含著作信息
        </a-tag>
        <a-tag color="green" class="flex items-center">
          <template #icon>
            <file-text-outlined />
          </template>
          已包含本章内容
        </a-tag>
      </div>

      <!-- <a-form-item label="公式类型" required>
        <a-radio-group v-model:value="formState.type">
          <a-radio value="inline">行内公式</a-radio>
          <a-radio value="block">独立公式</a-radio>
        </a-radio-group>
      </a-form-item> -->

      <a-form-item required>
        <a-textarea v-model:value="formState.prompt" :rows="8" placeholder="请输入你想要生成的公式内容或补充要求'" :maxlength="1000"
          showCount />
      </a-form-item>


    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { useChapterStore } from '@/stores/chapter'
import { generateFormula } from '@/utils/api/generate_formula'
import { BookOutlined, FileTextOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { onBeforeUnmount, onMounted, ref, watch } from 'vue'
import { UserService } from '~/services/user'
const { $eventBus } = useNuxtApp();

const defaultPrompt = ''

const chapterStore = useChapterStore()

const props = defineProps<{
  modelValue: boolean
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm', value: { content: any }): void
}>()

const loading = ref(false)
const formState = ref({
  // type: 'inline' as 'inline' | 'block',
  prompt: defaultPrompt,
})

interface GenerateFormulaResult {
  // formula: string
  // description: string
  content: any
}

const generateFormulaContent = async (): Promise<GenerateFormulaResult> => {
  if (!chapterStore.currentChapter?.key) {
    message.warning('请先选择章节')
    return { content: [] }
  }

  loading.value = true
  try {
    let userContext = ''
    if (chapterStore.referceText) {
      userContext = `${chapterStore.referceText}, \n 请结合上下文生成公式`
    }
    const response = await generateFormula({
      chapter_key: chapterStore.currentChapter.key,
      user_prompt: formState.value.prompt,
      user_context: userContext,
      teamId: chapterStore.teamId
    })

    if (response.success && response.content) {

      // formula.value = response.formula
      return {
        content: response.content,
      }
    } else {
      message.error('生成公式失败')
      return { content: [] }
    }
  } catch (error) {
    console.error('生成公式失败:', error)
    message.error('生成失败，请重试')
    return { content: [] }
  } finally {
    loading.value = false
  }
}
const sumbitData = async () => {
  const result = await generateFormulaContent()
  if (!result.content) return

  emit('confirm', {
    // type: formState.value.type,
    content: result.content,
  })
  chapterStore.referceText = ''
  emit('update:modelValue', false)
  await UserService.loadUserInfoAndAssistantMemberInfo()

  // 重置表单
  formState.value = {
    // type: 'inline',
    prompt: defaultPrompt,
  }
  // formula.value = ''
}
const handleOk = async () => {
  // if (!formState.value.prompt) {
  //   message.warning('请输入公式描述')
  //   return
  // }
  chapterStore.showPayModal = true


}

const handleCancel = () => {
  if (loading.value) {
    message.warning('正在创作中，请勿关闭弹窗')
    return
  }
  chapterStore.referceText = ''
  emit('update:modelValue', false)
  // 重置表单
  formState.value = {
    // type: 'inline',   
    prompt: defaultPrompt,
  }
  // formula.value = ''
}

// 监听 modelValue 变化，当弹窗打开时重置表单
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    formState.value = {
      // type: 'inline',
      prompt: defaultPrompt,
    }
    // formula.value = ''
  }
})

const handleEventConfirmCreate = (payType: string) => {
  if (chapterStore.payTriggerType != BOOK_PAY_TRIGGER_TYPE.GENERATE_MATH) {
    return
  }

  sumbitData()

}
onMounted(() => {

  $eventBus.on(StarloveConstants.keyOfEventBus.bookEditorCreateAfterConfirmPayModal, handleEventConfirmCreate)
})
onBeforeUnmount(() => {
  $eventBus.off(StarloveConstants.keyOfEventBus.bookEditorCreateAfterConfirmPayModal, handleEventConfirmCreate);
});
</script>

<style scoped>
.preview-section {
  margin-top: 16px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.preview-title {
  font-weight: bold;
  margin-bottom: 8px;
}

.text-ellipsis-wrapper {
  position: relative;
  overflow: hidden;
  height: 3em;
}

.text-ellipsis {
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  overflow: hidden;
  word-break: break-all;
  padding: 0;
  line-height: 1.5;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}

.preview-content {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60px;
  padding: 16px;
  background-color: white;
  border-radius: 4px;
}
</style>