<template>
  <div class="flex-none">
    <a-space>
      <a-button type="primary" @click="emit('showChart', 'bar')">
        <template #icon><bar-chart-outlined /></template>
        插入柱状图
      </a-button>
      <a-button type="primary" @click="emit('showChart', 'line')">
        <template #icon><line-chart-outlined /></template>
        插入折线图
      </a-button>
      <a-button type="primary" @click="emit('showChart', 'pie')">
        <template #icon><pie-chart-outlined /></template>
        插入饼图
      </a-button>
      <a-divider type="vertical" />
      <a-button @click="emit('export', 'json')">导出 JSON</a-button>
      <a-button @click="emit('export', 'html')">导出 HTML</a-button>
      <a-button @click="emit('export', 'markdown')">导出 Markdown</a-button>
      <a-button @click="emit('export', 'word')">导出 Word</a-button>
    </a-space>
  </div>
</template>

<script lang="ts" setup>
import { BarChartOutlined, LineChartOutlined, PieChartOutlined } from '@ant-design/icons-vue'

type ChartType = 'bar' | 'line' | 'pie'
type ExportType = 'json' | 'html' | 'markdown' | 'word'

const emit = defineEmits<{
  (e: 'showChart', type: ChartType): void
  (e: 'export', type: ExportType): void
}>()
</script> 