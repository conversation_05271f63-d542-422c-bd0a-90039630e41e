<template>
  <div class="w-full h-full flex flex-col text-gray-700">
    <!-- 大纲目录栏区域 -->
    <div class="p-4 bg-[#F5F7FF] outline_left_menu" :ref="(el) => {
      if (el) outlineMenuRef = el
    }">
      <h2 class="text-base font-medium flex justify-between" style="margin-top: 0px;">
        <span>
          <BookIconfont name="outline" :size="20"></BookIconfont> <span class="pl-2">大纲目录</span>
        </span>

        <div @click="handleEditChapters" class="flex items-center text-[15px] text-[#2551B5] cursor-pointer ml-1">
          <write theme="outline" size="16" /> <span class="pl-1">编辑</span>
        </div>
      </h2>
    </div>
    <div class="p-2 flex-1 overflow-y-auto" ref="chaptersContainerRef">
      <template v-for="(chapter, index) in chapterStore.bookValue?.flattened_chapters" :key="chapter.key">
        <ChapterTreeItem :chapter="chapter" :index="index + 1" :current-chapter="chapterStore.currentChapter"
          @select="handleSelectChapter" />
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import BookIconfont from '@/components/Book/BookIconfont.vue';
import { useTaskStore } from '@/stores/task';
import { Write } from '@icon-park/vue-next';
import { message } from 'ant-design-vue';
import { computed, nextTick, onMounted, ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import type { Chapter } from '~/types/book';
import ChapterTreeItem from './ChapterTreeItem.vue';
const route = useRoute()
const router = useRouter()

// 定义引用
const outlineMenuRef = ref();
const chaptersContainerRef = ref();

// // 定义属性
// const props = defineProps<{
//   book: Book | null
//   currentChapter: Chapter | null
// }>()

const chapterStore = useChapterStore()
const taskStore = useTaskStore()

// 计算当前选中的章节 key
const selectedChapterKey = computed(() => route.params.key as string)

// 定义事件
const emit = defineEmits<{
  select: [chapter: Chapter]
}>()

// 选择章节
const handleSelectChapter = (chapter: Chapter) => {
  emit('select', chapter)
}

// 编辑章节
const handleEditChapters = () => {
  console.log("编辑章节")

  if (taskStore.isTraversing) {
    message.warning('正在批量生成章节内容，请等待完成后再编辑大纲')
    return
  }

  if (taskStore.isGenerating) {
    message.warning('正在生成章节内容，请等待完成后再编辑大纲')
    return
  }

  if (chapterStore.bookValue?.key) {
    router.push(`/book/outline/${chapterStore.bookValue?.key}`)
  }
}

// 计算属性，添加参考文献章节
// const allChapters = computed(() => {
//   if (!props.book) return [];
//   return [
//     ...props.book.flattened_chapters,
//   ];
// });

// 滚动到活动章节
const scrollToActiveChapter = () => {
  if (!chapterStore.currentChapter) return;

  nextTick(() => {
    const activeElement = document.querySelector('.active-chapter');
    if (activeElement && chaptersContainerRef.value) {
      activeElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  });
}

// 监听currentChapter变化
watch(() => chapterStore.currentChapter?.key, (newVal, oldVal) => {
  if (newVal !== oldVal) {
    scrollToActiveChapter();
  }
}, { immediate: true });

onMounted(() => {

  scrollToActiveChapter();
})

// 暴露引用给父组件
defineExpose({
  outlineMenuRef
});
</script>
