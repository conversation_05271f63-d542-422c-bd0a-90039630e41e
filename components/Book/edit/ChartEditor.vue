<template>
  <div class="flex flex-col h-full">
    <!-- 工具栏区域-->
    <div class="flex-none p-4 bg-white border-b border-gray-200">
      <a-space>
        <a-button type="primary" @click="showChartModal('bar')">
          <template #icon><bar-chart-outlined /></template>
          插入柱状图
        </a-button>
        <a-button type="primary" @click="showChartModal('line')">
          <template #icon><line-chart-outlined /></template>
          插入折线图
        </a-button>
        <a-button type="primary" @click="showChartModal('pie')">
          <template #icon><pie-chart-outlined /></template>
          插入饼图
        </a-button>
        <a-divider type="vertical" />
        <a-button @click="handleExportJson">导出 JSON</a-button>
        <a-button @click="handleExportHtml">导出 HTML</a-button>
        <a-button @click="handleExportMarkdown">导出 Markdown</a-button>
        <a-button @click="handleExportWord">导出 Word</a-button>
      </a-space>
    </div>

    <!-- 可滚动的内容区域 -->
    <div class="flex-1 overflow-auto">
      <!-- 边框区域 -->
      <div class="border border-gray-200 rounded-lg min-h-full p-4">
        <editor-content v-if="editor" :editor="editor" class="bg-pink-200" />
      </div>
    </div>

    <!-- 图表数据编辑弹窗 -->
    <a-modal v-model:open="modalVisible" :title="modalTitle" @ok="handleModalOk" @cancel="handleModalCancel"
      :maskClosable="false" width="800px">
      <a-form :model="editingData" layout="vertical">
        <template v-if="currentChartType !== 'pie'">
          <!-- 柱状图和折线图的编辑表单 -->
          <a-form-item label="标签" required>
            <a-input v-model:value="editingData.labels" placeholder="请输入标签，用逗号分隔" />
          </a-form-item>
          <a-form-item label="数据" required>
            <a-input v-model:value="editingData.data" placeholder="请输入数据，用逗号分隔" />
          </a-form-item>
          <a-form-item label="数据集名称">
            <a-input v-model:value="editingData.datasetLabel" placeholder="请输入数据集名称" />
          </a-form-item>
        </template>
        <template v-else>
          <!-- 饼图的编辑表单 -->
          <a-form-item label="标签" required>
            <a-input v-model:value="editingData.labels" placeholder="请输入标签，用逗号分隔" />
          </a-form-item>
          <a-form-item label="数据" required>
            <a-input v-model:value="editingData.data" placeholder="请输入数据，用逗号分隔" />
          </a-form-item>
        </template>
      </a-form>
      <div class="mt-4">
        <h3 class="font-bold mb-2">预览1</h3>
        <div class="h-[200px]">
          <Bar v-if="currentChartType === 'bar'" :data="previewChartData" :options="chartOptions" />
          <Line v-if="currentChartType === 'line'" :data="previewChartData" :options="chartOptions" />
          <Pie v-if="currentChartType === 'pie'" :data="previewChartData" :options="chartOptions" />
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { BarChartOutlined, LineChartOutlined, PieChartOutlined } from '@ant-design/icons-vue'
import StarterKit from '@tiptap/starter-kit'
import { Editor, EditorContent } from '@tiptap/vue-3'
import {
  ArcElement,
  BarElement,
  CategoryScale,
  Chart as ChartJS,
  Legend,
  LinearScale,
  LineElement,
  PointElement,
  Title,
  Tooltip
} from 'chart.js'
import { defaultMarkdownSerializer } from 'prosemirror-markdown'
import { computed, onBeforeUnmount, onMounted, ref } from 'vue'
import { Bar, Line, Pie } from 'vue-chartjs'
import { Chart } from '~/components/tiptap/extensions/ChartExtension'
import { exportChartToWord } from '~/utils/export_chart_word'

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  PointElement,
  LineElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
)

const editor = ref<Editor>()
const modalVisible = ref(false)
const currentChartType = ref<'bar' | 'line' | 'pie'>('bar')
const editingData = ref({
  labels: '',
  data: '',
  datasetLabel: ''
})

const modalTitle = computed(() => {
  const typeMap = {
    bar: '柱状图',
    line: '折线图',
    pie: '饼图'
  }
  return `编辑${typeMap[currentChartType.value]}数据`
})

const chartOptions = {
  responsive: true,
  maintainAspectRatio: false
}

const previewChartData = computed(() => {
  const labels = editingData.value.labels.split(',').map(item => item.trim())
  const data = editingData.value.data.split(',').map(item => Number(item.trim()))

  if (currentChartType.value === 'pie') {
    return {
      labels,
      datasets: [{
        data,
        backgroundColor: [
          'rgb(255, 99, 132)',
          'rgb(54, 162, 235)',
          'rgb(255, 205, 86)',
          'rgb(75, 192, 192)',
          'rgb(153, 102, 255)'
        ]
      }]
    }
  }

  return {
    labels,
    datasets: [{
      label: editingData.value.datasetLabel || '数据集',
      data,
      backgroundColor: currentChartType.value === 'bar' ? 'rgba(75, 192, 192, 0.2)' : undefined,
      borderColor: 'rgb(75, 192, 192)',
      borderWidth: currentChartType.value === 'bar' ? 1 : undefined,
      tension: currentChartType.value === 'line' ? 0.1 : undefined,
      fill: currentChartType.value === 'line' ? false : undefined
    }]
  }
})

// 示例数据
type ChartDataset = {
  data: number[]
  label?: string
  backgroundColor?: string | string[]
  borderColor?: string
  borderWidth?: number
  fill?: boolean
  tension?: number
}

const sampleData: Record<'bar' | 'line' | 'pie', {
  labels: string[]
  datasets: ChartDataset[]
}> = {
  bar: {
    labels: ['一月', '二月', '三月', '四月', '五月'],
    datasets: [{
      label: '销售数据',
      data: [65, 59, 80, 81, 56],
      backgroundColor: 'rgba(75, 192, 192, 0.2)',
      borderColor: 'rgba(75, 192, 192, 1)',
      borderWidth: 1
    }]
  },
  line: {
    labels: ['一月', '二月', '三月', '四月', '五月'],
    datasets: [{
      label: '趋势数据',
      data: [65, 59, 80, 81, 56],
      fill: false,
      borderColor: 'rgb(75, 192, 192)',
      tension: 0.1
    }]
  },
  pie: {
    labels: ['红色', '蓝色', '黄色'],
    datasets: [{
      data: [300, 50, 100],
      backgroundColor: [
        'rgb(255, 99, 132)',
        'rgb(54, 162, 235)',
        'rgb(255, 205, 86)'
      ]
    }]
  }
}

const showChartModal = (type: 'bar' | 'line' | 'pie') => {
  currentChartType.value = type
  const data = sampleData[type]
  editingData.value = {
    labels: data.labels.join(', '),
    data: data.datasets[0].data.join(', '),
    datasetLabel: type === 'pie' ? '' : (data.datasets[0].label || '')
  }
  modalVisible.value = true
}

const handleModalOk = () => {
  const chartData = previewChartData.value
  if (editor.value) {
    editor.value.chain().focus().setChart({
      type: currentChartType.value,
      data: chartData
    }).run()
  }
  modalVisible.value = false
}

const handleModalCancel = () => {
  modalVisible.value = false
}

const handleExportJson = () => {
  if (editor.value) {
    console.log('JSON 内容：', editor.value.getJSON())
  }
}

const handleExportHtml = () => {
  if (editor.value) {
    console.log('HTML 内容：', editor.value.getHTML())
  }
}

const handleExportMarkdown = () => {
  if (editor.value) {
    const json = editor.value.getJSON()
    const doc = editor.value.schema.nodeFromJSON(json)
    console.log('Markdown 内容：', defaultMarkdownSerializer.serialize(doc))
  }
}

const handleExportWord = async () => {
  if (editor.value) {
    const json = editor.value.getJSON()
    try {
      const blob = await exportChartToWord(json.content || [])
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = '图表文档.docx'
      link.click()
      window.URL.revokeObjectURL(url)
    } catch (error) {
      console.error('导出Word文档失败:', error)
    }
  }
}

onMounted(() => {
  editor.value = new Editor({
    extensions: [
      StarterKit,
      Chart.configure({
        onEditChart: ({ type, data }) => {
          currentChartType.value = type as 'bar' | 'line' | 'pie'
          const labels = data.labels
          const dataset = data.datasets[0]
          editingData.value = {
            labels: labels.join(', '),
            data: dataset.data.join(', '),
            datasetLabel: type === 'pie' ? '' : (dataset.label || '')
          }
          modalVisible.value = true
        }
      })
    ],
    content: '<p>这是一个支持图表的编辑器示例。你可以：</p><ul><li>点击上方按钮插入不同类型的图表</li><li>在图表之间添加文本内容</li><li>点击图表上的编辑按钮可以修改数据</li></ul><p>开始尝试吧！</p>',
    editable: true
  })
})

onBeforeUnmount(() => {
  editor.value?.destroy()
})
</script>

<style>
.ProseMirror {
  @apply min-h-[300px] p-4;
}

.ProseMirror:focus {
  @apply outline-none;
}

.ProseMirror p {
  @apply mb-2;
}

.ProseMirror ul {
  @apply list-disc pl-6 mb-4;
}

.ProseMirror li {
  @apply mb-1;
}
</style>