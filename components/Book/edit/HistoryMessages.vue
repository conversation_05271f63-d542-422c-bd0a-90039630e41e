<!-- 历史消息组件 -->
<template>
    <div v-if="taskStore.messageContainers && taskStore.messageContainers.length > 0 || 1 > 0"
        class="history-messages-container flex-1" ref="messagesContainerRef">

        <!-- 按时间顺序展示所有消息容器 -->
        <div v-for="container in taskStore.sortedMessageContainers" :key="container.id">
            <!-- 书籍状态消息 -->
            <div v-if="container.type === 'book_status'"
                class="border rounded-lg p-3 bg-blue-50 border-blue-200 text-sm">
                <div class="flex justify-between items-center mb-1">
                    <!-- 时间戳 -->
                    <span class="text-xs text-gray-500">
                        {{ formatTimestamp(container.timestamp) }}
                    </span>
                </div>
                <div class="text-gray-800 whitespace-pre-wrap break-words">
                    {{ container.content }}
                </div>
            </div>

            <!-- 章节消息组 -->
            <div v-else-if="container.type === 'chapter_group'" class="border rounded-lg overflow-hidden"
                :class="{ 'border-gray-300 bg-gray-100': container.isExpanded, 'border-gray-200': !container.isExpanded }">

                <!-- 章节标题栏 - 点击切换展开/折叠 -->
                <div @click="toggleChapterGroup(container.id)"
                    class="flex justify-between items-center p-2 cursor-pointer"
                    :class="{ 'bg-gray-200': container.isExpanded, 'bg-gray-50': !container.isExpanded }">
                    <div class="flex items-center overflow-hidden text-sm">
                        <!-- 展开/折叠箭头 -->
                        <span class="mr-2 inline-block flex-shrink-0 transition-transform duration-200"
                            :class="{ 'transform rotate-90': container.isExpanded }">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round">
                                <path d="M9 18l6-6-6-6"></path>
                            </svg>
                        </span>

                        <span class="text-gray-800 truncate">{{ container.title }}</span>

                        <!-- 任务状态指示器 -->
                        <span
                            v-if="!container.isCompleted && taskStore.currentChapterMessageGroup && taskStore.currentChapterMessageGroup.id === container.id"
                            class="ml-2 text-xs px-2 py-0.5 bg-gray-200 text-gray-800 rounded-full flex-shrink-0">
                            进行中
                        </span>
                        <span v-else-if="container.isCompleted"
                            class="ml-2 text-xs px-2 py-0.5 bg-green-100 text-green-800 rounded-full flex-shrink-0">
                            已完成
                        </span>
                    </div>

                    <!-- 时间戳 -->
                    <!-- <span class="text-xs text-gray-500 flex-shrink-0 ml-2">
                        {{ formatTimestamp(container.timestamp) }}
                    </span> -->
                </div>

                <!-- 章节消息列表，仅在展开时显示 -->
                <div v-if="container.isExpanded" class="p-2">
                    <div v-for="(message, index) in container.messages" :key="message.id"
                        class="py-1.5 text-gray-800 text-sm  break-words">
                        <!-- whitespace-pre-wrap -->
                        <div v-if="message.content.includes('。')">
                            <div v-for="(sentence, i) in processedContent(message.content)" :key="i" class="text-sm"
                                :class="{ 'font-[600]': i > 0, 'py-1.5': i > 0, 'pb-1': i == 0 }">
                                {{ sentence }}
                            </div>
                        </div>
                        <div v-else>
                            {{ message.content }}
                        </div>
                        <a-spin
                            v-if="!container.isCompleted && taskStore.isGenerating && taskStore.currentChapterMessageGroup && taskStore.currentChapterMessageGroup.id === container.id && index === container.messages.length - 1"
                            class="ml-2" size="small" />
                    </div>

                    <!-- 空消息提示 -->
                    <div v-if="container.messages.length === 0" class="py-2 text-gray-500 text-sm italic">
                        尚无消息
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div v-else class="history-messages-container bg-gray-50 flex-1">
        <!-- 无消息时显示占位内容 -->
    </div>
</template>

<script setup lang="ts">
import { nextTick, onMounted, ref, watch } from 'vue';
import { useTaskStore } from '~/stores/task';

const taskStore = useTaskStore();
const messagesContainerRef = ref<HTMLElement | null>(null);

const processedContent = (content: string) => {
    // 按句号分割，并确保每个句子都以句号结尾
    return content
        .split('。')
        .filter((sentence: string) => sentence.trim() !== '') // 过滤空内容
        .map((sentence: string, index: number, array: string[]) => {
            // 对于最后一个元素，如果原内容不是以句号结尾，不添加句号
            if (index === array.length - 1 && !content.endsWith('。')) {
                return sentence;
            }
            // 其他情况，确保每个句子都以句号结尾
            return sentence + '。';
        });
}

// 监听消息变化，自动滚动到底部
watch(
    () => [
        taskStore.messageContainers?.length,
        taskStore.messageContainers?.map(container =>
            container.type === 'chapter_group' ? container.messages?.length || 0 : 0
        ).reduce((sum, current) => sum + current, 0)
    ],
    async () => {
        await nextTick();
        scrollToBottom();
    },
    { deep: true }
);

// 滚动到底部
const scrollToBottom = () => {
    if (messagesContainerRef.value) {
        messagesContainerRef.value.scrollTop = messagesContainerRef.value.scrollHeight;
    }
};

// 切换章节消息组的展开/折叠状态
const toggleChapterGroup = (groupId: number) => {
    taskStore.toggleChapterGroupExpansion(groupId);
    // 展开后等待DOM更新再滚动
    nextTick(() => scrollToBottom());
};

// 格式化时间戳
const formatTimestamp = (timestamp: number): string => {
    const date = new Date(timestamp);
    return date.toLocaleString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
    });
};

// 组件挂载后应用Mac系统特定样式
onMounted(() => {

    if (messagesContainerRef.value) {
        // 在DOM元素挂载后直接设置样式
        messagesContainerRef.value.style.overflowY = 'scroll';
        // 使用字符串索引属性语法设置webkitOverflowScrolling
        (messagesContainerRef.value.style as any)['-webkit-overflow-scrolling'] = 'touch';
    }




});
</script>

<style scoped>
.history-messages-container {
    min-height: 200px;
    padding: 12px;
    border-radius: 6px;
    /*margin-top: 16px;*/
    /* Mac特定样式，尝试强制始终显示滚动条 */
    overflow-y: scroll !important;
    /* 强制显示滚动条 */
    -webkit-overflow-scrolling: touch;
    -webkit-appearance: none;

    /* Firefox样式 */
    scrollbar-width: thin;
    scrollbar-color: rgba(155, 155, 155, 0.5) transparent;
}

/* 强制显示Webkit滚动条 */
.history-messages-container::-webkit-scrollbar {
    width: 8px;
    height: 8px;
    -webkit-appearance: none;
    display: block !important;
}

.history-messages-container::-webkit-scrollbar-track {
    background: transparent;
    display: block !important;
}

.history-messages-container::-webkit-scrollbar-thumb {
    background-color: rgba(155, 155, 155, 0.5);
    border-radius: 4px;
    display: block !important;
}

.history-messages-container::-webkit-scrollbar-thumb:hover {
    background-color: rgba(155, 155, 155, 0.7);
}

/* 适用于Mac系统的特殊样式 */
@media screen and (-webkit-min-device-pixel-ratio:0) {
    .history-messages-container {
        overflow-y: scroll !important;
        -webkit-overflow-scrolling: touch;
    }
}

.task-run-container {
    transition: all 0.3s ease;
}

/* 添加文本溢出样式 */
.truncate {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.break-words {
    overflow-wrap: break-word;
    word-wrap: break-word;
    word-break: break-word;
    max-width: 100%;
}
</style>