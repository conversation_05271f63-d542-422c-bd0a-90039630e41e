<template>
    <div class="flex gap-2 flex-1">
        <a-button block type="primary" class="bg-[#2551B5] btn-with-icon" style="background: #2551B5;"
            :loading="taskStore.isGenerating && !taskStore.isTraversing" @click="handlePressGenerateChapter">
            生成本节点
        </a-button>
    </div>
</template>

<script setup lang="ts">
import { fetchEventSource } from '@microsoft/fetch-event-source';
import { message } from 'ant-design-vue';
import { onMounted, onUnmounted, watch } from 'vue';
// 导入编辑器事件总线
import { UserService } from '~/services/user';
import { useChapterStore } from '~/stores/chapter';
import { useTaskStore } from '~/stores/task';
import { API_CONFIG } from '~/utils/book/config';
import { BOOK_PAY_TRIGGER_TYPE } from '~/utils/constants';
import { TaskAcitonEnum } from '~/utils/enums/enums';
import { StarloveConstants } from '~/utils/starloveConstants';

const props = defineProps({
    learningCount: {
        type: Number,
        required: true
    }
})

const { $eventBus } = useNuxtApp();
// 是否正在生成中
// 添加AbortController引用
let controller: AbortController | null = null

// 初始化章节Store
const chapterStore = useChapterStore()
const taskStore = useTaskStore()

let retryCount = 0;
const maxRetries = 5; // 最大重试次数
const retryInterval = 10 * 1000; // 重连间隔时间（毫秒）

// 定义enum 两个选项 start和continue
const startFetchEventSource = async (params: any, action: TaskAcitonEnum) => {
    // console.log('开始生成章节，用户提示:', userPrompt.value)

    let url;
    if (action == TaskAcitonEnum.START) {
        url = `${API_CONFIG.getBaseUrl()}/api/sse/auto_generate_chapter/`
    } else if (action == TaskAcitonEnum.CONTINUE) {
        url = `${API_CONFIG.getBaseUrl()}/api/sse/continue_generate_chapter/`
    }

    // 如果已经在生成中，不执行操作
    if (taskStore.isGenerating) {
        console.log('已经有生成任务在进行中，忽略此次请求')
        return
    }

    taskStore.setIsGenerating(true)
    // 设置编辑器为AI创建状态，禁用用户编辑
    // 清空历史消息
    console.log('状态已更新，isGenerating=true，历史消息已初始化')
    console.log('请求参数:', params)

    // 如果有正在进行的请求，取消它
    if (controller) {
        console.log('取消之前的请求')
        controller.abort()
    }

    // 创建新的 AbortController
    controller = new AbortController()
    console.log('创建新的请求控制器')

    setTimeout(() => {
        UserService.loadUserInfoAndAssistantMemberInfo()
    }, 2000)

    try {
        console.log('开始调用SSE接口')
        // 使用 fetchEventSource 订阅 SSE 事件
        await fetchEventSource(`${url}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'token': `Bearer ${UserService.getToken()}`
            },
            body: JSON.stringify(params),
            signal: controller.signal,
            credentials: 'include',
            openWhenHidden: true,
            onmessage: (event) => {
                taskStore.processSSEMessage(event)

            },
            onclose: () => {
                console.log('SSE连接关闭', taskStore?.isGenerating)
                if (taskStore?.isGenerating) {
                    throw new Error('SSE连接关闭，准备重试')
                } else {
                    console.log('Max retries reached. Giving up.');
                    // 连接关闭时的处理
                    console.log('SSE连接已关闭')
                    taskStore.setIsGenerating(false)
                    // 确保恢复编辑器可编辑状态

                    controller = null
                }


            },

            onerror: (err) => {
                // 错误处理
                console.error('SSE 连接错误:', err)
                message.error('生成过程中发生错误，请重试')
                taskStore.setIsGenerating(false)

                // 添加错误消息
                if (chapterStore.currentChapter) {
                    taskStore.addChapterStatusMessage(
                        '生成过程中发生错误，请重试',
                        chapterStore.currentChapter.key,
                        chapterStore.currentChapter.full_title || chapterStore.currentChapter.title || '章节'
                    )
                }

                controller = null

                // 抛出错误来终止连接，防止重复重试
                throw err
            }
        })
        console.log('SSE请求完成')
    } catch (e: any) {
        console.error('SSE请求异常:', e)
        // if (e instanceof Error && e?.name !== 'AbortError') {
        //     console.error('SSE请求失败:', e?.message)
        //     // message.error('生成请求失败，请重试')
        // } else {
        //     console.log('请求被中止或其他异常')
        // }
        if (retryCount < maxRetries) {
            retryCount++;
            console.log(`${retryInterval}s后，重试第 ${retryCount} 次...`);
            setTimeout(() => {
                // 重新开始请求前，加载后台数据
                chapterStore.loadChapter(chapterStore.currentChapter?.key || '')
                taskStore.setIsGenerating(false)
                startFetchEventSource(params, action);
            }, retryInterval);
        } else {
            message.error('生成请求失败，请重试')
            retryCount = 0
            taskStore.setIsGenerating(false)
        }
    }
}

const handleSubmit = async () => {
    // 构造请求参数
    const params = {
        book_key: chapterStore.currentBook?.key,
        chapter_key: chapterStore.currentChapter?.key,
        user_prompt: chapterStore.userEditCustomPrompt,
        spaceId: chapterStore.spaceId,
        teamId: chapterStore.teamId,
        file_urls: (chapterStore.bookUploadTemplateFileList || []).map(item => item?.fileUrl),
        fileIds: (chapterStore.bookUploadTemplateFileList || []).map(item => item?.id)
    }

    startFetchEventSource(params, TaskAcitonEnum.START)

    chapterStore.userEditCustomPrompt = ''
}

const handleEventConfirmCreate = (payType: string) => {
    if (payType == BOOK_PAY_TRIGGER_TYPE.CREATE_CHAPTER) {
        handleSubmit()
    }
}

watch(() => chapterStore.currentChapter, (newValue, oldValue) => {
    // console.log("chapterStore.currentChapter newValue ==> ", newValue)
    // console.log("chapterStore.currentChapter oldValue ==> ", oldValue)

    if (newValue?.key && newValue?.is_generating) {
        const params = {
            book_key: chapterStore.currentBook?.key,
            chapter_key: newValue?.key,
        }

        startFetchEventSource(params, TaskAcitonEnum.CONTINUE)
    }
})

const handlePressGenerateChapter = () => {
    if (props.learningCount > 0) {
        message.warning('资料库中所有文件学习完成后才可以开始生成')
        return
    }
    if (taskStore.isGenerating) {
        message.warning('正在生成章节内容，请等待完成后再切换章节')
        return
    }
    if (taskStore.isTraversing) {
        message.warning('正在批量生成章节内容，请等待完成后再切换章节')
        return
    }
    chapterStore.showPayModalByType(BOOK_PAY_TRIGGER_TYPE.CREATE_CHAPTER)
}

onMounted(() => {
    console.log('ContentGenerate组件已挂载');

    $eventBus.on(StarloveConstants.keyOfEventBus.bookEditorCreateAfterConfirmPayModal, handleEventConfirmCreate);
})
// 组件销毁时清除资源
onUnmounted(() => {
    console.log('ContentGenerate组件卸载')
    // 关闭可能存在的消息通知
    message.destroy()

    // 取消进行中的请求
    if (controller) {
        console.log('取消未完成的SSE请求')
        controller.abort()
        controller = null
    }

    $eventBus.off(StarloveConstants.keyOfEventBus.bookEditorCreateAfterConfirmPayModal, handleEventConfirmCreate);
})
</script>