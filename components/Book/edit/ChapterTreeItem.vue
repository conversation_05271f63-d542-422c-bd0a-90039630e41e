<template>

  <div class="" :class="{ 'active-chapter': chapter.key === currentChapter?.key }">

    <div :class="[
      'p-2 rounded-lg cursor-pointer hover:bg-gray-100 transition-colors duration-200',
      currentChapter?.key === chapter.key
        ? 'bg-primary-100 text-primary-600 font-medium shadow-sm'
        : 'text-gray-700'
    ]" :style="{ marginLeft: `${(chapter.level - 1) * 1}rem` }" @click="$emit('select', chapter)">
      <!-- 标题显示区域 -->
      <div class="flex items-center w-full">
        <span :style="{
          fontSize: '15px',
          color: chapter.level === 4 ? '#777777' : '#333333',
          fontWeight: chapter.level === 1 ? 'bold' : 'normal',
          whiteSpace: 'nowrap',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          width: '100%',
          display: 'block'
        }">{{ chapter.full_title || chapter.title }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { PropType } from 'vue';
import type { Chapter } from '~/types/book';

defineProps({
  chapter: {
    type: Object as PropType<Chapter>,
    required: true
  },
  index: {
    type: [String, Number],
    required: true
  },
  currentChapter: {
    type: Object as PropType<Chapter | null>,
    default: null
  },
  // selectedKey: {
  //   type: String,
  //   required: false
  // }
})

defineEmits<{
  (e: 'select', chapter: Chapter): void
}>()

</script>