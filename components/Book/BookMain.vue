<template>

    <RichEditor v-if="chapterStore.currentChapter && chapterStore.isLoaded"
        v-model="chapterStore.currentChapter.content" @change="handleEditorChange" />
    <div v-else class="flex h-full w-full justify-center items-center">
        <a-spin />
    </div>

</template>

<script setup lang="ts">
import RichEditor from '@/components/Book/common/RichEditor.vue';
import { useChapterStore } from '@/stores/chapter';

import { message } from 'ant-design-vue';
import { useBookEditorStore } from '~/stores/bookEditor';

const route = useRoute()
const chapterStore = useChapterStore()
// 获取路由参数
const key = computed(() => route.params.key as string)
// 自动保存函数
const autoSave = async () => {
    const bookEditorStore = useBookEditorStore()

    bookEditorStore.isLoadingSave = true
    const result = await chapterStore.saveContent()
    if (!result) {
        message.error('自动保存失败')
    }
    bookEditorStore.isLoadingSave = false
}

// 处理章节内容更新
const handleEditorChange = async (content: any) => {
    // console.log('handleEditorChange', content)
    autoSave()
}

// 页面加载时获取数据
onMounted(() => {
    console.log('Book Main onMounted', chapterStore?.currentChapter?.content)
})

// // 监听路由参数变化
// watch(() => key.value, (newKey) => {
//     if (newKey) {
//         console.log('chapter detail page watch key. got new key. will load chapter', newKey)
        
//     }
// }, { immediate: true })

</script>

<style scoped></style>