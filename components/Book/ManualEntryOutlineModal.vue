<template>
    <!---团队邀请，登录后的弹窗-->
    <Modal v-model="isOpen" :width="900" title="手动输入大纲" @confirm="handleConfirm">
        <div class="border-gray-200 p-4">
            <div class="max-w-4xl mx-auto">
                <div
                    class="bg-white border-blue-300/50 rounded-xl p-1 sm:p-2 pt-0 border transition-all group  mb-1 focus-within:shadow-[0_0_15px_rgba(59,130,246,0.3)] focus-within:border-blue-400 relative before:absolute before:inset-0 before:rounded-xl before:transition-all before:opacity-0 focus-within:before:opacity-100 before:bg-gradient-to-r before:from-blue-500/5 before:to-blue-400/5 before:-z-10">
                    <textarea v-model="outlineTextValue" :auto-size="{ minRows: 3, maxRows: 2 }"
                        placeholder="请在此处输入或粘贴大纲内容，我们将会对大纲结果进行智能格式优化与整理（支持markdown格式）"
                        class="w-full min-h-[280px] rounded-lg p-2 sm:p-3 resize-none outline-none focus:ring-0 focus:border-transparent text-sm sm:text-base bg-transparent"
                        :maxlength="maxLength" @keydown="handleKeyDown" @paste="handlePaste" />
                </div>
            </div>
        </div>
    </Modal>
</template>
<script setup lang="ts">
import Modal from '@/components/Common/Modal.vue';
import { computed } from 'vue';

const maxLength = 200
interface Props {
    modelValue: boolean,
    outlineText: string
}
const props = withDefaults(defineProps<Props>(), {
    modelValue: false,
    question: ''
})

const emit = defineEmits(['update:modelValue', 'action', 'update:outlineText'])
const outlineTextValue = computed({
    get: () => props.outlineText,
    set: (val) => {
        // console.log(val, 'val')
        emit('update:outlineText', val)
    }
})
const isOpen = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
})

const handleKeyDown = (event: KeyboardEvent) => {

}
const pasteFileInfo = ref()
const handlePaste = (event: ClipboardEvent) => {
}
const handleConfirm = () => {
    isOpen.value = false
    emit('action')
}

</script>