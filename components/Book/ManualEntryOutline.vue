<template>
    <div class="w-full mt-6 px-4 flex justify-between items-center">
        <div class="text-gray-500 text-sm">操作说明：点+ 新增同级和新增子级，最多支持输入三级子标题</div>
        <button
            class="px-2 py-1.5 text-sm text-blue-600 bg-blue-50 hover:bg-blue-100 rounded-md border border-blue-200 transition-colors"
            @click="handleManualEntryOutlineModal">手动输入大纲 </button>
    </div>
    <ManualEntryOutlineModal v-model="isModalOpen" v-model:outline-text="props.outlineText" @action="handleAction">
    </ManualEntryOutlineModal>
</template>
<script setup lang="ts">
import { ref } from 'vue';
import ManualEntryOutlineModal from './ManualEntryOutlineModal.vue';

const props = withDefaults(defineProps<{
    outlineText: string
}>(), {
    outlineText: ''
})

const isModalOpen = ref(false)
const handleManualEntryOutlineModal = () => {
    isModalOpen.value = true
}
const handleAction = () => {
    // Handle modal action here
}
</script>