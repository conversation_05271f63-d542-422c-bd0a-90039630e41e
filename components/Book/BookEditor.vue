<template>
  <div class="h-full flex flex-col ">
    <!-- 工具栏 -->
    <div class="bg-white border-b p-4 ">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <UButton color="primary" variant="soft" icon="i-heroicons-arrow-left-20-solid" @click="$router.back()">
            返回
          </UButton>
          <h2 class="text-lg font-medium text-gray-900">{{ title }}</h2>
        </div>
        <div class="flex items-center space-x-2">
          <UButton color="gray" variant="soft" icon="i-heroicons-document-20-solid">
            保存草稿
          </UButton>
          <UButton color="primary" icon="i-heroicons-check-20-solid">
            发布
          </UButton>
        </div>
      </div>

      <!-- 编辑工具栏 -->
      <div class="mt-4 flex items-center space-x-4 border-t pt-4">
        <div class="flex items-center space-x-2">
          <UButton color="gray" variant="soft" size="sm" icon="i-heroicons-text-size-20-solid">
            标题
          </UButton>
          <UButton color="gray" variant="soft" size="sm" icon="i-heroicons-bold-20-solid">
            加粗
          </UButton>
          <UButton color="gray" variant="soft" size="sm" icon="i-heroicons-italic-20-solid">
            斜体
          </UButton>
        </div>

        <div class="h-6 w-px bg-gray-200"></div>

        <div class="flex items-center space-x-2">
          <UButton color="gray" variant="soft" size="sm" icon="i-heroicons-list-bullet-20-solid">
            无序列表
          </UButton>
          <UButton color="gray" variant="soft" size="sm" icon="i-heroicons-list-ordered-20-solid">
            有序列表
          </UButton>
        </div>

        <div class="h-6 w-px bg-gray-200"></div>

        <div class="flex items-center space-x-2">
          <UButton color="gray" variant="soft" size="sm" icon="i-heroicons-photo-20-solid">
            插入图片
          </UButton>
          <UButton color="gray" variant="soft" size="sm" icon="i-heroicons-table-cells-20-solid">
            插入表格
          </UButton>
        </div>
      </div>
    </div>

    <!-- 主编辑区域 -->
    <div class="flex-1 flex">
      <!-- 编辑器 -->
      <div class="w-3/5 border-r">
        <div class="h-full p-6">
          <div class="h-full flex flex-col">
            <!-- 章节导航 -->
            <div class="mb-4">
              <select v-model="currentChapter"
                class="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500">
                <option v-for="chapter in chapters" :key="chapter.id" :value="chapter.id">
                  {{ chapter.title }}
                </option>
              </select>
            </div>

            <!-- 编辑区域 -->
            <div class="flex-1">
              <textarea v-model="content"
                class="w-full h-full p-4 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                placeholder="开始写作..."></textarea>
            </div>

            <!-- 字数统计 -->
            <div class="mt-2 text-sm text-gray-500">
              字数：{{ wordCount }} | 预计阅读时间：{{ readingTime }}分钟
            </div>
          </div>
        </div>
      </div>

      <!-- AI助手面板 -->
      <div class="w-2/5 bg-gray-50">
        <div class="h-full flex flex-col">
          <div class="p-4 border-b bg-white">
            <h3 class="text-lg font-medium text-gray-900">AI写作助手</h3>
            <!-- AI功能选项 -->
            <div class="mt-2 flex flex-wrap gap-2">
              <UBadge v-for="feature in aiFeatures" :key="feature.id" :color="feature.color" variant="soft"
                class="cursor-pointer" @click="handleAIFeature(feature)">
                {{ feature.name }}
              </UBadge>
            </div>
          </div>

          <!-- AI对话区域 -->
          <div class="flex-1 overflow-auto p-4">
            <div class="space-y-4">
              <div v-for="(message, index) in aiMessages" :key="index" :class="[
                'p-4 rounded-lg',
                message.role === 'ai' ? 'bg-white' : 'bg-primary-50'
              ]">
                <div class="flex items-start">
                  <div class="flex-shrink-0">
                    <div :class="[
                      'w-8 h-8 rounded-full flex items-center justify-center',
                      message.role === 'ai' ? 'bg-primary-100 text-primary-600' : 'bg-primary-600 text-white'
                    ]">
                      {{ message.role === 'ai' ? 'AI' : '我' }}
                    </div>
                  </div>
                  <div class="ml-3">
                    <p class="text-sm text-gray-900">{{ message.content }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- AI输入区域 -->
          <div class="p-4 border-t bg-white">
            <div class="flex space-x-2">
              <UInput v-model="aiInput" class="flex-1" placeholder="向AI助手提问或请求帮助..." @keyup.enter="handleAskAI" />
              <UButton color="primary" @click="handleAskAI">
                发送
              </UButton>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';

const props = defineProps<{
  title?: string
}>()

// 编辑器状态
const content = ref('')
const currentChapter = ref(1)

// 章节数据
const chapters = ref([
  { id: 1, title: '第一章：引言' },
  { id: 2, title: '第二章：文献综述' },
  { id: 3, title: '第三章：研究方法' },
  { id: 4, title: '第四章：研究结果' },
  { id: 5, title: '第五章：讨论' },
  { id: 6, title: '第六章：结论' }
])

// AI功能
const aiFeatures = [
  { id: 1, name: '完善段落', color: 'primary' as const },
  { id: 2, name: '优化表达', color: 'blue' as const },
  { id: 3, name: '语法检查', color: 'yellow' as const },
  { id: 4, name: '学术润色', color: 'indigo' as const },
  { id: 5, name: '扩展内容', color: 'red' as const },
  { id: 6, name: '生成摘要', color: 'green' as const }
]

// AI对话
const aiInput = ref('')
const aiMessages = ref([
  {
    role: 'ai',
    content: '你好！我是你的AI写作助手。我可以帮你：\n1. 优化文章结构\n2. 改进学术表达\n3. 完善论述内容\n4. 检查语法错误\n5. 生成文献引用\n请告诉我你需要什么帮助？'
  }
])

// 计算属性
const wordCount = computed(() => {
  return content.value.length
})

const readingTime = computed(() => {
  return Math.ceil(wordCount.value / 500) // 假设阅读速度为每分钟500字
})

// 方法
const handleAskAI = () => {
  if (!aiInput.value.trim()) return

  // 添加用户消息
  aiMessages.value.push({
    role: 'user',
    content: aiInput.value
  })

  // TODO: 调用AI API获取响应
  // 这里模拟AI响应
  setTimeout(() => {
    aiMessages.value.push({
      role: 'ai',
      content: '我理解你的问题，让我来帮你分析一下...'
    })
  }, 1000)

  aiInput.value = ''
}

const handleAIFeature = (feature: any) => {
  // TODO: 处理AI功能
  console.log('使用AI功能:', feature.name)
}
</script>