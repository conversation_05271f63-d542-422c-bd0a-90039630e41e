<template>
  <a-popover :disabled="isCodeViewMode" placement="bottom" trigger="click" :overlay-class-name="'tiptap-popper'"
    ref="popoverRef">
    <template #content>
      <div class="tiptap-popper__menu">
        <div class="tiptap-popper__menu__item">
          <create-table-popover @createTable="createTable" />
        </div>

        <div class="tiptap-popper__menu__item__separator" />

        <div :class="{ 'tiptap-popper__menu__item--disabled': !isTableActiveState }" class="tiptap-popper__menu__item"
          @mousedown="hidePopover" @click="editor.commands.addColumnBefore">
          <span>向左插入一列</span>
        </div>

        <div :class="{ 'tiptap-popper__menu__item--disabled': !isTableActiveState }" class="tiptap-popper__menu__item"
          @mousedown="hidePopover" @click="editor.commands.addColumnAfter">
          <span>向右插入一列</span>
        </div>

        <div :class="{ 'tiptap-popper__menu__item--disabled': !isTableActiveState }" class="tiptap-popper__menu__item"
          @mousedown="hidePopover" @click="editor.commands.deleteColumn">
          <span>删除列</span>
        </div>

        <div class="tiptap-popper__menu__item__separator" />

        <div :class="{ 'tiptap-popper__menu__item--disabled': !isTableActiveState }" class="tiptap-popper__menu__item"
          @mousedown="hidePopover" @click="editor.commands.addRowBefore">
          <span>向上插入一行</span>
        </div>

        <div :class="{ 'tiptap-popper__menu__item--disabled': !isTableActiveState }" class="tiptap-popper__menu__item"
          @mousedown="hidePopover" @click="editor.commands.addRowAfter">
          <span>向下插入一行</span>
        </div>

        <div :class="{ 'tiptap-popper__menu__item--disabled': !isTableActiveState }" class="tiptap-popper__menu__item"
          @mousedown="hidePopover" @click="editor.commands.deleteRow">
          <span>删除行</span>
        </div>

        <div class="tiptap-popper__menu__item__separator" />

        <div :class="{ 'tiptap-popper__menu__item--disabled': !enableMergeCellsState }"
          class="tiptap-popper__menu__item" @mousedown="hidePopover" @click="editor.commands.mergeCells">
          <span>合并单元格</span>
        </div>

        <div :class="{ 'tiptap-popper__menu__item--disabled': !enableSplitCellState }" class="tiptap-popper__menu__item"
          @mousedown="hidePopover" @click="editor.commands.splitCell">
          <span>拆分单元格</span>
        </div>

        <div class="tiptap-popper__menu__item__separator" />

        <div :class="{ 'tiptap-popper__menu__item--disabled': !isTableActiveState }" class="tiptap-popper__menu__item"
          @mousedown="hidePopover" @click="editor.commands.deleteTable">
          <span>删除表格</span>
        </div>
      </div>
    </template>

    <command-button :is-active="isTableActiveState" :enable-tooltip="enableTooltip" tooltip="表格"
      :readonly="isCodeViewMode" icon="table" />

  </a-popover>
</template>

<script lang="ts" setup>
import { enableMergeCells, enableSplitCell, isTableActive } from '@/utils/table'
import { Editor } from '@tiptap/vue-3'
import { message } from 'ant-design-vue'
import { computed, ref } from 'vue'
import { checkEditorFocus } from '~/utils/editor'
import CommandButton from './CommandButton.vue'
import CreateTablePopover from './CreateTablePopover.vue'

interface Props {
  editor: Editor
}

const props = defineProps<Props>()

const enableTooltip = ref(true)
const isCodeViewMode = ref(false)
const popoverRef = ref()

const hidePopover = () => {
  popoverRef.value?.hide()
}

const isTableActiveState = computed((): boolean => {
  return isTableActive(props.editor.state)
})

const enableMergeCellsState = computed((): boolean => {
  return enableMergeCells(props.editor.state)
})

const enableSplitCellState = computed((): boolean => {
  return enableSplitCell(props.editor.state)
})

const createTable = (data: { row: number; col: number }): void => {
  if (!checkEditorFocus(props.editor)) {
    message.warning('请选择插入位置')
    return
  }
  // 有焦点时在当前位置插入
  props.editor.commands.insertTable({
    rows: data.row,
    cols: data.col,
    withHeaderRow: true
  });

  // 在每个单元格中插入一个空格，确保后续输入正常
  const totalCells = data.row * data.col;
  for (let i = 0; i < totalCells; i++) {
    // 当前单元格插入空格
    props.editor.commands.insertContent(' ');
    // 如果不是最后一个单元格，则移动到下一个单元格
    if (i < totalCells - 1) {
      props.editor.commands.goToNextCell();
    }
  }

  hidePopover()
}
</script>

<style lang="scss" scoped>
.tiptap-popper {
  &__menu {

    &__item {
      padding: 5px 12px;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        background: #f5f5f5;
      }

      &--disabled {
        color: rgba(0, 0, 0, 0.25);
        cursor: not-allowed;
        pointer-events: none;
      }
    }

    &__item__separator {
      height: 1px;
      margin: 4px 0;
      background: #f0f0f0;
    }
  }
}
</style>
