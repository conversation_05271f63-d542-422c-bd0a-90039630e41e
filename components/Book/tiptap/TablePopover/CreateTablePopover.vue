<template>
  <a-popover ref="popoverRef" v-model:visible="popoverVisible" placement="right" trigger="hover"
    :overlay-class-name="'tiptap-popper'" @afterLeave="resetTableGridSize">
    <template #content>
      <div class="table-grid-size-editor">
        <div class="table-grid-size-editor__body">
          <div v-for="row in tableGridSize.row" :key="'r' + row" class="table-grid-size-editor__row">
            <div v-for="col in tableGridSize.col" :key="'c' + col" :class="{
              'table-grid-size-editor__cell--selected':
                col <= selectedTableGridSize.col && row <= selectedTableGridSize.row
            }" class="table-grid-size-editor__cell" @mouseover="selectTableGridSize(row, col)"
              @mousedown="confirmCreateTable(row, col)">
              <div class="table-grid-size-editor__cell__inner" />
            </div>
          </div>
        </div>

        <div class="table-grid-size-editor__footer">
          {{ selectedTableGridSize.row }} X {{ selectedTableGridSize.col }}
        </div>
      </div>
    </template>
    <div>插入表格</div>
  </a-popover>
</template>

<script lang="ts" setup>
import { reactive, ref } from 'vue'

const INIT_GRID_SIZE = 5
const MAX_GRID_SIZE = 10
const DEFAULT_SELECTED_GRID_SIZE = 2

interface GridSize {
  row: number
  col: number
}


const emit = defineEmits(['createTable']);

const popoverRef = ref()
const popoverVisible = ref(false)

const tableGridSize = reactive<GridSize>({
  row: INIT_GRID_SIZE,
  col: INIT_GRID_SIZE
})

const selectedTableGridSize = reactive<GridSize>({
  row: DEFAULT_SELECTED_GRID_SIZE,
  col: DEFAULT_SELECTED_GRID_SIZE
})

const confirmCreateTable = (row: number, col: number) => {
  emit('createTable', { row, col })
  popoverRef.value?.hide()
}

const selectTableGridSize = (row: number, col: number): void => {
  if (row === tableGridSize.row) {
    tableGridSize.row = Math.min(row + 1, MAX_GRID_SIZE)
  }

  if (col === tableGridSize.col) {
    tableGridSize.col = Math.min(col + 1, MAX_GRID_SIZE)
  }

  selectedTableGridSize.row = row
  selectedTableGridSize.col = col
}

const resetTableGridSize = () => {
  tableGridSize.row = INIT_GRID_SIZE
  tableGridSize.col = INIT_GRID_SIZE
  selectedTableGridSize.row = DEFAULT_SELECTED_GRID_SIZE
  selectedTableGridSize.col = DEFAULT_SELECTED_GRID_SIZE
}
</script>

<style lang="scss" scoped>
.table-grid-size-editor {


  &__body {
    display: flex;
    flex-direction: column;
    gap: 2px;
  }

  &__row {
    display: flex;
    gap: 2px;
  }

  &__cell {
    width: 20px;
    height: 20px;
    border: 1px solid #d9d9d9;
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      border-color: #1890ff;
    }

    &--selected {
      background: #e6f7ff;
      border-color: #1890ff;
    }

    &__inner {
      width: 100%;
      height: 100%;
    }
  }

  &__footer {
    margin-top: 8px;
    text-align: center;
    color: rgba(0, 0, 0, 0.65);
  }
}
</style>
