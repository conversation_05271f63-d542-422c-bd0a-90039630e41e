<template>
    <a-tooltip :title="tooltip" :mouseEnterDelay="0.35" :disabled="!enableTooltip || readonly" placement="top">
        <span @mousedown.prevent @click="onClick" class="command-button" :class="{
            'command-button--active': isActive,
            'command-button--readonly': readonly
        }">
            <Iconfont name="biaoge1" style="line-height: 13px; font-size: 13px"></Iconfont>
        </span>
    </a-tooltip>
</template>

<script lang="ts" setup>
import Iconfont from '@/components/Iconfont.vue';

interface Props {
    icon: string
    isActive: boolean
    tooltip: string
    enableTooltip: boolean
    readonly: boolean
}

const props = withDefaults(defineProps<Props>(), {
    isActive: false,
    readonly: false
})

const onClick = () => {
    if (!props.readonly) {
        // 这里可以添加具体的命令处理逻辑
        console.log('Command button clicked')
    }
}
</script>

<style lang="scss" scoped>
.command-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0 5px;
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
        color: #1890ff;
    }

    &--active {
        color: #1890ff;
    }

    &--readonly {
        color: rgba(0, 0, 0, 0.25);
        cursor: not-allowed;
        pointer-events: none;
    }
}
</style>