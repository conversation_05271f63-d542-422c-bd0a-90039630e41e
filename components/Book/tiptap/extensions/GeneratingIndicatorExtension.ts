import { Extension } from '@tiptap/core'
// @ts-ignore
import { Plugin, Plugin<PERSON>ey } from 'prosemirror-state'
// @ts-ignore
import { Decoration, DecorationSet } from 'prosemirror-view'
import { useTaskStore } from '~/stores/task'
// @ts-ignore
import { createVNode, render } from 'vue'
// @ts-ignore
import { Spin } from 'ant-design-vue'
// @ts-ignore
import { LoadingOutlined } from '@ant-design/icons-vue'

export interface GeneratingIndicatorOptions {
    isVisible: boolean
}

export const GeneratingIndicator = Extension.create<GeneratingIndicatorOptions>({
    name: 'generatingIndicator',

    addOptions() {
        return {
            isVisible: false,
        }
    },

    addProseMirrorPlugins() {
        const pluginKey = new PluginKey('generatingIndicator')
        const taskStore = useTaskStore()

        return [
            new Plugin({
                key: pluginKey,
                props: {
                    decorations: (state: any) => {

                        // console.log("taskStore.autoCreatingIndicatorVisible ==>", taskStore.autoCreatingIndicatorVisible)

                        if (!taskStore.autoCreatingIndicatorVisible) {
                            return DecorationSet.empty
                        }

                        const { doc } = state
                        const decorations: any[] = []

                        // 在文档末尾添加装饰
                        const pos = doc.content.size

                        // 创建一个widget来显示指示器
                        const decoration = Decoration.widget(pos, () => {
                            // 创建指示器元素
                            const container = document.createElement('div')
                            container.className = 'generating-indicator-widget'

                            const innerContainer = document.createElement('div')
                            innerContainer.className = 'flex items-center'
                            container.appendChild(innerContainer)

                            // 创建挂载Ant Design Vue Spin组件的容器
                            const spinContainer = document.createElement('div')
                            spinContainer.className = 'antd-spin-container'
                            innerContainer.appendChild(spinContainer)

                            // 使用Vue的render API渲染Ant Design Vue的Spin组件
                            const spinIndicator = createVNode(LoadingOutlined, {
                                style: 'font-size: 20px; color: #2563eb;'
                            })

                            const spinComponent = createVNode(Spin, {
                                indicator: spinIndicator,
                                spinning: true
                            })

                            // 将Vue组件渲染到DOM容器中
                            render(spinComponent, spinContainer)

                            // 添加文字标签
                            const tag = document.createElement('span')
                            tag.className = 'ml-3 text-blue-600 font-medium text-sm'

                            // 随机选择一个文字
                            const loadingTexts = [
                                '灵感迸发中…',
                                '奋笔疾书中…',
                                '脑洞大开中…',
                                '憋大招中…',
                                '妙笔生花ing',
                                '奇思妙想ing',
                                '创意无穷ing',
                                '文思泉涌ing',
                                '文思如尿崩、谁与我争锋',
                                '小in已成精，写作不能停',
                                '码字好累，你能懂吗',
                                '文字大坝泄洪，滔滔江水连绵不绝',
                            ]
                            const randomText = loadingTexts[Math.floor(Math.random() * loadingTexts.length)]
                            tag.textContent = randomText

                            innerContainer.appendChild(tag)

                            return container
                        }, {
                            // 确保我们的装饰显示在内容后面
                            side: 1,
                            // 确保它不会被其他东西选中或交互
                            ignoreSelection: true,
                        })

                        decorations.push(decoration)

                        return DecorationSet.create(doc, decorations)
                    },
                },
            }),
        ]
    },
}) 