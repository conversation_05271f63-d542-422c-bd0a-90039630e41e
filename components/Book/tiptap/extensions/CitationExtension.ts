import { Node, mergeAttributes } from '@tiptap/core'
import { VueNodeViewRenderer } from '@tiptap/vue-3'
import CitationComponent from '../CitationComponent.vue'

export interface CitationOptions {
    HTMLAttributes: Record<string, any>
    onEditCitation?: (props: { id?: string | number }) => void
    onClickCitation?: (props: { id?: string | number }) => void
    getDisplayNumber?: (props: { id?: string | number }) => string | number
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        citation: {
            setCitation: (options: { id?: string | number }) => ReturnType
            updateCitation: (options: { id?: string | number }) => ReturnType
        }
    }
}

export const Citation = Node.create<CitationOptions>({
    name: 'citation',

    // 设置为行内节点，可以在文本中插入
    inline: true,
    group: 'inline',

    // 原子节点，意味着不可编辑内部内容
    atom: true,

    addOptions() {
        return {
            HTMLAttributes: {},
            onEditCitation: undefined,
            onClickCitation: undefined,
            getDisplayNumber: undefined,
        }
    },

    addAttributes() {
        return {
            'paper-no': {
                default: null,
                parseHTML: element => element.getAttribute('paper-no'),
                renderHTML: attrs => {
                    if (!attrs['paper-no']) return {}
                    return { 'paper-no': attrs['paper-no'] }
                }
            },
            'opinion-id': {
                default: null,
                parseHTML: element => element.getAttribute('opinion-id'),
                renderHTML: attrs => {
                    if (!attrs['opinion-id']) return {}
                    return { 'opinion-id': attrs['opinion-id'] }
                }
            }
        }
    },

    parseHTML() {
        return [
            {
                tag: 'citation',
                priority: 51, // 设置较高的优先级
                getAttrs: (node) => {
                    if (typeof node === 'string') return false
                    const element = node as HTMLElement
                    const paperNo = element.getAttribute('paper-no')
                    const opinionId = element.getAttribute('opinion-id')

                    // 如果没有必要的属性，返回 false 表示不解析这个节点
                    if (!paperNo) return false

                    return {
                        'paper-no': paperNo,
                        'opinion-id': opinionId
                    }
                }
            }
        ]
    },

    renderHTML({ HTMLAttributes }) {
        const attrs: Record<string, string | null> = {}

        if (HTMLAttributes['paper-no']) {
            attrs['paper-no'] = HTMLAttributes['paper-no']
        }

        if (HTMLAttributes['opinion-id']) {
            attrs['opinion-id'] = HTMLAttributes['opinion-id']
        }

        return ['citation', mergeAttributes(this.options.HTMLAttributes, attrs)]
    },

    addCommands() {
        return {
            setCitation:
                (options) => ({ tr, dispatch }) => {
                    if (dispatch) {
                        const node = this.type.create({
                            'id': options.id
                        })
                        tr.replaceSelectionWith(node)
                    }
                    return true
                },
            updateCitation:
                (options) => ({ chain }) => {
                    return chain()
                        .updateAttributes(this.name, {
                            'id': options.id
                        })
                        .run()
                },
        }
    },

    addNodeView() {
        return VueNodeViewRenderer(CitationComponent)
    },
}) 