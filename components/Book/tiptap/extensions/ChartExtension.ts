import { Node, mergeAttributes } from '@tiptap/core'
import { VueNodeViewRenderer } from '@tiptap/vue-3'
import ChartComponent from '../ChartComponent.vue'

export interface ChartOptions {
  HTMLAttributes: Record<string, any>
  onEditChart?: (props: { type: string, data: any }) => void
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    chart: {
      setChart: (options: { type: string, data: any }) => ReturnType
      updateChart: (options: { type: string, data: any }) => ReturnType
    }
  }
}

export const Chart = Node.create<ChartOptions>({
  name: 'chart',

  group: 'block',

  atom: true,

  addOptions() {
    return {
      HTMLAttributes: {},
      onEditChart: undefined,
    }
  },

  addAttributes() {
    return {
      type: {
        default: 'line',
        parseHTML: element => element.getAttribute('data-type') || 'line',
        renderHTML: attributes => {
          return { 'data-type': attributes.type }
        },
      },
      data: {
        default: null,
        parseHTML: element => {
          const dataAttr = element.getAttribute('data-chart')
          if (dataAttr) {
            try {
              return JSON.parse(dataAttr)
            } catch (e) {
              return null
            }
          }
          return null
        },
        renderHTML: attributes => {
          return { 'data-chart': JSON.stringify(attributes.data) }
        },
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: 'chart-component',
      },
      {
        tag: 'div[data-type][data-chart]',
        priority: 51, // 确保优先级高于默认解析器
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    const attrs = mergeAttributes(
      this.options.HTMLAttributes,
      {
        'data-type': HTMLAttributes.type,
        'data-chart': JSONStringify(HTMLAttributes.data),
      },
      HTMLAttributes
    )
    return ['div', attrs, ['chart-component']]
  },

  addCommands() {
    return {
      setChart:
        (options) => ({ commands }) => {
          return commands.insertContent({
            type: this.name,
            attrs: options,
          })
        },
      updateChart:
        (options) => ({ commands }) => {
          return commands.updateAttributes(this.name, options)
        },
    }
  },

  addNodeView() {
    return VueNodeViewRenderer(ChartComponent)
  },
})

// 安全的 JSON.stringify，避免循环引用
function JSONStringify(obj: any): string {
  try {
    return JSON.stringify(obj)
  } catch (e) {
    return '{}'
  }
} 