import { Extension } from '@tiptap/core'
import { Plugin, PluginKey } from '@tiptap/pm/state'

export interface AISelectionMenuOptions {
    pluginKey?: PluginKey
    // 回调函数，用于处理AI编辑动作
    onPolish?: (selectedText: string) => void
    onExpand?: (selectedText: string) => void
    onShorten?: (selectedText: string) => void
    onSimpleAction?: (selectedText: string, prompt: string) => void
    onInsertContent?: (content: string, at: 'replace' | 'insert') => void
}

export const AISelectionMenu = Extension.create<AISelectionMenuOptions>({
    name: 'aiSelectionMenu',

    addOptions() {
        return {
            pluginKey: new PluginKey('aiSelectionMenu'),
            onPolish: () => { },
            onExpand: () => { },
            onShorten: () => { },
            onSimpleAction: () => { },
            onInsertContent: () => { },
        }
    },

    addProseMirrorPlugins() {
        return [
            new Plugin({
                key: this.options.pluginKey,
                view: () => {
                    // 用于检查选区是否包含特殊节点的函数
                    const containsSpecialNodes = (state: any, selection: any) => {
                        // 防御性检查：确保state和selection存在
                        if (!state || !selection) return false

                        const { from, to } = selection
                        // 防御性检查：确保from和to是有效值
                        if (from === undefined || to === undefined || from >= state.doc.content.size || to <= 0) return false

                        let hasSpecialNode = false

                        try {
                            // 遍历选区内的所有节点
                            state.doc.nodesBetween(from, to, (node: any) => {
                                // 防御性检查：确保node和node.type存在
                                if (!node || !node.type || !node.type.name) return true

                                // 检查节点类型是否为特殊类型（图片、表格、公式、图表等）
                                if (
                                    node.type.name === 'image' ||
                                    node.type.name === 'table' ||
                                    node.type.name === 'tableRow' ||
                                    node.type.name === 'tableCell' ||
                                    node.type.name === 'tableHeader' ||
                                    node.type.name === 'mathInline' ||
                                    node.type.name === 'mathBlock' ||
                                    node.type.name === 'chart'
                                ) {
                                    hasSpecialNode = true
                                    return false // 停止遍历
                                }
                                return true // 继续遍历
                            })
                        } catch (err) {
                            console.log('Error in containsSpecialNodes:', err)
                            return false
                        }

                        return hasSpecialNode
                    }

                    // 安全地获取位置坐标
                    const safeGetCoords = (view: any, pos: number) => {
                        if (!view || typeof pos !== 'number') {
                            return null
                        }

                        try {
                            // 确保位置在有效范围内
                            if (pos < 0 || (view.state && view.state.doc && pos > view.state.doc.content.size)) {
                                return null
                            }

                            // 安全调用coordsAtPos - 注意这返回的是DOMRectReadOnly对象
                            if (view.coordsAtPos) {
                                try {
                                    const rect = view.coordsAtPos(pos)
                                    // 返回一个新对象，而不是修改DOMRectReadOnly
                                    return {
                                        left: rect.left,
                                        right: rect.right,
                                        top: rect.top,
                                        bottom: rect.bottom
                                    }
                                } catch (coordsError) {
                                    console.warn(`Error in coordsAtPos at position ${pos}:`, coordsError)
                                    // 尝试退一步处理
                                    if (pos > 0) {
                                        try {
                                            const rect = view.coordsAtPos(pos - 1)
                                            return {
                                                left: rect.left,
                                                right: rect.right,
                                                top: rect.top,
                                                bottom: rect.bottom
                                            }
                                        } catch (fallbackError) {
                                            console.warn('Fallback position also failed:', fallbackError)
                                        }
                                    }
                                    return null
                                }
                            }

                            // 如果coordsAtPos不可用，返回一个默认值用于调试
                            console.warn('coordsAtPos method not available, using fallback')
                            return {
                                left: 0,
                                right: 0,
                                top: 0,
                                bottom: 0
                            }
                        } catch (err) {
                            console.error(`Error getting coordinates at position ${pos}:`, err)
                            return null
                        }
                    }

                    return {
                        update: (view: { coordsAtPos?: any; state?: any; editable?: any; domFromPos?: any }, prevState: any) => {
                            try {
                                // 基本检查：确保view和state存在
                                if (!view || !view.state) {
                                    return true
                                }

                                const { state, editable } = view
                                // 防御性检查：确保selection存在
                                if (!state.selection) {
                                    return true
                                }

                                const { selection } = state

                                // 检查编辑器是否可编辑，如果不可编辑，直接返回，不显示菜单
                                if (!editable) {
                                    // 发送清除事件，确保菜单不会显示
                                    const event = new CustomEvent('ai-selection-clear')
                                    document.dispatchEvent(event)
                                    return true
                                }

                                // 当前选区是否为空
                                const isSelectionEmpty = selection.empty

                                // 触发自定义事件，用于在Vue组件中处理选区变化
                                // AISelectionMenuBubble组件将监听这些事件
                                if (!isSelectionEmpty) {
                                    try {
                                        // 防御性检查：确保from和to是有效值
                                        if (selection.from === undefined || selection.to === undefined ||
                                            selection.from < 0 || selection.to > state.doc.content.size) {
                                            return true
                                        }

                                        // 获取选中的文本
                                        const selectedText = state.doc.textBetween(
                                            selection.from,
                                            selection.to,
                                            ' '
                                        )

                                        // 检查选中区域是否包含特殊节点（图片、表格、公式、图表等）
                                        const hasSpecialNodes = containsSpecialNodes(state, selection)

                                        // 如果包含特殊节点，不显示AI编辑菜单
                                        if (hasSpecialNodes) {
                                            const event = new CustomEvent('ai-selection-clear')
                                            document.dispatchEvent(event)
                                            return true
                                        }

                                        // 获取选区的位置信息
                                        const { from, to } = selection

                                        // 使用安全方法获取坐标
                                        const start = safeGetCoords(view, from)
                                        const end = safeGetCoords(view, to)

                                        // 如果完全无法获取坐标，就不显示菜单
                                        if (!start && !end) {
                                            console.warn('Could not safely get selection coordinates')
                                            const event = new CustomEvent('ai-selection-clear')
                                            document.dispatchEvent(event)
                                            return true
                                        }

                                        // 使用start或end或默认值，这里所有对象都已经是可写的
                                        const startPos = start || end || { left: 0, top: 0 }
                                        const endPos = end || start || { right: 0, bottom: 0 }

                                        // 构建选区位置信息对象
                                        const selectionPosition = {
                                            left: startPos.left || 0,
                                            top: startPos.top || 0,
                                            right: endPos.right || startPos.left + 100, // 假设一个宽度
                                            bottom: endPos.bottom || startPos.top + 20, // 假设一个高度
                                            width: (endPos.right || 0) - (startPos.left || 0) || 100,
                                            height: (endPos.bottom || 0) - (startPos.top || 0) || 20
                                        }

                                        // 分发自定义事件，传递选区信息和选中文本
                                        const event = new CustomEvent('ai-selection-update', {
                                            detail: {
                                                selectedText,
                                                selectionPosition,
                                                from,
                                                to
                                            }
                                        })
                                        document.dispatchEvent(event)
                                    } catch (err) {
                                        console.log('Error processing selection:', err)
                                        // 发生错误时，清除菜单
                                        const event = new CustomEvent('ai-selection-clear')
                                        document.dispatchEvent(event)
                                    }
                                } else {
                                    // 选区为空时，发送清除事件
                                    const event = new CustomEvent('ai-selection-clear')
                                    document.dispatchEvent(event)
                                }
                            } catch (error) {
                                console.log('Unexpected error in AISelectionMenu update:', error)
                                // 发生任何意外错误时，清除菜单
                                try {
                                    const event = new CustomEvent('ai-selection-clear')
                                    document.dispatchEvent(event)
                                } catch (e) {
                                    // 即使事件分发失败也不抛出错误
                                }
                            }

                            return true
                        },
                        destroy: () => {
                            // 清理事件监听
                            try {
                                const event = new CustomEvent('ai-selection-destroy')
                                document.dispatchEvent(event)
                            } catch (e) {
                                console.log('Error in destroy handler:', e)
                            }
                        }
                    }
                }
            })
        ]
    }
}) 