.image-uploading {
  position: relative;
  display: inline-block;
}

.image-uploading img {
  filter: blur(5px);
  opacity: 0.3;
}

.image-uploading::before {
  content: '';
  box-sizing: border-box;
  position: absolute;
  top: 50%;
  left: 50%;
  width: 30px;
  height: 30px;
  margin-top: -15px;
  margin-left: -15px;
  border-radius: 50%;
  border: 3px solid #ccc;
  border-top-color: #1e986c;
  z-index: 1;
  animation: spinner 0.6s linear infinite;
}

@keyframes spinner {
  to {
    transform: rotate(360deg);
  }
}

.image-edit-button {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 10;
  opacity: 0;
  transition: all 0.2s ease-in-out;
  background-color: #1890ff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  height: 28px;
  padding: 0 12px;
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  border: none;
  color: white;
  font-size: 12px;
}

.image-edit-button svg {
  width: 14px;
  height: 14px;
  color: white;
}

.image-edit-button span {
  line-height: 1;
  font-weight: 500;
}

.image-edit-button:hover {
  background-color: #40a9ff;
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.2);
}

.image-edit-button.active {
  opacity: 1;
}

/* 当鼠标悬停在图片上时显示编辑按钮 */
img:hover + .image-edit-button,
.image-edit-button:hover {
  opacity: 1;
}
