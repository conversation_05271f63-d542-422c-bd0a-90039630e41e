import { checkFileHash } from '@/api/repositoryFile.js';
import axios from 'axios';
import { generatePutUrl, uploadByUrl } from "~/api/upload";
import { ToastService } from "~/services/toast";
import { getFileSha256, removeQuestionMarkText } from "~/utils/utils";


const saveUploadByUrl = async (params: { fileName: string, fileUrl: string, fileSha256: string }) => {
    const res = await uploadByUrl(params)
    if (!res.success) {
        ToastService.error(params.fileName + '上传失败')
        return null
    }
    return res.data
}
export const uploadFn = async (file: any) => {
    try {
        // 文件需要上传
        // console.log(file, 'imgUpload')

        const item = file
        ToastService.loading()

        const sha256 = await getFileSha256(item as any)
        const checkFileShaResult = await checkFileHash({
            sha256: `${sha256}`,
        });

        if (!checkFileShaResult.ok) {
            ToastService.error(checkFileShaResult.message || '图片上传失败，请重试')
            return
        }

        if (checkFileShaResult.data != null) {
            const params = {
                fileName: checkFileShaResult.data.fileName,
                fileUrl: checkFileShaResult.data.fileUrl,
                fileSha256: checkFileShaResult.data.fileSha256
            }
            const fileData = await saveUploadByUrl(params)
            const _response = { fileUrl: params.fileUrl, fileId: fileData.id, name: file.name, status: 'done' }
            file.url = params.fileUrl
            ToastService.success('上传成功')
            return params.fileUrl
        } else {
            // console.log('imgUpload  file ==>', file[0])
            const cosClientAndParams = await generatePutUrl({
                filename: item.name
            })

            if (!cosClientAndParams.ok) {
                ToastService.error(cosClientAndParams.message || '图片上传失败，请重试')
                return
            }

            const response = await axios.put(cosClientAndParams.data.url, file, {
                headers: {
                    'Content-Type': cosClientAndParams.data.contentType
                },
            });

            if (response.status !== 200) {
                ToastService.error('上传失败')
                return
            }

            const params = {
                fileName: item.name,
                fileUrl: removeQuestionMarkText(cosClientAndParams.data.url),
                fileSha256: sha256
            }

            const fileData = await saveUploadByUrl(params)
            const _response = { fileUrl: params.fileUrl, fileId: fileData.id, name: file.name, status: 'done' }
            file.url = params.fileUrl
            ToastService.success('上传成功')
            return params.fileUrl

        }

    } catch (error) {
        ToastService.error('文件上传失败，请重试')
        throw error
    } finally {
        ToastService.hideLoading()
    }
}