<template>
  <node-view-wrapper 
    class="math-wrapper" 
    :class="{ 
      'node-selected': selected,
      'math-wrapper-inline': node.type.name === 'mathInline',
      'math-wrapper-block': node.type.name === 'mathBlock'
    }"
  >
    <div class="math-container">
      <div 
        :class="{ 'math-block': node.type.name === 'mathBlock', 'math-inline': node.type.name === 'mathInline' }" 
        v-html="renderedFormula"
        @dblclick.prevent="handleEdit"
      ></div>
    </div>
  </node-view-wrapper>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { NodeViewWrapper } from '@tiptap/vue-3'
import type { NodeViewProps } from '@tiptap/vue-3'
import katex from 'katex'

const props = defineProps<NodeViewProps>()

const handleEdit = () => {
  const extension = props.editor.extensionManager.extensions.find(
    ext => ext.name === props.node.type.name
  )
  
  if (extension?.options?.onEdit) {
    extension.options.onEdit({
      formula: props.node.attrs.formula,
      type: props.node.type.name === 'mathBlock' ? 'block' : 'inline'
    })
  }
}

const renderedFormula = computed(() => {
  try {
    return katex.renderToString(props.node.attrs.formula || '', {
      displayMode: props.node.type.name === 'mathBlock',
      throwOnError: false
    })
  } catch (error) {
    console.error('KaTeX rendering error:', error)
    return props.node.attrs.formula || ''
  }
})
</script>

<style>
.math-wrapper {
  border: 2px solid transparent;
  border-radius: 0.25rem;
}

/* 行内公式样式 */
.math-wrapper-inline {
  display: inline-flex;
  align-items: center;
}

.math-wrapper-inline .math-container {
  display: inline-flex;
  align-items: center;
}

.math-wrapper-inline .math-inline {
  display: inline-flex;
  align-items: center;
}

/* 块级公式样式 */
.math-wrapper-block {
  display: block;
  width: 100%;
}

.math-wrapper.node-selected {
  border-color: #4299e1;
}

.math-container {
  padding: 0.25rem;
}

.math-block {
  display: block;
  width: 100%;
}

.math-block :deep(.katex-display) {
  margin: 1.5rem 0;
  text-align: center;
}

.math-inline :deep(.katex) {
  vertical-align: middle;
}

:deep(.katex) {
  font-size: 1.1em;
  cursor: pointer;
}
</style> 