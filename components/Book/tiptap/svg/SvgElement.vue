<template>
    <g :key="stableId" :transform="`translate(${element.x}, ${element.y})`">
        <SvgRenderer v-for="(shadow, shadowIndex) in element.shadows" :key="`shadow-${shadow.id || shadowIndex}`"
            :element="shadow" />

        <SvgRenderer :key="element.defs.id" :element="element.defs" />

        <SvgRenderer v-for="(use, useIndex) in element.uses" :key="`use-${use.id || useIndex}`" :element="use" />
    </g>
</template>

<script lang="ts" setup>
import SvgRenderer from '@/components/Book/tiptap/svg/SvgRenderer.ts'
import { randomString } from '@/utils/svgUtils'
import { ref, type PropType } from 'vue'
import type { SvgObject } from '~/types/book'

const stableId = ref(randomString(12))
const defKey = ref(randomString(12))
// 定义属性
const props = defineProps({
    element: {
        type: Object as PropType<SvgObject>,
        required: true
    },
    index: {
        type: Number,
        required: true
    }
})
</script>
