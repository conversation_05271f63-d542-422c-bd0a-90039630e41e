<template>
    <svg class="svg-responsive" :viewBox="computedViewBox" :width="width" :height="height"
        preserveAspectRatio="xMidYMid meet">
        <SvgElement v-for="(element, index) in svgElements" :key="index" :element="element" :index="index" />
    </svg>
</template>

<script lang="tsx" setup>
import type { PropType } from 'vue'
import { computed } from 'vue'
import type { SvgObject } from '~/types/book'
import SvgElement from './SvgElement.vue'

// 定义属性
const props = defineProps({
    svgElements: {
        type: Array as PropType<SvgObject[]>,
        required: true
    },
    width: {
        type: [Number, String],
        default: '100%'
    },
    height: {
        type: [Number, String],
        default: '100%'
    },
    viewBox: {
        type: String,
        default: ''
    }
})

// 计算viewBox，如果没有提供则使用width和height
const computedViewBox = computed(() => {
    if (props.viewBox) {
        return props.viewBox
    }

    // 如果width和height是数字，创建viewBox
    const w = typeof props.width === 'number' ? props.width : 800
    const h = typeof props.height === 'number' ? props.height : 600

    return `0 0 ${w} ${h}`
})
</script>

<style scoped>
.svg-responsive {
    /* 强制覆盖内联样式，限制最大宽度为960px */
    max-width: 960px !important;
    width: auto !important;
    /* 覆盖内联width属性 */
    height: auto !important;
    /* 覆盖内联height属性，保持宽高比 */
    display: block;
    margin: 0 auto;
    box-sizing: border-box;
}

/* 当容器宽度小于960px时，确保SVG完全适应容器 */
@media (max-width: 960px) {
    .svg-responsive {
        max-width: 100% !important;
        width: auto !important;
        height: auto !important;
    }
}

/* 当容器宽度小于768px时 */
@media (max-width: 768px) {
    .svg-responsive {
        max-width: calc(100vw - 40px) !important;
        width: auto !important;
        height: auto !important;
    }
}

/* 当容器宽度小于480px时 */
@media (max-width: 480px) {
    .svg-responsive {
        max-width: calc(100vw - 20px) !important;
        width: auto !important;
        height: auto !important;
    }
}
</style>
