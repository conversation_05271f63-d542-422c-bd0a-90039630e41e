import type { Element } from '@svgdotjs/svg.js'
import { Text } from '@svgdotjs/svg.js'
import { defineComponent, h, onMounted, ref, type PropType } from 'vue'

const SvgRenderer = defineComponent({
    props: {
        element: {
            type: Object as PropType<Element>,
            required: true
        }
    },
    setup(props) {
        // const editingText = ref<Element | null>(null)
        // const editorVisible = ref(false)
        // const editorValue = ref('') // 添加到组件中的一个属性
        const forceUpdate = ref(0)

        // 在document.body中创建编辑器元素
        // const createEditor = () => {
        //   let editor = document.getElementById('svg-text-editor') as HTMLInputElement

        //   if (!editor) {
        //     editor = document.createElement('input')
        //     editor.id = 'svg-text-editor'
        //     editor.style.position = 'absolute'
        //     editor.style.zIndex = '9999'
        //     editor.style.border = '1px solid #00a8ff'
        //     editor.style.padding = '2px'
        //     editor.style.background = 'rgba(255, 255, 255, 0.9)'
        //     editor.style.display = 'none'

        //     editor.addEventListener('blur', (e) => {
        //       saveTextEdit()
        //     })
        //     editor.addEventListener('keydown', (e) => {
        //       if (e.key === 'Enter' && !e.shiftKey) {
        //         e.preventDefault()
        //         saveTextEdit()
        //       } else if (e.key === 'Escape') {
        //         e.preventDefault()
        //         cancelTextEdit()
        //       }
        //     })

        //     editor.addEventListener('input', (e) => {
        //       editorValue.value = (e.target as HTMLInputElement).value
        //     })

        //     document.body.appendChild(editor)
        //   }

        //   return editor
        // }

        // 显示编辑器
        // const showEditor = (textElement: Text) => {
        //   const editor = createEditor()

        //   // 获取SVG元素位置
        //   const svgElement = textElement.node
        //   const svgRect = svgElement.closest('.svg-container')?.getClientRects()
        //   // console.log('showEditor', editingText.value, svgRect)

        //   // 设置编辑器值
        //   editorValue.value = textElement.text()
        //   editor.value = editorValue.value

        //   // 设置编辑器样式
        //   const fontSize = textElement.attr('font-size') || '16px'
        //   const fontFamily = textElement.attr('font-family') || 'sans-serif'
        //   const color = textElement.attr('fill') || 'black'

        //   // console.log('showEditor', textElement.x(),  textElement.y())

        //   // 设置编辑器位置

        //   editor.style.left = `${textElement.x()}px`
        //   editor.style.top = `${textElement.y()}px`
        //   editor.style.minWidth = `${Math.max(100)}px`
        //   editor.style.height = `${24}px`
        //   editor.style.fontSize = fontSize
        //   editor.style.fontFamily = fontFamily
        //   editor.style.color = color
        //   editor.style.display = 'block'

        //   // 聚焦编辑器
        //   editor.focus()
        //   editor.select()

        //   editorVisible.value = true
        // }

        // 保存编辑的文本内容
        // const saveTextEdit = () => {
        //   // console.log('saveTextEdit', editingText.value, editorValue.value)
        //   if (editingText.value && editingText.value instanceof Text) {
        //     // console.log('saveTextEdit', text.id(), editorValue.value)
        //     editingText.value.text(editorValue.value)
        //     forceUpdate.value += 1 // 强制更新
        //   }
        //   cancelTextEdit()
        // }

        // 取消文本编辑
        // const cancelTextEdit = () => {
        //   if (editingText.value === null) {
        //     return
        //   }
        //   // console.log('cancelTextEdit', editorValue.value)
        //   const editor = document.getElementById('svg-text-editor')
        //   if (editor) {
        //     editor.style.display = 'none'
        //   }
        //   editorVisible.value = false
        //   editingText.value = null
        //   editorValue.value = ''
        //   editor?.remove()
        // }

        // 处理文本双击事件
        // const handleTextDoubleClick = (textElement: Element) => {
        //   if (!(textElement instanceof Text)) return

        //   editingText.value = textElement
        //   showEditor(textElement)
        // }

        // 在组件销毁时移除编辑器
        onMounted(() => {
            return () => {
                const editor = document.getElementById('svg-text-editor')
                if (editor) {
                    editor.remove()
                }
            }
        })

        const renderChildren = (element: Element): any[] => {
            if (!element || !element.children || typeof element.children !== 'function') {
                return []
            }

            const children = element.children()

            return children.map((child: Element | string) => {
                if (typeof child === 'string') {
                    return child
                }

                const type = child.type || child.node?.nodeName?.toLowerCase() || 'g'
                // 对attrs做浅拷贝，避免影响原始数据
                const attrs = child.attr ? { ...child.attr() } : {}

                // 如果是<use>标签，且fill存在且不为'none'，删除fill属性
                // if (type === 'use' && 'fill' in attrs && attrs.fill == '#ff00001a') {
                //   // attrs.fill = '#484848'
                // }

                // 为文本元素添加双击事件
                if (child instanceof Text) {
                    return h(
                        type,
                        {
                            ...attrs,
                            ondblclick: (event: MouseEvent) => {
                                event.stopPropagation()
                                // handleTextDoubleClick(child)
                            }
                        },
                        child.text()
                    )
                }

                return h(type, attrs, renderChildren(child))
            })
        }

        return () => {
            // 使用forceUpdate值，即使不直接显示它，也会导致依赖它的组件重新渲染
            const _ = forceUpdate.value
            const element = props.element
            const type = element.type || element.node?.nodeName?.toLowerCase() || 'g'

            // const attrs = element.attr ? element.attr() : {}
            // 对attrs做浅拷贝，避免影响原始数据
            const attrs = element.attr ? { ...element.attr() } : {}

            // 如果是<use>标签，且fill存在且不为'none'，删除fill属性
            // if (type === 'use' && 'fill' in attrs && attrs.fill == '#ff00001a') {
            //   // attrs.fill = '#484848'
            // }

            return h(type, attrs, renderChildren(element))
        }
    }
})

export default SvgRenderer
