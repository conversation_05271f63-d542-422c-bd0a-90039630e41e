<template>
    <a-modal v-model:open="visible" title="编辑示意图内容" okText="生 成" cancelText="取 消" @ok="handleOk" @cancel="handleCancel"
        :maskClosable="false" width="900px">
        <div>
            <div class="mb-1">示意图名称</div>
            <a-input v-model:value="title" placeholder="请输入示意图名称" :maxlength="30" show-count />
        </div>

        <div class="mt-4 mb-1">示意图内容</div>
        <a-form :model="localEditingData" layout="vertical">
            <div v-for="item in localEditingData" :key="item.id" class="flex items-center justify-between gap-3">
                <a-form-item name="name">
                    <a-input v-model:value="item.name" placeholder="请输入标题" :maxlength="15" show-count />
                </a-form-item>
                <a-form-item name="desc" class="w-[100%]">
                    <a-input v-model:value="item.desc" placeholder="请输入描述" :maxlength="20" show-count />
                </a-form-item>
                <a-form-item>
                    <a-popconfirm title="确定删除吗？" ok-text="确定" cancel-text="取消" @confirm="handleDeleteNode(item.id)"
                        :z-index="100000">
                        <DeleteOutlined class="cursor-pointer text-red-500 hover:text-red-700" />
                    </a-popconfirm>
                </a-form-item>
            </div>
        </a-form>
        <div class="flex justify-between mt-2">
            <div class="text-[#2551B5]">
                <a @click="handleAddNode" v-if="localEditingData.length < nodeMaxCount">新增节点</a>
            </div>
            <a class="text-[#2551B5]" @click="handleChangeStyle">换个样式</a>
        </div>
        <a-spin :spinning="svgEditLoading || svgChangeLoading">
            <div class="svg-container">
                <div class="svg-box">
                    <SvgBox ref="svgRef" :svg-elements="svgElements" :width="svgWidth" :height="svgHeight" />
                </div>
            </div>
        </a-spin>
    </a-modal>
</template>

<script lang="ts" setup>

import { useChapterStore } from '@/stores/chapter';
import { useSchematicSvgStore } from '@/stores/schematicSvgStore';
import { message } from 'ant-design-vue';
import 'katex/dist/katex.min.css';

import { computed, defineEmits, defineProps, onMounted, ref, watch } from 'vue';
import { changeSchematic } from '~/api/svg';
import type { SvgObject } from '~/types/book';
import { identifyTextElement } from '~/utils/textUtils';
import { uploadFn } from './extensions/Image/upload';


const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
});


const chapterStore = useChapterStore()

const svgRef = ref<SVGSVGElement | null>(null)

const schematicSvgStore = useSchematicSvgStore()

const hasDescription = ref(false)

const svgEditLoading = ref(false)
const svgChangeLoading = ref(false)

const emit = defineEmits(['update:visible', 'save', 'cancel'])
const visible = computed({
    get: () => props.visible,
    set: (val) => {
        emit('update:visible', val)
    }
})

const svgElements = ref<Array<SvgObject>>([])

const svgWidth = ref(0)
const svgHeight = ref(0)


const title = ref('')
const localEditingData = ref<Array<{ id: number; name: string; desc: string; icon: string }>>([])

const nodeMaxCount = computed(() => {
    const numList = schematicSvgStore.schematicData?.nums.split(',')
    return numList ? parseInt(numList[numList.length - 1]) : 0
})

// 初始化编辑数据
const initEditingData = () => {
    if (schematicSvgStore.schematicData) {
        // 初始化标题
        title.value = schematicSvgStore.schematicData.title

        // 初始化项目数据
        localEditingData.value = schematicSvgStore.schematicData.items.map((item, index) => {
            return {
                id: index + 1,
                icon: item.icon || '',
                name: item.name,
                desc: item.desc
            }
        })
    }
}

// 同步标题到 store
watch(title, (newTitle) => {
    if (schematicSvgStore.schematicData) {
        schematicSvgStore.setSchematicData({
            ...schematicSvgStore.schematicData,
            title: newTitle
        })
    }
})

// 同步编辑数据到 store
watch(
    localEditingData,
    (newData) => {
        if (schematicSvgStore.schematicData) {
            const updatedItems = newData.map((item) => {
                return {
                    name: item.name,
                    desc: item.desc,
                    icon: item.icon
                }
            })
            schematicSvgStore.setSchematicData({
                ...schematicSvgStore.schematicData,
                count: updatedItems.length,
                items: updatedItems
            })
        }
    },
    { deep: true }
)

const handleChangeStyle = async () => {
    try {
        // 检查是否有缓存的样式列表
        const list = schematicSvgStore.svgStyleList || []

        if (list.length > 0) {
            // 有缓存数据，使用第一个样式
            const firstStyle = list[0]
            // 更新当前示意图数据，保留标题和内容
            schematicSvgStore.setSchematicData({
                ...firstStyle,
                title: title.value,
                items: localEditingData.value.map((item) => ({
                    name: item.name,
                    desc: item.desc,
                    icon: item.icon || null
                }))
            })

            // 从列表中移除已使用的样式
            const remainingStyles = list.slice(1)
            schematicSvgStore.setSvgStyleData(remainingStyles)

            // 立即更新SVG展示
            await loadSvgData()
            return
        }

        // 没有缓存数据，调用API获取新样式
        svgChangeLoading.value = true
        const params = {
            type: schematicSvgStore.schematicData?.type,
            count: localEditingData.value.length,
            excludeNames: [schematicSvgStore.schematicData?.fileName]
        }

        const res = await changeSchematic(params)
        if (!res.ok || !res.data || !Array.isArray(res.data) || res.data.length === 0) {
            message.warning(res.message || '更换样式失败，未找到合适样式')
            return
        }

        // 成功获取新样式列表
        const newStyleList = res.data

        // 使用第一个样式
        const firstNewStyle = newStyleList[0]
        schematicSvgStore.setSchematicData({
            ...firstNewStyle,
            title: title.value,
            items: localEditingData.value.map((item) => ({
                name: item.name,
                desc: item.desc,
                icon: item.icon || null
            }))
        })

        // 存储剩余样式以便后续使用
        const remainingNewStyles = newStyleList.slice(1)
        schematicSvgStore.setSvgStyleData(remainingNewStyles)

        // 加载更新后的SVG数据
        await loadSvgData()
    } catch (error) {
        console.error('更换样式失败:', error)
        message.error('更换样式失败，请稍后重试')
    } finally {
        svgChangeLoading.value = false
    }
}

const handleDeleteNode = (id: number) => {
    const numList = schematicSvgStore.schematicData?.nums.split(',')
    if (!numList || numList?.length == 0) {
        return
    }

    if (parseInt(numList[0]) >= localEditingData.value.length) {
        message.warning(`至少保留${numList[0]}个节点`)
        return
    }

    localEditingData.value = localEditingData.value.filter((item) => item.id !== id)

    // 重新排序 ID
    localEditingData.value = localEditingData.value.map((item, index) => {
        return { ...item, id: index + 1 }
    })
}

const handleAddNode = () => {
    if (localEditingData.value.length >= nodeMaxCount.value) {
        message.warning(`最多添加${nodeMaxCount.value}个节点`)
        return
    }

    localEditingData.value.push({
        id: localEditingData.value.length + 1,
        name: '',
        desc: '',
        icon: ''
    })
}

const loadSvgData = async () => {
    if (!schematicSvgStore.schematicData) return

    const { fileName, count } = schematicSvgStore.schematicData
    const svgUrl = `https://static.xiaoin.cn/schematic/img/${fileName}-${count}.svg`

    svgElements.value = []

    try {
        svgEditLoading.value = true
        const response = await fetch(svgUrl)
        if (!response.ok) {
            throw new Error(`Failed to load SVG: ${response.status} ${response.statusText}`)
        }

        const svgContent = await response.text()

        const svgProcessor = new SvgProcessor(svgContent)

        const ret = svgProcessor.getResult()

        // 保持原始尺寸，让CSS处理缩放
        svgHeight.value = ret.svgHeight
        svgWidth.value = ret.svgWidth
        svgElements.value = ret.list

        hasDescription.value = false

        modifyTitleTextElement()
    } catch (err) {
        console.error('Error loading SVG:', err)
    } finally {
        svgEditLoading.value = false
    }
}

// 更新SVG中的文本元素
const updateSvgTexts = () => {
    // console.log('🔄 开始更新SVG文本，标题:', title.value, '编辑数据:', localEditingData.value)
    if (!svgElements.value || svgElements.value.length === 0) {
        // console.log('⚠️ SVG元素为空，跳过文本更新')
        return
    }

    // 遍历所有SVG对象
    for (const element of svgElements.value) {
        // 确保defs存在并有适当的方法
        if (!element.defs || typeof element.defs.find !== 'function') continue

        // 获取所有文本元素
        const texts = element.defs.find('text')
        if (!texts || !texts.length) continue

        // 找出名称和描述的文本元素
        texts.forEach((text: any) => {
            if (!text) return

            // 转换为能够调用text方法的元素
            const textEl = text as unknown as { text: (value?: string) => string }

            // 获取元素ID
            const id = text.id ? text.id() : ''

            // 获取当前文本内容
            const textContent = textEl.text()
            if (!textContent && !id) return

            // 解析对齐方式
            const textAnchor = text.attr('text-anchor') || 'start'
            const alignment = textAnchor === 'middle' ? 'center' : textAnchor === 'end' ? 'right' : 'left'

            // 检查是否包含特定字符串
            const containsSpecialPattern = (str: string) => {
                if (!str) return false
                return (
                    str.includes('tx-') &&
                    (str.includes('-end') ||
                        str.includes('-start') ||
                        str.includes('-value') ||
                        str.includes('-top') ||
                        str.includes('-over') ||
                        str.includes('-under') ||
                        str.includes('-x') ||
                        str.includes('-y') ||
                        str.includes('-minusy') ||
                        str.includes('-minusx') ||
                        str.includes('-letter'))
                )
            }

            // 如果ID或文本内容包含特定模式，则清空文本内容
            if (containsSpecialPattern(id) || containsSpecialPattern(textContent)) {
                textEl.text('')
                return
            }

            // 标题元素通过ID判断
            if (
                (id && id.includes('tx-') && id.includes('-title')) ||
                (textContent && textContent.includes('tx-') && textContent.includes('-title'))
            ) {

                textEl.text(title.value ? title.value : '')
                text.attr('font-size', '22px')
                text.attr('fill', '#2C3E50')
                text.attr('font-weight', 'bold')

                // 处理标题文本换行 - 使用实际获取的宽度
                handleTextWrapping(text, title.value, {
                    fontSize: '22px',
                    lineHeight: 1.5,
                    maxWidth: 1000, //Math.max(maxWidth, 200),
                    maxLines: 2,
                    alignment
                })

                return
            }

            // 其他元素通过文本内容判断
            const items = localEditingData.value

            // 识别文本元素类型和对应的数据
            const elementData = identifyTextElement(id, textContent)
            if (!elementData.valid) return

            const { type, index, subtype } = elementData

            // 根据类型和索引获取对应的文本内容
            let content = ''
            let fontSize = '16px'
            let maxLines = 2

            // console.log('type ==>', type)
            // console.log('textContent ==>', textContent)

            if (type === 'desc' && index >= 0 && index < items.length) {
                hasDescription.value = true
                // 描述类型
                content = items[index].desc
                // console.log('📝 更新描述文本:', id, '索引:', index, '新内容:', content)
                fontSize = content.length > 15 ? '12px' : content.length > 8 ? '14px' : '16px'
                maxLines = 3
                text.attr('font-weight', 'normal')
                text.attr('fill', '#484848')
            } else if (index >= 0 && index < items.length) {
                // 标题类型
                content = items[index].name
                // console.log('📝 更新名称文本:', id, '索引:', index, '新内容:', content)
                fontSize = content.length > 15 ? '14px' : content.length > 8 ? '16px' : '20px'
                maxLines = 2
                text.attr('font-weight', 'bold')
                // setUseTagFill(text)
            }
            text.attr('font-size', fontSize)

            // 处理不同对齐方式的文本换行
            if (!content) {
                textEl.text('')
                return
            }
            handleTextWrapping(text, content, {
                fontSize,
                lineHeight: 1.2,
                maxWidth: Math.max(300, 120), // 使用实际获取的宽度，确保最小宽度
                maxLines,
                alignment
            })
        })
    }
}

// 改进的文本换行处理函数
const handleTextWrapping = (
    text: any,
    content: string,
    options: {
        fontSize: string
        lineHeight: number
        maxWidth: number
        maxLines: number
        alignment?: 'left' | 'center' | 'right'
    }
) => {
    // if (!content) return

    // 清空原有文本
    text.text('')
    text.node.innerHTML = ''

    // 解析字体大小
    const fontSizeValue = parseInt(options.fontSize)

    // 根据字体特性估算字符宽度
    const avgCharWidthMap = {
        // 英文/数字/符号占用较小宽度
        en: fontSizeValue * 0.5,
        // 中文/日文/韩文占用较大宽度
        zh: fontSizeValue * 0.9,
        // 平均宽度
        avg: fontSizeValue * 0.7
    }

    // 根据内容估算更精确的每行字符数
    const getEstimatedCharsPerLine = (content: string): number => {
        // 计算中文字符占比
        const zhChars = content.match(/[\u4e00-\u9fa5\u3040-\u30ff\u3400-\u4dbf\uac00-\ud7af]/g) || []
        const zhRatio = zhChars.length / content.length

        // 根据中英文比例计算平均字符宽度
        const avgCharWidth = zhRatio * avgCharWidthMap.zh + (1 - zhRatio) * avgCharWidthMap.en

        // 根据最大宽度计算每行大约能容纳的字符数，添加一些边距
        const padding = 10 // 左右边距
        const effectiveWidth = Math.max(options.maxWidth - padding, 50) // 确保最小有效宽度
        return Math.floor(effectiveWidth / avgCharWidth)
    }

    // 估算每行容纳的字符数
    const charsPerLine = getEstimatedCharsPerLine(content)

    // 根据对齐方式调整文本分割逻辑
    const alignment = options.alignment || 'left'

    // 分割文本为多行
    const wordBreak = (text: string, maxChars: number): string[] => {
        // 如果是全英文文本，尝试在单词边界处分割
        if (/^[a-zA-Z0-9\s.,;:"'!?()-]+$/.test(text)) {
            const words = text.split(/\s+/)
            const lines = []
            let currentLine = ''

            for (const word of words) {
                const testLine = currentLine ? `${currentLine} ${word}` : word

                if (testLine.length > maxChars) {
                    if (currentLine) {
                        lines.push(currentLine)
                        currentLine = word
                    } else if (word.length > maxChars) {
                        // 单词过长，强制断开
                        lines.push(word.substring(0, maxChars))
                        currentLine = word.substring(maxChars)
                    } else {
                        currentLine = word
                    }
                } else {
                    currentLine = testLine
                }
            }

            if (currentLine) {
                lines.push(currentLine)
            }

            return lines
        }

        // 中文或混合文本，按字符数分割
        const lines = []
        let currentText = text

        while (currentText.length > 0 && lines.length < options.maxLines) {
            if (currentText.length <= maxChars) {
                lines.push(currentText)
                break
            }

            // 在指定字符处分割
            const lineText = currentText.substring(0, maxChars)
            lines.push(lineText)

            // 更新剩余文本
            currentText = currentText.substring(maxChars)

            // 检查是否达到最大行数
            if (lines.length >= options.maxLines - 1 && currentText.length > 0) {
                // 最后一行，需要添加省略号
                let lastLine = currentText.substring(0, maxChars - 3) + '...'
                lines.push(lastLine)
                break
            }
        }

        return lines
    }

    // 获取分行后的文本
    const lines = wordBreak(content, charsPerLine)

    // 设置文本锚点和基线对齐
    const updateTextPosition = (alignment: string) => {
        // text-anchor 设置水平对齐
        if (alignment === 'center') {
            text.attr('text-anchor', 'middle')
        } else if (alignment === 'right') {
            text.attr('text-anchor', 'end')
        } else {
            text.attr('text-anchor', 'start')
        }
    }

    // 更新文本位置
    updateTextPosition(alignment)

    // 获取X坐标
    const getXPosition = () => {
        if (alignment === 'center') {
            // 居中时，使用父元素宽度的一半或手动设置的值
            const parentWidth = text.parent() ? parseInt(text.parent().attr('width') || '0') : 0
            return parentWidth > 0 ? parentWidth / 2 : text.attr('x') || 0
        } else {
            // 左/右对齐时使用原始x坐标
            return text.attr('x') || 0
        }
    }

    // 获取X坐标
    const xPos = getXPosition()

    // 逐行创建tspan元素
    lines.forEach((line, index) => {
        const tspan = document.createElementNS('http://www.w3.org/2000/svg', 'tspan')
        tspan.textContent = line
        tspan.setAttribute('x', xPos.toString())

        // 第一行不需要设置dy偏移
        if (index === 0) {
            tspan.setAttribute('dy', '0')
        } else {
            tspan.setAttribute('dy', `${options.lineHeight}em`)
        }

        text.node.appendChild(tspan)
    })
}

// 替代原有的modifyTitleTextElement函数
const modifyTitleTextElement = () => {
    updateSvgTexts()
}

// 处理确认按钮点击
const handleOk = async () => {
    // 防止重复点击
    if (svgEditLoading.value) {
        return
    }

    try {
        svgEditLoading.value = true

        if (title.value.length > 30) {
            message.warning('标题不能超过30个字')
            return
        }

        for (const item of localEditingData.value) {
            if (item.name.length > 15) {
                message.warning('示意图内容标题不能超过15个字')
                svgEditLoading.value = false
                return
            }
            if (item.desc.length > 20) {
                message.warning('示意图内容描述不能超过20个字')
                svgEditLoading.value = false
                return
            }
        }

        let svgUrl = ''

        try {
            if (!schematicSvgStore.schematicData) {
                return
            }
            const { fileName, count } = schematicSvgStore.schematicData
            svgUrl = `https://static.xiaoin.cn/schematic/img/${fileName}-${count}.svg`

            // 异步更新SVG文本，并等待完成
            await new Promise((resolve) => {
                // 确保所有文本编辑已应用
                updateSvgTexts()
                // 延迟一小段时间确保DOM更新
                setTimeout(resolve, 50)
            })

            // 使用DOM节点生成SVG元素，而不是从头创建和序列化
            const svgBox = document.querySelector('.svg-box svg')
            let svgBlob: Blob
            let uploadFilename: string

            if (svgBox) {
                // 克隆节点，以便我们可以安全地修改它而不影响原始DOM
                const svgClone = svgBox.cloneNode(true) as SVGSVGElement

                // 获取当前SVG的尺寸
                const originalWidth = svgWidth.value || 800 // 提供默认值
                const originalHeight = svgHeight.value || 600 // 提供默认值
                const padding = 10 // 顶部填充
                const newHeight = originalHeight + padding

                // 确保SVG有正确的尺寸和命名空间
                svgClone.setAttribute('xmlns', 'http://www.w3.org/2000/svg')
                svgClone.setAttribute('width', originalWidth.toString())
                svgClone.setAttribute('height', newHeight.toString())

                // 调整viewBox，从y轴-10px的位置开始，保持宽度不变但增加高度
                // svgClone.setAttribute('viewBox', `0 -10 ${originalWidth} ${newHeight}`)
                // 设置默认viewBox
                const defaultViewBox = `0 0 ${originalWidth} ${originalHeight}`

                try {
                    // 尝试获取原始viewBox
                    const originalViewBox = svgBox.getAttribute('viewBox')
                    if (originalViewBox) {
                        // 验证viewBox格式
                        const [x, y, width, height] = originalViewBox.split(' ').map(Number)
                        if (!isNaN(x) && !isNaN(y) && !isNaN(width) && !isNaN(height)) {
                            // 使用调整后的viewBox，保持原始比例但增加顶部空间
                            svgClone.setAttribute('viewBox', `${x} ${y - padding} ${width} ${height + padding}`)
                        } else {
                            // 如果原始viewBox无效，使用默认值
                            svgClone.setAttribute('viewBox', defaultViewBox)
                        }
                    } else {
                        // 如果没有原始viewBox，使用默认值
                        svgClone.setAttribute('viewBox', defaultViewBox)
                    }
                } catch (viewBoxError) {
                    console.warn('设置viewBox时出错，使用默认值:', viewBoxError)
                    svgClone.setAttribute('viewBox', defaultViewBox)
                }

                // 获取SVG的字符串表示
                const svgString = new XMLSerializer().serializeToString(svgClone)

                // 创建Blob对象
                svgBlob = new Blob([svgString], { type: 'image/svg+xml' })

                // 使用当前时间戳+随机数生成唯一文件名
                const timestamp = new Date().getTime()
                const randomSuffix = Math.floor(Math.random() * 10000)
                uploadFilename = `schematic_${timestamp}_${randomSuffix}.svg`
            } else {
                // 如果找不到SVG元素，尝试使用现有的图像URL
                message.warning('未找到SVG元素，将使用原始URL')
                throw new Error('未找到SVG元素')
            }

            // 准备要上传的图片数据
            let uploadedImageUrl = ''

            try {

                // 将Blob对象转换为File对象
                const svgFile = new File([svgBlob], uploadFilename, { type: 'image/svg+xml' })

                message.info('正在上传图片，请稍候...')
                uploadedImageUrl = await uploadFn(svgFile)

                if (uploadedImageUrl) {
                    console.log('图片上传成功，URL:', uploadedImageUrl)
                    message.success('图片上传成功!')
                } else {
                    throw new Error('上传失败，未获取到URL')
                }
            } catch (uploadError) {
                console.error('上传图片失败:', uploadError)
                message.error('上传图片失败')
                return
            }

            // 使用上传后的图片URL或原始URL
            const finalImageUrl = uploadedImageUrl || svgUrl

            const data = {
                ...schematicSvgStore.schematicData,
                title: title.value,
                items: localEditingData.value.map((item) => ({
                    name: item.name,
                    desc: item.desc,
                    icon: item.icon
                })),
                count: localEditingData.value.length,
                svgUrl: finalImageUrl,
                svgWidth: svgWidth.value,
                svgHeight: svgHeight.value
            }

            // 注意：现在SVG数据将存储在图片节点的属性中，而不是IndexedDB
            // 这样可以确保数据与文档内容一起保存和导出

            console.log('save data ==>', data)
            // 提交保存的数据
            emit('save', data)
        } catch (error) {
            console.error('处理SVG数据时出错:', error)
            message.error('处理SVG数据时出错，使用标准保存方式')
        }

        emit('update:visible', false)
    } finally {
        svgEditLoading.value = false
    }
}


// 处理取消按钮点击
const handleCancel = () => {
    schematicSvgStore.setIsEdit(false)
    emit('cancel')
    emit('update:visible', false)
}

// 监听 visible 变化，在打开时初始化数据
watch(
    () => props.visible,
    (newVisible) => {
        if (newVisible) {
            initEditingData()
            loadSvgData()
        }
    }
)

// 监听 schematicData 变化，重新加载 SVG
watch(
    () => schematicSvgStore.schematicData,
    () => {
        if (props.visible) {
            loadSvgData()
        }
    },
    { deep: true }
)

// 在加载SVG后添加一个延时来更新文本，确保SVG元素已完全加载
watch(svgElements, () => {
    if (svgElements.value.length > 0) {
        setTimeout(() => {
            updateSvgTexts()
        }, 100)
    }
})

// 监视标题和编辑数据变化，自动更新SVG文本
watch(title, () => {
    updateSvgTexts()
})

watch(
    localEditingData,
    () => {
        updateSvgTexts()
    },
    { deep: true }
)

// 组件挂载完成
onMounted(() => {
    if (props.visible) {
        initEditingData()
        loadSvgData()
    }
})


</script>

<style scoped lang="scss">
.preview-math {
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    min-height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
}

:deep(.katex-display) {
    margin: 0;
}

.svg-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    max-width: 960px;
    width: 100%;
    margin: 0 auto;
    padding: 20px;
    box-sizing: border-box;

    .svg-box {
        min-height: 300px;
        max-width: 960px;
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
    }
}
</style>