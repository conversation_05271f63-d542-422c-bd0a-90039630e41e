<template>
    <a-modal v-model:open="visible" :title="modalTitle" okText="确定" cancelText="取消" @ok="handleOk"
        @cancel="handleCancel" :maskClosable="false" width="800px">
        <a-form :model="localFormData" layout="vertical">
            <a-form-item label="公式内容（LaTeX格式）" required>
                <a-textarea v-model:value="localFormData.formula" :rows="4" placeholder="请输入 LaTeX 格式的数学公式" />
            </a-form-item>
            <a-form-item label="公式类型">
                <a-radio-group v-model:value="localFormData.type">
                    <a-radio value="inline">行内公式</a-radio>
                    <a-radio value="block">独立公式</a-radio>
                </a-radio-group>
            </a-form-item>
        </a-form>
        <div class="mt-4">
            <h3 class="font-bold mb-2">预览</h3>
            <div class="preview-math" v-html="mathPreview"></div>
        </div>
    </a-modal>
</template>

<script lang="ts" setup>
import katex from 'katex';
import 'katex/dist/katex.min.css';
import { computed, ref, watch } from 'vue';

const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    formData: {
        type: Object,
        default: () => ({
            formula: '',
            type: 'inline' // 'inline' 或 'block'
        })
    }
});

const emit = defineEmits(['update:visible', 'save', 'cancel']);
const visible = computed({
    get: () => props.visible,
    set: (val) => {
        emit('update:visible', val)
    }
})

// 本地表单数据，避免直接修改props
const localFormData = ref({ ...props.formData });

// 当外部formData变化时，更新本地表单
watch(() => props.formData, (newVal) => {
    localFormData.value = { ...newVal };
}, { deep: true });

// 当visible变化时，重置本地表单
watch(() => props.visible, (newVal) => {
    if (newVal) {
        localFormData.value = { ...props.formData };
    }
});

// 计算弹窗标题
const modalTitle = computed(() => {
    return localFormData.value.type === 'inline' ? '插入行内公式' : '插入独立公式';
});

// 计算公式预览
const mathPreview = computed(() => {
    try {
        return katex.renderToString(localFormData.value.formula || '', {
            displayMode: localFormData.value.type === 'block',
            throwOnError: false
        });
    } catch (error) {
        return '预览错误：请检查公式语法';
    }
});

// 处理确认按钮点击
const handleOk = () => {
    emit('save', localFormData.value);
    emit('update:visible', false);
};

// 处理取消按钮点击
const handleCancel = () => {
    emit('cancel');
    emit('update:visible', false);
};
</script>

<style scoped>
.preview-math {
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    min-height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
}

:deep(.katex-display) {
    margin: 0;
}
</style>