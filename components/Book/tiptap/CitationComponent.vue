<template>
    <node-view-wrapper class="citation-wrapper" :class="{ 'node-selected': selected }">
        <span class="citation-container relative group cursor-pointer" @click="handleClick">
            <sup class="citation-mark">[{{ displayNumber }}]</sup>
        </span>
    </node-view-wrapper>
</template>

<script setup lang="ts">
import type { NodeViewProps } from '@tiptap/vue-3';
import { NodeViewWrapper } from '@tiptap/vue-3';
import { computed } from 'vue';

const props = defineProps<NodeViewProps>()

// 获取节点所有属性
const nodeAttrs = computed(() => {
    return {
        paperNo: props.node.attrs['paper-no'],
        opinionId: props.node.attrs['opinion-id']
    }
})

// 展示引用编号，只显示paperNo的值
const displayNumber = computed(() => {

    // return '123'
    // 优先使用外部计算好的展示编号
    // if (props.extension.options.getDisplayNumber) {
    //     return props.extension.options.getDisplayNumber(nodeAttrs.value)
    // }

    // // 只显示paperNo的值
    // return nodeAttrs.value.paperNo || '?'

    return nodeAttrs.value.paperNo

})

// 点击引用本身
const handleClick = () => {
    const extension = props.editor.extensionManager.extensions.find(
        ext => ext.name === 'citation'
    )

    if (extension?.options?.onClickCitation) {
        extension.options.onClickCitation({
            paperNo: nodeAttrs.value.paperNo,
            opinionId: nodeAttrs.value.opinionId
        })
    }
}
</script>

<style scoped>
.citation-wrapper {
    display: inline;
}

/* .citation-mark { */
/* color: #1890ff; */
/* font-size: 0.7em; */
/* font-weight: bold; */
/* } */

.citation-container {
    position: relative;
    /* padding: 0 1px; */
    /* line-height: 200%; */

}

.citation-container:hover .citation-mark {
    color: #40a9ff;
}

.node-selected .citation-mark {
    background-color: rgba(24, 144, 255, 0.1);
    border-radius: 3px;
}
</style>