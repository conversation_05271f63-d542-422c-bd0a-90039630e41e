<template>
    <BubbleMenu v-if="shouldShow({ editor, state: editor?.state } as BubbleMenuProps) && editor" :editor="editor"
        ref="bubbleMenuRef" :tippy-options="tippyOptions" class="bubble-menu" @show="handleBubbleMenuShow"
        @hide="handleBubbleMenuHide">
        <!-- 标准工具菜单 -->

        <div v-if="!aiEditModeActive" :class="editorWidthClass">
            <div class="bubble-tools flex justify-start items-center"
                :style="`max-width: ${menuMaxWidth}px; width: 100%;`">
                <a-tooltip placement="bottom" title="加粗">
                    <div @click="handleBold"
                        class="mx-1 my-0 px-1.25 w-7 h-7 border-0 rounded-md cursor-pointer hover:bg-gray-50 flex justify-center items-center"
                        :class="{ 'text-blue-600': editor?.isActive('bold') }">
                        <BookIconfont name="jiacu1"></BookIconfont>
                    </div>
                </a-tooltip>
                <a-tooltip placement="bottom" title="斜体">
                    <div @click="handleItalic"
                        class="mx-1 my-0 px-1.25 w-7 h-7 border-0 rounded-md cursor-pointer hover:bg-gray-50 flex justify-center items-center"
                        :class="{ 'text-blue-600': editor?.isActive('italic') }">
                        <BookIconfont name="xieti"></BookIconfont>
                    </div>
                </a-tooltip>
                <a-tooltip placement="bottom" title="下划线">
                    <div @click="handleUnderline"
                        class="mx-1 my-0 px-1.25 w-7 h-7 border-0 rounded-md cursor-pointer hover:bg-gray-50 flex justify-center items-center"
                        :class="{ 'text-blue-600': editor?.isActive('underline') }">
                        <BookIconfont name="xiahuaxian"></BookIconfont>
                    </div>
                </a-tooltip>
                <a-tooltip placement="bottom" title="上传图片">
                    <div @click="handleUploadImage"
                        class="mx-1 my-0 px-1.25 w-7 h-7 border-0 rounded-md cursor-pointer hover:bg-white flex justify-center items-center">
                        <BookIconfont name="tupian"></BookIconfont>
                    </div>
                </a-tooltip>
                <a-tooltip placement="bottom" title="行内公式">
                    <div @click="handleInsertInlineMath"
                        class="mx-1 my-0 px-1.25 w-7 h-7 border-0 rounded-md cursor-pointer hover:bg-white flex justify-center items-center">
                        <block theme="outline" size="13" />
                    </div>
                </a-tooltip>
                <a-tooltip placement="bottom" title="块公式">
                    <div @click="handleInsertBlockMath"
                        class="mx-1 my-0 px-1.25 w-7 h-7 border-0 rounded-md cursor-pointer hover:bg-white flex justify-center items-center">
                        <inline theme="outline" size="13" />
                    </div>
                </a-tooltip>

                <TablePopover v-if="editor" :editor="editor"></TablePopover>

                <a-divider type="vertical" />
                <button @click="handleAIEdit"
                    class="flex items-center text-sm bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent">
                    <BookIconfont name="a-bianzu144" :size="20">
                    </BookIconfont>
                    <span class="pl-0.5 ">AI编辑</span>
                </button>
                <div v-if="freeCount"
                    class="bg-[#E7EDFE] rounded-[5px] flex-shrink-0 text-center ml-[18px] text-[14px] text-[#2551B5] px-3 py-1">
                    剩余{{ freeCount }}次
                </div>
            </div>
        </div>

        <!-- AI编辑聊天界面区域-->
        <div class="flex item-center w-full" v-if="aiEditModeActive">
            <!-- 按钮操作区域 -->
            <div v-if="questionValue.length == 0" class="ai-chat-btns flex flex-col rounded-md max-h-[120px]"
                style="flex: 0 0 70px; min-width: 70px;">
                <button class="p-2 hover:bg-gray-50 rounded-md mb-1 text-[14px]"
                    @click="handlePressToolbarAIChange(ACTION_CODE.POLISH)"
                    :class="{ 'is-checked': isAIChangeChecked }">
                    改写
                </button>
                <button class="p-2 hover:bg-gray-50 rounded-md mb-1 text-[14px]"
                    @click="handlePressToolbarAIChange(ACTION_CODE.EXPAND)">扩写</button>
                <button class="p-2 hover:bg-gray-50 rounded-md text-[14px]"
                    @click="handlePressToolbarAIChange(ACTION_CODE.SHORTEN)">缩写</button>
            </div>

            <!-- 选中文字后的输入框和操作菜单区域 -->
            <div class="ai-chat-top flex-grow"
                :class="[{ 'space-reverse': !hasEnoughSpace, 'bottom-fixed': hasEnoughSpace }, editorWidthClass]"
                :style="`max-width: ${calculateContentWidth()}px; width: 100%;`">

                <!-- 内容输入和结果显示区域 -->
                <div class="bg-white rounded-xl p-1 sm:p-2 pt-0 group border border-[#e5e7eb] shadow-custom">
                    <!-- 结果显示区域 - 会向上增长 -->
                    <div class="p-1 border-b border-b-[#F1F1F1] result-content"
                        v-if="bookPolishLoading || bookPolishResult">
                        <a-spin :spinning="bookPolishLoading">
                            <div
                                class="text-[16px] text-[#333333] leading-[29px] max-h-[260px] min-h-[40px] overflow-y-auto result-text-container">
                                <div class="result-text-content word-break-anywhere">{{ bookPolishResult }}</div>
                            </div>
                            <div class="flex justify-between align-item mt-2" v-if="bookPolishResult">
                                <div>
                                    <button @click="handleReplace"
                                        class="bg-[#2551B5] text-[15px] rounded-[7px] text-[#ffffff] px-[15px] py-[3px]">替换</button>
                                    <button @click="handleInsert"
                                        class="bg-[#fffff] border border-[2551B5] text-[15px] rounded-[7px] text-[#2551B5] px-[15px] py-[3px] ml-[18px]">插入</button>
                                </div>
                                <button class="mr-2 p-2 text-gray-500 hover:text-blue-600" @click="copyContent">
                                    <copy theme="outline" size="18" />
                                </button>
                            </div>
                        </a-spin>
                    </div>

                    <!-- 优化指令或要求区域 - 当space-reverse时固定在底部 -->
                    <div class="relative input-container">
                        <div class="flex items-end">
                            <textarea ref="textareaRef" v-model="questionValue" placeholder="请输入优化指令或要求"
                                class="w-full flex-1 border-0 rounded-lg p-2 sm:p-3 resize-none outline-none focus:ring-0 text-[13px] sm:text-base bg-transparent min-h-[24px] max-h-[96px] overflow-y-auto mr-2 word-break-normal"
                                :maxlength="1000" @input="handleTextareaInput"
                                :style="`max-width: ${menuMaxWidth}px; width: 500px;`"></textarea>
                            <button :disabled="bookPolishLoading || questionValue.length == 0"
                                @click="handleSendAIQuestion"
                                class="w-[32px] h-[32px] bg-blue-600 hover:bg-blue-700 text-white rounded-md flex items-center justify-center transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
                                <BookIconfont name="fasong" :size="16" class="transform -rotate-45"></BookIconfont>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </BubbleMenu>
</template>

<script lang="ts" setup>
import BookIconfont from '@/components/Book/BookIconfont.vue';
import TablePopover from '@/components/Book/tiptap/TablePopover/index.vue';
import { useChapterStore } from '@/stores/chapter';
import { copyToClipboard } from '@/utils/copyToClipboard';
import { StarloveConstants } from '@/utils/starloveConstants';
import { Block, Copy, Inline } from '@icon-park/vue-next';
import { EditorState } from '@tiptap/pm/state';
import { BubbleMenu, Editor } from '@tiptap/vue-3';
import { message, Modal } from 'ant-design-vue';
import markdownit from 'markdown-it';
import { computed, nextTick, onBeforeUnmount, onMounted, ref, watch } from 'vue';
import { doAiAction } from '~/api/submissionEdit';
import { UserService } from '~/services/user';
import { useTaskStore } from '~/stores/task';
import type { Book, Chapter } from '~/types/book';
import { ACTION_CODE, BOOK_PAY_TRIGGER_TYPE } from '~/utils/constants';
import { isMarkdown } from '~/utils/utils';
// BubbleMenu的属性接口定义
interface BubbleMenuProps {
    editor: Editor;
    state: EditorState;
}

const props = defineProps({
    editor: {
        type: Object as () => Editor | null | undefined,
        default: null,
    },
    submissionId: {
        type: String,
        default: '',
    },
    book: {
        type: Object as () => Book | null,
        default: null,
    },
    chapter: {
        type: Object as () => Chapter | null,
        default: null,
    },
    editorWidth: {
        type: Number,
        default: 800, // 默认值，如果未提供
    }
});

const emit = defineEmits(['update-math', 'insert-inline-math', 'insert-block-math', 'upload-image']);

const bubbleMenuRef = ref();
const { $eventBus } = useNuxtApp();


// 选中文本长度限制
const TEXT_SELECTION_LIMIT = 1000;
// 状态变量
// const isVisible = ref(false);
const selectionPosition = ref({ left: 0, top: 0, width: 0, height: 0, bottom: 0, right: 0 });

const hasEnoughSpace = ref(true);
const lastSelection = ref<{ from: number, to: number } | null>(null);
const isModalVisible = ref(false);
const isBubbleMenuShowing = ref(false); // 记录气泡菜单是否正在显示
const aiEditModeActive = ref(false); // 记录AI编辑模式是否激活
const isAIChangeChecked = ref(false);
const bookPolishLoading = ref(false);
const bookPolishResult = ref('');
const questionValue = ref('');
const textareaRef = ref<HTMLTextAreaElement | null>(null);

const chapterStore = useChapterStore()

// 添加对生成状态的计算属性
const isGenerating = computed(() => taskStore.isGenerating);

// 添加一个标志来跟踪输入法状态
const isIMEComposing = ref(false);

// Tippy配置
const tippyOptions = {
    duration: 100,
    placement: 'bottom' as const,
    maxWidth: 'none' as const,
    zIndex: 40,
    onShow: () => {
        isBubbleMenuShowing.value = true;
        // console.log('BubbleMenu显示');
    },
    onHide: () => {
        // console.log('BubbleMenu开始隐藏 ==>');
        if (isBubbleMenuShowing.value && (bookPolishResult.value || bookPolishLoading.value)) {
            isBubbleMenuShowing.value = true;
            aiEditModeActive.value = true
            nextTick(() => {
                setTimeout(() => {
                    showCloseConfirmModal();
                }, 300);
            });

            return false
        }

    },
    onHidden: () => {

        // 气泡菜单隐藏完成后，标记为不显示
        isBubbleMenuShowing.value = false;
        console.log('BubbleMenu完全隐藏，当前AI编辑模式:', aiEditModeActive.value);
        resetData();
    },
};

const taskStore = useTaskStore();

// AI请求参数
const reqParams: any = reactive({
    code: '',
    content: '',
    params: { ask: questionValue.value },
    submissionId: props.submissionId || (props.book?.submission_id || ''),

});

// 会员信息相关
const knowledgeAssistantMemberInfo = computed(() => {
    return UserService.getKnowledgeAssistantMemberInfo();
});

// 剩余次数显示
const freeCount = computed(() => {

    if ((knowledgeAssistantMemberInfo.value?.vipLevel || 0) >= 3) {
        return `无限`;
    }
    return `${knowledgeAssistantMemberInfo.value?.freeAIOnlineEditing || 0}`;
});

// BubbleMenu显示条件
const shouldShow = (props: BubbleMenuProps): boolean => {
    try {
        // 如果AI编辑模式已经激活，始终显示菜单 ====== 此判断不能删除，否则确认弹窗显示时，BubbleMenu会隐藏
        if (aiEditModeActive.value) return true;

        // 如果编辑器不可编辑或者正在生成内容，不显示菜单
        if (!props.editor.isEditable || isGenerating.value) return false;

        // 选择为空时不显示
        if (props.state.selection.empty) return false;

        // 检查选区是否在表格内 - 如果是，不显示菜单
        if (isSelectionInTable(props.state)) return false;

        // 检查选区是否包含特殊节点
        if (containsSpecialNodes(props.state)) return false;

        // 检查是否有选中的文本

        const { from, to } = props.state.selection;
        const text = props.state.doc.textBetween(from, to, ' ');
        // 防止选择过程中BubbleMenu闪烁，只有当选区文本长度超过0时才显示

        return text.trim().length > 0;
    } catch (error) {
        console.error('Error getting selected text:', error);
        return false;
    } finally {
    }
};

// 检查选区是否在表格内
const isSelectionInTable = (state: EditorState): boolean => {
    try {
        if (!state || !state.selection) return false;

        const { from } = state.selection;
        const $from = state.doc.resolve(from);

        // 向上遍历节点深度，检查是否在表格内
        for (let depth = $from.depth; depth >= 0; depth--) {
            const node = $from.node(depth);
            if (node && node.type && ['table', 'tableRow', 'tableCell', 'tableHeader'].includes(node.type.name)) {
                return true;
            }
        }
        return false;
    } catch (err) {
        console.warn('Error checking if selection is in table:', err);
        return false;
    }
};

// 检查选区是否包含特殊节点
const containsSpecialNodes = (state: EditorState): boolean => {
    try {
        if (!state || !state.selection) return false;

        const { from, to } = state.selection;
        if (from === undefined || to === undefined || from >= state.doc.content.size || to <= 0) return false;

        let hasSpecialNode = false;

        // 遍历选区内的所有节点
        state.doc.nodesBetween(from, to, (node: any) => {
            if (!node || !node.type || !node.type.name) return true;

            // 检查节点类型是否为特殊类型
            if ([
                'image', 'table', 'tableRow', 'tableCell', 'tableHeader',
                'mathInline', 'mathBlock', 'chart'
            ].includes(node.type.name)) {
                hasSpecialNode = true;
                return false; // 停止遍历
            }
            return true; // 继续遍历
        });

        return hasSpecialNode;
    } catch (err) {
        console.error('Error in containsSpecialNodes:', err);
        return false;
    }
};

// 格式化工具函数
const handleBold = () => {
    if (!props.editor) return;
    try {
        // 保留当前焦点状态
        const hadFocus = props.editor.view.hasFocus();
        props.editor.chain().focus().toggleBold().run();
        // 确保操作后编辑器仍然保持焦点
        if (hadFocus) {
            setTimeout(() => {
                props.editor?.commands.focus();
            }, 10);
        }
    } catch (error) {
        console.error('Error toggling bold:', error);
    }
};

const handleItalic = () => {
    if (!props.editor) return;
    try {
        const hadFocus = props.editor.view.hasFocus();
        props.editor.chain().focus().toggleMark('italic').run();
        if (hadFocus) {
            setTimeout(() => {
                props.editor?.commands.focus();
            }, 10);
        }
    } catch (error) {
        console.error('Error toggling italic:', error);
    }
};

const handleUnderline = () => {
    if (!props.editor) return;
    try {
        const hadFocus = props.editor.view.hasFocus();
        props.editor.chain().focus().toggleMark('underline').run();
        if (hadFocus) {
            setTimeout(() => {
                props.editor?.commands.focus();
            }, 10);
        }
    } catch (error) {
        console.error('Error toggling underline:', error);
    }
};

const handleInsertInlineMath = () => {
    emit('insert-inline-math');
};

const handleInsertBlockMath = () => {
    emit('insert-block-math');
};

const handleUploadImage = () => {
    emit('upload-image');
};


// AI编辑相关函数
const handleAIEdit = () => {
    if (!props.editor) {
        message.warning('编辑器未完全初始化，请稍后再试');
        return;
    }

    // 检查选区是否有效
    const { selection } = props.editor.state;
    if (selection.empty) {
        message.warning('请先选择需要优化的文本');
        return;
    }

    // 获取选中文本长度并检查
    try {
        const { from, to } = selection;
        const selectedText = props.editor.state.doc.textBetween(from, to, ' ');

        // 检查选中文本长度
        if (selectedText.length > TEXT_SELECTION_LIMIT) {
            message.warning(`选中文本长度为${selectedText.length}字，超出限制。一次处理不能超过${TEXT_SELECTION_LIMIT}字，请缩小选择范围。`);
            return;
        }

        // 保存当前选区
        lastSelection.value = { from: selection.from, to: selection.to };

        // 先设置高亮，再激活AI编辑模式
        props.editor.chain().focus().setHighlight({ color: '#E7EDFE' }).run();

        // 确保在下一个事件循环中设置 aiEditModeActive
        nextTick(() => {
            aiEditModeActive.value = true;
            isBubbleMenuShowing.value = true;
        });

    } catch (error) {
        console.error('获取选中文本长度失败:', error);
        message.error('处理选中文本时出错');
    }
};

const handleTextareaInput = () => {
    if (!textareaRef.value) return;
    // 重置高度
    textareaRef.value.style.height = 'auto';
    // 计算新高度
    const newHeight = Math.min(textareaRef.value.scrollHeight, 96); // 96px = 4行高度
    // 设置新高度
    textareaRef.value.style.height = `${newHeight}px`;
};

const handlePressToolbarAIChange = async (code: string) => {

    if (bookPolishLoading.value) {
        return
    }
    reqParams.code = code;
    if (freeCount.value != '无限' && freeCount.value == '0' && (knowledgeAssistantMemberInfo.value?.coinNum || 0) < 10000) {
        chapterStore.payTriggerType = BOOK_PAY_TRIGGER_TYPE.AI_EDITOR
        chapterStore.showPayModal = true
        return;
    }
    await loadBookPolishData();
    await UserService.loadUserInfoAndAssistantMemberInfo();
};

const handleSendAIQuestion = async () => {

    if (!questionValue.value.trim()) {
        message.warning('请输入优化指令');
        return;
    }
    reqParams.code = ACTION_CODE.SIMPLE;
    reqParams.params.ask = questionValue.value;
    if (freeCount.value != '无限' && freeCount.value == '0' && (knowledgeAssistantMemberInfo.value?.coinNum || 0) < 10000) {
        chapterStore.payTriggerType = BOOK_PAY_TRIGGER_TYPE.AI_EDITOR
        chapterStore.showPayModal = true
        return;
    }
    await loadBookPolishData();
    await UserService.loadUserInfoAndAssistantMemberInfo();
};

const handleEventConfirmCreateContent = async () => {
    if (chapterStore.payTriggerType != BOOK_PAY_TRIGGER_TYPE.AI_EDITOR) {
        return
    }
    await loadBookPolishData();
    await UserService.loadUserInfoAndAssistantMemberInfo();
}

const loadBookPolishData = async () => {
    try {
        if (!props.editor) {
            message.warning('编辑器初始化失败');
            return;
        }
        if (!reqParams.code) {
            // console.log('reqParams.code:', reqParams.code);
            return
        }
        if (!reqParams.submissionId) {
            // console.log("loadBookPolishData ==>", reqParams.submissionId)
            reqParams.submissionId = props.book?.submission_id || '';
        }

        // 检查选区是否有效
        // const { selection } = props.editor.state;
        if (!lastSelection.value) {
            // console.log("loadBookPolishData selection ==>", lastSelection.value)
            return;
        }

        const { from, to } = lastSelection.value;

        // 验证选区范围
        if (from === undefined || to === undefined ||
            from < 0 || to > props.editor.state.doc.content.size ||
            from >= to) {
            message.warning('无效的选择范围');
            return;
        }

        // 安全地获取选中文本
        let selectedText;
        try {
            selectedText = props.editor.state.doc.textBetween(from, to);
            if (!selectedText || selectedText.trim().length === 0) {
                message.warning('请选择有效的文本内容');
                return;
            }

            // 检查选中文本长度
            if (selectedText.length > TEXT_SELECTION_LIMIT) {
                message.warning(`选中文本长度为${selectedText.length}字，超出限制。一次处理不能超过${TEXT_SELECTION_LIMIT}字，请缩小选择范围。`);
                return;
            }

            // console.log(`处理文本长度: ${selectedText.length}字`);
        } catch (error) {
            console.error('获取选中文本失败:', error);
            message.warning('获取选中文本失败');
            return;
        }

        reqParams.content = selectedText;
        bookPolishLoading.value = true;

        const res = await doAiAction(reqParams);
        if (!res.ok || !res.data) {
            message.error(res.message || '操作失败');
            return;
        }

        bookPolishResult.value = res.data || '';
    } catch (error) {
        console.error('AI处理失败:', error);
    } finally {
        bookPolishLoading.value = false;
    }
};

const handleReplace = (e: any) => {
    e.preventDefault();

    let textToInsert = bookPolishResult.value;
    if (isMarkdown(textToInsert)) {
        const md = markdownit();
        textToInsert = md.render(textToInsert);
    }
    clearTextHighlight()
    bookPolishResult.value = ''
    props.editor?.chain().focus().insertContent(`${textToInsert}`).run();

    setTimeout(() => {
        isBubbleMenuShowing.value = false;
    }, 300);
};

const handleInsert = () => {
    if (!props.editor) return;
    let to = lastSelection.value?.to;

    if (to === undefined) {
        console.error('无法获取选区的结束位置');
        return;
    }

    let textToInsert = bookPolishResult.value;
    if (isMarkdown(textToInsert)) {
        const md = markdownit();
        textToInsert = md.render(textToInsert);
    }

    clearTextHighlight()
    bookPolishResult.value = ''
    props.editor.commands.insertContentAt(to, textToInsert, {
        updateSelection: true,
        parseOptions: {
            preserveWhitespace: 'full'
        }
    });
    setTimeout(() => {
        isBubbleMenuShowing.value = false;
    }, 300);
};

const copyContent = () => {
    copyToClipboard(bookPolishResult.value);
    message.success('复制成功');
};

// 重置数据
const resetData = () => {
    bookPolishResult.value = '';
    questionValue.value = '';
    reqParams.code = '';
    isBubbleMenuShowing.value = false;
    aiEditModeActive.value = false;
    isModalVisible.value = false;
    clearTextHighlight()
    // console.log('已重置AI编辑模式');
};

// 显示关闭确认模态框
const showCloseConfirmModal = () => {
    if (isModalVisible.value) {
        return
    }
    isModalVisible.value = true;
    Modal.confirm({
        title: '是否确认关闭？',
        content: '关闭后将会丢失当前生成的AI内容',
        okText: '确定',
        cancelText: '取消',
        zIndex: 999,
        onOk: () => {
            // 先清除高亮
            clearTextHighlight();
            // 然后重置数据并关闭菜单
            resetData();
        },
        onCancel: () => {
            isModalVisible.value = false;
        }
    });
};

// 监听生成状态变化
watch(() => isGenerating.value, (newValue) => {
    if (newValue) {
        // 当开始生成时，如果菜单正在显示，需要关闭它
        if (aiEditModeActive.value && bookPolishResult.value.length > 0) {
            showCloseConfirmModal();
        } else {
            resetData();
        }
    }
});

// 计算菜单的最大宽度
const menuMaxWidth = computed(() => {
    try {
        // 计算最大宽度，确保气泡菜单不超出编辑器
        const minWidth = Math.min(400, props.editorWidth - 40); // 最小宽度，但不超过编辑器宽度减去边距
        let maxWidth = Math.max(minWidth, Math.min(props.editorWidth * 0.9, props.editorWidth - 40));

        // 如果在AI编辑模式，设置一个稍小的最大宽度
        if (aiEditModeActive.value) {
            maxWidth = Math.max(minWidth, Math.min(props.editorWidth * 0.8, props.editorWidth - 40));
        }

        return Math.round(maxWidth); // 返回整数宽度
    } catch (error) {
        console.error('计算菜单最大宽度出错:', error);
        return 800; // 默认宽度
    }
});

// 根据编辑器宽度返回合适的CSS类
const editorWidthClass = computed(() => {
    return props.editorWidth < 768 ? 'editor-narrow' : 'editor-wide';
});

// 监听编辑器宽度变化，动态调整布局
watch(() => props.editorWidth, (newWidth) => {
    // console.log('编辑器宽度变化:', newWidth);
    // 编辑器宽度变化时，可以在这里进行其他调整
    hasEnoughSpace.value = newWidth >= 768; // 根据宽度决定布局方向
});

// 计算内容区域宽度
const calculateContentWidth = () => {
    // 如果有显示按钮区域，需要减去按钮区域的宽度
    if (aiEditModeActive.value && questionValue.value.length === 0) {
        return menuMaxWidth.value - 90; // 减去按钮区域宽度和边距
    }
    return menuMaxWidth.value; // 没有按钮区域时使用全部宽度
};

// 处理BubbleMenu显示事件
const handleBubbleMenuShow = () => {
    isBubbleMenuShowing.value = true;
    // console.log('BubbleMenu显示事件触发');
};

// 处理BubbleMenu隐藏事件
const handleBubbleMenuHide = () => {
    // 如果确认弹窗正在显示或有AI结果，阻止BubbleMenu隐藏
    if (isModalVisible.value || bookPolishResult.value || aiEditModeActive.value) {
        // 延迟执行确保状态一致性
        nextTick(() => {
            isBubbleMenuShowing.value = true;
        });
        return;
    }

    isBubbleMenuShowing.value = false;
    console.log('BubbleMenu隐藏事件触发');

    // 当气泡菜单隐藏且AI编辑模式激活时，处理状态同步
    if (aiEditModeActive.value) {
        if (bookPolishResult.value && !isModalVisible.value) {
            // 有未保存的结果，显示确认框
            nextTick(() => {
                showCloseConfirmModal();
            });
        } else if (!bookPolishResult.value && !isModalVisible.value) {
            // 没有结果，直接重置
            resetData();
        }
    }
};

// 处理编辑器选区变化
const handleSelectionUpdate = (props: { editor: any, transaction?: any }) => {
    const { editor } = props;
    if (!editor || !editor.state) return;

    const selection = editor.state.selection;

    // 存储上次有效选区
    if (!selection.empty && !aiEditModeActive.value) {
        lastSelection.value = { from: selection.from, to: selection.to };
    }

    // 如果选区为空且AI编辑模式激活，处理状态
    if (selection.empty && aiEditModeActive.value) {
        // 如果气泡菜单已经不显示了，处理AI模式
        if (!isBubbleMenuShowing.value && !isModalVisible.value) {
            if (bookPolishResult.value) {
                // 有未保存的结果，显示确认框
                showCloseConfirmModal();
            } else {
                // 没有结果，直接重置
                resetData();
            }
        }
    }
};

// 添加键盘ESC监听器的函数引用
const handleKeyDown = (event: KeyboardEvent) => {
    if (event.key === 'Escape' && aiEditModeActive.value) {
        console.log('按下ESC键');
        if (bookPolishResult.value && !isModalVisible.value) {
            // 有未保存的结果，显示确认框
            showCloseConfirmModal();
        } else if (!bookPolishResult.value && !isModalVisible.value) {
            // 没有结果，直接重置
            resetData();
        }
    }
};

// 在生命周期钩子中添加对编辑器选区变化的监听
onMounted(() => {
    // 使用 nextTick 确保所有引用都已经初始化
    nextTick(() => {
        if (props.editor) {
            console.log('SafeAIBubbleMenu mounted with editor', props.editor);
            // 初始化时根据编辑器宽度设置布局
            hasEnoughSpace.value = props.editorWidth >= 768;

            // 监听编辑器选区变化
            props.editor.on('selectionUpdate', handleSelectionUpdate);
            // 使用DOM事件监听输入法状态
            const editorDom = props.editor.view.dom;

            // 输入法开始事件
            editorDom.addEventListener('compositionstart', () => {
                console.log('输入法开始事件触发');
                isIMEComposing.value = true;
            });

            // 输入法更新事件
            editorDom.addEventListener('compositionupdate', (event) => {
                console.log('输入法更新事件触发', event.data);
                isIMEComposing.value = true;

                // 如果有选区，隐藏菜单
                if (props.editor && !props.editor.state.selection.empty) {
                    isBubbleMenuShowing.value = false;
                }
            });

            // 输入法结束事件
            editorDom.addEventListener('compositionend', () => {
                console.log('输入法结束事件触发');
                // 延迟设置状态，确保所有处理完成
                setTimeout(() => {
                    isIMEComposing.value = false;
                    // 如果有选区，恢复菜单
                    // if (props.editor && !props.editor.state.selection.empty) {
                    //     // 更新选区位置，并恢复菜单显示
                    //     updateSelectionPosition();
                    //     isBubbleMenuShowing.value = true;
                    //     props.editor.commands.focus();
                    // }
                }, 100);
            });

            // 鼠标按下时，可能是开始一个新的选择
            props.editor.view.dom.addEventListener('mousedown', (event: MouseEvent) => {
                // 如果是shift键按下的选择操作，记录该状态
                if (event.shiftKey) {
                    // 标记为shift选择状态
                    setTimeout(() => {
                        if (!props.editor?.state.selection.empty) {
                            updateSelectionPosition();
                            // 手动触发一次选区更新，确保BubbleMenu能够显示
                            props.editor?.commands.focus();
                        }
                    }, 10);
                }
            });

            // 添加额外的事件监听，确保Shift+选择操作后能正确显示菜单
            props.editor.on('focus', () => {
                // 编辑器获得焦点时，如果有选区则更新位置
                if (!props.editor?.state.selection.empty) {
                    // if (isIMEComposing.value) {
                    //     return
                    // }
                    // updateSelectionPosition();
                }
            });
        }
    });

    // 添加对ESC键的监听，退出AI编辑模式
    document.addEventListener('keydown', handleKeyDown);

    $eventBus.on(StarloveConstants.keyOfEventBus.bookEditorCreateAfterConfirmPayModal, handleEventConfirmCreateContent);



    clearTextHighlight()
});

// 在组件卸载时移除各种事件监听
onBeforeUnmount(() => {
    // 移除编辑器选区变化监听
    if (props.editor) {
        try {
            props.editor.off('selectionUpdate', handleSelectionUpdate);
            props.editor.off('focus');

            // 移除输入法相关事件监听
            props.editor.view.dom.removeEventListener('compositionstart', () => { });
            props.editor.view.dom.removeEventListener('compositionupdate', () => { });
            props.editor.view.dom.removeEventListener('compositionend', () => { });

            // 移除鼠标事件监听
            props.editor.view.dom.removeEventListener('mousedown', () => { });
        } catch (error) {
            console.error('移除编辑器事件监听失败:', error);
        }
    }

    // 移除ESC键监听
    try {
        document.removeEventListener('keydown', handleKeyDown);
    } catch (error) {
        console.error('移除键盘监听失败:', error);
    }

    // 确保组件卸载时重置状态
    resetData();
});

// 更新选区位置的函数
const updateSelectionPosition = () => {
    try {
        if (!props.editor) return;

        const selection = props.editor.state.selection;
        if (!selection || selection.empty) return;

        const view = props.editor.view;
        if (!view) return;
        // 获取编辑器容器的滚动信息和位置
        const editorRect = view.dom.getBoundingClientRect();

        // 保存编辑器区域的宽度
        const viewportWidth = window.innerWidth;
        const availableWidth = Math.min(viewportWidth - 40, props.editorWidth);

        // 计算选中区域的中心位置
        const selectionStart = view.coordsAtPos(selection.from);
        const selectionEnd = view.coordsAtPos(selection.to);

        const selectionWidth = selectionEnd.right - selectionStart.left;
        const selectionCenterX = selectionStart.left + selectionWidth / 2;

        // 保存选中区域的位置信息，坐标是相对于视口的
        selectionPosition.value = {
            left: selectionStart.left,
            top: selectionStart.top,
            right: selectionEnd.right,
            bottom: selectionEnd.bottom,
            width: selectionWidth,
            height: selectionEnd.bottom - selectionStart.top
        };

        // 计算编辑器和窗口底部距离
        const distanceToBottom = editorRect.bottom - selectionEnd.bottom;
        const distanceToWindowBottom = window.innerHeight - selectionEnd.bottom;
        const safetyThreshold = 400;

        // 判断底部空间是否足够
        hasEnoughSpace.value = distanceToBottom > safetyThreshold && distanceToWindowBottom > safetyThreshold;

        // 窗口底部区域直接强制设为false
        if (distanceToWindowBottom < 400) {
            hasEnoughSpace.value = false;
        }

        console.log('已更新选区位置');
    } catch (error) {
        console.error('Error updating selection position:', error);
    }
};

// 清除文字高亮
const clearTextHighlight = () => {
    if (!props.editor) return;
    try {
        if (lastSelection.value) {
            // 有选区时，先定位到选区再清除高亮
            props.editor.commands.setTextSelection(lastSelection.value);
            props.editor.commands.unsetHighlight();
            props.editor.commands.unsetMark('highlight');

            // 只在需要时使用blur（不需要每次都失焦）
            if (!isIMEComposing.value) {
                props.editor.commands.blur();
            }

            lastSelection.value = null;
        } else {
            // 没有选区时，先创建一个包含整个文档的选区，然后清除高亮
            const { state } = props.editor;
            if (state && state.doc) {
                // 获取文档尺寸
                const docSize = state.doc.content.size;
                if (docSize > 0) {
                    // 设置从文档开始到结束的选区
                    const tempSelection = { from: 0, to: docSize };
                    // 使用选区清除方式清除高亮
                    props.editor.commands.setTextSelection(tempSelection);
                    props.editor.commands.unsetHighlight();
                    props.editor.commands.unsetMark('highlight');
                    // 不总是失去焦点，只在必要时
                    if (!isIMEComposing.value) {
                        props.editor.commands.blur();
                    }
                }

            }

        }
    } catch (e) {
        console.error('清除高亮失败:', e);
    }
};

// 修复引用问题：确保所有selectionMenuBubbleVisible的引用更新为aiEditModeActive
watch(() => aiEditModeActive.value, (newValue) => {
    if (newValue && props.editor) {
        // 延迟更新位置，确保DOM已更新
        nextTick(() => {
            updateSelectionPosition();
            // console.log('AI编辑模式已激活，更新位置');
        });
    } else if (!newValue && props.editor) {
        // setTextHighlight('transparent');
        // console.log('AI编辑模式已关闭，清除高亮', props.editor.chain().focus());
        clearTextHighlight()

    }
});
</script>

<style scoped>
.bubble-menu {
    display: flex;
}

.safe-ai-bubble-menu {
    z-index: 45;
}

.bubble-tools {
    box-shadow: 0px 0px 20px 0px #C7D6FE;
    padding: 10px;
    border-radius: 10px;
    background-color: #ffffff;
    overflow: hidden;
}

.bubble-tools button {
    padding: 3px 5px;
    border-width: 0px;
    border-radius: 5px;
    background-color: transparent;
    white-space: nowrap;
    font-size: 14px;
    cursor: pointer;
}

.bubble-tools button:disabled {
    cursor: not-allowed;
}

.bubble-tools button:hover {
    background-color: #f8f8f8;
}

.ai-editing-container {
    display: flex;


}


/* 按钮操作区样式 */
.ai-chat-btns {
    background: white;
    box-shadow: 0px 0px 20px 0px #C7D6FE;
    border-radius: 10px;
    position: sticky;
    top: 0;
}

.ai-chat-btns button {
    width: 100%;
    text-align: center;
    font-size: 14px;
    transition: all 0.2s;
}

.ai-chat-btns button.is-checked {
    background-color: #f0f7ff;
    color: #2551B5;
    font-weight: 500;
}

/* 显示在选中文字上方的样式 */
.ai-chat-top {
    display: flex;
    flex-direction: column;
    gap: 8px;
    border-radius: 8px;
    padding: 8px;
    max-height: 400px;
    overflow-y: auto;
    min-width: 300px;
}

/* 当内容固定在底部时的样式 */
.ai-chat-top.bottom-fixed {
    flex-direction: column;
    display: flex;
}

/* 确保输入框区域固定在最前 */
.ai-chat-top.bottom-fixed .input-container {
    position: relative;
    order: 2;
}

/* 空间不足时，输入框固定在底部 */
.ai-chat-top.space-reverse .input-container {
    position: relative;
    order: 2;
}

/* 让结果区域向上增长 */
.ai-chat-top.bottom-fixed .result-content {
    order: 1;
}

/* 空间不足时，结果区域向上增长 */
.ai-chat-top.space-reverse .result-content {
    order: 1;
}

/* 空间不够时反转布局方向 */
.ai-chat-top.space-reverse {
    flex-direction: column-reverse;
    position: relative;
}

textarea {
    line-height: 1.5;
    padding: 8px 12px;
    border-radius: 6px;
    transition: all 0.2s;
    font-size: 13px;
}

button[disabled] {
    opacity: 0.5;
    cursor: not-allowed;
}

textarea::-webkit-scrollbar {
    width: 4px;
}

textarea::-webkit-scrollbar-track {
    background: transparent;
}

textarea::-webkit-scrollbar-thumb {
    background: #e2e8f0;
    border-radius: 2px;
}

textarea::-webkit-scrollbar-thumb:hover {
    background: #cbd5e1;
}

.shadow-custom {
    box-shadow: 0px 0px 20px 0px #C7D6FE;
}

/* 结果文本容器样式 */
.result-text-container {
    max-width: 100%;
    overflow-x: hidden;
    overflow-y: auto;
}

/* 超长文本处理 */
.word-break-anywhere {
    word-break: break-word;
    white-space: pre-wrap;
    overflow-wrap: break-word;
}

.word-break-normal {
    word-break: normal;
    white-space: pre-wrap;
}

/* 自适应响应式媒体查询 */
@media (max-width: 768px) {
    .ai-chat-top {
        min-width: unset;
        width: 100%;
    }

    .editor-narrow .bubble-tools {
        flex-wrap: wrap;
        justify-content: center;
    }

    .editor-narrow .ai-chat-top {
        flex-direction: column;
    }
}
</style>
