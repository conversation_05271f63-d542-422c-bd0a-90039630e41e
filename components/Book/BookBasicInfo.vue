<template>
  <a-modal :open="open" :title="isEdit ? '编辑著作' : '创建新著作'" @cancel="handleCancel" @ok="form && handleSubmit()"
    :confirmLoading="loading" width="800px">
    <a-form :model="form" layout="vertical" ref="formRef">
      <!-- 标题 -->
      <a-form-item label="著作标题" name="title" :rules="[{ required: true, message: '请输入著作标题' }]">
        <a-input v-model:value="form.title" placeholder="请输入著作标题" />
      </a-form-item>

      <!-- 类型 -->
      <a-form-item label="著作类型" name="type">
        <a-select v-model:value="form.type" :getPopupContainer="(trigger) => trigger.parentNode">
          <a-select-option value="book">图书</a-select-option>
          <a-select-option value="paper">论文</a-select-option>
        </a-select>
      </a-form-item>

      <!-- 简介 -->
      <a-form-item label="简介" name="description">
        <a-textarea v-model:value="form.description" :rows="4" placeholder="请输入著作简介" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import type { FormInstance } from 'ant-design-vue'
import { nanoid } from 'nanoid'
import { computed, ref, watch } from 'vue'
import type { Book } from '~/types/book'
import { BookService } from '~/utils/api/book'

const props = defineProps<{
  open: boolean
  editBook?: Book
}>()

const emit = defineEmits<{
  'update:open': [value: boolean]
  'success': []
}>()

const loading = ref(false)
const formRef = ref<FormInstance>()

const isEdit = computed(() => !!props.editBook)

const form = ref<Omit<Book, 'id' | 'createdAt' | 'updatedAt'>>({
  key: nanoid(),
  title: '',
  type: 'book' as const,
  description: '',
  chapters: []
})

// 监听编辑模式，加载数据
watch(() => props.editBook, (newBook) => {
  if (newBook) {
    const { title, type, description, key, chapters } = newBook
    form.value = {
      key,
      title,
      type,
      description,
      chapters
    }
  } else {
    // 新建模式，重置表单
    form.value = {
      key: nanoid(),
      title: '',
      type: 'book',
      description: '',
      chapters: []
    }
  }
}, { immediate: true })

const handleCancel = () => {
  emit('update:open', false)
  formRef.value?.resetFields()
}

const handleSubmit = async () => {
  try {
    loading.value = true
    await formRef.value?.validate()

    if (isEdit.value && props.editBook) {
      // 更新现有书籍
      await BookService.updateBook(props.editBook.key, form.value)
    } else {
      // 创建新书籍
      await BookService.createBook(form.value)
    }

    emit('success')
    emit('update:open', false)
    formRef.value?.resetFields()
  } catch (error) {
    console.error('表单验证或提交失败:', error)
  } finally {
    loading.value = false
  }
}
</script>