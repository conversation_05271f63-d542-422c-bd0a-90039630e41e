<template>
  <!-- flex items-center -->
  <div class=" relative w-full">

    <div v-if="isLoggedIn">
      <!-- 硬币余额区域 -->
      <div v-if="showCoinBalanceArea && knowledgeAssistantMemberInfo?.vipLevel >= 1" @click="handleRechargeCoin"
        class="w-full bg-gradient-to-r from-[#E1EAFF] to-[#E5F3FB] border border-[#C3D5FD] text-white px-2 py-[12px] rounded-[10px] cursor-pointer">
        <div class="flex justify-between items-center">
          <div class="flex flex-col">
            <div class="flex items-center space-x-1">
              <img src="https://static-1256600262.file.myqcloud.com/xiaoin-h5/icons/new-pc/yingbi.png"
                class="w-[25px] h-[25px]" />
              <span class="text-[#2551B5] text-[15px]">{{ coinBalance || 0 }}</span>
            </div>
          </div>
          <button
            class="px-2 py-1 rounded-[7px] transition-colors whitespace-nowrap bg-[#2551B5] text-[#ffffff] text-[11px] self-center">
            充值
          </button>
        </div>
      </div>

      <!-- 会员等级显示区域 -->
      <template v-else>
        <!-- 至尊会员 -->
        <div v-if="knowledgeAssistantMemberInfo?.vipLevel > 2"
          class="w-full bg-gradient-to-r from-[#583F87] to-[#222962] text-white px-2 py-[12px] rounded-lg cursor-pointer">
          <div class="flex justify-between items-center">
            <div class="flex flex-col">
              <div class="flex items-center space-x-1">
                <img :src="KnowledgeAssistantMemberLevel.level4" class="w-[20px] h-[20px]" />
                <span class="text-[#FFE7B6] text-[13px]">至尊会员</span>
              </div>
              <div v-if="showExpireDate" class="mt-1 text-[11px] text-white/80">
                有效期至：{{ getDatePart(knowledgeAssistantMemberInfo.vipExpireTime) }}
              </div>
            </div>
            <button
              class="px-2 py-1 rounded-[7px] transition-colors whitespace-nowrap bg-[#FFEECC] text-[#CE5606] text-[11px] self-center"
              @click.stop="handleRecharge">
              续费会员
            </button>
          </div>
        </div>

        <!-- 高级会员 -->
        <div v-else-if="knowledgeAssistantMemberInfo?.vipLevel > 1"
          class="w-full px-3 py-[12px] rounded-lg bg-gradient-premium cursor-pointer" @click="handleRecharge">
          <div class="flex justify-between items-center">
            <div class="flex flex-col">
              <div class="flex items-center space-x-1">
                <img :src="KnowledgeAssistantMemberLevel.level3" class="w-[20px] h-[20px]" />
                <span class="text-white text-[12px]">高级会员</span>
              </div>
              <div v-if="showExpireDate" class="mt-1  text-[11px] text-white/80">
                有效期至：{{ getDatePart(knowledgeAssistantMemberInfo.vipExpireTime) }}
              </div>
            </div>
            <button
              class="px-2 py-1 rounded-[7px] transition-colors whitespace-nowrap bg-[#FFEECC] text-[#CE5606] text-[11px] self-center">
              升级
            </button>
          </div>
        </div>

        <!-- 标准会员 -->
        <div v-else-if="knowledgeAssistantMemberInfo?.vipLevel > 0.5"
          class="w-full px-3 py-[12px] rounded-lg bg-gradient-premium cursor-pointer" @click="handleRecharge">
          <div class="flex justify-between items-center">
            <div class="flex flex-col">
              <div class="flex items-center space-x-1">
                <img :src="KnowledgeAssistantMemberLevel.level2" class="w-[20px] h-[20px]" />
                <span class="text-white text-[12px]">标准会员</span>
              </div>
              <div v-if="showExpireDate" class="mt-1  text-[11px] text-white/80">
                有效期至：{{ getDatePart(knowledgeAssistantMemberInfo.vipExpireTime) }}
              </div>
            </div>
            <button
              class="px-2 py-1 rounded-[7px] transition-colors whitespace-nowrap bg-[#FFEECC] text-[#CE5606] text-[11px] self-center">
              升级
            </button>
          </div>
        </div>

        <!-- 体验会员 -->
        <div v-else-if="knowledgeAssistantMemberInfo?.vipLevel > 0"
          class="w-full bg-gradient-trial cursor-pointer text-white px-3 py-[12px] rounded-lg" @click="handleRecharge">
          <div class="flex justify-between items-center">
            <div class="flex flex-col">
              <div class="flex items-center space-x-1">
                <img :src="KnowledgeAssistantMemberLevel.level2" class="w-[20px] h-[20px]" />
                <span class="text-white text-[12px]">体验会员</span>
              </div>
              <div v-if="showExpireDate" class="mt-1  text-[11px] text-white/80">
                有效期至：{{ getDatePart(knowledgeAssistantMemberInfo.vipExpireTime) }}
              </div>
            </div>
            <button
              class="px-2 py-1 rounded-[7px] transition-colors whitespace-nowrap bg-[#FFEECC] text-[#CE5606] text-[11px] self-center">
              升级
            </button>
          </div>
        </div>


        <!-- 非会员 -->
        <div v-else-if="(!knowledgeAssistantMemberInfo?.vipLevel || knowledgeAssistantMemberInfo?.vipLevel == 0)"
          class="w-full bg-gradient-free cursor-pointer text-white px-1 py-[12px] rounded-lg flex items-center justify-between rounded-[28px]"
          @click="handleRecharge">
          <div class="flex items-center space-x-1">
            <img :src="KnowledgeAssistantMemberLevel.level4" class="w-[20px] h-[20px]" />
            <span class="text-[#AB4500] text-[12px]">开通会员，写作无忧</span>
          </div>
          <div class="bg-red/10 p-1">
            <right theme="outline" size="15" fill="#AB4500" />
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup>
import {
  Right,
} from '@icon-park/vue-next';
import { useRechargeStore } from '~/stores/recharge';

import { useUserStore } from '~/stores/user';
import { KnowledgeAssistantMemberLevel, RechargeModalTab } from '~/utils/constants';
import { getDatePart } from '~/utils/utils';
const props = defineProps({
  showExpireDate: {
    type: Boolean,
    default: false
  },
  showCoinBalanceArea: {
    type: Boolean,
    default: false
  }
})


const userStore = useUserStore()
const { isLogined: isLoggedIn, currentLoginInfo, knowledgeAssistantMemberInfo } = storeToRefs(userStore)

// const user = useUserStore()
// const exclusiveStore = useExclusiveStore()
const rechargeStore = useRechargeStore()

const coinBalance = computed(() => {

  return currentLoginInfo.value?.coinBalance || 0
})


const emit = defineEmits(['update:modelValue'])

const handleRechargeCoin = () => {
  emit('update:modelValue', false)

  rechargeStore.openRechargeModal(RechargeModalTab.coin)
}

const handleRecharge = () => {

  rechargeStore.openRechargeModal(RechargeModalTab.vip)
}

onMounted(() => {

})
</script>

<style scoped>
.fas {
  font-size: 1rem;
}

.bg-gradient-team {
  background: linear-gradient(90deg, #5B69E5 0%, #7FA9FF 100%);
}

.bg-gradient-free {
  background: linear-gradient(90deg, #FFDCAC 0%, #FFF5E8 100%);
}

.bg-gradient-trial {
  background: linear-gradient(90deg, #EF5A7D 0%, #F39C6D 100%);
}

.bg-gradient-standard {
  background: linear-gradient(90deg, #2196F3 0%, #03A9F4 100%);
}

.bg-gradient-premium {
  background: linear-gradient(90deg, #FF6C95 0%, #6680FE 100%);
}

.bg-gradient-supreme {
  background: linear-gradient(90deg, #9FAAFF 0%, #B988FF 100%);
}
</style>