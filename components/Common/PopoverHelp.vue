<template>
    <UPopover mode="hover" v-if="content" class="h-full flex items-center ">
        <div class="flex items-center justify-center ">
            <Help theme="outline" size="14" fill="#6B7280" class="ml-1 " />
        </div>

        <template #panel>
            <div class="p-2 text-sm text-white max-w-[300px] bg-gray-500 whitespace-pre-line">
                <div v-html="contentNew"></div>
            </div>
        </template>
    </UPopover>
</template>

<script setup lang="ts">
import { Help } from '@icon-park/vue-next';


const props = defineProps({
    content: {
        type: String,
        required: false
    }
})
const contentNew = computed(() => {
    return (props?.content || '').replace(/\\n/g, '\n')
}) 
</script>
