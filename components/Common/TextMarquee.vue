<template>
  <div class="relative flex items-center px-3 py-1 text-sm text-blue-600 rounded-full bg-blue-50 min-w-36 h-7">
    <transition-group name="marquee" tag="div" class="absolute will-change-transform">
      <div v-for="(text, index) in texts" :key="text" v-show="currentIndex === index" class="whitespace-nowrap">
        {{ text }}
      </div>
    </transition-group>
  </div>
</template>

<script setup>
const props = defineProps({
  texts: {
    type: Array,
    required: true,
    default: () => []
  },
  interval: {
    type: Number,
    default: 3000
  }
})

const currentIndex = ref(0)
const timer = ref(null)

const startInterval = () => {
  if (timer.value) clearInterval(timer.value)
  timer.value = setInterval(() => {
    currentIndex.value = (currentIndex.value + 1) % props.texts.length
  }, props.interval)
  // console.log('🎠 Marquee: 动画已启动')
}

const handleVisibilityChange = () => {
  if (document.hidden) {
    clearInterval(timer.value)
    // console.log('🎠 Marquee: 页面隐藏，动画已暂停')
  } else {
    startInterval()
    // console.log('🎠 Marquee: 页面可见，动画已重启')
  }
}

onMounted(() => {
  startInterval()
  // 监听页面可见性变化
  document.addEventListener('visibilitychange', handleVisibilityChange)
  // console.log('🎠 Marquee: 组件已挂载')
})

onUnmounted(() => {
  if (timer.value) {
    clearInterval(timer.value)
    // console.log('🎠 Marquee: 定时器已清理')
  }
  document.removeEventListener('visibilitychange', handleVisibilityChange)
  // console.log('🎠 Marquee: 组件已卸载')
})
</script>

<style scoped>
.marquee-enter-active,
.marquee-leave-active {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: absolute;
}

.marquee-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.marquee-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

.marquee-enter-to,
.marquee-leave-from {
  opacity: 1;
  transform: translateY(0);
}
</style>