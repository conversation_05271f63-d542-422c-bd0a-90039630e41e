<template>
    <div class="relative w-[360px]">
        <div class="shadow-lg bg-gradient-to-br from-white to-gray-50/30 rounded-xl bg-popover-pattern">
            <!-- 用户信息区域 -->
            <div class="flex items-start p-5 pb-4 space-x-3 border-b border-gray-100">
                <!-- 左侧头像 -->
                <div class="w-[45px] h-[45px]">
                    <UserAvatar />
                    <!-- <img :src="avatar" class="object-cover w-full h-full rounded-full" alt="用户头像" /> -->
                </div>
                <!-- 右侧信息 -->
                <div class="flex-1">
                    <!-- 第一行：昵称和切换账号 -->
                    <div class="flex items-center justify-between mb-1">
                        <div
                            class="text-gray-800 font-medium max-w-[160px] text-ellipsis whitespace-nowrap overflow-hidden">
                            {{ nickname }}
                        </div>

                    </div>
                    <!-- 第二行：用户ID和复制按钮 -->
                    <div class="flex items-center justify-between space-x-2">
                        <span class="text-sm text-gray-500">ID: {{ userId }}</span>
                        <button @click="copyUserId" class="text-gray-400 hover:text-gray-600">
                            <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
            <!-- 下半部分白色背景区域 -->
            <div class="bg-white rounded-[15px] shadow-[0px_1px_20px_0px_#C7D6FE] p-4">
                <!-- 头部区域 -->
                <div class="mb-4">
                    <UserInfoArea :showExpireDate="true" />
                </div>
                <!-- 权益列表 -->
                <div class="space-y-3">
                    <div class="flex items-center justify-between py-1 border-gray-100">
                        <div class="text-sm font-medium text-gray-400">基于知识库写作</div>

                        <div class="text-sm font-medium text-gray-700">
                            <svg v-if="(knowledgeAssistantMemberInfo?.vipLevel || 0) >= 1"
                                xmlns="http://www.w3.org/2000/svg" class="w-6 h-6" viewBox="0 0 20 20" fill="#2551B5">
                                <path fill-rule="evenodd"
                                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                    clip-rule="evenodd" />
                            </svg>
                            <svg v-else xmlns="http://www.w3.org/2000/svg" class="w-6 h-6" viewBox="0 0 20 20"
                                fill="#ff4242">
                                <path fill-rule="evenodd"
                                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                    clip-rule="evenodd" />
                            </svg>
                        </div>
                    </div>
                    <div class="flex items-center justify-between py-1 border-gray-100">
                        <div class="text-sm font-medium text-gray-400">文档学习免费</div>
                        <div class="text-sm font-medium text-gray-700">
                            <svg v-if="(knowledgeAssistantMemberInfo?.vipLevel || 0) >= 1"
                                xmlns="http://www.w3.org/2000/svg" class="w-6 h-6" viewBox="0 0 20 20" fill="#2551B5">
                                <path fill-rule="evenodd"
                                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                    clip-rule="evenodd" />
                            </svg>
                            <svg v-else xmlns="http://www.w3.org/2000/svg" class="w-6 h-6" viewBox="0 0 20 20"
                                fill="#ff4242">
                                <path fill-rule="evenodd"
                                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                    clip-rule="evenodd" />
                            </svg>
                        </div>
                    </div>
                    <div class="flex items-center justify-between py-1 border-gray-100">
                        <div class="text-sm font-medium text-gray-400">学术搜索次数</div>
                        <div class="flex items-center space-x-2">
                            <a-button @click="handleOpenExchangeModal" size="small" type="primary" class="text-sm">
                                兑换
                            </a-button>
                            <div class="text-sm font-medium">
                                <span class="text-blue-700">{{ knowledgeAssistantMemberInfo?.usedScholarSearch || 0
                                }}次</span>
                                <span class="text-gray-700">/{{ knowledgeAssistantMemberInfo?.maxScholarSearch ||
                                    0 }}次</span>
                            </div>
                        </div>
                    </div>

                    <div class="flex items-center justify-between py-1 border-gray-100">
                        <div class="text-sm font-medium text-gray-400">剩余提问次数</div>
                        <div class="text-sm font-medium">
                            <span class="text-blue-700" v-if="isSSSVip">无限次</span>
                            <span class="text-blue-700" v-else>
                                {{ getLearningWordCount((knowledgeAssistantMemberInfo?.maxChat || 0) -
                                    (knowledgeAssistantMemberInfo?.usedChat || 0)) }}
                                次</span>
                        </div>
                    </div>
                    <div class="flex items-center justify-between py-1 border-gray-100">
                        <div class="text-sm font-medium text-gray-400">知识库空间</div>
                        <div class="text-sm font-medium">
                            <span class="text-blue-700">{{ getCurrentUserSpaceUsedSize }}</span>
                            <span class="text-gray-700">/{{ getCurrentUserSpaceTotalSize }}</span>
                        </div>
                    </div>
                    <div class="flex items-center justify-between py-1 border-gray-100">
                        <div class="text-sm font-medium text-gray-400">剩余在线编辑次数</div>

                        <div class="text-sm font-medium text-gray-700">
                            <span class="text-gray-700"
                                v-if="(knowledgeAssistantMemberInfo?.vipLevel || 0) == 0">0次</span>
                            <span class="text-blue-700" v-else>
                                {{ freeAIOnlineEditing }}
                            </span>
                        </div>
                    </div>
                </div>

                <!-- 剩余硬币 -->
                <div class="flex items-center justify-between bg-[#F2F5FF] p-3 rounded-lg space-x-1 mt-4 cursor-pointer text-[#2551B5]"
                    @click="handleOpenCoinModal">
                    <div class="flex items-center">
                        <!-- <Funds theme="outline" size="20" class="text-yellow-500" /> -->
                        <img src="https://static-**********.file.myqcloud.com/xiaoin-h5/icons/new-pc/yingbi.png"
                            class="w-5 h-5" />
                        <span class="ml-2 text-sm">剩余硬币：</span>
                        <span class="text-sm">{{ coinBalance || 0 }}</span>
                    </div>
                    <div class="text-sm text-blue-700">充值</div>
                </div>

                <!-- 记录按钮区域 -->
                <div class="pt-4 mt-4 border-t border-gray-100">
                    <div class="grid grid-cols-4 gap-2">
                        <div @click="(e) => handleNavigate(e, '/profile/questions')"
                            class="flex flex-col items-center justify-center p-2 hover:bg-gray-50 rounded-[7px] transition-colors border border-[#CDD2E2] shadow-[0px_1px_20px_0px_#C7D6FE] cursor-pointer">
                            <span class="text-xs text-gray-600">提问记录</span>
                        </div>
                        <div @click="(e) => handleNavigate(e, '/profile/creations')"
                            class="flex flex-col items-center justify-center p-2 hover:bg-gray-50 rounded-[7px] transition-colors border border-[#CDD2E2] shadow-[0px_1px_20px_0px_#C7D6FE] cursor-pointer">
                            <span class="text-xs text-gray-600">写作记录</span>
                        </div>
                        <div @click="(e) => handleNavigate(e, '/profile/orders')"
                            class="flex flex-col items-center justify-center p-2 hover:bg-gray-50 rounded-[7px] transition-colors border border-[#CDD2E2] shadow-[0px_1px_20px_0px_#C7D6FE] cursor-pointer">
                            <span class="text-xs text-gray-600">我的订单</span>
                        </div>
                        <div @click="(e) => handleNavigate(e, '/profile/coins')"
                            class="flex flex-col items-center justify-center p-2 hover:bg-gray-50 rounded-[7px] transition-colors border border-[#CDD2E2] shadow-[0px_1px_20px_0px_#C7D6FE] cursor-pointer">
                            <span class="text-xs text-gray-600">硬币明细</span>
                        </div>
                    </div>
                </div>

                <!-- 个人中心入口 -->
                <div @click.stop="handleOpenUserCenter"
                    class="flex items-center justify-between p-3 mt-3 transition-colors rounded-lg cursor-pointer hover:bg-gray-50">
                    <span class="text-sm text-gray-700">个人中心</span>
                    <Right theme="outline" size="18" class="text-gray-400" />
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import UserAvatar from '@/components/Auth/UserAvatar.vue'
import { formatStorageSize, getLearningWordCount } from '@/utils/utils'
import { Right } from '@icon-park/vue-next'
import { message } from 'ant-design-vue'
import { computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import UserInfoArea from '~/components/Common/UserInfoArea.vue'
import { UserService } from '~/services/user'

import { useExchangeSearchStore } from '~/stores/exchangeSearch'
import { useRechargeStore } from '~/stores/recharge'
import { useSpaceStore } from '~/stores/space'
import { useSwitchAccountStore } from '~/stores/switchAccount'
import { useUpgradePopoverStore } from '~/stores/upgradePopover'
import { RechargeModalTab } from '~/utils/constants'

const props = defineProps({
    isOpenNewWindow: {
        type: Boolean,
        default: false
    },
})

const emit = defineEmits(['onOpenUserCenter'])

const switchAccountStore = useSwitchAccountStore()
const { currentAccount } = storeToRefs(switchAccountStore)
const spaceStore = useSpaceStore()
const { spaceUsedBytes, spaceQuotaBytes } = storeToRefs(spaceStore)

const upgradePopoverStore = useUpgradePopoverStore()

const rechargeStore = useRechargeStore()

const route = useRoute()
const router = useRouter()

// 用户信息
const userInfo = computed(() => {
    return UserService.getCurrentLoginInfo()
})

// 复制用户ID
const copyUserId = () => {
    const id = userInfo.value?.id

    if (!id) {
        message.error('用户ID不存在')
        return
    }
    navigator.clipboard
        .writeText(id)
        .then(() => {
            // 这里可以添加复制成功的提示
            message.success('复制成功')
        })
        .catch(() => {
            message.error('复制失败，请手动复制')
        })
}

const isSSSVip = computed(() => {
    return UserService.isSSSVip()
})
const knowledgeAssistantMemberInfo = computed(() => {
    return UserService.getKnowledgeAssistantMemberInfo()
})

const coinBalance = computed(() => {

    return userInfo.value?.coinBalance || 0
})

const nickname = computed(() => {

    return userInfo.value?.nickname || '未设置昵称'
})

const userId = computed(() => {

    return userInfo.value?.id || '-'
})


const freeAIOnlineEditing = computed(() => {
    if ((knowledgeAssistantMemberInfo.value?.vipLevel || 0) >= 3) {
        return `无限次`
    }
    return `${knowledgeAssistantMemberInfo.value?.freeAIOnlineEditing || 0}次`
})
// 组件初始化时获取一次数据
onMounted(async () => {
    if (switchAccountStore.accounts) {
        await switchAccountStore.fetchAccounts()
    }
})

// 修改计算属性，保持原有的错误处理
const getCurrentUserSpaceUsedSize = computed(() => {
    if (!currentAccount.value) {
        console.warn('当前账号信息未获取到')
        return '0'
    }
    return formatStorageSize(spaceUsedBytes.value > currentAccount.value?.space.spaceUsedBytes ? spaceUsedBytes.value : currentAccount.value?.space.spaceUsedBytes, 0)
})

const getCurrentUserSpaceTotalSize = computed(() => {
    if (!currentAccount.value) {
        console.warn('当前账号信息未获取到')
        return '0'
    }
    return formatStorageSize(spaceQuotaBytes.value > currentAccount.value?.space.spaceQuotaBytes ? spaceQuotaBytes.value : currentAccount.value?.space.spaceQuotaBytes, 0)
})



const handleOpenCoinModal = () => {
    rechargeStore.openRechargeModal(RechargeModalTab.coin)
}

const handleOpenExchangeModal = () => {
    // 通过store打开兑换模态弹窗
    const exchangeSearchStore = useExchangeSearchStore()
    exchangeSearchStore.openExchangeModal()
}

const handleOpenUserCenter = (event: Event) => {
    // 阻止事件冒泡
    event?.stopPropagation();
    emit('onOpenUserCenter', event);
}

const handleNavigate = (event: Event, path: string) => {
    event?.stopPropagation();
    // 使用 router 跳转到指定路径
    const fullPath = route.fullPath;
    const query = {
        returnPath: encodeURIComponent(fullPath)
    };
    upgradePopoverStore.hide();
    if (props.isOpenNewWindow) {
        window.open(path, '_blank')
        return
    }
    router.push({ path, query });
}
</script>

<style scoped lang="scss">
.bg-popover-pattern {
    background-image: url('https://static-**********.file.myqcloud.com/xiaoin-h5/image/user-popover-bg.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.bg-gradient-to-br {
    position: relative;
}

.bg-gradient-to-br::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: inherit;
    z-index: 0;
}

.bg-gradient-to-br>* {
    position: relative;
    z-index: 1;
}

@keyframes gradient {
    0% {
        background-position: 0% 50%;
    }

    50% {
        background-position: 100% 50%;
    }

    100% {
        background-position: 0% 50%;
    }
}
</style>
