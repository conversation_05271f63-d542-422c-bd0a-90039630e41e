<template>
    <div class="flex items-center text-sm space-x-1 mb-2">
        <template v-for="(item, index) in paths" :key="index">
            <!-- 分隔符 -->
            <div v-if="index > 0" class="text-gray-400 mx-1">></div>

            <!-- 面包屑项 -->
            <div :class="[
                'cursor-pointer max-w-[160px] truncate',
                index === paths.length - 1
                    ? 'text-gray-800 font-medium'
                    : 'text-gray-500 hover:text-blue-500 transition-colors'
            ]" @click="handleClick(item, index)">
                {{ item.title }}
            </div>
        </template>
    </div>
</template>

<script setup lang="ts">
interface BreadcrumbItem {
    id: string | number;
    title: string;
}

const props = defineProps<{
    paths: BreadcrumbItem[];
}>();

const emit = defineEmits<{
    (e: 'click', item: BreadcrumbItem, index: number): void;
}>();

const handleClick = (item: BreadcrumbItem, index: number) => {
    emit('click', item, index);
};
</script>

<style scoped>
.truncate {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
</style>