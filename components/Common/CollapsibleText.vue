<template>
    <div class="relative">
        <div class="flex items-start">
            <!-- 展开/收起按钮 -->
            <div v-if="shouldShowToggle"
                class="shrink-0 cursor-pointer p-1 hover:bg-gray-100 rounded transition-colors duration-200"
                @click="isExpanded = !isExpanded">
                <component :is="isExpanded ? Down : Right" theme="outline" size="14" :fill="'#6B7280'" />
            </div>

            <div :class="[
                ' flex-1',
                !isExpanded ? 'line-clamp-1' : 'whitespace-pre-wrap break-words',
                'pr-8' // 为复制按钮留出空间
            ]">
                {{ text }}
            </div>

            <!-- 复制按钮 -->
            <div class="absolute top-0 right-0 cursor-pointer p-1 hover:bg-gray-100 rounded transition-colors duration-200"
                @click="handleCopy">
                <copy theme="outline" size="14" :fill="copied ? '#10B981' : '#6B7280'"
                    class="transition-colors duration-200" />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { Copy, Down, Right } from '@icon-park/vue-next';
import { message } from 'ant-design-vue';
import { onMounted, ref } from 'vue';

const props = defineProps<{
    text: string
}>()

const isExpanded = ref(false)
const copied = ref(false)
const shouldShowToggle = ref(false)

// 检查文本是否需要显示展开/收起按钮
onMounted(() => {
    const el = document.createElement('div')
    el.style.cssText = 'position:absolute;left:-9999px;width:100%;white-space:pre-wrap;'
    el.textContent = props.text
    document.body.appendChild(el)
    const height = el.clientHeight
    document.body.removeChild(el)

    // 假设每行高度约为20px，如果内容高度超过一行，则显示展开按钮
    shouldShowToggle.value = height > 20
})

const handleCopy = async () => {
    try {
        await navigator.clipboard.writeText(props.text)
        copied.value = true
        setTimeout(() => {
            copied.value = false
        }, 2000)

        message.success('复制成功')
    } catch (err) {
        console.error('复制失败:', err)
        message.error('复制失败')
    }
}
</script>

<style scoped>
.line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>