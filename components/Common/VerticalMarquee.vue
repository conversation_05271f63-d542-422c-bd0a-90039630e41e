<template>
  <div class="relative h-6 overflow-hidden">
    <TransitionGroup name="slide-fade" tag="div" :css="false" @before-enter="beforeEnter" @enter="enter" @leave="leave">
      <div v-for="text in visibleTexts" :key="text" class="absolute w-full overflow-y-hidden text-sm"
        :class="color ? '' : 'text-white'" :style="color ? { color } : {}">
        {{ text }}
      </div>
    </TransitionGroup>
  </div>
</template>

<script setup lang="ts">
import gsap from 'gsap';
import { onMounted, onUnmounted, ref } from 'vue';

const props = defineProps<{
  texts: string[]
  color?: string
}>()

const visibleTexts = ref<string[]>([])
let timer: NodeJS.Timeout | null = null
let isAnimating = false

// 初始化显示第一条文本
visibleTexts.value = [props.texts[0]]

onMounted(() => {
  startMarquee()
})

onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
  }
})

function startMarquee() {
  timer = setInterval(() => {
    if (!isAnimating && props.texts.length > 1) {
      updateText()
    }
  }, 3000)
}

function updateText() {
  const currentText = visibleTexts.value[0]
  const currentIndex = props.texts.indexOf(currentText)
  const nextIndex = (currentIndex + 1) % props.texts.length
  visibleTexts.value = [props.texts[nextIndex]]
}

function beforeEnter(el: Element) {
  gsap.set(el, {
    opacity: 0,
    y: 20
  })
}

function enter(el: Element, done: () => void) {
  isAnimating = true
  gsap.to(el, {
    duration: 0.5,
    opacity: 1,
    y: 0,
    onComplete: () => {
      isAnimating = false
      done()
    }
  })
}

function leave(el: Element, done: () => void) {
  gsap.to(el, {
    duration: 0.5,
    opacity: 0,
    y: -20,
    onComplete: done
  })
}
</script>

<style scoped>
.slide-fade-move,
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-fade-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.slide-fade-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

.slide-fade-leave-active {
  position: absolute;
}
</style>