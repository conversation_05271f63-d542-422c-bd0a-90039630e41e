<template>
  <div v-if="modelValue" class="fixed inset-0 bg-black/50 flex items-center justify-center flex-col"
    :style="{ 'z-index': zIndex }" @touchmove.prevent>
    <div class="bg-white overflow-hidden" :class="[
      isTouchDevice ? 'rounded-t-2xl sm:rounded-2xl mobile-modal' : 'rounded-2xl relative',
      !isTouchDevice ? 'relative' : ''
    ]" :style="modalStyles">
      <!-- 关闭按钮 -->
      <button v-if="showClose" @click="handleCancel"
        class="absolute right-6 top-6 text-gray-400 hover:text-gray-600 z-10 cursor-pointer">
        <close theme="outline" size="24" />
      </button>

      <!-- 弹窗标题 -->
      <div v-if="!hideHeader" class="p-6">
        <div class="flex items-center" :class="{ 'justify-center': titleAlignCenter }">
          <h3 class="text-lg font-bold text-gray-800">{{ title }}</h3>
        </div>
        <p v-if="subtitle" class="text-gray-500 text-sm mt-2">{{ subtitle }}</p>
      </div>

      <!-- 弹窗内容 -->
      <div class="overflow-y-auto h-full" :style="maxHeight ? `max-height: ${maxHeight}px` : ''">
        <slot></slot>
      </div>

      <!-- 底部按钮 -->
      <div v-if="!hideFooter && ($slots.footer || showFooter)"
        class="p-6 border-t border-gray-100 flex justify-end space-x-4">
        <slot name="footer">
          <button v-if="showCancel" @click="handleCancel" class="px-4 py-2 text-gray-600 hover:text-gray-800">
            {{ cancelText }}
          </button>
          <button v-if="showConfirm" @click="handleConfirm"
            class="px-6 py-2 bg-blue-700 text-white rounded-full hover:bg-blue-800 transition-colors">
            {{ confirmText }}
          </button>
        </slot>
      </div>
    </div>
    <button v-if="showConfirmOnly" @click="handleConfirm"
      class="bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 h-12 w-80 mt-5 rounded-lg text-white text-nowrap text-ellipsis">
      {{ confirmText }} </button>
  </div>
</template>

<script setup>
import { Close } from '@icon-park/vue-next';
import { computed, onMounted, onUnmounted, watch } from 'vue';
import { useMobileDetection } from '~/composables/useMobileDetection';

// 移动端检测
const { isTouchDevice, screenWidth } = useMobileDetection()

const props = defineProps({
  modelValue: Boolean,
  title: {
    type: String,
    required: false
  },
  subtitle: String,
  width: Number,
  height: Number,
  maxHeight: Number,
  confirmText: {
    type: String,
    default: '确定'
  },
  cancelText: {
    type: String,
    default: '取消'
  },
  showClose: {
    type: Boolean,
    default: true
  },
  showFooter: {
    type: Boolean,
    default: true
  },
  showConfirm: {
    type: Boolean,
    default: true
  },
  showCancel: {
    type: Boolean,
    default: true
  },
  hideHeader: {
    type: Boolean,
    default: false
  },
  hideFooter: {
    type: Boolean,
    default: false
  },
  titleAlignCenter: {
    type: Boolean,
    default: false
  },
  zIndex: {
    type: [Number, String],
    default: 50
  },
  showConfirmOnly: {
    type: Boolean,
    default: false
  },
})

const emit = defineEmits(['update:modelValue', 'confirm', 'cancel'])

// 计算模态框样式
const modalStyles = computed(() => {
  const styles = {}

  if (isTouchDevice.value) {
    // 移动端样式
    styles.maxHeight = '90vh'
  } else {
    // 桌面端样式
    if (props.width) {
      styles.width = `${props.width}px`
    } else {
      styles.minWidth = '400px'
    }
    if (props.height) {
      styles.height = `${props.height}px`
    }
  }

  return styles
})

// 背景滚动锁定
let originalBodyStyle = ''
const lockBodyScroll = () => {
  if (typeof window !== 'undefined') {
    originalBodyStyle = document.body.style.cssText
    document.body.style.cssText += `
      overflow: hidden !important;
      position: fixed !important;
      width: 100% !important;
      height: 100% !important;
      touch-action: none !important;
    `
  }
}

const unlockBodyScroll = () => {
  if (typeof window !== 'undefined') {
    document.body.style.cssText = originalBodyStyle
  }
}

// 监听模态框显示状态
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    lockBodyScroll()
  } else {
    unlockBodyScroll()
  }
}, { immediate: true })

const handleConfirm = () => {
  emit('confirm')
}

const handleCancel = () => {
  emit('cancel')
  emit('update:modelValue', false)
}

// 添加 ESC 键关闭处理函数
const handleEscKey = (event) => {
  if (event.key === 'Escape' && props.modelValue) {
    handleCancel()
  }
}

// 组件挂载时添加键盘事件监听
onMounted(() => {
  document.addEventListener('keydown', handleEscKey)
})

// 组件卸载时移除键盘事件监听和解锁滚动
onUnmounted(() => {
  document.removeEventListener('keydown', handleEscKey)
  unlockBodyScroll()
})
</script>

<style scoped>
/* 移动端弹窗样式 */
.mobile-modal {
  position: fixed !important;
  top: 50% !important;
  left: 16px !important;
  right: 16px !important;
  width: auto !important;
  transform: translateY(-50%) !important;
  max-height: 90vh !important;
  overflow-y: auto !important;
}

/* 在小屏幕上应用移动端样式 */
@media (min-width: 640px) {
  .mobile-modal {
    position: relative !important;
    top: auto !important;
    left: auto !important;
    right: auto !important;
    width: auto !important;
    transform: none !important;
    max-height: none !important;
    overflow-y: visible !important;
  }
}
</style>