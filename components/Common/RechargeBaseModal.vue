<template>
    <div v-if="modelValue" class="fixed inset-0 bg-black/50 flex items-center justify-center z-1111"
        :style="{ zIndex: zIndex }">
        <div class="bg-transparent rounded-2xl relative max-h-screen overflow-hidden max-w-[1100px] w-full" :style="{
            height: height ? `${height}px` : 'auto',
            width: width ? `${width}px` : '',

        }">
            <!-- 关闭按钮 -->
            <button v-if="showClose" @click="handleCancel"
                class="absolute right-2 sm:right-6 top-2 sm:top-6 text-gray-400 hover:text-gray-600 z-50 p-1 rounded-full">
                <close theme="outline" size="24" />
            </button>

            <!-- 弹窗标题 -->
            <div v-if="!hideHeader" class="p-6 border-b border-gray-100">
                <div class="flex items-center">
                    <h3 class="text-xl font-bold text-gray-800">{{ title }}</h3>
                </div>
                <p v-if="subtitle" class="text-gray-500 text-sm mt-2">{{ subtitle }}</p>
            </div>

            <!-- 弹窗内容 -->
            <div class="overflow-y-auto h-full" :style="maxHeight ? `max-height: ${maxHeight}px` : ''">
                <slot></slot>
            </div>

            <!-- 底部按钮 -->
            <div v-if="!hideFooter && ($slots.footer || showFooter)"
                class="p-6 border-t border-gray-100 flex justify-end space-x-4">
                <slot name="footer">
                    <button v-if="showCancel" @click="handleCancel" class="px-4 py-2 text-gray-600 hover:text-gray-800">
                        {{ cancelText }}
                    </button>
                    <button v-if="showConfirm" @click="handleConfirm"
                        class="px-6 py-2 bg-blue-700 text-white rounded-full hover:bg-blue-800 transition-colors">
                        {{ confirmText }}
                    </button>
                </slot>
            </div>
        </div>
    </div>
</template>

<script setup>
import { Close } from '@icon-park/vue-next';
import { onMounted, onUnmounted } from 'vue';

const props = defineProps({
    modelValue: Boolean,
    title: {
        type: String,
        required: true
    },
    subtitle: String,
    width: Number,
    height: Number,
    maxHeight: Number,
    confirmText: {
        type: String,
        default: '确定'
    },
    cancelText: {
        type: String,
        default: '取消'
    },
    showClose: {
        type: Boolean,
        default: true
    },
    showFooter: {
        type: Boolean,
        default: true
    },
    showConfirm: {
        type: Boolean,
        default: true
    },
    showCancel: {
        type: Boolean,
        default: true
    },
    hideHeader: {
        type: Boolean,
        default: false
    },
    hideFooter: {
        type: Boolean,
        default: false
    },
    zIndex: {
        type: Number,
        default: 1111
    }
})

const emit = defineEmits(['update:modelValue', 'confirm', 'cancel'])

const handleConfirm = () => {
    emit('confirm')
}

const handleCancel = () => {
    emit('cancel')
    emit('update:modelValue', false)
}

// 添加 ESC 键关闭处理函数
const handleEscKey = (event) => {
    if (event.key === 'Escape' && props.modelValue) {
        handleCancel()
    }
}

// 组件挂载时添加键盘事件监听
onMounted(() => {
    document.addEventListener('keydown', handleEscKey)
})

// 组件卸载时移除键盘事件监听
onUnmounted(() => {
    document.removeEventListener('keydown', handleEscKey)
})
</script>