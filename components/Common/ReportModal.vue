<template>
  <a-modal :open="open" title="举报内容" @ok="handleSubmit" @cancel="close" okText="提交" cancelText="取消" :width="400"
    class="report-modal">
    <div class="mb-5">
      <div class="mb-3 font-medium">请选择举报原因 <span class="text-red-500">*</span></div>
      <div class="flex flex-wrap gap-2">
        <button v-for="(reason, index) in reportReasons" :key="index" @click="reportReason = reason.value" :class="[
          'p-2 border rounded-[10px] text-sm text-center transition-all',
          reportReason === reason.value
            ? 'bg-blue-500 border-blue-500 text-white hover:bg-blue-400 hover:border-blue-400'
            : 'bg-white border-gray-300 text-black hover:text-blue-500 hover:border-blue-500'
        ]">
          {{ reason.label }}
        </button>
      </div>
      <div v-if="errors.reportReason" class="text-red-500 text-sm mt-1">
        {{ errors.reportReason }}
      </div>
    </div>

    <div class="mb-5">
      <div class="mb-2 font-medium">详细说明 <span class="text-red-500">*</span></div>
      <a-textarea v-model:value="reportDetail" placeholder="请详细描述您的举报原因..." :rows="4" :maxlength="500" show-count />
      <div v-if="errors.reportDetail" class="text-red-500 text-sm mt-1">
        {{ errors.reportDetail }}
      </div>
    </div>

    <div class="mb-4">
      <div class="mb-2 font-medium">联系方式 <span class="text-red-500">*</span></div>
      <a-input v-model:value="contactPhone" placeholder="请输入您的手机号" :maxlength="11" />
      <div v-if="errors.contactPhone" class="text-red-500 text-sm mt-1">
        {{ errors.contactPhone }}
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { saveFeedback } from '@/api/user';
import { message } from 'ant-design-vue';
import { reactive, ref } from 'vue';

const props = defineProps<{
  open: boolean;
}>();

const emit = defineEmits(['update:open']);

const reportReasons = [
  { value: 'harmful', label: '有害/不安全' },
  { value: 'pornographic', label: '色情低俗' },
  { value: 'insulting', label: '谩骂攻击' },
  { value: 'illegal', label: '违法犯罪' },
  { value: 'privacy', label: '隐私相关' },
  { value: 'other', label: '其他' },
];

const reportReason = ref('');
const reportDetail = ref('');
const contactPhone = ref('');
const errors = reactive({
  reportReason: '',
  reportDetail: '',
  contactPhone: '',
});

const validateForm = () => {
  errors.reportReason = '';
  errors.reportDetail = '';
  errors.contactPhone = '';

  if (!reportReason.value) {
    message.warning('请选择举报原因');
    return false;
  }

  if (!reportDetail.value) {
    message.warning('请填写详细说明');
    return false;
  }

  if (!contactPhone.value) {
    message.warning('请填写联系方式');
    return false;
  } else if (!/^1[3-9]\d{9}$/.test(contactPhone.value)) {
    message.warning('请输入有效的手机号码');
    return false;
  }

  return true;
};

const handleSubmit = () => {
  if (!validateForm()) {
    return;
  }

  submit();
};

const submit = async () => {
  const res = await saveFeedback({
    description: contactPhone.value,
    titile: reportDetail.value,
  });

  if (!res.ok) {
    return;
  }

  message.success('举报成功');

  close();
};

const close = () => {
  emit('update:open', false);
  // 重置表单
  reportReason.value = '';
  reportDetail.value = '';
  contactPhone.value = '';
  // 重置错误提示
  Object.keys(errors).forEach((key) => {
    errors[key as keyof typeof errors] = '';
  });
};
</script>

<style scoped>
.report-modal :deep(.ant-modal-content) {
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.report-modal :deep(.ant-modal-body) {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}
</style>
