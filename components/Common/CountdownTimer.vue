<template>
    <div class="flex items-center gap-2 text-sm">
        <span class="text-gray-500">{{ title }}</span>
        <div class="flex items-center">
            <span class="inline-flex items-center justify-center w-7 h-7 text-white rounded" :class="colorClass">
                {{ remainingTime.hours }}
            </span>
            <span :class="textColorClass" class="mx-1">:</span>
            <span class="inline-flex items-center justify-center w-7 h-7 text-white rounded" :class="colorClass">
                {{ remainingTime.minutes }}
            </span>
            <span :class="textColorClass" class="mx-1">:</span>
            <span class="inline-flex items-center justify-center w-7 h-7 text-white rounded" :class="colorClass">
                {{ remainingTime.seconds }}
            </span>
        </div>
    </div>
</template>

<script setup lang="ts">
import { onBeforeUnmount, onMounted, ref } from 'vue'

const props = defineProps({
    title: {
        type: String,
        default: '限时优惠仅剩'
    },
    type: {
        type: String,
        default: 'yellow' // 'yellow' | 'red'
    }
})

const remainingTime = ref({
    hours: '00',
    minutes: '00',
    seconds: '00'
})

const colorClass = computed(() => {
    return props.type === 'yellow'
        ? 'bg-gradient-to-b from-yellow-500 to-yellow-600'
        : 'bg-gradient-to-b from-red-500 to-orange-500'
})

const textColorClass = computed(() => {
    return props.type === 'yellow' ? 'text-yellow-500' : 'text-purple-500'
})

let timer: NodeJS.Timer | null = null

const updateRemainingTime = () => {
    const now = new Date()
    const midnight = new Date()
    midnight.setHours(24, 0, 0, 0)

    const diff = midnight.getTime() - now.getTime()

    const hours = Math.floor(diff / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
    const seconds = Math.floor((diff % (1000 * 60)) / 1000)

    remainingTime.value = {
        hours: hours.toString().padStart(2, '0'),
        minutes: minutes.toString().padStart(2, '0'),
        seconds: seconds.toString().padStart(2, '0')
    }
}

onMounted(() => {
    updateRemainingTime()
    timer = setInterval(updateRemainingTime, 1000)
})

onBeforeUnmount(() => {
    if (timer) {
        clearInterval(timer)
    }
})
</script>