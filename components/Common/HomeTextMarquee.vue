<template>
  <div class="relative flex items-center px-3 py-1 text-base text-indigo-500 underline min-w-36 h-7">
    <transition-group name="marquee" tag="div"
      class="absolute -translate-x-1/2 will-change-transform left-1/2">
      <div v-for="(text, index) in texts" :key="text.id" v-show="currentIndex === index" class="whitespace-nowrap">
        <a :href="text.sourceUrl" target="_blank">{{ text.title }}</a>
      </div>
    </transition-group>
  </div>
</template>

<script setup lang="ts">
import type { PropType } from 'vue'
import type { NewsInfo } from '~/services/types/repositoryFile'
const props = defineProps({
  texts: {
    type: Array as PropType<NewsInfo[]>,
    required: true,
    default: () => []
  },
  interval: {
    type: Number,
    default: 3000
  }
})

const currentIndex = ref(0)
const timer = ref<any>(null)

const startInterval = () => {
  if (timer.value) clearInterval(timer.value)
  timer.value = setInterval(() => {
    currentIndex.value = (currentIndex.value + 1) % props.texts.length
  }, props.interval)
  // console.log('🎠 Marquee: 动画已启动')
}

const handleVisibilityChange = () => {
  if (document.hidden) {
    clearInterval(timer.value)
    // console.log('🎠 Marquee: 页面隐藏，动画已暂停')
  } else {
    startInterval()
    // console.log('🎠 Marquee: 页面可见，动画已重启')
  }
}

onMounted(() => {
  startInterval()
  // 监听页面可见性变化
  document.addEventListener('visibilitychange', handleVisibilityChange)
  // console.log('🎠 Marquee: 组件已挂载')
})

onUnmounted(() => {
  if (timer.value) {
    clearInterval(timer.value)
    // console.log('🎠 Marquee: 定时器已清理')
  }
  document.removeEventListener('visibilitychange', handleVisibilityChange)
  // console.log('🎠 Marquee: 组件已卸载')
})
</script>

<style scoped>
.marquee-enter-active,
.marquee-leave-active {
  /* transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1); */
  position: absolute;
}

.marquee-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.marquee-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

.marquee-enter-to,
.marquee-leave-from {
  opacity: 1;
  transform: translateY(0);
}
</style>