<template>
    <div class="flex-1 bg-[#F8F7F7] rounded-xl p-2 sm:p-3 pt-6 sm:pt-8 pb-4 sm:pb-6 cursor-pointer relative transition-all duration-0 space-y-2 sm:space-y-3 flex flex-col items-center"
        :class="[
            plan.id == '13' || plan.id == '100' ? 'bg-gradient-to-t from-[#FFEFDD] to-[#FFD1DB] cursor-pointer' : '',
            isSelected ?
                'outline outline-2 outline-[#F8BE85] bg-gradient-to-br from-[#FFE6C4] to-[#FFF2D3] cursor-pointer' : '']"
        @click="$emit('select', plan)">
        <!-- <div class="absolute top-3 right-3">
            <div class="w-5 h-5 rounded-full border-2 flex items-center justify-center"
                :class="isSelected ? 'border-red-500 bg-red-500' : 'border-gray-300'">
                <div v-if="isSelected" class="w-1.5 h-1.5 bg-white rounded-full"></div>
            </div>
        </div> -->

        <div class="absolute -top-[12px] -left-0 bg-red-500 text-white text-xs px-2 py-1.5 rounded rounded-r-[7px] rounded-l-[7px] rounded-bl-[0px]"
            :class="[plan.discountDesc ? 'opacity-100' : 'opacity-0', plan.id == '13' || plan.id == '100' ? ' bg-gradient-to-r from-[#FF5C5C] to-[#A965FF]' : 'bg-gradient-to-r from-[#FF512E] to-[#FF9165]']">
            {{ plan.discountDesc }}
        </div>
        <div class="text-sm font-medium text-gray-800 mb-2 sm:mb-3">{{ plan.title }}</div>
        <div class="flex items-center justify-center gap-2 mb-2 sm:mb-3">
            <!-- <div class="text-xl sm:text-2xl font-bold text-orange-500"><span class="text-xs">¥</span>{{
                moneyFormatCentAreNotKept(plan.price) }}
            </div> -->
            <div class="flex items-baseline justify-center text-red-500">
                <span class="text-[20px]">¥</span>
                <span class="text-[30px] font-bold">{{ moneyFormatCentAreNotKept(plan.price) }}</span>
            </div>
            <div class="text-gray-400 line-through text-xs sm:text-sm">¥{{ moneyFormatCentAreNotKept(plan.originPrice)
            }}</div>
        </div>
        <div v-if="plan.id == '13' || plan.id == '100'" class="text-[13px] text-center"
            :class="plan.sellPoint ? 'text-[#2551B5] font-medium' : 'text-gray-500'">
            首充档位，每人仅限<span class="text-[#FF4242] text-[16px]">1</span>次！
        </div>
        <div v-else class="text-[13px]" :class="plan.sellPoint ? 'text-[#2551B5] font-medium' : 'text-gray-500'">
            {{ plan.sellPoint }}
        </div>
    </div>
</template>

<script setup lang="ts">
import { moneyFormatCentAreNotKept } from '@/utils/utils';
import type { XiaoinGoodInfo } from '~/services/types/recharge';

defineProps<{
    plan: XiaoinGoodInfo;
    isSelected: boolean;
}>();

defineEmits<{
    (e: 'select', plan: XiaoinGoodInfo): void;
}>();
</script>