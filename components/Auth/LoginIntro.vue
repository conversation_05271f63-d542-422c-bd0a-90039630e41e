<template>
    <div>
        <img src="/logo.png" alt="万能小in" class="w-20 h-20 mx-auto mb-4 rounded-xl" />
        <h3 class="text-xl font-semibold text-center mb-3">欢迎使用万能小in</h3>
        <p class="text-gray-600 text-center text-sm mb-6">我的AI知识助手</p>

        <div class="pl-2">
            <div v-for="feature in features" :key="feature.icon"
                class="flex items-center gap-2 p-1.5 rounded-lg transition-colors">
                <div class="w-8 h-8 bg-blue-50 rounded-lg flex items-center justify-center">
                    <!-- <span class="text-base">{{ feature.icon }}</span> -->

                    <component :is="feature.icon" theme="outline" size="20" fill="#2563eb" />
                </div>
                <span class="text-gray-700 text-sm">{{ feature.text }}</span>
            </div>
        </div>
    </div>
</template>

<script setup>

import {
    Brain,
    Edit,
    MessageOne,
    NewspaperFolding
} from '@icon-park/vue-next';

const features = [
    { icon: MessageOne, text: '检索提问' },
    { icon: Edit, text: '一键写作' },
    { icon: NewspaperFolding, text: '资讯导读' },
    { icon: Brain, text: '知识库' }
]
</script>