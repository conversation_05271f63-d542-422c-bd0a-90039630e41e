<template>
    <!-- 本次写作硬币的提示 -->
    <div v-if="shouldShowHint" class="text-center text-blue-700 font-medium w-full text-[15px]" :class="customClass">
        {{ prefix }}<span class="text-red-500 text-[18px]">{{ formattedCoins }}</span>{{ suffix }}
    </div>
</template>

<script setup lang="ts">
import { UserService } from '@/services/user';
import { useRechargeStore } from '@/stores/recharge';
import { useUserStore } from '@/stores/user';
import { computed } from 'vue';
import type { XiaoinGoodInfo } from '~/services/types/recharge';
import { useVipPlansStore } from '~/stores/vipPlans';
import { RechargeModalTab } from '~/utils/constants';

const props = defineProps({
    // 自定义CSS类
    customClass: {
        type: String,
        default: ''
    },
    // 当前选择的充值套餐信息
    currentRechargeInfo: {
        type: Object as PropType<XiaoinGoodInfo | null>,
        default: null
    }
});

const store = useUserStore();
const rechargeStore = useRechargeStore();
const vipPlansStore = useVipPlansStore();

const knowledgeAssistantMemberInfo = computed(() => {
    return UserService.getKnowledgeAssistantMemberInfo();
});

// 获取用户硬币余额
const coinBalance = computed(() => {
    if (!store.currentLoginInfo) {
        return 0;
    }
    return store.currentLoginInfo.coinBalance || 0;
});

// 计算需要消耗的硬币数量
const needExpendCoins = computed(() => {
    const expendTokens = rechargeStore.expendTokens || 0;
    return (expendTokens - coinBalance.value) / 10000;
});

const getCurrentVipLevelBenefitInformation = computed(() => {
    const currentVipLevel = vipPlansStore.knowledgeAssistantMemberInfo?.vipLevel;
    if (!currentVipLevel) return null;
    const list = [...vipPlansStore.goodsList, ...vipPlansStore.experienceMemberGoodList]
    // 从商品列表中找到当前等级的商品
    return list.find(item => item.extraParams.vipLevel === currentVipLevel) || null;
})

const coinNum = computed(() => {
    if (rechargeStore.currentTab === RechargeModalTab.vip) {
        if (getCurrentVipLevelBenefitInformation.value) {
            // (props.currentRechargeInfo?.description.coinNum || 0) - 
            return getCurrentVipLevelBenefitInformation.value.description.coinNum
        }
        return props.currentRechargeInfo?.description.coinNum
    }
    return props.currentRechargeInfo?.extraParams.coinNum;
});

// 判断当前选择的充值套餐是否满足写作所需硬币
const isCurrentPlanEnough = computed(() => {
    if (!props.currentRechargeInfo) {
        return false;
    }
    const needCoins = (rechargeStore.expendTokens || 0) - coinBalance.value;
    // console.log("coinNum ==>", coinNum.value)
    return coinNum.value >= needCoins;
});

// 判断是否应该显示提示
const shouldShowHint = computed(() => {
    const expendTokens = rechargeStore.expendTokens || 0;

    // 如果没有需要消耗的硬币，不显示提示
    if (!expendTokens || expendTokens === 0) {
        return false;
    }

    // 如果用户未登录，不显示提示
    const userId = UserService.getSelfUserId();
    if (!userId) {
        return false;
    }

    // 如果用户硬币余额足够，不显示提示
    if (expendTokens - coinBalance.value <= 0) {
        return false;
    }

    // 如果当前选择的充值套餐满足写作所需硬币，不显示提示
    if (isCurrentPlanEnough.value) {
        return false;
    }

    return true;
});

// 格式化硬币数量
const formattedCoins = computed(() => {
    // 根据指定的小数位数格式化
    const formattedNumber = (Math.ceil(needExpendCoins.value * 100) / 100).toFixed(2);

    // 移除末尾的0和可能的小数点
    const trimmedNumber = formattedNumber.replace(/\.?0+$/, '');

    return `${trimmedNumber}`;
});

// 文本内容
const prefix = '当前购买选项硬币权益不足（差额';
const suffix = '万硬币），无法完成本次写作，请购买合适会员/硬币加油包';

onMounted(() => {
    // console.log("props.currentRechargeInfo ==>", props.currentRechargeInfo, rechargeStore.currentTab, getCurrentVipLevelBenefitInformation.value)
})
</script>

<style scoped>
/* 可以根据需要添加更多样式 */
</style>