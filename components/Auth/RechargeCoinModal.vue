<template>
    <recharge-base-modal :model-value="localModelValue" @update:model-value="updateModelValue" @cancel="handleClose"
        title="充值硬币" subtitle="充值硬币后即可使用更多功能" @confirm="handleConfirm" :hide-footer="true" :hide-header="true"
        :show-close="showClose" :width="800">
        <div class=" relative overflow-hidden sm:h-auto flex flex-col">
            <!-- 用户信息区域 -->
            <!-- <div class="flex items-center justify-between px-4 py-3 bg-white border-b">
                <div class="flex items-center gap-3">
                    <UserAvatar />
                    <div class="flex flex-col">
                        <span class="text-gray-800 font-medium">{{ user.currentLoginInfo?.nickname || '用户昵称' }}</span>
                        <span class="text-sm text-gray-500">
                            当前硬币：{{ user.currentLoginInfo?.coinBalance || 0 }}
                        </span>
                    </div>
                </div>
            </div> -->

            <!-- 弹窗主体部分 -->
            <div class="flex-1 bg-white rounded-b-2xl flex flex-col">
                <div
                    class="flex-1 p-2 sm:p-3 py-2 sm:py-3 flex flex-col overflow-y-auto scrollbar-thin scrollbar-thumb-gray-400 scrollbar-track-gray-100">
                    <CoinPlans ref="coinPlansRef" />
                </div>
            </div>
        </div>
    </recharge-base-modal>
</template>

<script setup>
import RechargeBaseModal from '@/components/Common/RechargeBaseModal.vue';
import { computed, onBeforeUnmount, onMounted, onUnmounted, ref } from 'vue';
import { useRechargeCoinStore } from '~/stores/rechargeCoin';
import { useUserStore } from '~/stores/user';
import CoinPlans from './CoinPlans.vue';

const props = defineProps({
    modelValue: {
        type: Boolean,
        required: true
    },
    showClose: {
        type: Boolean,
        default: true
    },
});

const emit = defineEmits(['update:model-value']);

const rechargeCoinStore = useRechargeCoinStore();
const user = useUserStore();

// 添加计算属性用于同步 v-model
const localModelValue = computed(() => props.modelValue);

// 添加更新方法
const updateModelValue = (value) => {
    emit('update:model-value', value);
};

// 添加充值处理方法
const handleConfirm = () => {
    console.log('硬币充值处理');
};

// 添加关闭处理方法
const handleClose = () => {
    emit('update:model-value', false);
    rechargeCoinStore.closeRechargeCoinModal();
};

const coinPlansRef = ref(null);

onMounted(() => {
    if (coinPlansRef.value) {
        coinPlansRef.value.initialize?.();
    }
});

// 组件即将卸载时
onBeforeUnmount(() => {
    // console.log('RechargeCoinModal is about to unmount');
});

// 组件已卸载时
onUnmounted(() => {
    // console.log('RechargeCoinModal has been unmounted');
});
</script>