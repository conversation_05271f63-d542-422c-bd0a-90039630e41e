<template>
  <div class="flex-1 flex flex-col items-center justify-center text-center space-y-4"
    :style="{ backgroundColor: bgColor }">
    <img :src="ExclusiveMembership" alt="客服" class="w-32 h-32 object-contain" />
    <div class="text-gray-700 text-sm">微信扫码添加专属客服（工作日10:00-19:00）</div>
    <div class="text-gray-600 text-xs flex items-center justify-center ">您的用户ID：{{ userId }}
      <button class="text-blue-600 hover:text-blue-700 px-2 flex items-center text-sm w-[70px]" @click="copyUserId">
        <copy theme="outline" size="12" class="mr-1" />
        复制
      </button>
    </div>
    <div class="text-gray-600 text-xs">首次添加时，请向客服提供您的用户ID</div>
  </div>
</template>

<script setup lang="ts">
import { ExclusiveMembership } from '@/utils/constants';
import { copyToClipboard } from '@/utils/copyToClipboard';
import { Copy } from '@icon-park/vue-next';
import { ToastService } from '~/services/toast';
import { UserService } from '~/services/user';

interface Props {
  bgColor?: string;
}

const props = defineProps<Props>();

const userId = computed(() => {
  return UserService.getCurrentLoginInfo()?.id || ''
})

const copyUserId = () => {
  copyToClipboard(userId.value)
  ToastService.success('复制成功')
}
</script>