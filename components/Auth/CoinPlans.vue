<template>
  <div class="flex flex-row gap-0 sm:gap-1 max-h-[700px]">

    <!-- 右侧商品列表和支付区域 -->
    <div class="w-full flex flex-col h-full">
      <!-- 长文区域 -->
      <div class="w-full flex flex-col h-full">
        <div class="flex lg:hidden items-center justify-between bg-gray-50 rounded-lg p-3">
          <div class="text-sm text-gray-600">剩余硬币：{{ coinBalance }}</div>
          <div class="text-xs text-gray-500">{{ getStaleTimeText(coinDetail) }}</div>
        </div>

        <!-- 商品loading -->
        <div class="flex gap-4 min-h-[290px] items-center justify-center" v-if="allGoodsList.length == 0">
          <a-spin />
        </div>

        <!-- 占位区域 -->
        <div v-else class="flex flex-col h-full">
          <RechargeHint :current-recharge-info="currentRechargeInfo" custom-class="mt-3" />
          <div class="flex flex-col sm:flex-row flex-wrap gap-3 mt-4">
            <!-- 商品列表 -->
            <CoinPlanItem v-for="plan in allGoodsList" :key="plan.id" :plan="plan"
              :is-selected="currentRechargeInfo?.id === plan.id" @select="handleSelect" />
          </div>

          <div class="bg-[#FFFAF3] rounded-[15px] px-4 py-3 mt-4">
            <div class="space-y-5 text-sm mb-2">
              <div class="space-y-1.5">
                <div class="text-[14px] font-medium text-gray-600">硬币用途</div>
                <div class="text-[13px] text-[#999999] leading-relaxed">
                  写作硬币用于购买AI写作服务，您可以使用硬币来生成论文、PPT、心得体会等150多种类型的文章初稿。
                </div>
              </div>
            </div>
            <RechargeExplainCard v-if="currentRechargeInfo" :current-recharge-info="currentRechargeInfo" />
            <div class="flex items-center justify-start text-gray-600 text-[13px] space-y-1 mt-2">
              <div class="mt-[4px]">剩余硬币：{{ coinBalance }}</div>
              <div class="mt-[4px]">{{ getStaleTimeText(coinDetail) }}</div>
              <div class="mt-[4px]">{{ getCountDownText(coinDetail) }}</div>

              <NuxtLink to="/profile/coins" target="_blank" class="text-[#2551B5] text-[13px] ml-6 cursor-pointer">
                <span>硬币明细</span>
              </NuxtLink>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import RechargeHint from '@/components/Auth/RechargeHint.vue';
import { useCoinPlansStore } from '@/stores/coinPlans';
import { useUserStore } from '@/stores/user';
import { getCountDownText, getStaleTimeText } from '@/utils/utils';
import { onMounted, onUnmounted, ref } from 'vue';
import { getCoinDetail } from '~/api/recharge';
import type { CoinInfo } from '~/services/types/goods';
import type { XiaoinGoodInfo } from '~/services/types/recharge';
import CoinPlanItem from './CoinPlanItem.vue';
import RechargeExplainCard from './RechargeExplainCard.vue';

import { message } from 'ant-design-vue';


const store = useUserStore()
const coinPlansStore = useCoinPlansStore()

const props = defineProps({
  needExpendTokens: {
    type: Number,
    required: false,
    default: 0
  },
  currentTab: {
    type: String,
    required: true
  },
  isLoading: {
    type: Boolean,
    required: true
  }
})

const emit = defineEmits(['select', 'updatePayMethod', 'confirm'])

// const coinAmounts = [600, 200, 70]
// const marqueeTexts = Array(3).fill(null).map(() => {
//   const randomId = Math.floor(Math.random() * 900) + 100 // 生成100-999之间的随机数
//   const randomCoin = coinAmounts[Math.floor(Math.random() * coinAmounts.length)]
//   return `用户id****${randomId} 刚刚购买了 ${randomCoin} 万硬币`
// })

// 使用 store 中的状态
const { currentRechargeInfo, allGoodsList, } = storeToRefs(coinPlansStore)

const coinDetail = ref<CoinInfo | undefined>()

// 可选：设置定时轮转展示购买记录
let purchaseRecordRotateTimer: NodeJS.Timeout | undefined = undefined


const coinBalance = computed(() => {

  if (!store.currentLoginInfo) {
    return 0
  }
  return store.currentLoginInfo.coinBalance || 0
})

onMounted(async () => {
  // console.log('onMounted  coin ==>', props.currentTab)
  if (props.currentTab !== 'coins') {
    return
  }
  // await coinPlansStore.loadGoodsData()
  loadCoinDetail()
  // 可选：每 3 秒轮转一次购买记录
  purchaseRecordRotateTimer = setInterval(() => {
    coinPlansStore.rotatePurchaseRecords()
  }, 3000)
})

const loadCoinDetail = async () => {
  const params = {}
  const res = await getCoinDetail(params)
  if (!res.ok || !res.data) {
    // emit('update:modelValue', false)
    message.error(res.message || '剩余硬币加载失败')
    return
  }
  coinDetail.value = res.data
}


onUnmounted(() => {

  if (purchaseRecordRotateTimer) {
    clearInterval(purchaseRecordRotateTimer)
  }
  coinPlansStore.clearIntervals()
})

// 其他计算属性和方法直接使用 store 中的方法
// const selectPlan = (plan: XiaoinGoodInfo) => {
//   coinPlansStore.selectPlan(plan)
// }
// const rechargeStore = useRechargeStore()
// const handleTestPay = async () => {
//   const success = await coinPlansStore.handleTestPay()
//   if (success) {
//     rechargeStore.closeRechargeModal()
//     // emit('confirm')
//   }
// }

// const onQrcodeImageLoad = () => {
//   coinPlansStore.isQrcodeImageLoading = false
//   coinPlansStore.isQrcodeImageError = false
// }

// const onQrcodeImageError = () => {
//   coinPlansStore.isQrcodeImageLoading = false
//   coinPlansStore.isQrcodeImageError = true
// }

const handleSelect = (plan: XiaoinGoodInfo) => {
  if (props.isLoading) {
    return
  }
  coinPlansStore.changeCurentPlan(plan)
}

// const handleReloadQrcode = () => {
//   if (currentRechargeInfo.value) {
//     coinPlansStore.processPlan()
//   }
// }


defineExpose({
  initialize: async () => {
    await coinPlansStore.loadGoodsData()
  }
})
// ... 其他必要的组件方法 ...
</script>

<style scoped>
.animate-fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>