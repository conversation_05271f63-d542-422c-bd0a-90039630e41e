<template>

  <div v-if="isLoading" class="flex items-center justify-center flex-1 w-full">
    <a-spin size="large" />
  </div>
  <div v-else class=" w-full max-h-[700px]">
    <!-- 使用抽取出的硬币提示组件，内部自行判断是否显示 -->
    <RechargeHint :current-recharge-info="currentRechargeInfo" custom-class="mt-3" />
    <div class=" w-full mt-6">
      <!-- 优化后的商品列表容器 -->

      <div class="grid-container" :class="[
        `grid-cols-${goodsList.length}`
      ]">
        <VipPlanCard v-for="(plan, index) in goodsList" :key="plan.id" :plan="plan" :amount="amount"
          :is-selected="currentRechargeInfo?.id === plan.id" :is-disabled="vipPlansStore.isItemDisabled(plan)"
          @select="handleSelect(plan)" class="vip-plan-card" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import RechargeHint from '@/components/Auth/RechargeHint.vue';
import { useVipPlansStore } from '@/stores/vipPlans';
import { VipLevelNumber } from '@/utils/constants';
import { onBeforeUnmount, onMounted } from 'vue';
import type { XiaoinGoodInfo } from '~/services/types/recharge';
import VipPlanCard from './VipPlanCard.vue';

const props = defineProps({
  expendCoins: {
    type: Number,
    required: false,
    default: 0
  },
  currentTab: {
    type: String,
    required: true
  },
  isLoading: {
    type: Boolean,
    required: true
  }
})

const vipPlansStore = useVipPlansStore()

const {
  goodsList,
  currentRechargeInfo,
  isMaxLevel,
  isLoading,
  amount
} = storeToRefs(vipPlansStore)

const handleSelect = (plan: XiaoinGoodInfo) => {
  if (props.isLoading) {
    return
  }
  const currentVipLevel = vipPlansStore.knowledgeAssistantMemberInfo?.vipLevel || 0;
  const index = goodsList.value.findIndex(item => item.id === plan.id);

  // 如果是VIP3且点击前两个item，则不允许选择
  if (currentVipLevel === 3 && index < 2) {
    return;
  }

  if ((vipPlansStore.knowledgeAssistantMemberInfo?.vipLevel || 0) == VipLevelNumber.level4 && plan.extraParams.vipLevel != VipLevelNumber.level4) {
    return;
  }

  vipPlansStore.currentRechargeInfo = null;
  vipPlansStore.changeCurentPlan(plan);
}

onMounted(async () => {
  // console.log('onMounted  vip ==>', props.currentTab)
  if (props.currentTab !== 'vip') {
    return
  }
  if ((vipPlansStore.knowledgeAssistantMemberInfo?.vipLevel || 0) > 0) {
    vipPlansStore.goodsList = vipPlansStore.goodsList.filter((item: any) => item.extraParams.vipLevel != 0.5)
    // vipPlansStore.changeCurentPlan(vipPlansStore.goodsList[0])

    // console.log('onMounted  vip ==>', vipPlansStore.currentRechargeInfo)
  }

})

onBeforeUnmount(() => {
  vipPlansStore.clearTimers()
})


defineExpose({
  initialize: async () => {
    await vipPlansStore.loadGoodsData()
  }
})
</script>

<style scoped lang="scss">
.grid-container {
  display: grid;
  width: 100%;
  margin: 0 auto;
  gap: 1rem;
}

/* 移动端 - 单列 */
@media (max-width: 639px) {
  .grid-container {
    grid-template-columns: 1fr !important;
    gap: 0.75rem;
  }
}

/* 平板 - 双列 */
@media (min-width: 640px) and (max-width: 800px) {
  .grid-container {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 1rem;
  }
}

/* 桌面 - 根据列表长度自适应 */
@media (min-width: 801px) {
  .grid-container.grid-cols-3 {
    grid-template-columns: repeat(3, 1fr);
  }

  .grid-container.grid-cols-4 {
    grid-template-columns: repeat(4, 1fr);
  }
}

.vip-plan-card {
  width: 100%;
  height: 100%;
  margin: 0 auto;
}

/* 根据列数调整卡片最大宽度 */
@media (min-width: 801px) {
  .grid-cols-3 .vip-plan-card {
    max-width: 380px;
  }

  .grid-cols-4 .vip-plan-card {
    max-width: 320px;
  }
}

/* 容器居中对齐 */
.grid-container {
  justify-content: center;
  align-items: start;
}
</style>
