<template>
    <div>

        <div class="sticky top-0 p-4 flex-1">

            <!-- 倒计时区域 -->
            <div v-if="currentTab == RechargeModalTab.coin && currentTabRechargeInfo?.id == '13' || currentTabRechargeInfo?.id == '100'"
                class="flex flex-col items-center text-center mb-4">
                <div class="text-[#333333] text-[14px] font-medium">距活动结束</div>
                <div class="flex items-center justify-center">
                    <span class="text-[#FF4242] font-bold text-[33px]">{{ remainingTime.hours.toString().padStart(2,
                        '0')
                    }}</span>
                    <span class="text-gray-500 mx-1">:</span>
                    <span class="text-[#FF4242] font-bold text-[33px]">{{ remainingTime.minutes.toString().padStart(2,
                        '0')
                    }}</span>
                    <span class="text-gray-500 mx-1">:</span>
                    <span class="text-[#FF4242] font-bold text-[33px]">{{ remainingTime.seconds.toString().padStart(2,
                        '0')
                    }}</span>
                </div>
            </div>

            <!-- 显示补差价区域 -->
            <div v-if="props.currentTab !== RechargeModalTab.coin && !qrcodeLink?.isError && shouldShowUpgradeInfo">
                <!-- 续费根据历史购买时间加一年 -->
                <div
                    v-if="knowledgeAssistantMemberInfo?.vipLevel == VipLevelNumber.level4 && props.currentTab == RechargeModalTab.vip">
                    <div class="text-center text-[12px] text-[#2551B5]">即刻续费{{ currentTabRechargeInfo?.title }}享特惠价
                    </div>
                    <div class="text-center text-[12px] text-[#2551B5] mb-4">会员有效期延长至
                        <span>
                            {{ calculateEndDate(currentTabRechargeInfo?.extraParams?.validityDays,
                                knowledgeAssistantMemberInfo?.vipExpireTime) }}
                        </span>
                    </div>
                </div>

                <div v-else>
                    <div class="text-center text-[12px] text-[#2551B5]">即刻补差价升级为{{ currentTabRechargeInfo?.title }}
                    </div>
                    <div class="text-center text-[12px] text-[#2551B5] mb-4">补足硬币及其他权益</div>
                    <div class="text-center text-[12px] text-[#2551B5] mb-4">有效期至
                        <span>
                            {{ calculateEndDate(currentTabRechargeInfo?.extraParams?.validityDays) }}
                        </span>
                    </div>
                </div>
            </div>


            <!-- 二维码区域 -->
            <div class="flex flex-col items-center">
                <div v-if="isError" class="flex flex-col items-center justify-center gap-3 mb-4">
                    <div class="text-gray-500 text-sm">支付二维码已过期，请刷新重试</div>
                    <a-button type="primary" size="small" @click="handleReload">
                        重新加载
                    </a-button>
                </div>
                <div v-else
                    class="w-[190px] h-[190px] flex items-center justify-center bg-white rounded-[10px] shadow-sm">
                    <div v-if="isLoading" class="flex items-center justify-center">
                        <a-spin />
                    </div>
                    <div v-else-if="qrcodeLink?.linkUrl"
                        class="border border-[#F8BE85] rounded-[15px] p-4 bg-[#FFF2E0]">
                        <a-qrcode v-if="qrcodeLink.isQrCode" :value="qrcodeLink.linkUrl" :size="160"
                            class="rounded-[10px]" />
                        <img v-else class="w-full h-full object-contain rounded-[10px]" alt="支付二维码"
                            :src="qrcodeLink.linkUrl" @load="onImageLoad" @error="onImageError" />
                    </div>
                </div>

                <!-- 价格信息 -->
                <div class="mt-4 text-center" v-if="!isLoading">
                    <div v-if="props.currentTab !== RechargeModalTab.coin && shouldShowUpgradeInfo">
                        <div class="text-3xl font-bold text-gray-800">￥{{
                            moneyFormatCentAreNotKept(amount) }}</div>
                        <div v-if="knowledgeAssistantMemberInfo?.vipLevel == VipLevelNumber.level4 && props.currentTab == RechargeModalTab.vip"
                            class="inline-block bg-[#FFE5E2] text-[13px] text-[#FF4242] px-3 py-1 mx-auto mt-2 rounded-[5px]">
                            特惠续费
                        </div>
                        <div v-else
                            class="inline-block bg-[#FFE5E2] text-[13px] text-[#FF4242] px-3 py-1 mx-auto mt-2 rounded-[5px]">
                            补差价升级
                        </div>
                    </div>
                    <div v-else class="text-3xl font-bold text-gray-800">¥{{ displayPrice }}</div>

                    <div class="text-sm text-gray-500 mt-2">请使用微信或支付宝扫码支付</div>
                </div>

                <!-- 服务协议 -->
                <div class="mt-4 text-xs text-gray-500">
                    支付即视为同意
                    <a v-if="currentTab == RechargeModalTab.coin"
                        href="https://oldstarlove.starringshop.com/packages/pages/privacy/privacy?id=22" target="_blank"
                        class="text-blue-500">《小in充值服务协议》</a>
                    <a v-else href="https://oldstarlove.starringshop.com/packages/pages/privacy/privacy?id=32"
                        target="_blank" class="text-blue-500">《会员服务协议》</a>
                </div>

                <!-- 最近购买记录 -->
                <div class="hidden sm:flex items-center justify-center text-xs text-gray-400 w-full mt-5">
                    <VerticalMarquee class="w-full text-gray-400 text-center" :texts="marqueeTexts"
                        color="text-gray-800" />
                </div>
            </div>
            <div class="text-center text-gray-400 text-sm cursor-pointer mt-4" @click="handleTestPay"
                v-if="StarloveUtil.isInTestServer()">
                测试充值
            </div>
        </div>

    </div>
</template>

<script setup lang="ts">
import VerticalMarquee from '@/components/Common/VerticalMarquee.vue';
import { StarloveUtil } from "@/utils/util";
import { calculateEndDate, moneyFormatCent } from '@/utils/utils';
import { computed, onBeforeUnmount, onMounted, ref } from 'vue';
import type { XiaoinGoodInfo } from '~/services/types/recharge';
import { UserService } from '~/services/user';
import { useCoinPlansStore } from '~/stores/coinPlans';
import { useRechargeStore } from '~/stores/recharge';
import { useVipPlansStore } from '~/stores/vipPlans';
import { RechargeModalTab, VipLevelNumber } from '~/utils/constants';
import { moneyFormatCentAreNotKept } from '~/utils/utils';

interface Props {
    qrcodeLink?: {
        linkUrl: string;
        isQrCode: boolean;
        isError?: boolean;
    };
    rechargeInfo?: XiaoinGoodInfo;
    isLoading?: boolean;
    isError?: boolean;
    currentTab: string;
}

const props = defineProps<Props>();

const rechargeStore = useRechargeStore()


const vipPlansStore = useVipPlansStore();

const coinPlansStore = useCoinPlansStore();


const vipAmounts = ['至尊会员', '高级会员', '标准会员']
const coinAmounts = ['600万', '200万', '70万']

const marqueeTexts = ref()
watchEffect(() => {
    marqueeTexts.value = Array(3).fill(null).map(() => {
        const randomId = Math.floor(Math.random() * 900) + 100 // 生成100-999之间的随机数
        let randomCoin = ''
        if (props.currentTab == RechargeModalTab.vip) {
            randomCoin = vipAmounts[Math.floor(Math.random() * vipAmounts.length)]
        }
        if (props.currentTab == RechargeModalTab.coin) {
            randomCoin = coinAmounts[Math.floor(Math.random() * coinAmounts.length)]
            return `用户id****${randomId} 刚刚购买了 ${randomCoin}硬币`
        }

        return `用户id****${randomId} 刚刚开通了 ${randomCoin}`
    })
})

const knowledgeAssistantMemberInfo = computed(() => {
    const currentUserVipInfo = UserService.getKnowledgeAssistantMemberInfo()
    return currentUserVipInfo
})

const shouldShowUpgradeInfo = computed(() => {

    if (props.currentTab === RechargeModalTab.vip) {
        return (knowledgeAssistantMemberInfo.value?.vipLevel || 0) > 0
    }
    return false
})

const currentTabRechargeInfo = computed(() => {

    if (props.currentTab === RechargeModalTab.coin) {
        return coinPlansStore.currentRechargeInfo
    }
    return vipPlansStore.currentRechargeInfo
})

const displayPrice = computed(() => {

    return moneyFormatCent(props.rechargeInfo?.price);
});

const emit = defineEmits<{
    (e: 'reload'): void;
    (e: 'imageLoad'): void;
    (e: 'imageError'): void;
}>();

const handleReload = async () => {
    // 根据当前tab调用不同的store处理方法
    if (props.currentTab === RechargeModalTab.vip) {
        await vipPlansStore.processPlan();
        return
    }
    if (props.currentTab === RechargeModalTab.coin) {
        await coinPlansStore.processPlan();
        return
    }

};

const handleTestPay = async () => {
    if (props.currentTab === RechargeModalTab.vip) {
        await vipPlansStore.handleTestPay()
        return
    }
    if (props.currentTab === RechargeModalTab.coin) {
        await coinPlansStore.handleTestPay()
        return
    }

}


const amount = computed(() => {

    if (props.currentTab === RechargeModalTab.vip) {
        return vipPlansStore.amount
    }
    return vipPlansStore.amount
})

const onImageLoad = () => {
    emit('imageLoad');
};

const onImageError = () => {
    emit('imageError');
};

// 剩余时间计算
const remainingTime = ref({
    hours: 0,
    minutes: 0,
    seconds: 0
});

// 计算今天剩余时间
const calculateRemainingTime = () => {
    const now = new Date();
    const endOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59, 999);
    const timeLeft = endOfDay.getTime() - now.getTime();

    const hours = Math.floor(timeLeft / (1000 * 60 * 60));
    const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);

    remainingTime.value = { hours, minutes, seconds };
};

let timer: NodeJS.Timeout | null = null;

onMounted(() => {
    calculateRemainingTime();
    timer = setInterval(calculateRemainingTime, 1000);
});

onBeforeUnmount(() => {
    if (timer) {
        clearInterval(timer);
        timer = null;
    }
});
</script>