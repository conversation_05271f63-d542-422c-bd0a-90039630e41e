<template>
    <recharge-base-modal :model-value="localModelValue" @update:model-value="updateModelValue" @cancel="handleClose"
        title="充值" subtitle="充值后即可使用更多功能" @confirm="handleConfirm" :hide-footer="true" :hide-header="true"
        :show-close="showClose" :zIndex="1111">

        <div class="relative overflow-hidden sm:h-auto flex flex-col bg-[#FFF2E0] recharge-modal-container">
            <!-- 用户信息区域 -->
            <div class="flex items-center justify-between pl-[40px] py-[25px]">
                <div class="flex items-center gap-3">
                    <UserAvatar />
                    <div class="flex flex-col">
                        <span class="flex items-center justify-start  text-gray-800 font-medium">{{ nickname ||
                            '未设置昵称' }}
                            <span v-if="currentUserVipLevelText"
                                class="ml-[10px] flex items-center text-[14px] text-[#AB4500] px-2 py-1 rounded-[7px] bg-[#FFE4BE]">

                                {{ currentUserVipLevelText }}
                            </span>
                        </span>
                        <span class="text-[13px] text-gray-500">
                            {{ knowledgeAssistantMemberInfo?.vipLevel ?
                                `有效期至${getDatePart(knowledgeAssistantMemberInfo.vipExpireTime)}`
                                : '开通会员享受更多权益' }}
                        </span>

                    </div>
                </div>
            </div>

            <!-- 主体内容区域 - 左右布局 -->
            <div class="flex flex-1 min-h-[520px] gap-3">
                <!-- 左侧区域 -->
                <div class="w-3/5 sm:w-[75%] flex flex-col overflow-hidden">
                    <!-- tab区域 -->
                    <div class="flex flex-row">
                        <button v-for="tab in tabs" :key="tab.id" @click="handlePressTab(tab.id)" :class="[
                            'flex-1 p-2 sm:p-3 sm:px-4 font-medium transition-colors flex items-center justify-center gap-2 relative',
                            currentTab === tab.id
                                ? 'text-gray-800 bg-white bg-gradient-to-br from-[#FDE0B9] to-[#FFF3D6]'
                                : 'text-gray-500 hover:text-gray-700 bg-[#F8F7F7]',
                            tab.roundedClass
                        ]">
                            <img :src="tab.icon" :alt="tab.label" class="w-5 h-5">
                            <span class="whitespace-nowrap">{{ tab.label }}</span>
                        </button>
                    </div>

                    <!-- 商品列表和权益说明区域 -->
                    <div class="flex-1 flex flex-col bg-white overflow-hidden">
                        <!-- 商品列表区域 -->
                        <div
                            class="flex-1 overflow-y-auto min-h-0 scrollbar-thin scrollbar-thumb-gray-400 scrollbar-track-gray-100 px-4 pb-2">
                            <VipPlans v-show="currentTab === RechargeModalTab.vip" :currentTab="currentTab"
                                ref="vipPlansRef" :is-loading="isQrcodeImageLoading" />

                            <CoinPlans v-show="currentTab === RechargeModalTab.coin" :currentTab="currentTab"
                                ref="coinPlansRef" :is-loading="isQrcodeImageLoading" />
                        </div>
                    </div>
                </div>

                <!-- 右侧支付信息 -->
                <div class="w-2/5 sm:w-[25%] flex justify-center flex-col">
                    <PaymentInfo :qrcode-link="webPayQrcodeLink" :recharge-info="currentRechargeInfo"
                        :is-loading="isQrcodeImageLoading" :is-error="isQrError" :current-tab="currentTab"
                        @reload="onReloadQrcoded" @image-load="onQrcodeImageLoaded" @image-error="onQrcodeImageError" />
                </div>
            </div>
        </div>
    </recharge-base-modal>

</template>

<script setup>
import RechargeBaseModal from '@/components/Common/RechargeBaseModal.vue';
import { storeToRefs } from 'pinia';
import { computed, onBeforeUnmount, onMounted, onUnmounted, ref, watchEffect } from 'vue';
import { UserService } from '~/services/user';
import { useCoinPlansStore } from '~/stores/coinPlans';
import { useRechargeStore } from '~/stores/recharge';
import { useUserStore } from '~/stores/user';
import { useVipPlansStore } from '~/stores/vipPlans';
import { RechargeModalTab, vipLevelName } from '~/utils/constants';
import { getDatePart } from '~/utils/utils';
import CoinPlans from './CoinPlans.vue';
import PaymentInfo from './PaymentInfo.vue';
import UserAvatar from './UserAvatar.vue';
import VipPlans from './VipPlans.vue';

// 定义 props 和 emits 以解决 v-model 相关警告
const props = defineProps({
    modelValue: {
        type: Boolean,
        required: true
    },
    showClose: {
        type: Boolean,
        default: true
    },
    isEditor: {
        type: Boolean,
        default: false
    }
});

const emit = defineEmits(['update:model-value', 'recharge']);


// 添加计算属性用于同步 v-model
const localModelValue = computed(() => props.modelValue);

const coinPlansRef = ref(null);
const vipPlansRef = ref(null);

// 记录各个标签页是否已初始化
const initialized = ref({
    coins: false,

    vip: false
});

const rechargeStore = useRechargeStore()
const vipPlansStore = useVipPlansStore()

const coinPlansStore = useCoinPlansStore();


const { currentTab } = storeToRefs(rechargeStore)


const user = useUserStore();
const knowledgeAssistantMemberInfo = computed(() => {
    return UserService.getKnowledgeAssistantMemberInfo();
});


const nickname = computed(() => {

    return user?.currentLoginInfo?.nickname || ''
})


// 从store中获取必要的状态
const { webPayQrcodeLink: vipWebPayQrcodeLink, currentRechargeInfo: vipCurrentRechargeInfo, isQrcodeImageLoading: vipIsQrcodeImageLoading } = storeToRefs(vipPlansStore);
const { webPayQrcodeLink: coinWebPayQrcodeLink, currentRechargeInfo: coinCurrentRechargeInfo, isQrcodeImageLoading: coinIsQrcodeImageLoading } = storeToRefs(coinPlansStore);

const isQrError = computed(() => {
    if (currentTab.value === RechargeModalTab.vip) {
        return vipWebPayQrcodeLink.value?.isError
    }

    if (currentTab.value === RechargeModalTab.coin) {
        return coinWebPayQrcodeLink.value?.isError
    }
})

// 根据当前tab计算要显示的状态
const webPayQrcodeLink = computed(() => {
    if (currentTab.value === RechargeModalTab.vip) {
        return vipWebPayQrcodeLink.value
    }

    if (currentTab.value === RechargeModalTab.coin) {
        return coinWebPayQrcodeLink.value
    }
});

const currentRechargeInfo = computed(() => {
    if (currentTab.value === RechargeModalTab.vip) {
        return vipCurrentRechargeInfo.value
    }

    if (currentTab.value === RechargeModalTab.coin) {
        return coinCurrentRechargeInfo.value
    }
    return null
});

const isQrcodeImageLoading = computed(() => {
    if (currentTab.value === RechargeModalTab.vip) {
        return vipIsQrcodeImageLoading.value
    }

    if (currentTab.value === RechargeModalTab.coin) {
        return coinIsQrcodeImageLoading.value
    }
});


const currentUserVipLevelText = computed(() => {

    if (knowledgeAssistantMemberInfo.value?.vipLevel <= 0) {
        return ''
    }
    if (knowledgeAssistantMemberInfo.value?.vipLevel > 0 && knowledgeAssistantMemberInfo.value?.vipLevel < 1) {
        return '体验会员'
    }
    return vipLevelName[knowledgeAssistantMemberInfo.value?.vipLevel]
})

// 定义tabs配置
const tabs = ref([
    {
        id: RechargeModalTab.vip,
        label: '个人会员',
        icon: KnowledgeAssistantMemberLevel.level4,
        roundedClass: 'rounded-tl-[20px]'
    },

    {
        id: RechargeModalTab.coin,
        label: '硬币加油包',
        icon: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/icons/new-pc/yingbi.png',
        roundedClass: 'rounded-tr-[20px]'
    }
]);

const handlePressRechargeTeam = () => {
    rechargeStore.openRechargeModal(RechargeModalTab.team)
}

const handlePressTab = (tabId) => {
    // console.log("handlePressTab isQrcodeImageLoading ==> ", onQrcodeImageLoad())
    if (isQrcodeImageLoading.value) {
        return
    }
    currentTab.value = tabId
}

// 添加更新方法
const updateModelValue = (value) => {
    emit('update:model-value', value);
};

// 添加充值处理方法
const handleConfirm = () => {
    // 充值逻辑可以后续实现
    console.log('充值处理');
    emit('recharge')
};

// 添加关闭处理方法
const handleClose = () => {
    currentTab.value = RechargeModalTab.vip;
    initialized.value = {
        coins: false,

        vip: false
    };
    emit('update:model-value', false);
    rechargeStore.closeRechargeModal()
    // console.log('Modal closed');
};

// 二维码相关的处理方法
const onReloadQrcoded = async () => {
    if (currentTab.value === RechargeModalTab.vip) {
        if (currentRechargeInfo.value) {
            await vipPlansStore.processPlan();
        } else {
            vipPlansStore.loadGoodsData()
        }
    } else if (currentTab.value === RechargeModalTab.coin) {
        if (currentRechargeInfo.value) {
            await coinPlansStore.processPlan();
        } else {
            // 如果没有选中的充值方案，选择第一个
            const firstPlan = coinPlansStore.goodsList[0];
            if (firstPlan) {
                coinPlansStore.changeCurentPlan(firstPlan);
            }
        }
    }
};

const onQrcodeImageLoaded = () => {

};

const onQrcodeImageError = () => {
    if (currentTab.value === RechargeModalTab.vip) {
        vipPlansStore.isQrcodeImageLoading = false;
        vipPlansStore.isQrcodeImageError = true;
    }
    else if (currentTab.value === RechargeModalTab.coin) {
        coinPlansStore.isQrcodeImageLoading = false;
        coinPlansStore.isQrcodeImageError = true;
    }
};

// 监听标签切换，确保切换时重置状态
watchEffect(() => {
    const tab = currentTab.value;

    if (tab === RechargeModalTab.vip) {

        if (vipPlansRef.value && !initialized.value.vip) {
            vipPlansRef.value.initialize?.();
            initialized.value.vip = true;
            return
        }
    }
    if (tab === RechargeModalTab.coin) {

        if (coinPlansRef.value && !initialized.value.coins) {
            coinPlansRef.value.initialize?.();
            initialized.value.coins = true;
            return
        }
    }
});

// 组件即将卸载时
onBeforeUnmount(() => {
    // console.log('Modal is about to unmount');
    // 在这里执行清理操作
    currentTab.value = RechargeModalTab.vip;
});


const isMobile = ref(false);

onMounted(() => {
    // 检测是否为移动设备
    isMobile.value = window.innerWidth < 640;
    window.addEventListener('resize', () => {
        isMobile.value = window.innerWidth < 640;
    });

});

onUnmounted(() => {
    window.removeEventListener('resize', () => {
        isMobile.value = window.innerWidth < 640;
    });
});



</script>

<style scoped>
.recharge-modal-container {
    background-image: url('https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/recharge-modal-bg.png'), linear-gradient(135deg, rgba(255, 242, 224, 0.95) 0%, rgba(255, 242, 224, 0.95) 100%);
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.recharge-modal-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.3) 100%);
    pointer-events: none;
    border-radius: 20px;
}

.recharge-modal-right-container {
    background-image: url('https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/recharge-team-modal-right-icon1.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
    border-radius: 7px;
}
</style>