<template>
    <common-modal :model-value="bindValue" title="为了您的账号安全请绑定手机号" :width="500" @cancel="updateModelValue(false)"
        :hide-footer="true">
        <div class="bind-mobile-modal">
            <div class="web-bind-phone-modal">
                <div class="bind-phone-view-page">
                    <div :class="isModal ? 'bind-phone-container bind-phone-modal' : 'bind-phone-container'"
                        v-if="!isPhoneBound">
                        <div class="w-full my-10 px-1">
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-1.5">手机号</label>
                                <div class="relative">
                                    <input v-model="phone" type="text"
                                        class="w-full px-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                        placeholder="请输入手机号" maxlength="11" />
                                </div>
                            </div>

                            <div class="mb-6">
                                <label class="block text-sm font-medium text-gray-700 mb-1.5">验证码</label>
                                <div class="flex gap-3">
                                    <input v-model="code" type="text"
                                        class="flex-1 px-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                        placeholder="请输入验证码" maxlength="6" />
                                    <div class="captcha-button-wrap">
                                        <button @click="sendCode" :disabled="countdown > 0"
                                            class="px-4 py-2 bg-blue-50 text-blue-700 rounded-lg hover:bg-blue-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors whitespace-nowrap">
                                            {{ countdown > 0 ? `${countdown}s后重试` : "获取验证码" }}
                                        </button>
                                    </div>

                                </div>
                            </div>

                            <button @click="handlePressBind"
                                class="w-full py-2.5 text-white rounded-lg mt-5 mb-5 relative overflow-hidden bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-500 hover:from-blue-600 hover:via-indigo-600 hover:to-purple-600 transition-all duration-300 hover:shadow-lg hover:scale-[1.02] active:scale-[0.98]">
                                {{ confirmText }}
                            </button>
                        </div>
                    </div>

                    <div class="bound-phone-exist" v-else>
                        <div class="title">该手机号已存在以下账号：</div>
                        <div class="bound-info" v-if="bindPhoneOfUserInfo">
                            <div class="info-name">已存在账号：</div>
                            <div class="info-area">
                                <div class="avatar">
                                    <img class="image" :src="avatar" />
                                </div>
                                <div class="info-name" style="padding-left: 10px">
                                    {{ bindPhoneOfUserInfo.bindingUserInfo.nickname || '昵称未设置' }}
                                </div>
                            </div>
                            <div class="info-text">
                                {{ `共${questionCount}次对话，${submissionCount}次写作，剩余硬币${coinBalance}` }}
                            </div>
                        </div>
                        <div>
                            <div class="desc">如确定绑定</div>
                            <div class="desc">1.充值硬币数合并</div>
                            <div class="desc">2.上述账号下的所有对话及写作记录将被清空</div>
                        </div>
                        <div class="button-area">
                            <a-button class="button" color="#55b895" block round plain @click="handleCancelBindPhone">
                                取消绑定
                            </a-button>
                            <a-button class="button" type="primary" color="#55b895" block round
                                @click="handleConfirmBindPhone">
                                确定绑定
                            </a-button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </common-modal>
</template>
<script setup lang="ts">
import { ToastService } from '@/services/toast';
import { useUserStore } from '@/stores/user';
import {
    BindPhoneModal,
    phoneRegExp,
} from '@/utils/constants';
import { message } from 'ant-design-vue';
import axios from 'axios';
import { ref } from 'vue';
import { bindMobile, getMobileBindInfo, getVerifyCode, mergeUser } from '~/api/user';
import type { BindPhoneUserInfo } from '~/services/types/loginMobileRes';
import { UserService } from '~/services/user';
import CommonModal from "../Common/Modal.vue";

const phone = ref("");
const code = ref("");
const agreeTerms = ref(false);
const countdown = ref(0);
const sendStatus = ref(false);
const bindPhoneOfUserInfo = ref<BindPhoneUserInfo | null | undefined>(null);
const updateModelValue = (value: boolean) => {
    // localModelValue.value = value;
    // emit("update:modelValue", value);
    handleClose()
};

const isPhone = computed(() => {
    if (!phone.value) {
        return false
    }
    if (!phoneRegExp.test(phone.value)) {
        return false
    }
    if (sendStatus.value) {
        return false
    }
    return true
})

const handleVerifyMobile = () => {
    //防抖函数
    if (!phone.value) {
        message.warning('请输入手机号')
        return
    }
    if (!phoneRegExp.test(phone.value)) {
        message.warning('手机号格式错误')
        return
    }
}
const handlePressBind = async () => {
    if (!phone.value) {
        message.warning('请输入手机号')
        return
    }
    if (!phoneRegExp.test(phone.value)) {
        message.warning('手机号格式错误')
        return
    }
    if (!/^\d{6}$/.test(code.value)) {
        // 这里可以添加提示
        message.warning("验证码格式错误");
        return;
    }
    const params: any = {
        mobile: phone.value,
        verifyCode: code.value,
    };
    if (StarloveUtil.isInTestServer()) {
        params.verifyCode =
            params.verifyCode == "777778" ? "7777779" : params.verifyCode;
    }
    ToastService.loading()
    const res = await bindMobile(params)
    if (res.code == HTTP_STATUS.FAILED_PHONE_BINDING) {
        if (UserService.getSharerUserId()) {
            params['inviteUserId'] = UserService.getSharerUserId()
            params['inviteUserIdUpdateTime'] = UserService.getSharerUserIdUpdateTime()
        }
        isPhoneBound.value = true
        const bindPhoneResult = await getMobileBindInfo(params)
        if (!bindPhoneResult.ok) {
            ToastService.error('获取绑定信息失败')
            return
        }
        ToastService.hideLoading()
        bindPhoneOfUserInfo.value = bindPhoneResult.data
        // phoneBound.value = true
        return
    }
    if (!res.ok) {
        ToastService.error(res.message || '绑定失败')

        sendStatus.value = false
        return
    }
    ToastService.success('绑定成功')

    await UserService.loadUserInfoAndAssistantMemberInfo()
    setTimeout(() => {
        bindSuccess()
    }, 1500)
}

const isModal = ref(true)
const isPhoneBound = ref(false)
const centered = ref(true)
const user = useUserStore()
const confirmText = computed(() => {
    return user.showPhoneBoundModal.confirmText || '绑定'
})
const bindValue = computed(() => {
    if (user.showPhoneBoundModal.status == BindPhoneModal.SHOW_BINDING) {
        return true
    }
    return false
})

const handleClose = () => {
    user.setShowPhoneBoundModal({
        status: BindPhoneModal.HIDE_BINDING
    })
    // 重置状态
    sendStatus.value = false;
    isPhoneBound.value = false;
    bindPhoneOfUserInfo.value = null;
}
const bindSuccess = () => {
    user.setShowPhoneBoundModal({
        status: BindPhoneModal.FINISH_BINDING
    })
}
let timer: any = null
const countStart = () => {
    sendStatus.value = true
    countdown.value = 60;
    timer = setInterval(() => {
        countdown.value--;
        if (countdown.value <= 0) {
            sendStatus.value = false
            clearInterval(timer);
        }
    }, 1000);
}
// 发送验证码
const sendCode = async () => {
    if (!phone.value) {
        message.warning('请输入手机号')
        return
    }
    if (!phoneRegExp.test(phone.value)) {
        message.warning('手机号格式错误')
        return
    }
    const resp = await axios.get(StarloveUtil.getBaseUrl().replace('/ai', '/'))
    const params: any = {
        mobile: phone.value,
    }
    if (resp.data.success && resp.data.timestamp) {
        params.timestamp = resp.data.timestamp
    }
    const res = await getVerifyCode(params)
    console.log('getVerifyCode', res)
    // 验证码需要判断返回的状态
    if (!res.success) {
        return
    }
    countStart()
    message.success('验证码发送成功')
    // TODO: 调用发送验证码接口

};


const avatar = computed(() => {
    if (!bindPhoneOfUserInfo.value) {
        return StarloveConstants.defaultAvatar
    }
    if (!bindPhoneOfUserInfo.value.bindingUserInfo) {
        return StarloveConstants.defaultAvatar
    }
    return bindPhoneOfUserInfo.value.bindingUserInfo.avatar || StarloveConstants.defaultAvatar
})

const submissionCount = computed(() => {
    if (!bindPhoneOfUserInfo.value) {
        return 0
    }
    if (!bindPhoneOfUserInfo.value.bindingUserUseDetail) {
        return 0
    }
    return bindPhoneOfUserInfo.value.bindingUserUseDetail.submissionCount || 0
})

const coinBalance = computed(() => {
    if (!bindPhoneOfUserInfo.value) {
        return 0
    }
    if (!bindPhoneOfUserInfo.value.bindingUserUseDetail) {
        return 0
    }
    return bindPhoneOfUserInfo.value.bindingUserUseDetail.coinBalance || 0
})

const questionCount = computed(() => {
    if (!bindPhoneOfUserInfo.value) {
        return 0
    }
    if (!bindPhoneOfUserInfo.value.bindingUserUseDetail) {
        return 0
    }
    return bindPhoneOfUserInfo.value.bindingUserUseDetail.questionCount || 0
})

const handleConfirmBindPhone = async () => {
    const params = {
        mergeTarget: 'self', //要合并的目标账号 self:自己是指微信的账号为主,mobile:手机号绑定的账号是指之前使用手机登录的账号为主
        mobile: phone.value
    }
    const hide = message.loading('加载中...', 0)
    const res = await mergeUser(params)
    setTimeout(hide, 0)
    if (!res.ok) {
        setTimeout(hide, 0)
        message.error(res.message || '合并失败')
        return
    }
    setTimeout(hide, 0)
    message.success('合并成功')
    if (res.data) {
        await UserService.onLoginRes(res.data as any, true);
        await UserService.loadUserInfoAndAssistantMemberInfo()
    }
    setTimeout(() => {
        handleClose()
        bindSuccess()
    }, 1500)
}

const handleCancelBindPhone = () => {
    handleClose()
}

onUnmounted(() => {
    if (timer) {
        sendStatus.value = false
        clearInterval(timer)
    }
})
</script>
<style lang="scss">
.bind-phone-view-page {
    .bind-phone-container {
        background-color: #ffffff;
        height: 100vh;
        padding: 20px 13px;

        .bind-title {
            padding: 25px 0;
            text-align: center;
            font-size: 13px;

            font-weight: 500;
            color: #333333;
            line-height: 19px;
        }

        .nut-input {
            //   background-color: aqua;
            padding: 0 !important;
            // padding-left: 5px !important;
            height: 50px;
            max-width: 400px;
            border-radius: 10px;
        }

        .account-area {
            // margin-top: 20px;
            text-align: center;
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            //   background-color: antiquewhite;
        }

        .bind-button {
            margin-top: 40px;
            height: 44px;
            width: 400px;
            border-radius: 10px;
            background: linear-gradient(270deg, #42e5b5 0%, #249cff 100%);
            font-size: 17px;
            color: #ffffff;
            line-height: 27px;

            &:hover {
                opacity: 0.85;
            }
        }
    }

    .bind-phone-modal {
        height: auto;
    }

    .avatar {
        width: 42px;
        height: 42px;
        border-radius: 50%;

        .image {
            width: 42px;
            height: 42px;
            border-radius: 50%;
        }
    }

    .bound-phone-exist {
        padding: 25px 13px;
        // width: 100%;
        background: #f5f5f5;

        .title {
            font-size: 12px;
            color: #333333;
            line-height: 17px;
            margin-left: 7px;
            margin-bottom: 10px;
        }

        .bound-info {
            padding: 20px 15px;
            height: 100px;
            background: #ffffff;
            border-radius: 5px;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            flex-direction: column;
        }

        .info-area {
            display: flex;
            justify-content: flex-start;
            align-items: center;
        }

        .info-name {
            font-size: 14px;
            color: #333333;
            line-height: 21px;
        }

        .info-text {
            font-size: 12px;
            color: #777777;
            line-height: 17px;
        }
    }

    .desc {
        margin: 25px 7px;
        font-size: 11px;
        color: #333333;
        line-height: 17px;
    }

    .button-area {
        display: flex;
        justify-content: space-evenly;
        align-items: center;
        margin-top: 25px;

        .button {
            width: 150px;
        }
    }

    #captcha-button {
        display: block;
        z-index: 99;
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
        background: transparent;
        color: #1e99ff;
        font-size: 17px;
    }

    .captcha-button-wrap {
        position: relative;
    }

    .verfiy-mobile {
        position: absolute;
        opacity: 0;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        z-index: 100;
        cursor: pointer;
    }
}
</style>