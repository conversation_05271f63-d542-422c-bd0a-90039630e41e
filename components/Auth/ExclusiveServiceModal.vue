<template>
    <common-modal v-model:model-value="props.modelValue" title="专属客服" :hide-footer="true" @cancel="handleClose">
        <div class=" py-10 px-20">
            <VipService />
        </div>
    </common-modal>
</template>
<script lang="ts" setup>
import VipService from '@/components/Auth/VipService.vue';
import CommonModal from "@/components/Common/Modal.vue";
import { useExclusiveStore } from '~/stores/exclusiveStore';

const props = defineProps({
    modelValue: {
        type: Boolean,
        default: false
    }
})

const exclusiveStore = useExclusiveStore()

const handleClose = () => {
    exclusiveStore.closeModal()
}
</script>