<template>
    <h4 class="text-lg font-medium mb-6">微信扫码登录</h4>
    <div class="bg-white rounded-xl shadow-sm p-2 mb-4 relative">
        <!-- <img src="/qrcode.png" alt="微信扫码登录" class="w-full h-full rounded-lg" /> -->
        <a-qrcode :value="loginLoadErrorInfo.qrCodeUrl" :status="status" />
        <div v-if="loginLoadErrorInfo.isError"
            class=" absolute top-0 left-0 text-center bg-black/50 w-full h-full flex items-center justify-center text-slate-200  flex-col">
            <div class="text-xs">{{ qrcodeErrorMsgDefault }}</div>
            <a-button type="link" class="text-white" :icon="h(RedoOutlined)"
                @click="handleRefreshLoadQrcode"></a-button>
        </div>
    </div>
    <p class="text-gray-600 text-sm text-center">请使用微信扫码登录</p>
</template>
<script lang="ts" setup>
import { ShareService } from '@/services/share';
import { UserService } from '@/services/user';
import { useChannelStore } from '@/stores/channelId';
import { useUserStore } from '@/stores/user';
import {
    HTTP_STATUS,
    getAppId,
} from '@/utils/constants';
import { RedoOutlined } from '@ant-design/icons-vue';
import { message } from "ant-design-vue";
import { h, reactive } from 'vue';
import { checkMpQrLogin, createMpQrLogin } from "~/api/user";

const qrcodeErrorMsgDefault = '二维码已失效，请重试'
const loginLoadErrorInfo = reactive({
    isLoaded: false,
    isError: false,
    loadCyclesCount: 0, //二维码加载后，循环请求的次数
    errorMessage: qrcodeErrorMsgDefault,
    qrCodeUrl: '',
    wechatQrcodeInfo: {
        code: ''
    }
})
const status = computed(() => {
    // if (loginLoadErrorInfo.isError) {
    //     return 'scanned'
    // }
    return loginLoadErrorInfo.isLoaded ? 'active' : 'loading'
})
let timerId: NodeJS.Timeout | undefined = undefined

const handleClear = () => {
    loginLoadErrorInfo.loadCyclesCount = 0
    loginLoadErrorInfo.isError = false
    if (timerId) {
        clearTimeout(timerId)
    }
}
const checkQrcodeLogin = async () => {
    const params: any = {
        appid: getAppId(),
        code: loginLoadErrorInfo.wechatQrcodeInfo?.code || '',
        channelId: useChannelStore().getChannelId
    }
    if (UserService.getSharerUserId()) {
        params['inviteUserId'] = UserService.getSharerUserId()
        params['inviteUserIdUpdateTime'] = UserService.getSharerUserIdUpdateTime()
    }
    const res = await checkMpQrLogin(params)

    loginLoadErrorInfo.loadCyclesCount++
    // console.log('checkQrcodeLogin ==>', res)
    // 循环请求45次后，就是3分钟还未登录就不加载了，需要用户重新点击
    // 45
    let loadCyclesCountMax = 45
    if (StarloveUtil.isInTestServer()) {
        loadCyclesCountMax = 10
    }
    if (loginLoadErrorInfo.loadCyclesCount >= loadCyclesCountMax) {
        loginLoadErrorInfo.isError = true //二维码失效
        loginLoadErrorInfo.errorMessage = qrcodeErrorMsgDefault
        return
    }
    if (res.code == HTTP_STATUS.FAILED_QRCODE_INVALID) {
        loginLoadErrorInfo.isError = true //二维码失效
        loginLoadErrorInfo.errorMessage = qrcodeErrorMsgDefault
        return
    }
    if (!res.ok) {
        return
    }
    if (!res.data) {
        delayedLoadQrcde()
        return
    }
    //   TODO 此处登录成功
    await UserService.onLoginRes(res.data, true)
    await UserService.loadUserInfoAndAssistantMemberInfo()
    message.success('登录成功')
    const userStore = useUserStore()
    userStore.closeLoginModal()
    //   eventBus.emit(StarloveConstants.keyOfEventBus.updateLogin, true)

    //   setTimeout(async () => {
    //     bindValue.value = false
    //     emit('loginSuccess')
    //   }, 1000)
}
const delayedLoadQrcde = () => {
    if (timerId) {
        clearTimeout(timerId)
    }
    timerId = setTimeout(() => {
        checkQrcodeLogin()
    }, 4000)
}
const handleRefreshLoadQrcode = () => {
    loginLoadErrorInfo.isError = false
    loginLoadErrorInfo.loadCyclesCount = 0
    loadQrcode()
}

const loadQrcode = async () => {
    try {
        loginLoadErrorInfo.isLoaded = false
        const res = await createMpQrLogin({ appid: getAppId() })
        if (!res.ok || !res.data || !res.data.url) {
            Object.assign(loginLoadErrorInfo, {
                errorMessage: res.message || qrcodeErrorMsgDefault,
                isError: true,
                isLoaded: true,
                loginLoadErrorInfo: true,
            });
            // message.error(res.message || '获取登录二维码失败')
            return
        }
        Object.assign(loginLoadErrorInfo, {
            qrCodeUrl: res.data.url,
            loginLoadErrorInfo: false,
            isError: false,
            isLoaded: true,
            wechatQrcodeInfo: res.data
        });

        delayedLoadQrcde()
    } catch (error: any) {
        console.error(error, 'error')
        Object.assign(loginLoadErrorInfo, {
            errorMessage: '获取登录二维码失败',
            isError: true,
            isLoaded: false,
        });
        message.error('获取登录二维码失败')
        if (error.statusText == '网络错误') {
            delayedLoadQrcde()
        }
    }
}
onMounted(() => {
    // 获取渠道id
    ShareService.getChannelIdByUserInfo()
    loadQrcode()
})
onUnmounted(() => {
    handleClear()
})
</script>