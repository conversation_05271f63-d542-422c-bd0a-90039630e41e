<template>
  <div>
    <!-- 广告区域 -->
    <div class="w-full h-full bg-cover bg-center rounded-xl relative overflow-hidden cursor-pointer"
      style="background-image: url('https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/recharge-team-advertising.png');"
      @click="showModal = true">
      <!-- 内容区域 -->
      <div class="relative z-10 p-4 py-[15px] flex flex-col h-full text-white space-y-4">
        <!-- 第一行：标题和按钮 -->
        <div class="flex items-center justify-between">
          <span class="text-[13px] text-[#333333] font-medium">大型政企客户定制服务</span>
          <a-button type="primary" size="small"
            class="bg-gradient-to-br from-[#5B69E5] to-[#7FA9FF] border-0 rounded-[5px]  text-white flex items-center justify-center"
            @click.stop="handleConsult">
            <span class="text-[13px] ">立即咨询</span>
          </a-button>
        </div>

        <!-- 第二行和第三行：文字和勾 -->
        <div class="grid grid-cols-2 gap-x-3 gap-y-2">
          <div class="flex items-center justify-start text-[13px] text-[#777777]">
            <span>支持本地部署</span>
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 ml-4" viewBox="0 0 20 20" fill="#2551B5">
              <path fill-rule="evenodd"
                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                clip-rule="evenodd" />
            </svg>
          </div>
          <div class="flex items-center justify-start text-[13px] text-[#777777]">
            <span>不限成员数</span>
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 ml-4" viewBox="0 0 20 20" fill="#2551B5">
              <path fill-rule="evenodd"
                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                clip-rule="evenodd" />
            </svg>
          </div>
          <div class="flex items-center justify-start text-[13px] text-[#777777]">
            <span>不限写作次数</span>
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 ml-4" viewBox="0 0 20 20" fill="#2551B5">
              <path fill-rule="evenodd"
                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                clip-rule="evenodd" />
            </svg>
          </div>
          <div class="flex items-center justify-start text-[13px] text-[#777777]">
            <span>定制资源库</span>
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 ml-4" viewBox="0 0 20 20" fill="#2551B5">
              <path fill-rule="evenodd"
                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                clip-rule="evenodd" />
            </svg>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal -->
    <a-modal v-model:open="showModal" centered title="完善资料" @ok="handleSubmit" @cancel="handleCancel" :footer="null"
      width="600px" :z-index="1400">
      <div class="p-3">
        <div class="text-[13px] mb-4">收到资料后我们将由大客户经理与您进行联系，请保持联系方式的畅通</div>

        <!-- 表单区域 -->
        <a-form :model="formState" :rules="rules" ref="formRef" layout="vertical">
          <a-form-item label="团队名称" name="teamName">
            <a-input v-model:value="formState.teamName" placeholder="请输入团队名称" />
          </a-form-item>

          <a-form-item label="联系人" name="contactPerson">
            <a-input v-model:value="formState.contactPerson" placeholder="请输入联系人姓名" />
          </a-form-item>

          <a-form-item label="联系方式" name="phone" :validate-status="phoneValidateStatus"
            :help="phoneValidateStatus === 'error' ? '请输入正确的手机号' : ''">
            <a-input v-model:value="formState.phone" placeholder="请输入手机号" @change="validatePhone" />
          </a-form-item>

          <a-form-item label="定制需求" name="customRequirement">
            <a-textarea v-model:value="formState.customRequirement" placeholder="请详细描述您的定制需求"
              :auto-size="{ minRows: 4, maxRows: 8 }" :maxlength="1000" />
          </a-form-item>
        </a-form>

        <!-- 提交按钮 -->
        <div class="mt-6 flex justify-center">
          <a-button type="primary" size="large" class="bg-gradient-to-br from-[#5B69E5] to-[#7FA9FF] border-0 px-10"
            :loading="submitting" @click="handleSubmit">
            提交
          </a-button>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { message, type FormInstance } from 'ant-design-vue'
import type { Rule } from 'ant-design-vue/es/form'
import { reactive, ref } from 'vue'
import { saveFeedback } from '~/api/user'

const showModal = ref(false)
const submitting = ref(false)
const formRef = ref<FormInstance>()
const phoneValidateStatus = ref<'' | 'error'>('')

interface FormState {
  teamName: string
  contactPerson: string
  phone: string
  customRequirement: string
}

const formState = reactive<FormState>({
  teamName: '',
  contactPerson: '',
  phone: '',
  customRequirement: ''
})

const rules: Record<string, Rule[]> = {
  teamName: [{ required: true, type: 'string', message: '请输入团队名称', trigger: 'blur' }],
  contactPerson: [{ required: true, type: 'string', message: '请输入联系人姓名', trigger: 'blur' }],
  phone: [{ required: true, type: 'string', message: '请输入联系方式', trigger: 'blur' }],
  customRequirement: [{ required: true, type: 'string', message: '请输入定制需求', trigger: 'blur' }]
}

const validatePhone = () => {
  const phoneRegex = /^1[3-9]\d{9}$/
  if (!phoneRegex.test(formState.phone)) {
    phoneValidateStatus.value = 'error'
    return false
  }
  phoneValidateStatus.value = ''
  return true
}

const handleConsult = (e: Event) => {
  e.stopPropagation()
  showModal.value = true
}

const handleCancel = () => {
  showModal.value = false
  formRef.value?.resetFields()
  phoneValidateStatus.value = ''
}

const handleSubmit = async () => {
  try {

    await formRef.value?.validate()
    if (!validatePhone()) {
      return
    }
    submitting.value = true

    // TODO: 这里添加提交表单的逻辑
    console.log('表单数据：', formState)


    const params = {
      title: formState.phone,
      description: `团队名称：${formState.teamName}，联系人：${formState.contactPerson}，定制需求：${formState.customRequirement}`
    }
    const res = await saveFeedback(params)
    if (!res.ok) {
      message.error(res.message || '提交失败')
      return
    }

    // 提交成功后的处理
    showModal.value = false
    formRef.value?.resetFields()
    phoneValidateStatus.value = ''
    message.success('提交成功')
  } catch (error) {
    console.error('表单验证失败：', error)
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
/* 防止内容溢出 */
.text-\[13px\] {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 自定义表单样式 */
:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:deep(.ant-form-item-label > label) {
  font-size: 14px;
  color: #333;
}

:deep(.ant-input),
:deep(.ant-input-textarea) {
  border-radius: 6px;
}

:deep(.ant-input:focus),
:deep(.ant-input-textarea:focus) {
  border-color: #5B69E5;
  box-shadow: 0 0 0 2px rgba(91, 105, 229, 0.1);
}
</style>