<template>
    <div class="space-y-3 bg-[#FFF5E5] rounded-lg p-3">
        <a-row :gutter="12">
            <a-col :xs="24" :sm="12" :md="12" :xl="8" :xxl="8">
                <div class="flex items-center gap-0.5">
                    <span class="text-xs text-gray-600">约</span>
                    <span class="text-sm font-medium text-blue-500">{{ paperCount }}</span>
                    <span class="text-xs text-gray-600">篇专业论文</span>
                </div>
            </a-col>
            <a-col :xs="24" :sm="12" :md="12" :xl="8" :xxl="8">
                <div class="flex items-center gap-0.5">
                    <span class="text-xs text-gray-600">约</span>
                    <span class="text-sm font-medium text-blue-500">{{ articleCount }}</span>
                    <span class="text-xs text-gray-600">篇长文</span>
                </div>
            </a-col>
            <a-col :xs="24" :sm="12" :md="12" :xl="8" :xxl="8">
                <div class="flex items-center gap-0.5">
                    <span class="text-xs text-gray-600">约</span>
                    <span class="text-sm font-medium text-blue-500">{{ pptCount }}</span>
                    <span class="text-xs text-gray-600">篇PPT</span>
                </div>
            </a-col>
        </a-row>
        <a-row :gutter="12">
            <a-col :xs="24" :sm="24" :md="12" :xl="8" :xxl="8">
                <div class="flex items-center gap-1.5 text-xs text-gray-500">
                    <span class="whitespace-nowrap text-[11px]">论文大纲、论文摘要、开题报告</span>
                    <CheckOutlined class="text-green-500 text-sm" />
                </div>
            </a-col>
            <a-col :xs="24" :sm="24" :md="12" :xl="8" :xxl="8">
                <div class="flex items-center gap-1.5 text-xs text-gray-500">
                    <span class="whitespace-nowrap text-[11px]">字数保障、灵活选择、专业规范</span>
                    <CheckOutlined class="text-green-500 text-sm" />
                </div>
            </a-col>
            <a-col :xs="24" :sm="24" :md="12" :xl="8" :xxl="8">
                <div class="flex items-center gap-1.5 text-xs text-gray-500">
                    <span class="whitespace-nowrap text-[11px]">内容准确、海量模版、精美图表</span>
                    <CheckOutlined class="text-green-500 text-sm" />
                </div>
            </a-col>
        </a-row>
        <!-- 论文部分 -->
        <!-- <div class="flex items-left justify-between flex-col">
            <div class="flex items-center gap-0.5">
                <span class="text-xs text-gray-600">约</span>
                <span class="text-sm font-medium text-blue-500">{{ paperCount }}</span>
                <span class="text-xs text-gray-600">篇专业论文</span>
            </div>
            <div class="flex items-center gap-1.5 text-xs text-gray-500">
                <span class="whitespace-nowrap text-[11px]">论文大纲、论文摘要、开题报告</span>
                <CheckOutlined class="text-green-500 text-sm" />
            </div>
        </div> -->

        <!-- 长文部分 -->
        <!-- <div class="flex items-left justify-between flex-col">
            <div class="flex items-center gap-0.5">
                <span class="text-xs text-gray-600">约</span>
                <span class="text-sm font-medium text-blue-500">{{ articleCount }}</span>
                <span class="text-xs text-gray-600">篇长文</span>
            </div>
            <div class="flex items-center gap-1.5 text-xs text-gray-500">
                <span class="whitespace-nowrap text-[11px]">字数保障、灵活选择、专业规范</span>
                <CheckOutlined class="text-green-500 text-sm" />
            </div>
        </div> -->

        <!-- PPT部分 -->
        <!-- <div class="flex items-left justify-between flex-col">
            <div class="flex items-center gap-0.5">
                <span class="text-xs text-gray-600">约</span>
                <span class="text-sm font-medium text-blue-500">{{ pptCount }}</span>
                <span class="text-xs text-gray-600">篇PPT</span>
            </div>
            <div class="flex items-center gap-1.5 text-xs text-gray-500">
                <span class="whitespace-nowrap text-[11px]">内容准确、海量模版、精美图表</span>
                <CheckOutlined class="text-green-500 text-sm" />
            </div>
        </div> -->

        <!-- 提示文字 -->
        <div class="text-[10px] text-gray-400 text-left">
            以上仅为单项的初步估算，具体硬币消耗以实际使用时系统显示为准
        </div>
    </div>
</template>

<script setup lang="ts">
import { CheckOutlined } from '@ant-design/icons-vue';
import { computed } from 'vue';
import type { XiaoinGoodInfo } from '~/services/types/recharge';

interface Props {
    currentRechargeInfo?: XiaoinGoodInfo
}
const props = withDefaults(defineProps<Props>(), {})

const extraParams = computed(() => {
    if (!props.currentRechargeInfo) {
        return
    }
    if (!props.currentRechargeInfo.extraParams) {
        return
    }
    return props.currentRechargeInfo.extraParams
})

const chatCount = computed(() => {
    if (!extraParams.value) {
        return 0
    }
    return Math.floor(extraParams.value.coinNum / 1000 / 1.5)
})

const paperCount = computed(() => {
    if (!extraParams.value) {
        return 0
    }
    return Math.floor(extraParams.value.coinNum / 100000)
})

const pptCount = computed(() => {
    if (!extraParams.value) {
        return 0
    }
    return Math.floor(extraParams.value.coinNum / 60000)
})

const articleCount = computed(() => {
    if (!extraParams.value) {
        return 0
    }
    const count = Math.floor(extraParams.value.coinNum / 50000)
    return count
})
</script>

<style scoped>
/* 移除原有的 scss 样式 */
</style>