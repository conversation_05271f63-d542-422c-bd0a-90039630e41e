<template>
    <div class="relative">
        <button class="flex items-center text-white hover:text-blue-200 bg-transparent" @click="handleLogin">
            <img :src="avatar" alt="User Avatar" class="w-10 h-10 rounded-full border-2 border-white object-cover">
        </button>
        <div class="absolute -top-[9px] -right-[9px]" v-if="shouldShowVipLevel">
            <img style="width: 20px; height: 20px" class="rotate-45" v-if="decorationUrl" :src="decorationUrl" />
        </div>
    </div>
</template>
<script lang="ts" setup>
import { defaultAvatar, KnowledgeAssistantMemberLevel } from '@/utils/constants';
import { computed } from 'vue';
import { useRouter } from 'vue-router';

import { useUserStore } from '~/stores/user';

const props = defineProps({
    shouldShowVipLevel: {
        type: Boolean,
        default: true
    },
    avatarUrl: {
        type: String,
        default: ''
    }
})

const router = useRouter()
const userStore = useUserStore()
const { isLogined, knowledgeAssistantMemberInfo, currentLoginInfo } = storeToRefs(userStore)

// 计算装饰图标 URL
const decorationUrl = computed(() => {

    const vipLevel = knowledgeAssistantMemberInfo.value?.vipLevel
    if (vipLevel === 1) {
        return KnowledgeAssistantMemberLevel.level2
    }
    if (vipLevel === 2) {
        return KnowledgeAssistantMemberLevel.level3
    }
    if (vipLevel === 3) {
        return KnowledgeAssistantMemberLevel.level4
    }
    return ''
})

// 计算头像 URL - 简化逻辑，统一使用 store 数据
const avatar = computed(() => {
    // 1. 如果提供了 props.avatarUrl，优先使用
    if (props.avatarUrl) {
        return props.avatarUrl
    }

    // 3. 个人身份使用个人头像
    return currentLoginInfo.value?.avatar || defaultAvatar
})

const handleLogin = () => {
    if (!isLogined.value) {
        userStore.openLoginModal()
        return;
    }
    router.push('/profile')
}

</script>