<template>
    <div @click=" !isDisabled && $emit('select')"
        class="rounded-xl px-2 pb-4 relative transition-all duration-0 space-y-2 mt-2 bg-[#F8F7F7]" :class="[
            isDisabled ? 'cursor-not-allowed opacity-50 bg-gray-50' : 'cursor-pointer',
            isSelected ?
                'outline outline-2 outline-[#F8BE85] bg-gradient-to-br from-[#FFE6C4] to-[#FFF2D3] cursor-pointer' :
                ''
        ]">
        <div v-if="plan.sellPoint"
            class="absolute -top-[12px] -left-0 bg-red-500 text-white text-xs px-2 py-1.5 rounded bg-gradient-to-br from-[#FF512E] to-[#FF9165] rounded-r-[7px] rounded-l-[7px] rounded-bl-[0px]">
            {{ plan.sellPoint }}
        </div>

        <!-- 会员等级名称 -->
        <div class="flex items-center justify-center text-center mb-3 pt-3">
            <img :src="levelIcon(plan)" class="w-[20px] h-[20px] mr-2" />
            <div class="text-base font-semibold text-gray-800">{{ plan.title }}</div>
        </div>

        <!-- 价格展示 -->
        <div class="flex items-end justify-center text-center my-2 mt-4">
            <div class="flex items-baseline justify-center">
                <span class="text-[20px] text-red-500">¥</span>
                <span class="text-3xl font-bold text-red-500">{{ moneyFormatCentAreNotKept(plan.price) }}</span>
                <span class="text-xs" v-if="plan.extraParams.vipLevel != 0.5">/年</span>
            </div>
            <div class="text-gray-400 line-through text-xs mt-1 ml-1" v-if="plan.price < plan.originPrice">¥{{
                moneyFormatCentAreNotKept(plan.originPrice) }}/年</div>
        </div>

        <div class="w-full h-[20px]">
            <div v-if="shouldShowUpgradePrice" class="text-[13px] text-[#ff4242] text-center">
                补差价升级仅需
                <span class="text-red-500">{{ moneyFormatCentAreNotKept(amount) }}</span>
            </div>
        </div>

        <div class="space-y-0.5">
            <div class="flex items-center justify-between px-2 py-2">
                <span class="text-gray-600 text-xs flex items-center gap-1">
                    赠送写作硬币
                </span>
                <div class="flex flex-col items-center gap-1">
                    <span :class="'text-gray-800'" class="text-sm font-semibold flex items-center">
                        <img src="https://static-1256600262.file.myqcloud.com/xiaoin-h5/icons/new-pc/yingbi.png" alt=""
                            class="w-4 h-4">
                        <div v-if="shouldShowPlusSign">
                            <span class="text-[#2551B5] text-[16px]">+</span>
                        </div>
                        {{ getFieldValue('coinNum') / 10000 }}万
                    </span>
                </div>
            </div>

            <div class="flex items-center justify-between px-2 py-2">
                <span class="text-gray-600 text-xs flex items-center gap-1.5">
                    基于知识库写作
                </span>
                <span class="text-gray-800 text-xs font-semibold py-0.5">
                    <svg v-if="plan.extraParams.vipLevel >= 1" xmlns="http://www.w3.org/2000/svg" class="h-6 w-6"
                        viewBox="0 0 20 20" fill="#2551B5">
                        <path fill-rule="evenodd"
                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                            clip-rule="evenodd" />
                    </svg>
                    <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" viewBox="0 0 20 20" fill="#ff4242">
                        <path fill-rule="evenodd"
                            d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                            clip-rule="evenodd" />
                    </svg>
                </span>
            </div>
            <div class="flex items-center justify-between px-2 py-2">
                <span class="text-gray-600 text-xs flex items-center gap-1.5">
                    文档学习免费
                </span>
                <span class="text-gray-800 text-xs font-semibold py-0.5">
                    <svg v-if="plan.extraParams.vipLevel >= 1" xmlns="http://www.w3.org/2000/svg" class="h-6 w-6"
                        viewBox="0 0 20 20" fill="#2551B5">
                        <path fill-rule="evenodd"
                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                            clip-rule="evenodd" />
                    </svg>
                    <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" viewBox="0 0 20 20" fill="#ff4242">
                        <path fill-rule="evenodd"
                            d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                            clip-rule="evenodd" />
                    </svg>
                </span>
            </div>
            <div class="flex items-center justify-between px-2 py-2">
                <span class="text-gray-600 text-xs flex items-center gap-1.5">
                    学术搜索次数
                </span>
                <!-- <span :class="'text-gray-800'"
                    class="text-xs font-semibold py-0.5 rounded-full bg-transparent border border-transparent">
                    {{ plan.description.maxScholarSearch }}次
                </span> -->
                <span
                    class="text-xs text-gray-800 font-semibold py-0.5 rounded-full bg-transparent border border-transparent flex items-center">
                    <div v-if="shouldShowPlusSign">
                        <span class="text-[#2551B5] text-[16px]">+</span>
                    </div>
                    <!-- <span v-if="plan.description.freeAIOnlineEditing > infiniteNum">无限</span> -->
                    <span>
                        {{ getFieldValue('maxScholarSearch') }} 次
                    </span>
                </span>
            </div>
            <div class="flex items-center justify-between px-2 py-2">
                <span class="text-gray-600 text-xs flex items-center gap-1.5">
                    提问次数
                </span>
                <span :class="'text-gray-800'"
                    class="text-xs font-semibold py-0.5 rounded-full bg-transparent border border-transparent flex items-center">
                    <div v-if="shouldShowPlusSign">
                        <span class="text-[#2551B5] text-[16px]">+</span>
                    </div>
                    <span v-if="plan.description.question > infiniteNum">无限</span>
                    <span v-else>
                        {{ getFieldValue('question') }} 次
                    </span>
                </span>
            </div>

            <div class="flex items-center justify-between px-2 py-2">
                <span class="text-gray-600 text-xs flex items-center gap-1.5">
                    知识库空间
                </span>
                <span :class="'text-gray-800'"
                    class="text-xs font-semibold py-0.5 rounded-full bg-transparent border border-transparent">
                    {{ getFieldValue('spaceQuotaBytes') }}
                </span>
            </div>

            <div class="flex items-center justify-between px-2 py-2">
                <span class="text-gray-600 text-xs flex items-center gap-1.5">
                    AI在线编辑
                </span>
                <span
                    class="text-xs text-gray-800 font-semibold py-0.5 rounded-full bg-transparent border border-transparent flex items-center">
                    <div v-if="shouldShowPlusSign">
                        <span class="text-[#2551B5] text-[16px]">+</span>
                    </div>
                    <span v-if="plan.description.freeAIOnlineEditing > infiniteNum">无限</span>
                    <span v-else>
                        {{ getFieldValue('freeAIOnlineEditing') }} 次
                    </span>
                </span>
            </div>

            <!-- 免费赠送AI鼠标区域-->
            <div class="relative" v-if="plan.extraParams.vipLevel == 3">
                <div
                    class="flex items-center justify-center bg-[#FFE9E9] rounded-full px-3 py-2 relative overflow-hidden">

                    <!-- 闪光效果 -->
                    <div class="absolute inset-0 animate-shimmer">
                        <div
                            class="absolute inset-[-10%] w-[120%] h-[120%] bg-gradient-to-r from-transparent via-white/20 to-transparent transform -rotate-15 translate-x-[-100%]">
                        </div>
                    </div>

                    <NuxtLink to="/mouse" target="_blank" class="block ">
                        <span class="text-[#FF4242] text-xs flex items-center gap-1.5 font-medium relative ml-6">
                            <!-- <mouse theme="outline" size="16" fill="#ffffff" /> -->
                            免费赠送AI鼠标 >
                        </span>
                    </NuxtLink>
                </div>
                <div class="absolute top-[3px] left-0 w-[26px] h-[26px] ml-3">
                    <img src="https://static-1256600262.file.myqcloud.com/xiaoin-h5/icons/new-pc/recharge-mouse-icon.png"
                        class="w-[26px] h-[26px]" />
                </div>
            </div>
        </div>

    </div>
</template>

<script setup>
import { UserService } from '@/services/user';
import { KnowledgeAssistantMemberLevel, VipLevelNumber } from '@/utils/constants';
import { moneyFormatCentAreNotKept } from '@/utils/utils';
import { computed, watch } from 'vue';
import { useVipPlansStore } from '~/stores/vipPlans';

const props = defineProps({
    plan: {
        type: Object,
        required: true
    },
    isSelected: {
        type: Boolean,
        default: false
    },
    isDisabled: {
        type: Boolean,
        default: false
    },
    amount: {
        type: Number,
        default: 0
    },
    currentVipExpireTime: {
        type: Number,
        default: 0
    }
});

const infiniteNum = 9999999999

const vipPlansStore = useVipPlansStore()

const knowledgeAssistantMemberInfo = computed(() => {
    return UserService.getKnowledgeAssistantMemberInfo();
});

const levelIcon = (plan) => {
    if (plan.extraParams.vipLevel == 1) {
        return KnowledgeAssistantMemberLevel.level2
    }
    if (plan.extraParams.vipLevel == 2) {
        return KnowledgeAssistantMemberLevel.level3
    }
    if (plan.extraParams.vipLevel == 3) {
        return KnowledgeAssistantMemberLevel.level4
    }
    return KnowledgeAssistantMemberLevel.level0
}

const getCurrentVipLevelBenefitInformation = computed(() => {
    const currentVipLevel = knowledgeAssistantMemberInfo.value?.vipLevel;
    if (!currentVipLevel) return null;
    const list = [...vipPlansStore.goodsList, ...vipPlansStore.experienceMemberGoodList]
    // 从商品列表中找到当前等级的商品
    return list.find(item => item.extraParams.vipLevel === currentVipLevel) || null;
})

const getFieldValue = (field) => {

    if (knowledgeAssistantMemberInfo.value?.vipLevel == VipLevelNumber.level4) {
        return props.plan.description[field]
    }

    // console.log("getCurrentVipLevelBenefitInformation.value==>", getCurrentVipLevelBenefitInformation.value)
    if (!knowledgeAssistantMemberInfo.value?.vipLevel) return props.plan.description[field];
    if (!getCurrentVipLevelBenefitInformation.value) return props.plan.description[field];
    if (knowledgeAssistantMemberInfo.value?.vipLevel > props.plan.extraParams.vipLevel) return props.plan.description[field];

    // 特殊处理知识库空间
    if (field === 'spaceQuotaBytes') {
        if (props.plan.description[field]) {
            return `扩容至${props.plan.description[field]}`;
        }
        return '';
    }

    // 处理不同字段对应的当前值
    const currentValueMap = {
        coinNum: getCurrentVipLevelBenefitInformation.value.description.coinNum,
        question: getCurrentVipLevelBenefitInformation.value.description.question,
        freeAIOnlineEditing: getCurrentVipLevelBenefitInformation.value.description.freeAIOnlineEditing,
        maxScholarSearch: getCurrentVipLevelBenefitInformation.value.description.maxScholarSearch,
    };
    // console.log("knowledgeAssistantMemberInfo.value.coinNum==>", props.plan.description[field] - knowledgeAssistantMemberInfo.value.coinNum)
    return props.plan.description[field] - (currentValueMap[field] || 0);
}

const amount = computed(() => {
    return vipPlansStore.amount
})

// 计算是否显示补差价
const shouldShowUpgradePrice = computed(() => {
    // 如果当前没有会员，不显示补差价
    if (!knowledgeAssistantMemberInfo.value?.vipLevel) return false;

    if (knowledgeAssistantMemberInfo.value.vipLevel == VipLevelNumber.level4) return false;

    if (props.isSelected && props.plan.price > props.amount) {
        return true
    }
    return false;
});

const shouldShowPlusSign = computed(() => {
    // 如果当前没有会员，不显示补差价
    if (!knowledgeAssistantMemberInfo.value?.vipLevel) return false;
    if (knowledgeAssistantMemberInfo.value.vipLevel == VipLevelNumber.level4) return false;
    return true;
});

// 添加 watch 来监控 isDisabled 的变化
watch(() => props.isDisabled, (newVal) => {
    console.log('isDisabled changed:', newVal)
})

onMounted(() => {

})

defineEmits(['select'])
</script>

<style scoped>
@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }

    100% {
        transform: translateX(100%);
    }
}

.animate-shimmer>div {
    animation: shimmer 1.5s linear infinite;
}
</style>