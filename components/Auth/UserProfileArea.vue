<template>
    <div class="flex items-center justify-start w-full space-x-3">
        <div class="w-[38px] h-[38px] min-w-[38px]">
            <UserAvatar />
        </div>
        <div class="flex flex-col justify-between flex-1">
            <div
                class="text-left text-[15px] font-[500] text-[#333333] truncate max-w-[95px] text-ellipsis whitespace-nowrap overflow-hidden">
                {{ nickname || '未设置昵称' }}
            </div>
            <div class="flex items-center justify-between"
                v-if="knowledgeAssistantMemberInfo && knowledgeAssistantMemberInfo.vipLevel >= 1">
                <div class="text-[#333333] text-[13px]">{{
                    vipLevelName[(knowledgeAssistantMemberInfo?.vipLevel ?? 1) as keyof typeof vipLevelName] }}
                </div>
                <button v-if="knowledgeAssistantMemberInfo?.vipLevel >= 3"
                    class="px-2 py-1 rounded-[7px] transition-colors whitespace-nowrap bg-[#2551B5] text-[#ffffff] text-[11px] self-center"
                    @click.stop="handleRecharge">
                    续费
                </button>
                <button v-else
                    class="px-2 py-1 rounded-[7px] transition-colors whitespace-nowrap bg-[#FFEECC] text-[#CE5606] text-[11px] self-center"
                    @click.stop="handleRecharge">
                    升级
                </button>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import UserAvatar from '@/components/Auth/UserAvatar.vue'
import { computed } from 'vue'
import { useRechargeStore } from '~/stores/recharge'
import { useUserStore } from '~/stores/user'
import { vipLevelName } from '~/utils/constants'

const userStore = useUserStore()
const rechargeStore = useRechargeStore()

// 直接使用 store 的响应式状态
const { isLogined, currentLoginInfo, knowledgeAssistantMemberInfo } = storeToRefs(userStore)

const nickname = computed(() => {
    if (!isLogined.value || !currentLoginInfo.value) {
        return ''
    }

    return currentLoginInfo.value?.nickname || ''
})

const handleRecharge = () => {
    rechargeStore.openRechargeModal()
}
</script>