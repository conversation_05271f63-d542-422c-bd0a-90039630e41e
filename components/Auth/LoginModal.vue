<template>
  <common-modal :model-value="localModelValue" @update:model-value="updateModelValue" title="登录" subtitle="登录后即可使用更多功能"
    :width="!app?.isMouse ? 900 : 700" @confirm="handleLogin" @cancel="updateModelValue(false)" :hide-footer="true"
    :hide-header="true" :zIndex="99999">
    <div class="flex flex-col gap-4 p-4 py-6 sm:p-6 sm:flex-row sm:gap-6 sm:py-8 login-model-a">
      <!-- 左侧：万能小in介绍 - 在移动端隐藏 -->
      <login-intro class="hidden md:block" :class="[
        !app?.isMouse ? '' : 'w-[300px]'
      ]" />

      <!-- 分隔线 - 在移动端隐藏 -->
      <div class="hidden md:block w-[1px] bg-gradient-to-b from-transparent via-gray-200 to-transparent"></div>

      <!-- 中间：手机号登录 - 在移动端占满宽度 -->
      <div class="w-full px-1 sm:px-1" :class="[
        !app?.isMouse ? 'md:w-5/12' : ''
      ]">
        <h4 class="mb-4 text-lg font-medium text-center sm:text-lg sm:mb-6">手机号登录</h4>
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 mb-1.5">手机号</label>
          <div class="relative">
            <input v-model="phone" type="text"
              class="w-full px-4 py-3 text-base transition-all border border-gray-200 rounded-lg sm:py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent sm:text-sm"
              placeholder="请输入手机号" maxlength="11" @input="handleInputPhone" @paste="handlePhonePaste"
              @keydown="handlePhoneKeydown" />
          </div>
        </div>

        <div class="mb-6">
          <label class="block text-sm font-medium text-gray-700 mb-1.5">验证码</label>
          <div class="flex gap-2 sm:gap-3">
            <input v-model="code" type="text"
              class="flex-1 px-4 py-3 text-base transition-all border border-gray-200 rounded-lg sm:py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent sm:text-sm"
              placeholder="请输入验证码" maxlength="6" @keydown="handleKeyDown" />
            <div class="captcha-button-wrap">
              <button id="captcha-button" :disabled="countdown > 0"
                class="px-3 sm:px-4 py-3 sm:py-2 bg-blue-50 text-blue-700 rounded-lg hover:bg-blue-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors whitespace-nowrap text-sm sm:text-base min-h-[44px] sm:min-h-auto">
                {{ countdown > 0 ? `${countdown}s后重试` : "获取验证码" }}
              </button>
              <div v-if="!isPhone" class="absolute top-0 left-0 w-full h-full cursor-pointer "
                @click="handleVerifyMobile"></div>
            </div>

          </div>
        </div>

        <login-button :loading="isLoading" @click="handleLogin" />

        <div class="flex items-center gap-2">
          <span class="text-xs leading-relaxed text-gray-600 sm:text-sm">
            登录注册即表示同意
            <a :href="userAgreement" target="_blank" class="text-blue-600 underline hover:text-blue-700">《用户协议》</a>
            和
            <a :href="userPrivacy" target="_blank" class="text-blue-600 underline hover:text-blue-700">《隐私政策》</a>
          </span>
        </div>
      </div>
      <template v-if="!app?.isMouse">
        <!-- 分隔线 - 在移动端隐藏 -->
        <div class="hidden md:block w-[1px] bg-gradient-to-b from-transparent via-gray-200 to-transparent"></div>

        <!-- 右侧：微信扫码登录 - 在移动端隐藏 -->
        <div class="hidden w-1/4 md:block ">
          <div class="flex flex-col items-center justify-center h-full ">
            <login-qr-code />
          </div>
        </div>
      </template>

    </div>
  </common-modal>
</template>

<script setup lang="ts">
import {
  phoneRegExp,
  userAgreement,
  userPrivacy
} from '@/utils/constants';
import { StarloveUtil } from "@/utils/util";
import { message } from "ant-design-vue";
import axios from 'axios';
import { onBeforeUnmount, onMounted, onUnmounted, ref } from "vue";
import { getVerifyCode } from "~/api/user";
import { useApp } from '~/composables/useApp';
import { UserService } from '~/services/user';
import CommonModal from "../Common/Modal.vue";
import LoginButton from "./LoginButton.vue";
import LoginIntro from "./LoginIntro.vue";
import LoginQrCode from "./LoginQrCode.vue";

const props = defineProps({
  modelValue: Boolean,
});

const emit = defineEmits(["update:modelValue"]);
const app = useApp()

// 表单数据
const phone = ref("");
const code = ref("");
const countdown = ref(0);
const sendStatus = ref(false)

const isLoading = ref(false)
let captchaButton: HTMLElement | null = null
let captcha

// 创建本地响应式变量
const localModelValue = ref(props.modelValue);

const handleKeyDown = (event: { key: string; preventDefault: () => void; }) => {

  if (event.key == 'Enter') {
    event.preventDefault()
    handleLogin()
  }
}

// 更新方法
const updateModelValue = (value: boolean) => {
  localModelValue.value = value;
  emit("update:modelValue", value);
};
let countDownTimer: string | number | NodeJS.Timeout | undefined = undefined
const isPhone = computed(() => {
  if (!phone.value) {
    return false
  }
  if (!phoneRegExp.test(phone.value)) {
    return false
  }
  if (sendStatus.value) {
    return false
  }
  return true
})

const handleInputPhone = (event: Event) => {
  const target = event.target as HTMLInputElement
  // 移除所有非数字字符，并限制长度为11位
  const cleanValue = target.value.replace(/\D/g, '').slice(0, 11)
  phone.value = cleanValue
  // 更新输入框显示值
  target.value = cleanValue
}

const handlePhonePaste = (event: ClipboardEvent) => {
  event.preventDefault()
  const pastedText = event.clipboardData?.getData('text') || ''
  // 移除所有非数字字符，并限制长度为11位
  const cleanValue = pastedText.replace(/\D/g, '').slice(0, 11)
  phone.value = cleanValue
  // 更新输入框显示值
  const target = event.target as HTMLInputElement
  target.value = cleanValue
}

const handlePhoneKeydown = (event: KeyboardEvent) => {
  // 阻止空格键输入
  if (event.key === ' ' || event.code === 'Space') {
    event.preventDefault()
  }
}
const handleVerifyMobile = () => {

  if (!phone.value) {
    message.warning('请输入手机号')
    return
  }
  if (!phoneRegExp.test(phone.value)) {
    message.warning('手机号格式错误')
    return
  }
}
const startCoundDown = () => {
  sendStatus.value = true
  countdown.value = 60;
  countDownTimer = setInterval(() => {
    countdown.value--;
    if (countdown.value <= 0) {
      sendStatus.value = false
      clearInterval(countDownTimer);
    }
  }, 1000);
}


// 处理登录
const handleLogin = async () => {
  const userStore = useUserStore()

  if (!phone.value) {
    message.warning('请输入手机号')
    return
  }
  if (!phoneRegExp.test(phone.value)) {
    message.warning('手机号格式错误')
    return
  }
  if (!/^\d{6}$/.test(code.value)) {
    message.warning("验证码格式错误");
    return;
  }

  isLoading.value = true;

  try {
    const params: {
      mobile: string;
      verifyCode: string;
      inviteUserId?: string;
      inviteUserIdUpdateTime?: number;
    } = {
      mobile: phone.value,
      verifyCode: code.value,
    };

    if (UserService.getSharerUserId()) {
      params.inviteUserId = UserService.getSharerUserId()
      params.inviteUserIdUpdateTime = UserService.getSharerUserIdUpdateTime()
    }
    if (StarloveUtil.isInTestServer()) {
      params.verifyCode =
        params.verifyCode == "777778" ? "7777779" : params.verifyCode;
    }

    await userStore.login(params)
    await UserService.loadKnowledgeAssistantMemberInfo()
    userStore.closeLoginModal()
    UserService.loadOpenRecordAdd()

    message.success('登录成功')
    // 如果是鼠标环境下， 绑定鼠标设备
    if (window && window.sessionStorage) {
      const mouseDeviceInfo = sessionStorage.getItem(StarloveConstants.keyOflocalStorage.mouseDeviceInfo)
      if (mouseDeviceInfo) {
        try {
          await UserService.bindMouseDevice(JSON.parse(mouseDeviceInfo))
        } catch (error) {
          console.log(error)
        }
      }
    }
    if (window.location.search.indexOf('action=login') > -1) {
      try {
        // 解析redirectUrl的值
        const redirectUrl = sessionStorage.getItem(StarloveConstants.keyOflocalStorage.redirectUrl);
        if (redirectUrl) {
          // 将redirectUrl存储到sessionStorage
          window.sessionStorage.setItem(StarloveConstants.keyOflocalStorage.redirectUrl, '');
          window.location.href = decodeURIComponent(redirectUrl);
        }
      } catch (error) {
        console.error('解析redirectUrl失败:', error);
      }
    }
  } catch (error) {
    console.log('login error', error)
  } finally {
    isLoading.value = false
  }
};

const captchaVerifyCallback = async (captchaVerifyParam: any) => {
  const params: {
    mobile: string;
    captchaVerifyParam: any;
    timestamp?: string | number;
  } = { mobile: phone.value, captchaVerifyParam };

  try {
    const resp = await axios.get(StarloveUtil.getBaseUrl().replace('/ai', '/'))
    if (resp.data.success && resp.data.timestamp) {
      params.timestamp = resp.data.timestamp
    }
    const res = await getVerifyCode(params)
    // console.log('getVerifyCode', res)
    // 验证码需要判断返回的状态
    if (!res.success) {
      let captchaResult = false;
      let errorMessage = res?.message || '验证码发送失败，请重试'
      if (res?.data) {
        // if (typeof res?.data === 'string') {
        //   errorMessage = res.data
        // } else if (typeof res?.data === 'object') {
        //   if (res?.data?.message) {
        //     errorMessage = res?.data?.message
        //   }
        //   if (res?.data?.captchaStatus === true) {
        //     captchaResult = true
        //   }
        // }
        if (typeof res?.data === 'object') {
          if (res?.data?.message) {
            errorMessage = res?.data?.message
          }
          if (res?.data?.captchaStatus === true) {
            captchaResult = true
          }
        }
      }
      // // 异常请求--->改成验证失败
      // if (errorMessage == '异常请求') {
      //   errorMessage = '验证失败'
      // }
      message.error(errorMessage)
      return {
        captchaResult: captchaResult,
        bizResult: null
      }
    }
    startCoundDown()
    message.success('验证码发送成功')

    return {
      captchaResult: true,
      bizResult: true
    }
    //下面是新逻辑
    // if (res.code == 1203) {
    //   // 鉴于滑块消失比较慢，统一错误的提示可能看不到，故5秒后显示错误提示
    //   setTimeout(() => {
    //     message.error(res.message || '验证成功，但发送失败')
    //   }, 5000)
    //   return {
    //     captchaResult: true,
    //     bizResult: null
    //   }
    // }


    // if (!res.data) {
    //   return {
    //     captchaResult: false,
    //     bizResult: null
    //   }
    // }

    // if (!res.data.captchaStatus) {
    //   // 鉴于 滑块拖动失败 不算业务错误，不会被request错误处理机制拦截 故必须本初提示错误
    //   // 鉴于 验证窗口不会消失 所以此处提示不是很重要 在后面ok
    //   // 鉴于 如果延迟，可能造成用户困扰，所以不延迟
    //   message.error(res.message || '验证失败')
    //   return {
    //     captchaResult: false,
    //     bizResult: null
    //   }
    // }

    // if (!res.data.sendStatus) {
    //   // 鉴于滑块消失比较慢，统一错误的提示可能看不到，故5秒后显示错误提示
    //   setTimeout(() => {
    //     message.error(res.message || '验证成功，但发送失败')
    //   }, 5000)

    //   return {
    //     captchaResult: true,
    //     bizResult: true
    //   }
    // }

    // startCoundDown()
    // message.success('验证码发送成功')

    // return {
    //   captchaResult: true,
    //   bizResult: true
    // }

  } catch (error) {

    console.log('captchaVerifyCallback catched error', error)
    return {
      captchaResult: false,
      bizResult: null
    }
  }

}
const onBizResultCallback = () => {
  console.log('onBizResultCallback')
}
const getInstance = (instance: any) => {
  captcha = instance
}

const setupCaptcha = () => {
  try {
    if (window.initAliyunCaptcha && !captchaButton) {
      captchaButton = document.getElementById('captcha-button')
      const captchaElement = document.getElementById('captcha-element')
      console.log('captchaElement', captchaElement)

      console.log('captchaButton', captchaButton)
      if (captchaButton) {
        console.log('initAliyunCaptcha', captchaButton)
        window.initAliyunCaptcha({
          SceneId: '1vf31mmc', // 场景ID。根据步骤二新建验证场景后，您可以在验证码场景列表，获取该场景的场景ID
          prefix: '1bahad', // 身份标。开通阿里云验证码2.0后，您可以在控制台概览页面的实例基本信息卡片区域，获取身份标
          mode: 'popup', // 验证码模式。popup表示要集成的验证码模式为弹出式。无需修改
          // element: '#captcha-element', // 页面上预留的渲染验证码的元素，与原代码中预留的页面元素保持一致。
          button: '#captcha-button', // 触发验证码弹窗的元素。button表示单击登录按钮后，触发captchaVerifyCallback函数。您可以根据实际使用的元素修改element的值
          captchaVerifyCallback: captchaVerifyCallback, // 业务请求(带验证码校验)回调函数，无需修改
          onBizResultCallback: onBizResultCallback, // 业务请求结果回调函数，无需修改
          getInstance: getInstance, // 绑定验证码实例函数，无需修改
          slideStyle: {
            width: 360,
            height: 40
          }, // 滑块验证码样式，支持自定义宽度和高度，单位为px。其中，width最小值为320 px
          language: 'cn', // 验证码语言类型，支持简体中文（cn）、繁体中文（tw）、英文（en）
          region: 'cn' //验证码示例所属地区，支持中国内地（cn）、新加坡（sgp）
        })
      }

    }
  } catch (error) {
    console.log(error)
  }
}


onMounted(() => {

  setTimeout(() => setupCaptcha(), 500)
})

onBeforeUnmount(() => {
  console.log('onBeforeUnmount')
  captchaButton = null
  // 必须删除相关元素，否则再次mount多次调用 initAliyunCaptcha 会导致多次回调 captchaVerifyCallback
  document.getElementById('aliyunCaptcha-mask')?.remove()
  document.getElementById('aliyunCaptcha-window-popup')?.remove()

})
onUnmounted(() => {
  if (countDownTimer) {
    sendStatus.value = false
    clearInterval(countDownTimer)
  }
})
</script>
<style lang="scss">
#captcha-button {
  display: block;
  z-index: 99;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  background: transparent;
  color: #1e99ff;
  font-size: 17px;
}

.captcha-button-wrap {
  position: relative;
}

.login-model-a {

  /* 移动端优化样式 */
  @media (max-width: 640px) {

    /* 确保输入框在移动端有足够的触摸区域 */
    input[type="text"] {
      min-height: 44px;
      font-size: 16px;
      /* 防止iOS缩放 */
    }

    /* 优化按钮触摸区域 */
    button {
      min-height: 44px;
      touch-action: manipulation;
      /* 防止双击缩放 */
    }

    /* 优化协议链接的触摸区域 */
    a {
      padding: 2px 4px;
      margin: -2px -4px;
    }


  }

  /* 防止移动端输入框缩放 */
  @media screen and (max-width: 640px) {

    input[type="text"],
    input[type="tel"],
    input[type="number"] {
      font-size: 16px !important;
      transform-origin: left top;
      transform: scale(1);
    }
  }
}
</style>