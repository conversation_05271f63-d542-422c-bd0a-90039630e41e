<template>
    <button :disabled="loading" @click="$emit('click')"
        class="w-full py-3 sm:py-2.5 text-white rounded-lg mb-5 relative overflow-hidden bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 disabled:opacity-70 disabled:cursor-not-allowed min-h-[44px] sm:min-h-auto text-base sm:text-sm font-medium">
        <template v-if="loading">
            <div class="flex items-center justify-center gap-2">
                <!-- <span
                    class="inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></span> -->
                <a-spin class="flex items-center white-spin mr-2"></a-spin>
                <span>登录中...</span>
            </div>
        </template>
        <template v-else>
            <slot>登录</slot>
        </template>
    </button>
</template>

<script setup lang="ts">
defineProps<{
    loading: boolean
}>()

defineEmits<{
    (e: 'click'): void
}>()
</script>

<style scoped>
.white-spin :deep(.ant-spin-dot-item) {
    background-color: white !important;
}
</style>