<template>
    <a-modal v-model:open="showModal" @cancel="handleCancel" :footer="null" :width="1035">
      <div class="essay-example-modal">
        <div class="example-modal-title">
          以下范文样例由
          <text style="color: #333333">万能小in</text>
          生成
        </div>
  
        <a-row class="image-area" :gutter="12">
          <a-col
            :xs="24"
            :sm="12"
            :md="24 / imageList.length"
            :xl="24 / imageList.length"
            :xxl="24 / imageList.length"
            v-for="item in imageList"
            :key="item"
          >
            <img :src="item" style="width: 100%; height: auto; max-width: 300px; max-height: 450px" />
          </a-col>
        </a-row>
  
        <a-button type="success" class="example-button" size="large" @click="handleCancel">
          我知道了
        </a-button>
      </div>
    </a-modal>
  </template>
  
  <script setup lang="ts">
  import { computed, ref } from 'vue'
  
  interface Props {
    showModal: boolean
    imageList: string[]
  }
  const props = withDefaults(defineProps<Props>(), {})
  
  const emit = defineEmits(['update:showModal'])
  const showModal = computed({
    get: () => props.showModal,
    set: (val) => {
      emit('update:showModal', val)
    }
  })
  
  const handleCancel = () => {
    showModal.value = false
  }
  </script>
  
  <style lang="scss">
  .essay-example-modal {
    text-align: center;
    .example-modal-title {
      text-align: center;
      margin-top: 20px;
      font-size: 18px;
  
      font-weight: 500;
      color: #777777;
      line-height: 29px;
    }
  
    .image-area {
      width: 100%;
      margin: 40px 0;
      // img {
      //   height: 450px;
      //   width: auto;
      // }
    }
  
    .example-button {
      border-radius: 10px;
      padding: 13px 80px;
      font-size: 16px;
  
      font-weight: 400;
      color: #ffffff;
      line-height: 25px;
      height: 50px;
      border: none;
      background: #3089d3;
      margin-bottom: 20px;
    }
  }
  </style>
  