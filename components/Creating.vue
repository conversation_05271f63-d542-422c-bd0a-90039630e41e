<template>
    <div class="submission-detail-ing-part">

        <!-- v-if="isUseLargeWorker" -->
        <div class="flex justify-center step">
            <div class="relative pl-8 py-4">
                <div class="absolute left-[15px] top-8 bottom-8 w-[2px] bg-blue-500"></div>

                <div class="relative flex items-start mb-8">
                    <div class="absolute -left-8 flex items-center justify-center w-8 h-8">
                        <div class="w-[18px] h-[18px] rounded-full flex items-center justify-center bg-blue-500">
                            <template v-if="isStep1Done">
                                <check-outlined class="text-white text-xs" />
                            </template>
                            <template v-else-if="isStep1Loading">
                                <loading-outlined class="text-white text-xs" />
                            </template>
                            <template v-else>
                                <div class="text-white text-xs">1</div>
                            </template>
                        </div>
                    </div>
                    <div class="flex-1 ml-2 mt-2">
                        <div class="text-sm" :class="{ 'text-blue-500': isStep1Active }">
                            {{ titleOfStep1 }}
                        </div>
                        <div v-if="isStep1Active && lastMessage" class="mt-1 text-xs text-gray-500 last-content">
                            {{ lastMessage }}
                        </div>
                    </div>
                </div>

                <div class="relative flex items-start mb-8">
                    <div class="absolute -left-8 flex items-center justify-center w-8 h-8">
                        <div class="w-[18px] h-[18px] rounded-full flex items-center justify-center bg-blue-500">
                            <template v-if="isStep2Done">
                                <check-outlined class="text-white text-xs" />
                            </template>
                            <template v-else-if="isStep2Loading">
                                <loading-outlined class="text-white text-xs" />
                            </template>
                            <template v-else>
                                <div class="text-white text-xs">2</div>
                            </template>
                        </div>
                    </div>
                    <div class="flex-1 ml-2 mt-2">
                        <div class="text-sm" :class="{ 'text-blue-500': isStep2Active }">
                            {{ titleOfStep2 }}
                        </div>
                        <div v-if="isStep2Active && lastMessage" class="mt-1 text-xs text-gray-500 last-content">
                            {{ lastMessage }}
                        </div>
                    </div>
                </div>

                <div class="relative flex items-start">
                    <div class="absolute -left-8 flex items-center justify-center w-8 h-8">
                        <div class="w-[18px] h-[18px] rounded-full flex items-center justify-center bg-blue-500">
                            <template v-if="isStep3Done">
                                <check-outlined class="text-white text-xs" />
                            </template>
                            <template v-else-if="isStep3Loading">
                                <loading-outlined class="text-white text-xs" />
                            </template>
                            <template v-else>
                                <div class="text-white text-xs">3</div>
                            </template>
                        </div>
                    </div>
                    <div class="flex-1 ml-2 mt-2">
                        <div class="text-sm" :class="{ 'text-blue-500': isStep3Active }">
                            {{ titleOfStep3 }}
                        </div>
                        <div v-if="isStep3Active && lastMessage" class="mt-1 text-xs text-gray-500 last-content">
                            {{ lastMessage }}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- <div v-else class="text-center py-12">
            <div class="w-20 h-20 mx-auto mb-4 bg-gray-50 rounded-full flex items-center justify-center">
                <loading theme="outline" size="30" class="text-gray-400 animate-spin" />
            </div>
            <h3 class="text-lg font-medium text-gray-500 mb-2">写作中...</h3>
        </div> -->

    </div>
</template>

<script setup lang="ts">
import type { CreatorsInfo } from '@/services/types/appMessage';
import type { SubmissionExtraInfo, SubmissionInfo } from '@/services/types/submission';
import {
    SubmissionCreatePPTStage,
    SubmissionCreatePaperStage,
    SubmissionStage, SubmissionStatus
} from '@/utils/constants';
import { CheckOutlined, LoadingOutlined } from '@ant-design/icons-vue';
import { computed } from 'vue';


interface Props {
    submission: SubmissionInfo
    creatorData: CreatorsInfo
    // currentWaitingCount: number
}
const props = withDefaults(defineProps<Props>(), {})

const currentStage = computed(() => {
    if (!props.submission.extra) {
        return undefined
    }

    if (props.submission.status == SubmissionStatus.error) {
        return undefined
    }

    const extra: SubmissionExtraInfo = JSON.parse(props.submission.extra)
    return extra.stage
})

const lastMessage = computed(() => {
    if (!props.submission.extra) {
        return undefined
    }

    const extra: SubmissionExtraInfo = JSON.parse(props.submission.extra)
    return extra.lastMessage
})

const titleOfStep1 = computed(() => {
    // console.log('currentStage.value ==>', currentStage.value)
    // if (props.submission.status == SubmissionStatus.ing) {
    //     if (currentStage.value == SubmissionStage.studying) {
    //         //文献综述的状态
    //         return '学习中'
    //     }
    //     if (currentStage.value == SubmissionStage.drafting) {
    //         //文献综述的状态
    //         return '写作中'
    //     }
    // }
    // if (isStep1Done.value) {
    //     return '写作准备'
    // }
    // return '写作中'

    return '资料收集与分析'
})

const titleOfStep2 = computed(() => {
    // if (!currentStage.value) {
    //     return '等待中'
    // }

    // if (currentStage.value == SubmissionCreatePPTStage.creatingOutline) {
    //     return '等待中'
    // }

    // if (currentStage.value == SubmissionCreatePPTStage.creatingPages) {
    //     return '写作内容'
    // }

    // if (
    //     currentStage.value == SubmissionCreatePPTStage.creatingPages ||
    //     currentStage.value == SubmissionCreatePaperStage.creatingPages
    // ) {
    //     return '写作内容'
    // }

    // if (currentStage.value == SubmissionCreatePPTStage.completing) {
    //     return '即将完成'
    // }

    // if (currentStage.value == SubmissionCreatePPTStage.completed) {
    //     return '写作内容'
    // }

    // return '等待中'
    return "内容撰写与优化"
})

const titleOfStep3 = computed(() => {
    // if (!currentStage.value) {
    //     return '等待中'
    // }

    // if (currentStage.value == SubmissionCreatePPTStage.creatingOutline) {
    //     return '等待中'
    // }

    // if (
    //     currentStage.value == SubmissionCreatePPTStage.creatingPages ||
    //     currentStage.value == SubmissionCreatePaperStage.creatingPages
    // ) {
    //     return '等待中'
    // }

    // if (currentStage.value == SubmissionCreatePPTStage.completing) {
    //     return '即将完成'
    // }

    // if (currentStage.value == SubmissionCreatePPTStage.completed) {
    //     return '即将完成'
    // }

    // return '等待中'

    return '格式整理与定稿'
})


const isUseLargeWorker = computed(() => {
    if (props.creatorData.creator.code == 'ppt') {
        return true
    }
    return props.creatorData.creator.config?.isUseLargeWorker || false
})

const progressValue = computed(() => {
    if (!props.submission.extra) {
        return 0
    }
    // <!-- "extra": "{\"current\":4,\"total\":12}", -->
    const extra = JSON.parse(props.submission.extra)
    if (!extra.current || extra.current == 0) {
        return 0
    }
    if (!extra.total || extra.total <= 0) {
        return 0
    }
    if (extra.current == extra.total) {
        return 100
    }
    return ((extra.current / extra.total) * 100).toFixed(0)
})

const queueIndex = computed(() => {
    if (!props.submission.extra) {
        return
    }
    const extra = JSON.parse(props.submission.extra)
    return extra.queueIndex
})

const isStep1Active = computed(() => {
    return !currentStage.value ||
        currentStage.value === SubmissionStage.studying ||
        currentStage.value === SubmissionStage.drafting
})

const isStep1Loading = computed(() => {
    return isStep1Active.value && currentStage.value !== SubmissionCreatePPTStage.completed
})

const isStep1Done = computed(() => {
    return currentStage.value === SubmissionCreatePPTStage.creatingPages ||
        currentStage.value === SubmissionCreatePaperStage.creatingPages ||
        currentStage.value === SubmissionCreatePPTStage.completing ||
        currentStage.value === SubmissionCreatePPTStage.completed

})

const isStep2Active = computed(() => {
    return currentStage.value == SubmissionCreatePaperStage.creatingOutline || currentStage.value === SubmissionCreatePPTStage.creatingPages || currentStage.value === SubmissionCreatePaperStage.creatingPages
})

const isStep2Loading = computed(() => {
    return isStep2Active.value && currentStage.value !== SubmissionCreatePPTStage.completed
})

const isStep2Done = computed(() => {
    return currentStage.value === SubmissionCreatePPTStage.completing ||
        currentStage.value === SubmissionCreatePPTStage.completed
})

const isStep3Active = computed(() => {
    return currentStage.value === SubmissionCreatePPTStage.completing
})

const isStep3Loading = computed(() => {
    return isStep3Active.value && currentStage.value !== SubmissionCreatePPTStage.completed
})

const isStep3Done = computed(() => {
    return currentStage.value === SubmissionCreatePPTStage.completed
})
</script>

<style>
.nut-steps-vertical .nut-step-line {
    background-color: #1e99ff;
}

.nut-step.nut-step-wait .nut-step-icon.is-icon {
    background-color: #1e99ff;
}
</style>
