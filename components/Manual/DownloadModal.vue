<template>
    <Modal :model-value="modelValue" @update:model-value="$emit('update:model-value', $event)" title="下载文档" :width="500"
        :show-footer="false">
        <div class="p-6 space-y-4 text-center">
            <div class="text-lg font-medium text-gray-900">
                2万硬币（价值￥2元）
            </div>
            <div class="text-gray-500 line-through">
                原价：5万硬币（价值￥5元）
            </div>

            <div class="mt-4">
                <!-- 状态1：未登录 -->
                <div v-if="!userStore.isLogined" class="flex items-center justify-center space-x-2">
                    <span>你的硬币余额：</span>
                    <span @click="handleLogin" class="text-blue-500 cursor-pointer hover:text-blue-600">登录</span>
                    <span class="text-gray-500">后查看</span>
                </div>

                <!-- 状态2和3：已登录 -->
                <template v-else>
                    <div class="flex items-center justify-center space-x-2">
                        <span>你的硬币余额：</span>
                        <span class="text-blue-500 font-medium">{{ userStore.currentLoginInfo?.coinBalance || 0
                        }}</span>
                    </div>
                </template>
            </div>

            <!-- 三个状态互斥显示 -->
            <!-- 状态1：未登录时显示用户福利 -->
            <div v-if="!userStore.isLogined" class="bg-gray-50 p-4 rounded-lg mt-6">
                <div class="font-medium mb-2">新用户福利</div>
                <div class="text-sm text-gray-600">
                    <p>使用手机号注册为新用户，即送1万硬币</p>
                    <p>邀请好友得更多硬币奖励！</p>
                </div>
            </div>

            <!-- 状态2：已登录但硬币不足时显示充值按钮 -->
            <div v-else-if="(userStore.currentLoginInfo?.coinBalance || 0) < REQUIRED_COINS"
                class="mt-6 flex justify-center">
                <button @click="handleRecharge"
                    class="px-8 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                    充值
                </button>
            </div>

            <!-- 状态3：已登录且硬币足够时显示确认支付按钮 -->
            <div v-else class="mt-6 flex justify-center">
                <button @click="handleDownload"
                    class="px-8 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
                    :disabled="isLoading">
                    <template v-if="isLoading">
                        <div class="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        <span>处理中...</span>
                    </template>
                    <span v-else>确认支付</span>
                </button>
            </div>
        </div>
    </Modal>
</template>

<script setup lang="ts">
import { message } from 'ant-design-vue';
import { computed, ref } from 'vue';
import { useRoute } from 'vue-router';
import { seoFileBuy } from '~/api/seo';
import { getBalance } from '~/api/user';
import Modal from '~/components/Common/Modal.vue';
import { useTracking } from '~/composables/useTracking';
import { useRechargeStore } from '~/stores/recharge';
import { useUserStore } from '~/stores/user';
const { track } = useTracking();

const route = useRoute();
const router = useRouter();
const REQUIRED_COINS = 20000;
const userStore = useUserStore();
const rechargeStore = useRechargeStore();

const props = defineProps({
    modelValue: {
        type: Boolean,
        required: true
    },
});

const emit = defineEmits(['update:model-value', 'download']);

const canDownload = computed(() => {
    return userStore.isLogined && (userStore.currentLoginInfo?.coinBalance || 0) >= REQUIRED_COINS;
});

const handleClose = () => {
    emit('update:model-value', false);
};

const isLoading = ref(false);


const postId = computed(() => route.params.id);



const handleDownload = async () => {

    if (!canDownload.value || isLoading.value) return;

    isLoading.value = true;

    try {
        const urlRes = await $fetch<any>(`https://www.xiaoin.com.cn/wp-json/wp/v2/posts/${postId.value}`, {
            params: {
                _fields: 'link'
            }
        });

        if (!urlRes?.link) {
            throw new Error('获取文章原始链接失败');
        }

        const params = {
            site: 'https://www.xiaoin.com.cn',
            sourceUrl: urlRes.link,
            articleId: `${postId.value}`
        }

        const res = await seoFileBuy(params);

        if (!res.ok) {
            message.error(res.message || '购买失败');
            return
        }

        message.success(res.message || '购买成功');
        // 获取最新余额并更新用户信息
        const resultBalance = await getBalance();
        if (resultBalance.ok && userStore.currentLoginInfo) {
            userStore.currentLoginInfo.coinBalance = resultBalance.data;
        }
        router.push(`/manual/detail/${res.data.id}`);
        emit('download');
        handleClose();

        track('manual_buy',
            postId.value.toString(),
            '文库购买'
        );
    } catch (error) {
        console.error('下载出错:', error);
    } finally {
        isLoading.value = false;
    }
};

const handleLogin = () => {
    userStore.openLoginModal();
    handleClose();
};

const handleRecharge = () => {
    rechargeStore.openRechargeModal(RechargeModalTab.coin);
};
</script>