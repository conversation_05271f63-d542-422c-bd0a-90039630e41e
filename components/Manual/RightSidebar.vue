<template>
    <div class="flex flex-col items-center w-full">

        <!-- 正文 -->
        <div ref="contentRef" class="flex flex-col items-center w-full">
            <div class="m-4 w-full" v-if="!showAll">
                <NuxtLink :to="`/create/${code}`" :tag="isMobileDevice() ? 'div' : 'a'" @click="handleAIButtonClick"
                    class="w-full h-[59px] bg-gradient-to-br from-[#5B69E5] to-[#7FA9FF] rounded-[10px] shadow-sm flex justify-center items-center cursor-pointer">
                    <img width="20px" height="20px" class="mr-[2px]"
                        src="https://static-1256600262.file.myqcloud.com/xiaoin-pc/images/seo/writing-code.png"
                        alt="" />
                    <span class="text-[22px] font-medium text-white leading-[37px]">AI写同款</span>
                </NuxtLink>
            </div>


            <div v-if="!showAll"
                class="h-[59px] w-full bg-white border-2 border-[#2551B5] rounded-[10px] shadow-lg flex justify-center items-center hover:shadow-xl transition-shadow cursor-pointer"
                @click="handleDownloadClick">
                <img width="20px" height="20px" class="mr-[2px]"
                    src="https://static-1256600262.file.myqcloud.com/xiaoin-pc/images/seo/download-code.png" alt="">
                <span
                    class="text-[22px] font-medium text-[#2551B5] flex justify-center items-center leading-[37px]">下载此文档</span>
            </div>


            <button class="m-4 w-full" v-if="showAll">
                <NuxtLink :to="`/create/${code}`" :tag="isMobileDevice() ? 'div' : 'a'" @click="handleAIButtonClick">
                    <img src="https://static-1256600262.file.myqcloud.com/xiaoin-pc/images/seo/writing-top.png" alt="">
                </NuxtLink>
            </button>


            <div class="bg-white rounded-lg shadow-sm p-4 m-4 w-full">
                <div class="flex items-center mb-4">
                    <img width="18px" height="18px"
                        src="https://static-1256600262.file.myqcloud.com/xiaoin-pc/images/seo/hot-code.png" alt="">
                    <span class="text-base font-medium text-[#333333] leading-[25px]">{{ showAll ? '热门文章' : '更多推荐'
                        }}</span>
                </div>
                <!-- 加载状态 -->
                <div v-if="!posts" class="py-8 text-center text-gray-500">
                    <div class="flex justify-center mb-2">
                        <div class="animate-spin rounded-full h-6 w-6 border-2 border-blue-600 border-t-transparent">
                        </div>
                    </div>
                    <p>数据加载中...</p>
                </div>
                <!-- 热门文章列表 -->
                <div v-else class="flex flex-col w-full">
                    <div v-for="item in sortedPosts" :key="item.id"
                        class="w-full py-2.5 border-b border-gray-100 last:border-b-0">
                        <button @click="() => handleArticleClick(item)"
                            class="flex items-center justify-between w-full group bg-transparent border-0 p-0 text-left">
                            <h3 class="text-[14px] font-normal text-[#333333] leading-[21px] group-hover:text-blue-600 transition-colors line-clamp-1 flex-1"
                                v-html="item.title">
                            </h3>
                            <span class="text-xs font-normal text-[#999999] leading-[17px] ml-2 whitespace-nowrap">
                                浏览量：{{ item.views || 0 }}次
                            </span>
                        </button>
                    </div>
                </div>
            </div>
        </div>


        <div v-show="showScrollButton" class="fixed bottom-24 right-8 z-50">
            <button @click="handleScrollToTop"
                class="border border-gray-300 rounded-lg p-1.5 sm:p-2 text-gray-500 hover:text-gray-700 transition-colors relative">
                <arrow-up theme="outline" size="24" fill="#333" />
            </button>
        </div>


        <!-- 下载弹窗 -->
        <DownloadModal v-model="showDownloadModal" />



    </div>
</template>

<script setup lang="ts">
import { ArrowUp } from '@icon-park/vue-next';
import { message } from 'ant-design-vue';
import { onMounted, onUnmounted, ref } from 'vue';
import { getPayedFile } from '~/api/seo';
import { useTracking } from '~/composables/useTracking';
import { UserService } from '~/services/user';
import { isMobileDevice, loadSeoFriendlySchemeLink } from '~/utils/manual/manual';

interface Props {
    parentCategoryId: number;
    showAll?: boolean;
    code: any;
    category?: any;
}

const props = withDefaults(defineProps<Props>(), {
    showAll: false,
    category: null,
});

const { track } = useTracking();
const route = useRoute();
const router = useRouter();

const categoryId = computed(() => route.params.id as string);
const device = ref(true)

interface WPPost {
    id: number;
    title: {
        rendered: string;
    };
    date: string;
    post_views_count: number;
    _embedded?: {
        'wp:term'?: Array<Array<{
            slug: string;
        }>>;
    };
}

// 获取热门文章
const { data: posts } = await useAsyncData(`top-posts-${props.parentCategoryId}`, async () => {
    try {
        const posts = await $fetch(`https://www.xiaoin.com.cn/wp-json/custom/v1/top-posts`, {
            params: {
                category_id: props.parentCategoryId
            }
        }) as WPPost[];

        // 按浏览量排序
        return posts.sort((a, b) => (b.post_views_count || 0) - (a.post_views_count || 0));
    } catch (error) {
        // console.error('获取热门文章失败:', error);
        return [];
    }
});

// 直接使用返回的文章列表
const sortedPosts = computed(() => posts.value?.map(post => ({
    ...post,
    views: post.post_views_count || 0 // 转换为视图所需的views字段
})) || []);

const emit = defineEmits(['scrollToTop']);

const handleScrollToTop = () => {
    emit('scrollToTop');
};

const contentRef = ref<HTMLElement | null>(null);
const showScrollButton = ref(false);
const observer = ref<IntersectionObserver | null>(null);
const showDownloadModal = ref(false);
const isPurchased = ref(false);
const purchaseData = ref<any>(null);

const checkArticlePurchaseStatus = async () => {
    try {
        const res = await getPayedFile('https://www.xiaoin.com.cn', categoryId.value);
        isPurchased.value = !!res.data;
        purchaseData.value = res.data;
    } catch (error) {
        // console.error('检查购买状态失败:', error);
        isPurchased.value = false;
        purchaseData.value = null;
    }
}

const handleDownloadClick = async () => {

    if (!UserService.isLogined()) {
        showDownloadModal.value = true;
        return;
    }

    // 点击时检查购买状态
    await checkArticlePurchaseStatus();

    if (isPurchased.value && purchaseData.value?.id) {
        // 如果已购买，直接跳转到下载页面
        router.push(`/manual/detail/${purchaseData.value.id}`);
    } else {
        // 如果未购买，显示购买弹窗
        showDownloadModal.value = true;
    }
    track('manual_download', props.parentCategoryId.toString(), '下载按钮点击')
};

onMounted(async () => {
    observer.value = new IntersectionObserver(
        (entries) => {
            // 当目标元素不在视口内时，显示按钮
            showScrollButton.value = !entries[0].isIntersecting;
        },
        {
            threshold: 0.1 // 当10%的内容可见时触发
        }
    );

    if (contentRef.value) {
        observer.value.observe(contentRef.value);
    }

    await waitForPageLoad();
});




function waitForPageLoad() {
    return new Promise<void>((resolve) => {
        if (document.readyState === "complete") {
            resolve(); // 页面已加载，直接执行
        } else {
            window.addEventListener("load", () => resolve(), { once: true });
        }
    });
}

onUnmounted(() => {
    if (observer.value) {
        observer.value.disconnect();
    }
});

// 添加获取文章详情路径的方法
const handleArticleClick = async (item: WPPost) => {
    if (!item?.id) {
        console.error('文章ID不存在');
        return;
    }

    try {
        const response = await $fetch<WPPost>(`https://www.xiaoin.com.cn/wp-json/wp/v2/posts/${item.id}`, {
            params: {
                _fields: 'id,title,content,date,categories,meta,acf,_links,_embedded',
                _embed: 'wp:term'
            }
        });


        const postCategories = response._embedded?.['wp:term']?.[0] || [];

        if (response && postCategories.length > 0) {
            const categorySlug = postCategories[0].slug;
            navigateTo(`/manual/${categorySlug}/${item.id}`);
        }
    } catch (error) {
        console.error('获取文章详情失败:', error);
    }
};



// 处理 AI 按钮点击
const handleAIButtonClick = (event: Event) => {

    if (isMobileDevice()) {
        setTimeout(() => {
            handleShare();
            track('manual_aisameparagraph', props.parentCategoryId.toString(), 'AI写同款按钮点击')
        }, 50);

    } else {
        // PC端跳转到创建页面
        console.log('PC端跳转到创建页面');
        navigateTo(`/create/${props.code}`);
        track('manual_aisameparagraph', props.parentCategoryId.toString(), 'AI写同款按钮点击')
    }
}

const handleShare = () => {
    console.log('handleShare跳转小程序');
    loadSeoFriendlySchemeLink(props.code, `channel=comcn-${route.params.id || props.code}`)
}
</script>