<template>
  <div class="flex items-center space-x-3">
    <!-- 下载APP -->
    <button class="relative group">
      <div
        class="relative flex items-center bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200/50 px-4 py-2 rounded-lg hover:shadow-md transition-all duration-200">
        <download theme="filled" size="20" class="mr-2 text-blue-500" />
        <span
          class="font-medium text-transparent bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text">下载APP</span>
        <div class="ml-2 bg-blue-500/10 rounded-full px-2 py-0.5 text-xs text-blue-600">
          新版本
        </div>
      </div>
    </button>


    <div class="relative group">
      <button class="flex items-center text-white hover:text-blue-200" @click="navigateTo('/profile')">
        <img src="https://aippt-domestic.aippt.com/aippt-web/%E5%A4%B4%E5%83%8F.png" alt="User Avatar"
          class="w-10 h-10 rounded-full mr-2 border-2 border-white">
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Download, Shield } from '@icon-park/vue-next'
</script>