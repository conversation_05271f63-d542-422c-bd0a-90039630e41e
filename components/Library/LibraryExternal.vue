<template>
    <div class="flex flex-col space-y-2">
        <button v-for="tab in tabs" :key="tab.value" @click="handleAddFromSource(tab.value)"
            class="flex-1 flex items-center space-x-2 text-xs sm:text-sm px-3 sm:px-4 py-2 text-[#333333] rounded-lg transition-colors font-medium hover:text-[#2551B5]">
            <component :is="tab.icon" theme="outline" size="18" class="text-inherit" :fill="'currentColor'" />
            <span>{{ tab.label }}</span>
        </button>


        <UModal :model-value="modelValue" :ui="{
            width: 'sm:max-w-lg md:max-w-xl lg:max-w-2xl',
            height: 'h-[370px]',
            container: 'items-center',
        }">
            <div class="w-full flex justify-evenly items-center px-2 mb-[20px] ">
                <div class="flex-1 p-2"></div>
                <div class="flex p-2 ">
                    <div class="px-4 py-2 text-base transition-colors relative group mt-4">
                        <span class="pb-1 inline-block text-[#333333]">{{tabs.find(tab => tab.value ===
                            activeTab)?.label
                            }}</span>
                    </div>
                </div>
                <div class="flex-1 flex justify-end p-2">
                    <button @click="modelValue = false" class="text-gray-400 hover:text-gray-600">
                        <close theme="outline" size="18" fill="#333" />
                    </button>
                </div>
            </div>

            <div class="h-[225px] mb-[30px] mx-[20px] sm:mb-[40px] sm:mx-[40px] md:mb-[50px] md:mx-[70px]">
                <div class="mb-5">
                    <label class="text-sm font-medium  text-[#333333]">{{tabs.find(tab => tab.value ===
                        activeTab)?.slogan
                    }}</label>
                </div>

                <div class="mb-8">
                    <textarea v-model="inputValue" :placeholder="tabs.find(tab => tab.value === activeTab)?.placeholder"
                        class="w-full h-[96px] px-4 py-2.5 text-base bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none placeholder:text-[15px] placeholder:text-[#999999] resize-none"></textarea>
                </div>

                <div class="flex justify-center">
                    <button @click="importContent" :disabled="!isValidUrl"
                        class="px-6 py-3 text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 disabled:from-gray-400 disabled:to-gray-500 rounded-lg transition-colors shadow-sm">
                        {{tabs.find(tab => tab.value === activeTab)?.buttonText}}
                    </button>
                </div>
            </div>
        </UModal>
    </div>
</template>

<script setup lang="ts">
import { addFiles } from '@/api/repositoryFile.js';
import { generatePutUrl, uploadByUrl } from '@/api/upload';
import { removeQuestionMarkText } from '@/utils/utils';
import { Close, Copy, Search, Unlink } from '@icon-park/vue-next';
import { message } from 'ant-design-vue';
import axios from 'axios';
import { computed, ref } from 'vue';
import { useUserStore } from '~/stores/user';

// 定义组件 props
const props = defineProps<{
    folderId?: string
}>()


const user = useUserStore()
const spaceId = computed(() => {
    return user.currentLoginInfo?.id || ''
})


const emit = defineEmits(['action'])

const modelValue = ref(false)
const activeTab = ref('url')
const inputValue = ref('')

const handleAddFromSource = (type: string) => {
    activeTab.value = type
    modelValue.value = true
}

const tabs = ref([
    {
        label: '网址导入',
        value: 'url',
        slogan: '请避免非法抓取他人网站的侵权行为，保证链接可公开访问，且网站内容可复制抓取的网页仅为内容固定不变的静态网页，例如新闻文章、产品介绍等',
        placeholder: '请粘贴网址或输入链接，一次支持添加1个网址',
        buttonText: '导入网页',
        icon: markRaw(Unlink)
    },
    {
        label: '搜索添加',
        value: 'search',
        slogan: '搜索专业学术文献，一键打包下载',
        placeholder: '请输入你想搜索的文献题目或关键词',
        buttonText: '搜索',
        icon: markRaw(Search)
    },
    {
        label: '文本粘贴',
        value: 'paste',
        slogan: '以TXT格式保存到知识库',
        placeholder: '请输入你想保存文本内容',
        buttonText: '添加',
        icon: markRaw(Copy)
    }
])


const isValidUrl = computed(() => {
    if (activeTab.value === 'paste') {
        // 文本粘贴模式下，只要有内容就可以
        return inputValue.value.trim().length > 0
    }

    if (activeTab.value === 'url') {
        // URL模式下验证URL格式
        try {
            new URL(inputValue.value)
            return true
        } catch {
            return false
        }
    }

    // 搜索模式下，只要有内容就可以
    return inputValue.value.trim().length > 0
})

// 添加网址导入方法
const importUrl = () => {
    const userStore = useUserStore()
    if (!userStore.isLogined) {
        userStore.openLoginModal()
        return
    }

    if (!inputValue.value) {
        message.error('请输入有效的网址')
        return
    }

    emit('action', { type: 'urlImported', value: inputValue.value })
    inputValue.value = ''
    modelValue.value = false
}

// 添加搜索导入方法
const importSearch = () => {
    const userStore = useUserStore()
    if (!userStore.isLogined) {
        userStore.openLoginModal()
        return
    }
    if (inputValue.value) {
        // 这里可以添加搜索逻辑
        console.log('搜索内容:', inputValue.value)
        // TODO: 实现搜索功能
        navigateTo(`/scholar/search?keyword=${inputValue.value}`)
    }
}

// 添加文本粘贴导入方法
const importPaste = async () => {
    const userStore = useUserStore()
    if (!userStore.isLogined) {
        userStore.openLoginModal()
        return
    }

    if (!inputValue.value.trim()) {
        message.error('请输入文本内容')
        return
    }

    try {
        // 取前8个字符作为文件名，如果不足8个字符则使用全部内容
        const fileName = inputValue.value.trim().substring(0, 8) + '.txt'

        // 创建文本文件的 Blob
        const textBlob = new Blob([inputValue.value], { type: 'text/plain;charset=utf-8' })

        // 创建 File 对象，添加更多属性
        const textFile = new File([textBlob], fileName, {
            type: 'text/plain',
            lastModified: Date.now()
        })

        // 直接上传新创建的文件
        const cosClientAndParams = await generatePutUrl({
            filename: fileName
        })

        if (!cosClientAndParams.ok) {
            message.error('文件上传失败，请重试')
            return
        }
        // 上传文件到云存储 - 尝试直接使用 Blob
        const response = await axios.put(cosClientAndParams.data.url, textBlob, {
            headers: {
                'Content-Type': 'text/plain; charset=utf-8'
            }
        })

        console.log('上传响应:', response)
        if (response.status !== 200) {
            message.error('文件上传失败')
            return
        }

        const params = {
            fileName: fileName,
            fileUrl: removeQuestionMarkText(cosClientAndParams.data.url),
            fileSha256: ''
        }

        // 保存文件信息到系统
        const uploadByUrlResult = await uploadByUrl(params)

        if (!uploadByUrlResult.ok) {
            message.error('文件保存失败')
            return
        }

        const res = await addFiles({
            "spaceId": spaceId.value,
            "folderId": props.folderId || '',
            fileIds: [uploadByUrlResult.data.id]
        })

        if (res?.code == HTTP_STATUS.MOBILE_NOT_BOUND) {
            user.setShowPhoneBoundModal({
                status: BindPhoneModal.SHOW_BINDING,
            })
            return
        }
        // 触发成功事件
        emit('action', {
            type: 'pasteImported',
        })

        // 清空输入并关闭弹窗
        inputValue.value = ''
        modelValue.value = false
    } catch (error) {
        console.error('文本粘贴导入失败:', error)
        message.error('文本导入失败，请重试')
    }
}

// 添加文本粘贴导入方法
const importContent = () => {
    if (activeTab.value == 'url') {
        importUrl()
    } else if (activeTab.value == 'search') {
        importSearch()
    } else if (activeTab.value == 'paste') {
        importPaste()
    }
}



</script>