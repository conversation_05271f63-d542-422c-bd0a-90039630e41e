<template>
    <UModal :model-value="modelValue" @update:model-value="$emit('update:modelValue', $event)" :ui="{
        base: 'w-full',
        width: 'sm:max-w-lg md:max-w-xl lg:max-w-3xl',
        height: 'h-[600px]',
        background: 'bg-[#FFFFFF]',
        padding: 'p-0'
    }">
        <div class="h-full flex flex-col">
            <!-- 头部 -->
            <div class="p-6 pb-2">
                <div class="flex flex-row items-center justify-between space-x-2">
                    <span class="text-[#333333] text-lg font-medium">移动到</span>
                    <button @click="handleCancel" class="text-gray-400 hover:text-gray-600 z-10">
                        <close theme="outline" size="18" />
                    </button>
                </div>
            </div>

            <!-- 主体内容区域 -->
            <div class="flex-1 min-h-0 px-6">
                <div class="h-full flex flex-col bg-[#FFFFFF] border border-[#EEEEEE] rounded-lg p-4">
                    <!-- 导航路径 -->
                    <div class="flex items-center space-x-2 px-4">
                        <div class="flex items-center flex-wrap py-4">
                            <button @click="handleBackToParent()" :class="[
                                'text-sm transition-colors',
                                currentPath.length === 0
                                    ? 'text-blue-600 font-medium cursor-default'
                                    : 'text-gray-500 hover:text-blue-600 cursor-pointer'
                            ]">
                                个人知识库
                            </button>
                            <template v-for="(path, index) in currentPath" :key="index">
                                <div class="text-gray-400">
                                    <right theme="outline" size="16" />
                                </div>
                                <button @click="handleNavigateToPath(index)" :class="[
                                    'text-sm transition-colors',
                                    index === currentPath.length - 1
                                        ? 'text-blue-600 font-medium cursor-default'
                                        : 'text-gray-500 hover:text-blue-600 cursor-pointer'
                                ]">
                                    {{ path }}
                                </button>
                            </template>
                        </div>
                    </div>

                    <!-- 文件夹列表区域 -->
                    <div class="flex-1 overflow-y-auto min-h-0">
                        <!-- 新建文件夹输入框 -->
                        <div v-if="showNewFolderInput" class="flex justify-between p-3">
                            <div class="flex items-center flex-1">
                                <img src="https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/library/small-folders.png"
                                    alt="" class="w-5 h-5 mx-1 pb-0.5">
                                <input v-model="newFolderName" type="text"
                                    class="w-full border border-[#EEEEEE] rounded-lg p-2 focus:outline-none"
                                    placeholder="请输入文件夹名称" @keyup.enter="handleCreateFolder">
                            </div>
                            <div class="flex items-center space-x-2">
                                <button @click="handleCreateFolder" class="p-1 hover:text-[#2563EB]">
                                    <check theme="outline" size="18" />
                                </button>
                                <button @click="handleCancelCreate" class="p-1 hover:text-[#DC2626]">
                                    <close theme="outline" size="18" />
                                </button>
                            </div>
                        </div>

                        <div v-for="folder in filteredFolders" :key="folder.id" @click="handleSelectFolder(folder)"
                            @dblclick="handleEnterFolder(folder)" :class="[
                                'p-3  rounded-lg hover:bg-gray-100 cursor-pointer transition-colors',
                                { 'bg-gray-100': selectedFolderId === folder.id }
                            ]">
                            <div class="flex justify-between">

                                <div class="flex items-center">
                                    <img src="https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/library/small-folders.png"
                                        alt="" class="w-5 h-5 mx-1 pb-0.5">
                                    <span class="text-sm text-gray-700">{{ folder.folderName }}</span>
                                </div>


                                <button @click="handleEnterFolder(folder)" class="hover:text-[#2563EB]">
                                    <right theme="outline" size="18" />
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 底部按钮 -->
            <div class="p-6 pt-2 ">
                <div class="flex justify-between space-x-3">
                    <button @click="showNewFolderInput = true"
                        class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
                        新建文件夹
                    </button>

                    <div class="flex justify-end space-x-3">
                        <button @click="handleCancel"
                            class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
                            取消
                        </button>
                        <button @click="handleConfirm" :disabled="!canConfirm"
                            class="px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 disabled:from-gray-400 disabled:to-gray-500 rounded-lg transition-colors">
                            确定
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </UModal>
</template>

<script setup lang="ts">
import { addFolder, changeFolder, getSubFolders } from '@/api/repositoryFile';
import type { NewRepositoryFile, RepositoryFolder } from '@/services/types/repositoryFile';
import { useUserStore } from '@/stores/user';
import { Check, Close, Right } from '@icon-park/vue-next';
import { message } from 'ant-design-vue';
import { computed, onMounted, ref } from 'vue';

interface Props {
    modelValue: boolean
    currentItem: RepositoryFolder | NewRepositoryFile | null
    type: 'folder' | 'file'
}

const props = defineProps<Props>()

const emit = defineEmits<{
    'update:modelValue': [value: boolean]
    'confirm': []
}>()


const user = useUserStore()


const selectedFolderId = ref<string | null>(null)
const localFolders = ref<RepositoryFolder[]>([])
const currentPath = ref<string[]>([])
const folderHistory = ref<{ id: string; folders: RepositoryFolder[] }[]>([])
const showNewFolderInput = ref(false)
const newFolderName = ref('')
const hasOperations = ref(false)

const spaceId = computed(() => {

    return user.currentLoginInfo?.id || ''

})

// 过滤后的文件夹列表
const filteredFolders = computed(() => {
    if (props.type === 'folder') {
        // 如果是移动文件夹，需要过滤掉当前文件夹及其所有子文件夹
        return localFolders.value.filter(folder => {
            const currentFolderId = (props.currentItem as RepositoryFolder)?.id
            return folder.id !== currentFolderId && !currentPath.value.includes(folder.folderName)
        })
    }
    return localFolders.value
})

// 判断是否允许确认移动
const canConfirm = computed(() => {
    // 获取当前项的父文件夹ID
    const currentParentId = props.type === 'folder'
        ? (props.currentItem as RepositoryFolder)?.parentId
        : (props.currentItem as NewRepositoryFile)?.folderId

    // 获取当前所在的文件夹ID（根据路径历史）
    const currentLocationId = folderHistory.value.length > 0
        ? folderHistory.value[folderHistory.value.length - 1].id
        : '0'

    // 如果在根目录（currentLocationId === '0'）
    if (currentLocationId === '0') {
        // 如果当前项就在根目录下，必须选择一个文件夹
        if (currentParentId === '0') {
            return selectedFolderId.value !== null
        }
    }

    // 不能移动到当前文件所在的父文件夹
    if (selectedFolderId.value === currentParentId) {
        return false
    }

    // 如果在某个文件夹内，不选择任何文件夹表示移动到当前文件夹
    return true
})

// 初始化数据
const initializeData = () => {
    // 使用 handleEnterFolder 加载根目录
    handleEnterFolder({ id: '0', folderName: '个人知识库' } as RepositoryFolder)
}

onMounted(() => {
    initializeData()
})


// 选择文件夹
const handleSelectFolder = (folder: RepositoryFolder) => {
    selectedFolderId.value = folder.id
}

// 进入文件夹
const handleEnterFolder = async (folder: RepositoryFolder) => {
    try {

        const params = {
            folderId: folder.id
        }

        const res = await getSubFolders(spaceId.value, params)
        if (res.data) {
            // 如果是根目录，不需要保存历史记录
            if (folder.id !== '0') {
                // 保存当前状态到历史记录
                folderHistory.value.push({
                    id: folder.id,
                    folders: localFolders.value
                })
                // 更新当前路径
                currentPath.value.push(folder.folderName)
            }
            // 更新文件夹列表
            localFolders.value = res.data
            // 清除选中状态
            selectedFolderId.value = null
        }
    } catch (error) {
        console.error('获取子文件夹失败:', error)
        // message.error('获取文件夹列表失败')
    }
}

const handleBackToParent = async () => {
    if (folderHistory.value.length > 0) {
        const previousState = folderHistory.value.pop()
        if (previousState) {
            localFolders.value = previousState.folders
            currentPath.value.pop()
            selectedFolderId.value = null
        }
    } else {
        // 如果没有历史记录，说明在根目录，重新初始化
        initializeData()
    }
}

// 导航到路径
const handleNavigateToPath = async (targetIndex: number) => {
    // 如果点击的是当前路径，不做任何操作
    if (targetIndex === currentPath.value.length - 1) return

    // 计算需要回退的次数
    const stepsToGoBack = currentPath.value.length - 1 - targetIndex

    // 执行回退操作
    for (let i = 0; i < stepsToGoBack; i++) {
        await handleBackToParent()
    }
}

const handleCancel = () => {
    // 只在有操作时发送 confirm 事件
    if (hasOperations.value) {
        emit('confirm')
    }
    // 关闭弹窗
    emit('update:modelValue', false)
    // 重置所有状态
    selectedFolderId.value = null
    currentPath.value = []
    folderHistory.value = []
    hasOperations.value = false
    localFolders.value = []
}

// 确定移动
const handleConfirm = async () => {
    // 检查是否允许确认
    if (!canConfirm.value) return

    // 如果在非根目录且没有选中文件夹，使用当前所在的文件夹
    const currentLocationId = folderHistory.value.length > 0
        ? folderHistory.value[folderHistory.value.length - 1].id
        : '0'

    const toFolderId = selectedFolderId.value || (currentLocationId !== '0' ? currentLocationId : '0')
    const fromFolderId = props.currentItem?.folderId || '0'

    // props.type === 'folder'
    // ? (props.currentItem as RepositoryFolder)?.folderId
    // : (props.currentItem as NewRepositoryFile)?.folderId


    const params = {
        items: [{ id: props.currentItem?.id || '0', type: props.type }],
        fromFolderId,
        toFolderId
    }

    const res = await changeFolder(spaceId.value, params)
    if (!res.ok) {
        // console.error('移动文件失败')
        // message.error(res.message || '移动文件失败')
        return
    }
    hasOperations.value = true // 标记已进行操作
    message.success(res.message || '移动文件成功')
    emit('confirm')
    emit('update:modelValue', false)
    // 重置所有状态
    selectedFolderId.value = null
    currentPath.value = []
    folderHistory.value = []
    hasOperations.value = false
}

// 处理新建文件夹
const handleCreateFolder = async () => {
    if (!newFolderName.value.trim()) return

    try {
        const params = {
            folderName: newFolderName.value.trim(),
            parentId: folderHistory.value.length > 0
                ? folderHistory.value[folderHistory.value.length - 1].id
                : '0'
        }

        const res = await addFolder(spaceId.value, params)

        if (!res.ok) {
            // message.error(res.message || '创建文件夹失败')
            return
        }

        hasOperations.value = true // 标记已进行操作
        message.success(res.message || '创建文件夹成功')
        // 刷新文件夹列表
        handleEnterFolder({ id: selectedFolderId.value || '0', folderName: '' } as RepositoryFolder)
        // 重置状态
        showNewFolderInput.value = false
        newFolderName.value = ''
    } catch (error) {
        console.error('创建文件夹失败:', error)
        message.error('创建文件夹失败')
    }
}

// 取消新建文件夹
const handleCancelCreate = () => {
    showNewFolderInput.value = false
    newFolderName.value = ''
}
</script>