<template>
  <div class="simpleMindMap flex">
    <div id="mindMapContainer" ref="mindMapContainer" class="mindMapContainer flex-1"></div>
    <div class="operateContent">
      <div class="wrapCom">
        <span class="ope-icon" @click="mindExport">
          <download theme="outline" size="24" fill="#333" />
        </span>
        <span class="ope-icon ope-icon-zsy" @click="zoomFit">
          <screenshot-one theme="outline" size="24" fill="#333" />
        </span>
        <span class="ope-icon" @click="zoomOut">
          <minus theme="outline" size="24" fill="#333" />
        </span>
        <span class="ope-icon" @click="zoomIn">
          <plus theme="outline" size="24" fill="#333" />
        </span>
        <span class="ope-icon" @click="toFullscreenOneShow" v-if="!app?.isMouse">
          <full-screen-one theme="outline" size="24" fill="#333" />
        </span>
      </div>
    </div>


    <SidebarTrigger />
    <!-- 右侧抽屉区域 -->

    <div v-if="mindMap">
      <Contextmenu :mindMap="mindMap"></Contextmenu>

      <a-drawer :open="sidebarVisible" :width="300" placement="right" :closable="true" :mask="false"
        @close="handleDrawerClose" :rootClassName="'mind-map-drawer'" :get-container="false">
        <template #title>
          <span v-if="activeSidebar === 'shortcutKey'">快捷键</span>
          <span v-else-if="activeSidebar === 'outline'">大纲</span>
          <span v-else-if="activeSidebar === 'theme'">模版</span>
          <span v-else-if="activeSidebar === 'structure'">结构</span>
        </template>

        <Theme v-if="activeSidebar === 'theme'" :mindMap="mindMap" />
        <ShortcutKey v-else-if="activeSidebar === 'shortcutKey'" />
        <OutlineSidebar v-else-if="activeSidebar === 'outline'" :mindMap="mindMap" :wnMindMap="wnMindMap" />
        <Structure v-else-if="activeSidebar === 'structure'" :mindMap="mindMap" />
      </a-drawer>

    </div>
  </div>
</template>

<script setup lang="ts">
import { useMindMapStore } from '@/stores/mindMapStore';
import wnMindMap from '@/utils/simpleMindMap/index';
import {
  Download,
  FullScreenOne,
  Minus,
  Plus,
  ScreenshotOne
} from '@icon-park/vue-next';
import { updateAnswerContent, updateInspiration } from '~/api/create';
import Contextmenu from '~/components/SimpleMindMap/ContextMenu.vue';
import OutlineSidebar from '~/components/SimpleMindMap/OutlineSidebar.vue';
import ShortcutKey from '~/components/SimpleMindMap/ShortcutKey.vue';
import SidebarTrigger from '~/components/SimpleMindMap/SidebarTrigger.vue';
import Theme from '~/components/SimpleMindMap/Theme.vue';
import { useApp } from '~/composables/useApp';
import { UserService } from '~/services/user';

interface Props {
  mindMapData: any
  title?: string
  isReadOnly: boolean
  knowledgeId?: string
  submissionId?: string
}

const props = withDefaults(defineProps<Props>(), {
  title: '思维导图',
  isReadOnly: false
})

const app = useApp()
const mindMap = ref()
const mindMapStore = useMindMapStore()
const { sidebarVisible, activeSidebar } = storeToRefs(mindMapStore)


// 使用计算属性获取状态
// const activeSidebar = computed(() => mindMapStore.activeSidebar)
// const sidebarVisible = ref(false)

// watch(() => mindMapStore.activeSidebar, (val) => {
//   console.log("mindMapStore.activeSidebar ==>", val)
//   sidebarVisible.value = val !== ''
// })

let isFirstLoad = true // 用于标记是否是首次加载

// 修复类型问题
const scaleVal = ref(100) // 默认缩放比例

const init = () => {
  // 初始化
  mindMap.value = wnMindMap?.initMind('mindMapContainer', props.mindMapData, true, props.isReadOnly, mindMapStore.themeName)
  // 监听画布缩放事件
  mindMap.value.on('scale', (val: number) => {
    scaleVal.value = val * 100
  })
  // svg画布的鼠标按下事件监听
  mindMap.value.on('svg_mousedown', (_e: MouseEvent) => {
    // hideletCustomNoteContent()
  })

  // 浏览器窗口变化监听
  window.addEventListener('resize', () => {
    if (mindMap.value) {
      wnMindMap?.mindMapResize()
      wnMindMap?.mindMapFit()
    }
  })

  console.log("mindMap.value ==>", mindMap.value)
}

// 隐藏自定义备注弹窗
const hideletCustomNoteContent = () => {
  const isCustomNoteContent = document.getElementById('isCustomNoteContent')
  if (isCustomNoteContent) {
    isCustomNoteContent.style.display = 'none'
  }
}


const zoomOut = () => {
  wnMindMap?.narrow()
}
const zoomIn = () => {
  wnMindMap?.enlarge()
}
const zoomFit = () => {
  wnMindMap?.mindMapFit()
}
const toFullscreenOneShow = () => {
  if (!wnMindMap || !wnMindMap?.toFullscreenShow) {
    console.error('wnMindMap is not initialized or toFullscreenShow is not a function')
    return
  }
  wnMindMap?.toFullscreenShow()
}
const removeExtension = (filename: string) => {
  return filename.replace(/\.[^\.]+$/, '')
}
const mindExport = () => {
  wnMindMap?.mindMapGetData()
  const exportTitle = removeExtension(props.title || '思维导图')
  wnMindMap?.mindMapExportPng(exportTitle)
}

const renderMindMapData = (mindMapData: any) => {
  // wnMindMap.mindMapSetData(mindMapData)
  // wnMindMap.render()
}

onBeforeUnmount(() => {
  if (mindMap.value) {
    wnMindMap?.mindMapDestroy()
    mindMap.value = null
  }
})

onMounted(() => {

  if (!props.mindMapData) return

  init()

  // 设置自动保存
  if (!props.isReadOnly) {
    wnMindMap?.initAutoSave(async (data) => {
      // 如果是首次加载，不触发保存
      if (isFirstLoad) {
        isFirstLoad = false
        return
      }

      // 检查用户登录状态
      if (!UserService.isLogined()) {
        return
      }

      try {
        // 思维导图应用保存数据
        if (props.submissionId) {
          const params = {
            submissionId: props.submissionId,
            content: data
          }
          await updateAnswerContent(params)
          return
        }
        if (props.knowledgeId) {
          // 知识库思维导图保存数据
          const params = {
            id: props.knowledgeId, //// 知识库文件id
            inspiration: data
          }
          await updateInspiration(params)
        }
      } catch (error) {
        console.error('Failed to save mind map data:', error)
      }
    }, 2000) // 1秒的防抖时间
  }
})

// 处理抽屉关闭
const handleDrawerClose = () => {
  mindMapStore.hideSidebar()
}

defineExpose({
  renderMindMapData
})
</script>

<style lang="scss" scoped>
.simpleMindMap {
  width: 100%;
  height: 100%;
  padding: 0px 0px 0px 0px;
  box-sizing: border-box;
  background-color: #fff;
  position: relative;

  .operateContent {
    position: absolute;
    bottom: 24px;
    left: 28px;
    background: rgb(255, 255, 255);
    border: 1px solid rgb(232, 234, 242);
    display: flex;
    align-items: center;
    user-select: none;
    border-radius: 8px;

    .wrapCom {
      box-sizing: border-box;
      display: flex;
      align-items: center;
      margin: 0 8px;
    }

    .ope-icon {
      display: flex;
      color: #333;
      padding: 5px;
      font-size: 20px;
      cursor: pointer;
    }

    .ope-icon-zsy {
      font-size: 22px;
    }
  }

  .toolbarContainer {
    .toolbar {
      position: absolute;
      left: 130px;
      transform: translateX(-50%);
      top: 10px;
      width: max-content;
      display: flex;
      font-size: 12px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: rgba(26, 26, 26, 0.8);
      z-index: 2;

      .toolbarBlock {
        display: flex;
        background-color: #fff;
        padding: 10px 20px;
        border-radius: 6px;
        box-shadow: 0 2px 16px 0 rgba(0, 0, 0, 0.06);
        border: 1px solid rgba(0, 0, 0, 0.06);
        margin-right: 20px;
        flex-shrink: 0;
        position: relative;

        &:last-child {
          margin-right: 0px;
        }

        .toolbarNodeBtnList {
          display: flex;

          .toolbarBtn {
            display: flex;
            justify-content: center;
            // flex-direction: column;
            // cursor: pointer;
            margin-right: 20px;
            align-items: center;

            &:last-child {
              margin-right: 0px;
            }

            .text {
              margin-top: 3px;
            }
          }
        }
      }
    }
  }

  #mindMapContainer {
    width: 100%;
    height: 100%;
  }

  #mindMapContainer * {
    margin: 0;
    padding: 0;
  }

  .icon-posotion {
    position: absolute;
    left: 0;
    top: 0;
  }

  .nodeSelectModal {
    position: absolute;
    left: 0;
    top: 0;
    border-radius: 6px;
    box-shadow: 0 2px 16px 0 rgba(0, 0, 0, 0.06);
    border: 1px solid rgba(0, 0, 0, 0.06);
    background-color: #fff;
  }

  #isCustomNoteContent {
    display: none;
    background-color: #fff;
    padding: 10px 20px;
    border-radius: 6px;
    box-shadow: 0 2px 16px 0 rgba(0, 0, 0, 0.06);
    border: 1px solid rgba(0, 0, 0, 0.06);
    max-width: 300px;
    height: auto;
    max-height: 350px;
    overflow: hidden;
    overflow-y: auto;

    p {
      font-size: 12px;
    }
  }
}
</style>

<style lang="scss">
#isCustomNoteContent {
  p {
    font-size: 14px;
  }
}

// 添加抽屉相关样式
.mind-map-drawer {
  .ant-drawer-content-wrapper {
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  }

  .ant-drawer-header {
    padding: 12px 16px;
    border-bottom: 1px solid #e8e8e8;
  }

  .ant-drawer-body {
    padding: 0px;
  }
}
</style>

​
