<template>
  <div class="prose max-w-none h-full">
    <div v-if="!isShowSimpleMindMap" class="min-h-[400px] flex flex-col items-center justify-center">
      <img style="width: 127px; height: 127px; margin-bottom: 30px"
        src="//static-1256600262.file.myqcloud.com/xiaoin-h5/image/knowledge-mind-map.jpg" alt="" />
      <button class="bg-blue-500 text-white font-bold py-2 px-4 rounded" @click="analysisMindMap">
        开始解析
      </button>
    </div>
    <div v-else class="h-full flex justify-center items-center">
      <div v-if="isLoading">
        <div class="w-20 h-20 mx-auto mb-4 bg-gray-50 rounded-full flex items-center justify-center">
          <loading theme="outline" size="40" class="text-gray-400 animate-spin" />
        </div>
        <h3 class="text-lg font-medium text-gray-600 mb-2">加载中...</h3>
      </div>

      <template v-else>
        <template v-if="inspirationData">
          <ClientOnly class=" h-full">
            <MindForKnowledge class="w-full h-full min-w-[300px] min-h-[300px] bg-white" :mindMapData="inspirationData"
              :knowledgeId="knowledgeDetail?.id" :pListValue="pListValue" :isReadOnly="false">
            </MindForKnowledge>
          </ClientOnly>
        </template>
        <template v-else>
          <div>{{ KnowledgeAssistantTypeCn.mind }}解析失败</div>
        </template>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { repositoryFileGetDetail, repositoryFileStartInspiration } from '@/api/repositoryFile';
import MindForKnowledge from '@/components/Library/MindForKnowledge.vue';
import { type RepositoryFile } from '@/services/types/repositoryFile';
import { useUserStore } from '@/stores/user';
import { Loading } from '@icon-park/vue-next';
import markdown from 'simple-mind-map/src/parse/markdown.js';
import { onMounted, ref } from 'vue';
import { KnowledgeAssistantTypeCn } from '~/utils/constants';
import { convertPListByMarkdownList } from '~/utils/mind-map';


interface Props {
  knowledgeDetail: RepositoryFile | null
}
const props = withDefaults(defineProps<Props>(), {
  knowledgeDetail: null,
})

const isShowSimpleMindMap = ref(false)
const inspirationData = ref(null)
const pListValue = ref<any>([])

const user = useUserStore()

let interval: NodeJS.Timeout | undefined;
let timeout: NodeJS.Timeout;


const spaceId = computed(() => {

  return user.currentLoginInfo?.id || ''

})



const knowledgeInfo = ref(props.knowledgeDetail)

const getInspirationData = (datail: any) => {
  const knowledgeDetail = datail

  if (!knowledgeDetail?.processData?.inspiration_data) {
    return
  }
  if (knowledgeDetail?.processData?.inspiration_status !== 100) {
    return
  }
  let _inspiration_data = knowledgeDetail?.processData?.inspiration_data || ''

  if (interval) {
    clearInterval(interval)
  }
  isLoading.value = false


  try {
    const jsonMatch = JSON.parse(_inspiration_data)
    if (jsonMatch.content) {
      _inspiration_data = jsonMatch.content
    }
  } catch (e) {
  }

  const regex = /```markdown\n([\s\S]*?)\n```/
  _inspiration_data = _inspiration_data.replace(/\\n/g, '\n')
  const match = _inspiration_data.match(regex)
  if (match && match[1]) {
    _inspiration_data = match[1]
  }

  const dd = markdown.transformMarkdownTo(_inspiration_data)
  console.log('dd ==>', dd)
  pListValue.value = convertPListByMarkdownList(dd || [])

  if (!dd) {
    console.log('格式错误dd=>', knowledgeDetail?.processData?.inspiration_data)
  }
  inspirationData.value = dd

  isShowSimpleMindMap.value = true

}
const isLoading = ref(false)

const startTimer = () => {
  interval = setInterval(() => {
    getKnowledgeAssistantDetail()
  }, 5000)

  // 1分钟后清除定时器
  timeout = setTimeout(() => {
    clearInterval(interval)
    console.log('定时器已停止')
  }, 60000)
}

const analysisMindMap = async () => {
  try {
    isShowSimpleMindMap.value = true
    isLoading.value = true
    const res = await repositoryFileStartInspiration(spaceId.value, { id: props.knowledgeDetail?.id })

    if (!res.ok) {
      isLoading.value = false
      isShowSimpleMindMap.value = false
      // message.error(res.message || '解析思维导图错误')
      return
    }
    startTimer()
  } catch (error) {
    isLoading.value = false
  }
}

const getKnowledgeAssistantDetail = async () => {
  const result = await repositoryFileGetDetail({ spaceId: spaceId.value, id: props.knowledgeDetail?.id })

  if (!result.ok || !result.data) {
    isLoading.value = false
    if (result.code == HTTP_STATUS.SERVER_ERROR) {
      // message.error(result.message || '解析思维导图错误')
      return
    }
    // message.error(result.message || '解析思维导图错误')
    return
  }
  getInspirationData(result.data)
  knowledgeInfo.value = result.data
}

onMounted(() => {
  knowledgeInfo.value = props.knowledgeDetail
  getInspirationData(props.knowledgeDetail)

  if (knowledgeInfo.value?.processData?.inspiration_status != 100) {
    isLoading.value = true
    return
  }
  isLoading.value = false
})
</script>
