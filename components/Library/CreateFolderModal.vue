<template>
    <UModal :model-value="modelValue" @update:model-value="emit('update:modelValue', $event)" :ui="{
        container: 'items-center',
    }">
        <div class="p-6">
            <div class="space-y-4">
                <div class="space-y-2">
                    <label class="text-sm font-medium text-gray-700">新建文件夹</label>

                    <UInput v-model="folderName" placeholder="请输入文件夹名称" size="lg" color="gray" variant="outline"
                        class="w-full">
                    </UInput>

                </div>
                <div class="flex justify-end space-x-3">
                    <button @click="handleCancel"
                        class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
                        取消
                    </button>
                    <button @click="handleConfirm" :disabled="!folderName.trim()"
                        class="px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 disabled:from-gray-400 disabled:to-gray-500 rounded-lg transition-colors">
                        确定
                    </button>
                </div>
            </div>
        </div>
    </UModal>
</template>

<script setup lang="ts">
import { ref } from 'vue';

interface Props {
    modelValue: boolean
}

const props = defineProps<Props>()
const emit = defineEmits(['update:modelValue', 'confirm'])

const folderName = ref('')

const handleCancel = () => {
    folderName.value = ''
    emit('update:modelValue', false)
}

const handleConfirm = () => {
    if (folderName.value.trim()) {
        emit('confirm', {
            name: folderName.value.trim()
        })
        handleCancel()
    }
}
</script>