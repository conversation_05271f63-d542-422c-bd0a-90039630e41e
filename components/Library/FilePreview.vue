<template>
    <PdfFrame v-if="suffix == KnowledgeFileSuffix.PDF" :pdfUrl="url" :pageNum="pageNum" :meta="meta"
        ref="simplepageViewerRef" />
    <MarkDownPreview :url="url" :suffix="suffix" :original-url="originalUrl" :title="title"
        v-else-if="suffix == KnowledgeFileSuffix.MD || suffix == KnowledgeFileSuffix.TXT" />
    <ImagePreview :url="url" :suffix="suffix" :original-url="originalUrl" :title="title"
        v-else-if="suffix == KnowledgeFileSuffix.JPG || suffix == KnowledgeFileSuffix.PNG || suffix == KnowledgeFileSuffix.JPEG" />
    <div class="text-center py-4 text-gray-500" v-else-if="[`${KnowledgeFileSuffix.CSV}`].includes(suffix)">
        该文件格式暂不支持预览
    </div>
    <OfficPreview
        v-else-if="[`${KnowledgeFileSuffix.XLSX}`, `${KnowledgeFileSuffix.XLS}`, `${KnowledgeFileSuffix.PPTX}`, `${KnowledgeFileSuffix.PPT}`, `${KnowledgeFileSuffix.DOC}`, `${KnowledgeFileSuffix.DOCX}`].includes(suffix)"
        :url="`https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(url)}`" :suffix="suffix"
        :original-url="originalUrl" :title="title" />
    <WebSitePreview :url="url" :suffix="suffix" :original-url="originalUrl" :title="title"
        v-else="suffix == KnowledgeFileSuffix.HTML" />

</template>
<script lang="ts" setup>
import { KnowledgeFileSuffix } from '@/utils/constants';
import ImagePreview from './ImagePreview.vue';
import MarkDownPreview from './MarkDownPreview.vue';
import OfficPreview from './OfficPreview.vue';
import PdfFrame from './pdf/PdfFrame.vue';
import WebSitePreview from './WebSitePreview.vue';
interface Props {
    url: string
    suffix: string
    originalUrl: string
    title: string
    pageNum: number
    meta: string
}
const props = withDefaults(defineProps<Props>(), {
    url: '',
    suffix: '',
    originalUrl: '',
    title: '',
    pageNum: 1,
    meta: '',

})
</script>