<template>
    <div class="knowledge-img-preview">
        <div class="text-center mt-4">{{ title }}</div>
        <div class="knowledge-img-preview-content p-4">
            <img class="w-auto object-contain" :src="originalUrl" />
        </div>
    </div>
</template>
<script lang="ts" setup>

interface Props {
    url: string
    suffix: string
    originalUrl: string
    title: string
}
const props = withDefaults(defineProps<Props>(), {
    url: '',
    suffix: '',
    originalUrl: '',
    title: ''
})
</script>
<style>
.knowledge-img-preview {
    position: relative;
    display: flex;
    flex-direction: column;
    overflow: auto;
    height: 100%;
    border: none;
}
</style>