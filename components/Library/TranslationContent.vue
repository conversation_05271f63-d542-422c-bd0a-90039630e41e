<template>
  <div class="h-full p-0 overflow-auto max-w-none">

    <div v-if="isTranslateLoading && !isShowTranslateContent" style="width: 100%; height: 100%">
      <a-skeleton active />
    </div>
    <template v-else>
      <div v-if="isShowTranslateContent" class="p-6 bg-white border border-gray-100 shadow-sm rounded-xl">
        <div class="space-y-4">

          <div v-for="(item, index) in translateList" :key="index"
            class="rounded-lg p-2 mb-2 shadow-[0_4px_8px_rgba(0,0,0,0.5)]">

            <div class="flex items-center justify-between">
              <span class="flex-1 h-[10px] bg-gray-200"></span>
              <span class="text-l text-gray-500 px-2">
                {{ item[0].source.meta.page }}
              </span>
              <span class="flex-1 h-[10px] bg-gray-200"></span>
            </div>
            <!-- <button v-for="text in item" :key="text.sourceIndex" @click="handleClickTranslate(text)"
              class="relative p-1 m-0 leading-relaxed text-gray-600 transition-colors rounded-lg cursor-pointer hover:bg-blue-50"
              :class="{
                'my-knowledge-detail-translate-box-content': true,
                'translate-container-content-page-text-active': translateActiveKey == text.sourceIndex
              }">

              <div v-if="text.source.meta.layout_type == 'figure'" class="flex justify-center items-center">
                <a-image width="60%" :src="getImageUrl(text.source.meta.image)" />
              </div>
              <div v-if="text.content" v-html="(text.content)"></div>
              <div v-if="translateActiveKey == text.sourceIndex" @click.stop="handleCopy(text.content)"
                class="absolute top-[-40px] right-2 cursor-pointer">
                <div
                  class="flex items-center gap-1.5 text-sm text-gray-500 font-medium cursor-pointer h-8 w-16 border border-gray-200 rounded-lg shadow-md bg-white justify-center">
                  <img class="w-[11px] h-[11px]"
                    src="//static-1256600262.file.myqcloud.com/xiaoin-h5/icons/black_copy.png" alt="" />
                  复制
                </div>
              </div>
            </button> -->

            <p v-for="(text) in item" :key="text.sourceIndex" @click="handleClickTranslate(text)" :class="{
              'my-knowledge-detail-translate-box-content': true,
              'translate-container-content-page-text-active': translateActiveKey == text.sourceIndex
            }" class="relative p-1 m-0 leading-relaxed text-gray-600 transition-colors rounded-lg cursor-pointer">

              <!-- 标题渲染 -->
            <h2 v-if="text.source.meta.layout_type == 'title'" class="text-xl font-bold py-2">{{ text.content }}</h2>
            <!-- 兼容表格 -->
            <div v-if="text.content && text.source.meta.layout_type == 'table'"
              v-html="renderMarkdownContent(text.content)" class="flex text-center justify-center items-center py-2">
            </div>
            <!-- 正文渲染 -->
            <div
              v-if="text.content && text.source.meta.layout_type != 'title' && text.source.meta.layout_type != 'table' && !text.source.meta.image"
              v-html="setContent(text.content)" class="custom-html break-words whitespace-normal"></div>

            <div v-if="text.source.meta.image" class="flex justify-center items-center" @click.stop>
              <a-image :width="getImageWH(text.source.meta.bbox, 'width')"
                :height="getImageWH(text.source.meta.bbox, 'height')" :src="getImageUrl(text.source.meta.image)" />
            </div>
            <div v-if="translateActiveKey == text.sourceIndex" @click.stop="handleCopy(text.content)"
              class="absolute top-[-40px] right-2 cursor-pointer">
              <div
                class="flex items-center gap-1.5 text-sm text-gray-500 font-medium cursor-pointer h-8 w-16 border border-gray-200 rounded-lg shadow-md bg-white justify-center">
                <img class="w-[11px] h-[11px]"
                  src="//static-1256600262.file.myqcloud.com/xiaoin-h5/icons/black_copy.png" alt="" />
                复制
              </div>
            </div>
            </p>


          </div>
          <!-- 翻译中的骨架屏 -->
          <div v-if="isTranslateLoading" class="w-full h-[100px]">
            <a-skeleton active />
          </div>
        </div>
      </div>


      <div class="h-full" v-if="[
        FileTranslateStatusEnum.translationError,
        FileTranslateStatusEnum.noTranslation
      ].includes(translateStatus)
      ">
        <div class="flex flex-col items-center justify-center h-full">

          <div v-if="isClickStartTranslate" style="width: 100%; height: 100%">
            <a-skeleton active />
          </div>



          <template v-else>
            <div class="min-h-[400px] flex flex-col justify-center items-center">
              <img :src="`${cdnBaseUrl}/xiaoin-h5/image/knowledge-translate-360.png`" alt=""
                style="width: 127px; height: 127px; margin-bottom: 30px" />
              <div class="flex items-center space-x-6">
                <div class="flex items-center space-x-2">
                  <span class="text-sm text-gray-600">源语言</span>
                  <div
                    class="min-w-[120px] text-sm border border-gray-200 rounded-lg px-3 py-2 bg-gray-50 hover:bg-gray-100 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500/20">
                    <span value="auto">🔍 自动检测</span>
                  </div>
                </div>
                <div class="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-gray-500" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
                  </svg>
                </div>
                <div class="flex items-center space-x-2">
                  <select v-model="targetLanguage"
                    class="min-w-[120px] text-sm border border-gray-200 rounded-lg px-3 py-2 bg-gray-50 hover:bg-gray-100 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500/20">
                    <option value="zh">中文</option>
                    <option value="en">英文</option>
                  </select>
                </div>
              </div>
            </div>


            <button @click="handleTranslate" v-if="translateStatus == FileTranslateStatusEnum.translationError"
              class="flex items-center px-8 py-3 space-x-2 text-white transition-colors bg-blue-500 rounded-xl hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed">
              <span>重新翻译</span>
              <svg v-if="!isTranslating" xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" fill="none"
                viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
              </svg>
            </button>

            <button @click="handleTranslate" v-else
              class="flex items-center px-4 py-2 space-x-2 text-white transition-colors bg-blue-500 rounded-xl hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed">
              <span>开始翻译</span>
              <svg v-if="!isTranslating" xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" fill="none"
                viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
              </svg>
            </button>
          </template>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { getTranslateList, startTranslate } from '@/api/repositoryFile';
import { type RepositoryFile } from '@/services/types/repositoryFile';
import { cdnBaseUrl, FileTranslateStatusEnum } from '@/utils/constants';
import { StarloveConstants } from '@/utils/starloveConstants';
import { Skeleton as ASkeleton, message } from 'ant-design-vue';
import markdownit from 'markdown-it';
import { computed, nextTick, onMounted, onUnmounted, ref } from 'vue';
import { useApp } from '~/composables/useApp';
import { useUserStore } from '~/stores/user';
const app = useApp()
const { $eventBus } = useNuxtApp();

interface Props {
  knowledgeDetail: RepositoryFile | null
}
const props = withDefaults(defineProps<Props>(), {
  knowledgeDetail: null
})

let timer: NodeJS.Timeout | undefined
const translateList = ref<any[]>([])
const targetLanguage = ref('zh')
const isTranslating = ref(false)
const translateStatus = ref(0)
const isTranslateLoading = ref(true)
const isTranslateError = ref(false)
const isClickStartTranslate = ref(false)
const shouldAutoScroll = ref(true)

const user = useUserStore()

const spaceId = computed(() => {

  return user.currentLoginInfo?.id || ''

})

// 检查是否滚动到底部
const isScrolledToBottom = (element: Element) => {
  const { scrollTop, scrollHeight, clientHeight } = element;
  // 距离底部的距离
  const distanceToBottom = scrollHeight - scrollTop - clientHeight;
  // 如果距离底部小于50px就认为是接近底部
  const isNearBottom = distanceToBottom <= 50;
  // console.log('距离底部:', distanceToBottom + 'px', '自动滚动:', isNearBottom);
  return isNearBottom;
}

// 监听滚动事件
const handleScroll = (event: Event) => {
  const container = event.target as Element;
  const wasAutoScrollEnabled = shouldAutoScroll.value;
  shouldAutoScroll.value = isScrolledToBottom(container);

  // 当自动滚动状态发生变化时打印日志
  if (wasAutoScrollEnabled !== shouldAutoScroll.value) {
    console.log('自动滚动状态变更:', shouldAutoScroll.value ? '开启' : '关闭');
  }
}

// 滚动到底部的函数
const scrollToBottom = () => {
  if (!shouldAutoScroll.value) {
    console.log('自动滚动已关闭，不执行滚动');
    return;
  }

  const container = document.querySelector('.p-0.max-w-none.overflow-auto');
  if (container) {
    container.scrollTop = container.scrollHeight;
    console.log('执行滚动到底部');
  }
}

const languageLabel = targetLanguage.value === 'zh' ? '中文' :
  targetLanguage.value === 'en' ? '英文' :
    '未知语言';

const isShowTranslateContent = computed(() => {
  return translateList.value.length > 0 ||
    translateStatus.value == FileTranslateStatusEnum.inTranslation ||
    translateStatus.value == FileTranslateStatusEnum.translationEnd;
})

const handleTranslate = async () => {
  if (!props.knowledgeDetail) return;
  isClickStartTranslate.value = true;
  isTranslateLoading.value = true; // 显示骨架屏

  // 根据用户选择的语言动态传递参数
  const params = {
    language: targetLanguage.value === 'zh' ? '中文' : '英文', // 动态设置语言
    repositoryFileId: props.knowledgeDetail?.id,
  };

  try {
    const res = await startTranslate(spaceId.value, params);
    if (!res.ok) {
      isTranslateLoading.value = false;
      isClickStartTranslate.value = false;
      isTranslateError.value = true;
      message.error(res.message || '翻译失败');
      return;
    }

    // 成功后立即开始获取翻译内容
    getTranslateData();
  } catch (error) {
    isTranslateLoading.value = false;
    isTranslateError.value = true;
    message.error('翻译启动失败');
  }
};





const getTranslateData = async (index = -1) => {
  if (index === -1) isTranslateLoading.value = true;

  const params = {
    repositoryFileId: props.knowledgeDetail?.id,
    index
  };

  try {
    const res = await getTranslateList(spaceId.value, params);
    if (!res.ok || !res.data) {
      isTranslateError.value = true;
      return;
    }

    const list = res.data.list || [];
    if (index === -1) {
      translateList.value = groupByPage(list);
      // translateList.value = list;
    } else {
      const newList = groupByPage(list);
      translateList.value = mergeGroupedPages(translateList.value, newList);
    }

    // 使用新的滚动控制逻辑
    // nextTick(() => {
    //   scrollToBottom();
    // });

    translateStatus.value = res.data.translateStatus;
    if (res.data.translateStatus !== FileTranslateStatusEnum.translationEnd) {
      timer = setTimeout(() => getTranslateData(res.data?.index), 2000);
      isTranslating.value = true;
      // 不在这里设置loading为false，保持加载状态
    } else if (res.data.translateStatus == FileTranslateStatusEnum.translationEnd && res.data.list.length == 50) {
      // 翻译状态完成但还有更多数据需要获取，继续保持加载状态
      getTranslateData(Number(list[list.length - 1].sourceIndex) + 1);
    } else {
      // 只有当翻译完全结束且没有更多数据需要获取时才关闭加载状态
      isTranslating.value = false;
      isTranslateLoading.value = false;
    }
  } catch (error) {
    isTranslateError.value = true;
    isTranslateLoading.value = false;
    isTranslating.value = false;
  }
};

const setupData = async () => {
  if (!props.knowledgeDetail) return;

  if (props.knowledgeDetail.processData.translate_status == FileTranslateStatusEnum.translationEnd) {
    translateStatus.value = FileTranslateStatusEnum.translationEnd;
    await getTranslateData();  // 获取最新翻译数据
  } else if (props.knowledgeDetail.processData.translate_status == FileTranslateStatusEnum.inTranslation) {
    await getTranslateData();  // 获取正在翻译的数据
  } else {
    translateStatus.value = FileTranslateStatusEnum.noTranslation;
  }
  isTranslateLoading.value = false;
};

onMounted(() => {
  const container = document.querySelector('.p-0.max-w-none.overflow-auto');
  if (container) {
    container.addEventListener('scroll', handleScroll);
  }

  if (props.knowledgeDetail?.processData?.translate_status &&
    !isNaN(props.knowledgeDetail?.processData?.translate_status)
  ) {
    translateStatus.value = props.knowledgeDetail?.processData?.translate_status;
  }
  setupData();
  isClickStartTranslate.value = false;
  $eventBus.on(StarloveConstants.keyOfEventBus.pdfPreviewPageChange, (page: number) => {
    handlePdfPreviewPageChange(page)
  })
});

// 在组件卸载时移除事件监听
onUnmounted(() => {
  const container = document.querySelector('.p-0.max-w-none.overflow-auto');
  if (container) {
    container.removeEventListener('scroll', handleScroll);
  }
  $eventBus.off(StarloveConstants.keyOfEventBus.pdfPreviewPageChange)
});

const md = markdownit({
  html: true,
  linkify: app.value?.isMouse ? false : true
});
console.log(app.value?.isMouse, 'app.value?.isMouse')
const setContent = (content: string) => {
  let data = content;
  const regex = /```markdown\n([\s\S]*?)\n```/;
  const match = data.match(regex);
  if (match && match[1]) {
    data = match[1];
  }

  if (data.startsWith('<html>')) {
    data = data.replace(/\\n/g, '');
  }
  return md.render(data.replace(/\\n/g, '<br/>').replace(/(\d+\.\d+)\*\*/g, '<strong>$1</strong>'));
};

const renderMarkdownContent = (rawContent: string) => {
  // 把字符串中的 '\\n' 替换成换行符
  let content = rawContent.replace(/\\n/g, '\n')

  // 提取代码块内容，如果有的话
  const codeBlockMatch = content.match(/```markdown\n([\s\S]*?)\n```/)
  if (codeBlockMatch && codeBlockMatch[1]) {
    content = codeBlockMatch[1]
  }

  // 如果是 html 内容，去掉转义的换行符（可选）
  if (content.startsWith('<html>')) {
    content = content.replace(/\\n/g, '')
  }

  return md.render(content)
}
// -----------------------------------------------------

const translateActiveKey = ref('xx')


const handleClickTranslate = (item: any) => {
  // console.log(item)
  translateActiveKey.value = item.sourceIndex
  // console.log(item, 'handleClickTranslate')
  $eventBus.emit(StarloveConstants.keyOfEventBus.highLightTranslate, {
    bbox: {
      ...item.source.meta?.bbox
    },
    page: item.source.meta?.page,
    sourceContent: item.source.content
  })
}


const handleCopy = (content: string) => {
  navigator.clipboard
    .writeText(content)
    .then(() => {
      console.log('文章内容已复制')
      message.success('复制成功')
    })
    .catch((err) => {
      console.error('复制失败:', err)
      message.error('失败')
    })
}


// 按页码分组
const groupByPage = (data: any[]) => {
  const grouped = data.reduce((acc: any, item: any) => {
    const page = item.source.meta.page;
    if (!acc[page]) acc[page] = [];
    acc[page].push(item);
    return acc;
  }, {});
  return Object.keys(grouped)
    .sort((a, b) => Number(a) - Number(b))
    .map(page => grouped[page]);
};

// 合并按页码分组的两组数据
const mergeGroupedPages = (oldGroups: any[], newGroups: any[]) => {
  // 用一个 Map 来索引已有页码
  const pageMap = new Map();

  // 先把老数据页码和内容映射起来
  oldGroups.forEach((group: any) => {
    if (group.length > 0) {
      const page = group[0].source.meta.page;
      pageMap.set(page, group);
    }
  });

  // 遍历新数据分组，合并或新增
  newGroups.forEach((group: any) => {
    if (group.length > 0) {
      const page = group[0].source.meta.page;
      if (pageMap.has(page)) {
        // 合并页数据
        pageMap.get(page).push(...group);
      } else {
        // 新页直接加入
        pageMap.set(page, group);
      }
    }
  });

  // 返回排序后的二维数组
  return Array.from(pageMap.values()).sort((a, b) => {
    const pageA = a[0].source.meta.page;
    const pageB = b[0].source.meta.page;
    return pageA - pageB;
  });
};

// 获取图片url
const getImageUrl = (image: string) => {
  if (image.startsWith('data:image/')) {
    return image
  } else if (image.startsWith('/9j/')) {
    return `data:image/jpeg;base64,${image}`
  } else if (image.startsWith('iVBORw0KGgo')) {
    return `data:image/png;base64,${image}`
  } else {
    return image
  }
}

const handlePdfPreviewPageChange = (page: number) => {
  // 查找对应页码的内容元素
  nextTick(() => {
    // 查找包含该页码的元素
    const pageElements = document.querySelectorAll('.rounded-lg.p-2.mb-2');
    for (let i = 0; i < pageElements.length; i++) {
      const pageElement = pageElements[i] as HTMLElement;
      const pageNumberElement = pageElement.querySelector('.text-l.text-gray-500');

      if (pageNumberElement && pageNumberElement.textContent?.trim() == page.toString()) {
        // 找到了对应页码的元素，使用自定义平滑滚动
        const container = document.querySelector('.p-0.overflow-auto.max-w-none') as HTMLElement;
        if (container) {
          const targetPosition = pageElement.offsetTop - 60; // 距离顶部20px
          const startPosition = container.scrollTop;
          const distance = targetPosition - startPosition;
          const duration = 800; // 滚动持续时间，毫秒
          let startTime: number;

          function scrollAnimation(currentTime: number) {
            if (!startTime) startTime = currentTime;
            const timeElapsed = currentTime - startTime;
            const progress = Math.min(timeElapsed / duration, 1);

            // 使用缓动函数让滚动更平滑
            const easeInOutQuad = (t: number) => t < 0.5 ? 2 * t * t : 1 - Math.pow(-2 * t + 2, 2) / 2;

            container.scrollTop = startPosition + distance * easeInOutQuad(progress);

            if (timeElapsed < duration) {
              requestAnimationFrame(scrollAnimation);
            }
          }

          requestAnimationFrame(scrollAnimation);
        } else {
          // 回退到原始方法
          pageElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
        break;
      }
    }
  });
}

// 获取图片宽高
const getImageWH = (bbox: [number, number, number, number], type: string) => {
  if (!bbox) {
    return '100%'
  }
  if (type == 'width') {
    return (bbox[2] - bbox[0]) * 0.642
  } else if (type == 'height') {
    return (bbox[3] - bbox[1]) * 0.642
  }
  return '100%'
}



</script>

<style lang="scss" scoped>
div::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

div::-webkit-scrollbar-thumb {
  background-color: #d1d5db;
  border-radius: 4px;
}

div::-webkit-scrollbar-track {
  background-color: transparent;
}

.my-knowledge-detail-translate-box-content {
  transition: all 0.3s ease;
}

/* 在你的全局 CSS 或 scoped CSS 中添加 */
.custom-html :deep(p:first-of-type),
.custom-html :deep(div:first-of-type),
.custom-html :deep(span:first-of-type) {
  text-indent: 2em;
}

.translate-container-content-page-text-active {
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

.translate-container-content-page-text-active {
  background-color: #f0f8ff;
  /* 背景颜色变化 */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  /* 添加阴影 */
  cursor: pointer;
}
</style>