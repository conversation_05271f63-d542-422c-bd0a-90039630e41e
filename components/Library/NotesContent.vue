<template>
  <div class="notes-editor p-6">
    <div class="notes-editor-container bg-white rounded-xl shadow-[0_0_40px_-15px_rgba(0,0,0,0.1)] overflow-hidden">
      <!-- 编辑器菜单栏 -->
      <div class="border-b border-gray-100 bg-white p-2 flex items-center justify-between leading">
        <div class="flex items-center space-x-1">
          <!-- 基础格式工具组 -->
          <div class="flex items-center space-x-0.5 pr-3 border-r border-gray-100">
            <button v-for="item in basicFormatMenuItems" :key="item.icon" @click="item.action" class="notes-editor-btn"
              :class="{ 'is-active': item.isActive?.() }" :title="item.title">
              <Icon :name="item.icon" class="w-5 h-5" />
            </button>
          </div>

          <!-- 列表工具组 -->
          <div class="flex items-center space-x-0.5 px-3 border-r border-gray-100">
            <button v-for="item in listMenuItems" :key="item.icon" @click="item.action" class="notes-editor-btn"
              :class="{ 'is-active': item.isActive?.() }" :title="item.title">
              <Icon :name="item.icon" class="w-5 h-5" />
            </button>
          </div>

          <!-- 标题工具组 -->
          <div class="flex items-center space-x-0.5 px-3">
            <button v-for="item in headingMenuItems" :key="item.label" @click="item.action"
              class="notes-editor-btn font-bold min-w-[32px] text-center" :class="{ 'is-active': item.isActive?.() }"
              :title="item.title">
              {{ item.label }}
            </button>
          </div>
        </div>

        <!-- 导出按钮 -->
        <span class="save-status">{{ isLoadingSave ? '保存中' : '已保存' }}</span>
        <button @click="exportNotes" :disabled="isExprotWordLoading"
          class="flex items-center space-x-1.5 px-3 py-1.5 text-sm rounded-md transition-colors" :class="[
            editor?.isEmpty
              ? 'text-gray-300 bg-gray-50 cursor-not-allowed'
              : 'text-gray-500 hover:text-blue-600 hover:bg-blue-50',
          ]">
          <Icon name="material-symbols:download" class="w-4 h-4" />
          <span> {{ isExprotWordLoading ? '导出中...' : '导出笔记' }}</span>
        </button>
      </div>

      <!-- 编辑器内容区 -->
      <editor-content :editor="editor"
        class="px-8 py-6 min-h-[400px] prose max-w-none focus:outline-none notebook-paper" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useUserStore } from '@/stores/user'
import { StarloveConstants } from '@/utils/starloveConstants'
import { downloadFile, sleep } from '@/utils/utils'
import Underline from '@tiptap/extension-underline'
import StarterKit from '@tiptap/starter-kit'
import { EditorContent, useEditor } from '@tiptap/vue-3'
import { message } from 'ant-design-vue'
import { debounce } from 'lodash'
import markdownit from 'markdown-it'
import { computed, onBeforeUnmount } from 'vue'
import {
  getExportCode,
  repositoryFileGetDetail,
  repositorySaveEditorData,
} from '~/api/repositoryFile'
import type { RepositoryFile } from '~/services/types/repositoryFile'



interface Props {
  knowledgeDetail: RepositoryFile | null
}
const props = withDefaults(defineProps<Props>(), {
  knowledgeDetail: null,
})

const { $eventBus } = useNuxtApp()

const user = useUserStore()

const spaceId = computed(() => {

  return user.currentLoginInfo?.id || ''

})

const repositoryFileId = computed(() => {
  if (!props.knowledgeDetail) {
    return ''
  }
  return props.knowledgeDetail.id
})

const updateDuration = 500
const isExprotWordLoading = ref(false)
const isLoadingSave = ref(false)

const saveData = async (id: string, editorData: any) => {
  isLoadingSave.value = true
  await repositorySaveEditorData(spaceId.value, { id, editorData: JSON.stringify(editorData) })
  isLoadingSave.value = false
}
const onUpdate = ({ editor: _editor }: any) => {
  const content = _editor.getJSON()
  console.log(content, 'content')
  saveData(repositoryFileId.value, content)
}
const editor = useEditor({
  content: '',
  extensions: [
    StarterKit,
    Underline,
  ],
  editorProps: {
    attributes: {
      class: 'prose prose-slate max-w-none focus:outline-none min-h-[400px]',
    },
  },
  onUpdate: debounce(onUpdate, updateDuration),
})

// 基础格式菜单项
const basicFormatMenuItems = computed(() => [
  {
    icon: 'material-symbols:format-bold-rounded',
    action: () => editor.value?.chain().focus().toggleBold().run(),
    isActive: () => editor.value?.isActive('bold'),
    title: '加粗',
  },
  {
    icon: 'material-symbols:format-italic-rounded',
    action: () => editor.value?.chain().focus().toggleItalic().run(),
    isActive: () => editor.value?.isActive('italic'),
    title: '斜体',
  },
  {
    icon: 'material-symbols:format-underlined-rounded',
    action: () => editor.value?.chain().focus().toggleUnderline().run(),
    isActive: () => editor.value?.isActive('underline'),
    title: '下划线',
  },
])

// 列表菜单项
const listMenuItems = computed(() => [
  {
    icon: 'material-symbols:format-list-bulleted-rounded',
    action: () => editor.value?.chain().focus().toggleBulletList().run(),
    isActive: () => editor.value?.isActive('bulletList'),
    title: '无序列表',
  },
  {
    icon: 'material-symbols:format-list-numbered-rounded',
    action: () => editor.value?.chain().focus().toggleOrderedList().run(),
    isActive: () => editor.value?.isActive('orderedList'),
    title: '有序列表',
  },
])

// 标题菜单项
const headingMenuItems = computed(() => [
  {
    label: 'H1',
    action: () => editor.value?.chain().focus().toggleHeading({ level: 1 }).run(),
    isActive: () => editor.value?.isActive('heading', { level: 1 }),
    title: '一级标题',
  },
  {
    label: 'H2',
    action: () => editor.value?.chain().focus().toggleHeading({ level: 2 }).run(),
    isActive: () => editor.value?.isActive('heading', { level: 2 }),
    title: '二级标题',
  },
  {
    label: 'H3',
    action: () => editor.value?.chain().focus().toggleHeading({ level: 3 }).run(),
    isActive: () => editor.value?.isActive('heading', { level: 3 }),
    title: '三级标题',
  },
])

// 导出笔记
const exportNotes = async () => {
  console.log('exportNotesexportNotes')
  isExprotWordLoading.value = true

  const content = editor.value?.getHTML()
  await sleep(500)
  const res = await getExportCode(spaceId.value, { id: repositoryFileId.value })
  isExprotWordLoading.value = false
  if (!res.success) {
    return
  }
  const code = res.data
  if (code) {
    downloadFile(
      `${StarloveUtil.getBaseUrl()}/repositoryFile/exportWord?code=${code}`,
      repositoryFileId.value
    )
  }
  console.log('导出笔记:', content)
}
const getKnowledgeAssistantDetailById = async (id: string) => {
  if (!id) {
    return message.error('id参数找不到')
  }
  const result = await repositoryFileGetDetail({ spaceId: spaceId.value, id })
  if (!result.ok || !result.data) {
    return
  }

  // console.log(result)

  let editorData = ''
  if (result.data.editorData) {
    editorData = JSON.parse(result.data.editorData)
  }

  if (editor.value) {
    editor.value.commands.setContent(editorData)
  }
}

const isMarkdown = (text: string) => {
  // 检查是否包含标题（#）、列表（*）、加粗/斜体（*或_）、链接（[]()）等Markdown特征
  const markdownRegex = /(^#{1,6}\s)|(^-|\*\s)|(\*\*|__)|(\[.*?\]\(.*?\))|(\n(?=(\n+)))/gm
  return markdownRegex.test(text)
}
const handlePressPanelInsert = (textToInsert: string) => {
  textToInsert = textToInsert.replace(/\\n/g, '<br/>').replace(/\n/g, '<br/>')
  if (isMarkdown(textToInsert)) {
    const md = markdownit()
    textToInsert = md.render(textToInsert)
  }
  const textToAppend = textToInsert
  // 确保 editor.value 是有效的
  console.log(textToAppend, '接收到的内容')

  if (!editor.value) {
    console.error('Editor is not initialized')
    return
  }

  const existingContent = editor.value.getHTML() || ''

  editor.value.commands.setContent(existingContent + textToInsert, false)
  setTimeout(() => {
    onUpdate({ editor: editor.value })
    message.success('添加成功')
  }, 500)
  // 手动触发更新
}

onMounted(() => {
  getKnowledgeAssistantDetailById(repositoryFileId.value)
  $eventBus.on(StarloveConstants.keyOfEventBus.addToNotes, (item) => {
    handlePressPanelInsert(item)
  })
})
// 组件销毁时清理编辑器实例
onBeforeUnmount(() => {
  editor.value?.destroy()
  $eventBus.off(StarloveConstants.keyOfEventBus.addToNotes)
})
</script>

<style scoped>
/* 所有样式都限定在 notes-editor 命名空间内 */
.notes-editor .editor-btn {
  @apply p-1.5 rounded-md transition-colors text-gray-500;
  @apply hover:bg-gray-50 hover:text-gray-700;
}

.save-status {
  padding: 0 10px;
  font-size: 14px;
  color: #999999;
  white-space: nowrap;
}

.notes-editor .editor-btn.is-active {
  @apply bg-blue-50 text-blue-600;
}

.notes-editor .ProseMirror {
  outline: none;
  min-height: 400px;
}

.notes-editor .ProseMirror p {
  min-height: 1.8rem;
}

.notes-editor .ProseMirror p.is-editor-empty:first-child::before {
  content: '开始记录你的想法...';
  float: left;
  color: #94a3b8;
  pointer-events: none;
  height: 0;
}

/* 限定笔记本样式作用范围 */
.notes-editor .notebook-paper {
  background-color: #fcfcfc;
  /* background-image: linear-gradient(#f1f5f9 1px, transparent 1px); */
  background-size: 100% 1.8rem;
  position: relative;
}

.notes-editor .notebook-paper::before {
  content: '';
  position: absolute;
  left: 32px;
  top: 0;
  bottom: 0;
  width: 1px;
  background-color: #ef444422;
}

.notes-editor .notebook-paper .ProseMirror {
  padding-left: 48px;
}

/* 限定 prose 样式作用范围 */
.notes-editor .prose {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
    sans-serif;
  color: #334155;
  /* line-height: 1.8rem; */
}

.notes-editor .prose p {
  margin-top: 0;
  margin-bottom: 0;
  /* line-height: 1.8rem; */
  padding-top: 0;
  padding-bottom: 0;
  /* height: 1.8rem; */
}

.notes-editor .prose h1,
.notes-editor .prose h2,
.notes-editor .prose h3 {
  color: #1e293b;
  margin-top: 1.8rem;
  margin-bottom: 0;
  line-height: 1.8rem;
  height: 1.8rem;
}

.notes-editor .prose ul,
.notes-editor .prose ol {
  margin-top: 0;
  margin-bottom: 0;
  padding-left: 1.5rem;
  color: #334155;
}

.notes-editor .prose li {
  margin-top: 0;
  margin-bottom: 0;
  padding-left: 0.25rem;
  line-height: 1.8rem;
  height: 1.8rem;
}
</style>
