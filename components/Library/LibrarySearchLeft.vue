<template>
    <div class="flex flex-col h-screen space-y-2">
        <!-- 面包屑 -->
        <div class="flex items-center flex-shrink-0 h-20 px-2 overflow-x-auto sm:px-4">
            <div class="flex items-center space-x-2">
                <button @click="router.back()"
                    class="px-5 py-2 text-sm text-[##333333] hover:text-[#2551B5] rounded-lg flex items-center">
                    <left theme="outline" size="18" class="text-[##333333] hover:text-[#2551B5]" />
                    返回
                </button>
                <span class="text-sm text-[#333333]">
                    搜索结果（{{ searchResultCount }}）
                </span>
            </div>
            <div v-if="links.length > 1" class="flex flex-wrap items-center gap-1 sm:gap-2">
                <button @click="handleBreadcrumbClick(links[0])"
                    class="flex items-center space-x-2 cursor-pointer hover:text-blue-600 whitespace-nowrap">
                    <span class="text-xs text-gray-500 sm:text-lg">{{ links[0]?.label || '根目录' }}</span>
                </button>
                <template v-for="(link, index) in links.slice(1)" :key="link.id">
                    <div class="text-gray-400">
                        <right theme="outline" size="24" class="sm:size-16" />
                    </div>
                    <button @click="index < links.length - 2 && handleBreadcrumbClick(link)" :class="[
                        'text-xs sm:text-lg transition-colors whitespace-nowrap max-w-[100px] sm:max-w-[150px] md:max-w-none overflow-hidden text-ellipsis',
                        index === links.length - 2
                            ? 'text-blue-600 font-medium cursor-default'
                            : 'text-gray-500 hover:text-blue-600 cursor-pointer'
                    ]">
                        {{ link.label }}
                    </button>
                </template>
            </div>
        </div>



        <!-- 搜索框 -->
        <div
            class="flex items-center flex-1 mx-2 transition-all duration-200 bg-white border shadow-md sm:mx-4 md:flex-none border-blue-200/50 rounded-xl focus-within:border-blue-300/50 focus-within:ring-2 focus-within:ring-blue-200/50">
            <!-- 搜索图标 -->
            <div class="px-3 text-blue-500">
                <search theme="outline" size="24" fill="#3b82f6" />
            </div>
            <!-- 输入框 -->
            <input v-model="searchQuery" type="text" placeholder="搜索文件名、文件夹名称"
                class="flex-1 h-[40px] bg-transparent text-[#333] placeholder-[#999] focus:outline-none text-base"
                @keyup.enter="handleSearch(searchQuery)" />
            <!-- 搜索按钮 -->
            <button @click="handleSearch(searchQuery)"
                class="bg-[#2551B5] h-full px-4 rounded-lg text-white text-sm font-medium transition-all duration-200 hover:bg-blue-600">
                <span class="hidden lg:inline">搜索</span>
            </button>
        </div>

        <!-- 文件和文件夹 -->
        <div class="flex-1 px-2 overflow-y-auto sm:px-4">
            <div class="flex items-center flex-1 h-full overflow-hidden">
                <!-- 加载状态 -->
                <div v-if="isFilesLoading || isFoldersLoading" class="flex items-center justify-center w-full py-10">
                    <loading theme="outline" size="40" class="text-gray-400 animate-spin" />
                </div>

                <!-- 内容区域：文件夹和文件列表 -->
                <div v-if="(folders.length > 0 || teamFiles.length > 0) && !isFilesLoading && !isFoldersLoading"
                    class="flex flex-col justify-between flex-1 w-full h-full">
                    <!-- 文件夹和文件列表容器 -->
                    <div ref="scrollContainer" class="flex-1 min-h-0 overflow-y-auto" @scroll="handleScroll">
                        <!-- 文件夹列表 -->
                        <div v-if="folders.length > 0" class="mb-3 sm:mb-4">
                            <FolderItem :folders="folders" @select="handleFolderClick" @rename="handleRename('folder')"
                                @move="handleMove" @delete="handleDeleteItem('folder')" />
                        </div>

                        <!-- 文件列表 -->
                        <div v-if="teamFiles.length > 0">
                            <KnowledgeItem :items="teamFiles" @delete="handleDeleteItem('file')"
                                @rename="handleRename('file')" @move="handleMove" :links="links" />
                        </div>

                        <!-- 加载更多指示器 -->
                        <div v-if="isLoadingMore" class="flex items-center justify-center py-4">
                            <loading theme="outline" size="24" class="mr-2 text-gray-400 animate-spin" />
                            <span class="text-sm text-gray-500">加载中...</span>
                        </div>

                        <!-- 没有更多数据提示 -->
                        <div v-if="teamFiles.length > 0 && !hasMoreData && !isLoadingMore"
                            class="flex items-center justify-center py-4">
                            <span class="text-sm text-gray-400">没有更多数据了</span>
                        </div>
                    </div>
                </div>

                <!-- 空状态 -->
                <div v-if="!isFilesLoading && !isFoldersLoading && folders.length === 0 && teamFiles.length === 0"
                    class="flex flex-col items-center justify-center w-full py-12 sm:py-20">
                    <div class="flex flex-col items-center">
                        <div
                            class="flex items-center justify-center w-16 h-16 mb-3 rounded-full sm:w-20 sm:h-20 sm:mb-4 bg-gray-50">
                            <folder-open theme="outline" size="36" class="text-gray-400" />
                        </div>
                        <h3 class="mb-1 text-base font-medium text-gray-600 sm:mb-2 sm:text-lg">知识库中暂无文档
                        </h3>
                        <p class="text-xs text-gray-500 sm:text-sm">上传一个文档，开始使用您的知识库吧</p>
                    </div>
                </div>
            </div>

            <!-- 移动文件弹窗 -->
            <MoveFileModal v-if="showMoveModal" :type="moveType" v-model="showMoveModal" @confirm="handleMoveConfirm"
                :currentItem="currentFolder" />
        </div>
    </div>
</template>


<script setup lang="ts">
import { folderSearchV2, getSubFolders, listFile } from '@/api/repositoryFile';
import FolderItem from '@/components/Library/FolderItem.vue';
import KnowledgeItem from '@/components/Library/KnowledgeItem.vue';
import type { NewRepositoryFile, RepositoryFolder } from '@/services/types/repositoryFile';
import { useLibraryUploadStore } from '@/stores/libraryUpload';
import { useUserStore } from '@/stores/user';
import { FolderOpen, Left, Loading, Right, Search } from '@icon-park/vue-next';
import { message } from 'ant-design-vue';
import { onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { UserService } from '~/services/user';
import { useSpaceStore } from '~/stores/space';

interface Folder {
    id: number
    name: string
    fileCount: number
    updateTime: string
}
interface Options {
    isShowLoading?: boolean
    isLoadMore?: boolean
}
const router = useRouter()
const route = useRoute()
const user = useUserStore()
const libraryUploadStore = useLibraryUploadStore()
const spaceStore = useSpaceStore()


const searchQuery = ref('')
const showMoveModal = ref(false)
const currentFolder = ref<RepositoryFolder | NewRepositoryFile | null>(null)
const moveType = ref<'folder' | 'file'>('folder')
let timerId: NodeJS.Timeout | undefined = undefined
const showUploadModal = ref(false)
const isFilesLoading = ref(true)
const isFoldersLoading = ref(true)
const teamFiles = ref<NewRepositoryFile[]>([])
const folders = ref<RepositoryFolder[]>([])

// 无限滚动相关状态
const isLoadingMore = ref(false)
const hasMoreData = ref(true)
const scrollContainer = ref<HTMLElement | null>(null)

const searchResultCount = ref(0)
// 添加状态来区分是搜索模式还是文件夹浏览模式
const isSearchMode = ref(false)
// 面包屑
const links = ref<Array<{
    label: string;
    to: string;
    id: string | number;
}>>([])
const page = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
    pages: 1,
    folderId: route.query.folderId || '0'
})

const spaceId = computed(() => {
    return user.currentLoginInfo?.id || ''
})


// 添加一个 ref 来跟踪是否有处理中的文件
let hasProcessingFiles = false

// 添加处理文件状态的公共函数
const handleProcessingFiles = (files: any[], callback: () => void) => {
    const processingList = files.filter((d) => d.status !== RepositoryFileStatus.ERROR && d.status !== RepositoryFileStatus.DONE && d.progress < 100 && d.progress > -1) || []
    if (timerId) {
        clearTimeout(timerId)
    }
    if (processingList.length > 0) {
        hasProcessingFiles = true
        timerId = setTimeout(() => {
            callback()
        }, 5000)
    } else if (hasProcessingFiles) {
        // 如果之前有处理中的文件，现在没有了，说明文件都处理完成了
        hasProcessingFiles = false
        spaceStore.loadSpaceInfo()
    }
}


// 搜索文件函数
const searchFiles = async (query: string, options: { isShowLoading?: boolean; isLoadMore?: boolean } = { isShowLoading: true, isLoadMore: false }) => {
    if (options.isShowLoading) {
        isFilesLoading.value = true
        isFoldersLoading.value = true
    }

    const params = {
        keyword: query,
        searchTypes: ['file', 'folder'],
        pageNo: page.current,
        pageSize: page.pageSize
    }

    // const res = await folderSearch({ spaceId: spaceId.value, ...params })
    const res = await folderSearchV2({ spaceId: spaceId.value, ...params })

    if (!res.ok) {
        console.error('搜索失败')
        isFilesLoading.value = false
        isFoldersLoading.value = false
        return
    }

    page.total = res.data.file.total || 0
    page.pageSize = res.data.file.size || 0
    page.pages = res.data.file.pages || 0
    page.current = res.data.file.current || 1

    // 处理文件数据
    const newFiles = res.data.file.records.map((item: any) => ({
        ...item,
        progress: getProgress(item),
        folderId: item.parentId
    }))

    // 处理文件夹数据
    const newFolders = res.data.folder.records.map((item: any) => ({
        ...item,
        folderId: page.folderId
    }))

    // 如果是加载更多，追加到现有列表；否则替换列表
    if (options.isLoadMore) {
        teamFiles.value = [...teamFiles.value, ...newFiles]
        folders.value = [...folders.value, ...newFolders]
    } else {
        teamFiles.value = newFiles
        folders.value = newFolders
    }

    searchResultCount.value = res.data.folder.records.length + res.data.file.records.length || 0

    // 更新是否还有更多数据的状态
    hasMoreData.value = page.current < page.pages

    if (options.isShowLoading) {
        isFilesLoading.value = false
        isFoldersLoading.value = false
    }

    handleProcessingFiles(teamFiles.value, () => searchFiles(query, { isShowLoading: false }))
}

const handleSearch = async (query: string) => {
    const userStore = useUserStore()
    if (!userStore.isLogined) {
        userStore.openLoginModal()
        return
    }

    const trimmedQuery = query.trim();

    if (!trimmedQuery) {
        message.warning('请输入搜索关键词');
        return;
    }

    // 设置为搜索模式
    isSearchMode.value = true
    // 重置分页和无限滚动状态
    page.current = 1
    hasMoreData.value = true
    isLoadingMore.value = false
    isFilesLoading.value = true
    isFoldersLoading.value = true

    // 重置面包屑，只保留根目录
    links.value = [{
        label: '根目录',
        to: `/library/search?keyword=${trimmedQuery}`,
        id: '0'
    }]

    await searchFiles(query)
}


// 滚动处理函数
const handleScroll = () => {
    if (!scrollContainer.value || isLoadingMore.value || !hasMoreData.value) return

    const { scrollTop, scrollHeight, clientHeight } = scrollContainer.value
    const isBottom = scrollTop + clientHeight >= scrollHeight - 50 // 距离底部 50px 内触发

    if (isBottom && page.current < page.pages) {
        loadMoreFiles()
    }
}

// 加载更多文件
const loadMoreFiles = async () => {
    if (isLoadingMore.value || !hasMoreData.value) return

    isLoadingMore.value = true
    page.current++

    try {
        if (isSearchMode.value) {
            // 搜索模式下，调用搜索接口加载更多
            await searchFiles(searchQuery.value, { isShowLoading: false, isLoadMore: true })
        } else {
            await getFileType({ isShowLoading: false, isLoadMore: true })
        }
    } catch (error) {
        console.error('加载更多文件失败:', error)
        page.current-- // 回退页码
    } finally {
        isLoadingMore.value = false
    }
}

// 面包屑点击处理
const handleBreadcrumbClick = async (link: { id: string | number; to: string; label: string }) => {
    page.current = 1
    // 重置无限滚动状态
    hasMoreData.value = true
    isLoadingMore.value = false

    // 点击面包屑后切换到文件夹浏览模式
    isSearchMode.value = false
    // 设置当前文件夹ID
    page.folderId = link.id.toString()

    // 更新面包屑导航（删除当前点击项之后的所有项）
    const index = links.value.findIndex(item => item.id === link.id)
    if (index !== -1) {
        links.value = links.value.slice(0, index + 1)
    }

    // 如果点击的是根目录，执行搜索
    if (link.id === '0' || link.id === 0) {
        if (route.query.keyword) {
            searchQuery.value = route.query.keyword as string
            await handleSearch(searchQuery.value)
        }
        return
    }

    // 重新加载文件夹内容
    await getFileType({ isShowLoading: true })
    await getFolders({ isShowLoading: true })
}

// 获取文件进度
const getProgress = (record: NewRepositoryFile) => {
    if (record?.processData?.error) {
        return -1
    }
    if (record.status == RepositoryFileStatus.DONE) {
        return 100
    }
    return record.processData?.summary_status || 0
}


// 获取文件夹
const getFolders = async (options: Options | null = null) => {
    const defaultOptions: Options = { isShowLoading: true }
    const _options = options ?? defaultOptions

    if (_options.isShowLoading) {
        isFoldersLoading.value = true
    }
    if (!spaceId.value) {
        return
    }
    const params = {
        folderId: page.folderId,
    }
    const res = await getSubFolders(spaceId.value, params)
    if (!res.ok) {
        // console.error('获取文件夹列表失败')
        if (_options.isShowLoading) {
            isFoldersLoading.value = false
        }
        return
    }
    folders.value = res.data?.map((d: Folder) => ({
        ...d,
        folderId: page.folderId
    }))

    if (_options.isShowLoading) {
        isFoldersLoading.value = false
    }
}

// 获取文件
const getFileType = async (options: Options | null = null) => {
    const defaultOptions: Options = { isShowLoading: true, isLoadMore: false }
    const _options = options ?? defaultOptions

    if (_options.isShowLoading) {
        isFilesLoading.value = true
    }
    if (!spaceId.value) {
        console.error('spaceId.value==>', spaceId.value)
        return
    }

    const params = {
        folderId: page.folderId,
        pageNo: page.current,
        pageSize: page.pageSize
    }

    const res = await listFile(spaceId.value, params)

    if (!res.ok) {
        // console.error('获取文档列表失败')
        isFilesLoading.value = false
        return
    }

    // 如果当前页没有数据且不是第一页,自动跳转到上一页
    if (res.data?.records?.length === 0 && page.current > 1 && !_options.isLoadMore) {
        page.current--
        isFilesLoading.value = false
        return await getFileType(options)
    }

    const newFiles = res.data?.records?.map((d: NewRepositoryFile) => ({
        ...d,
        progress: getProgress(d),
        folderId: page.folderId
    })) || []

    // 如果是加载更多，追加到现有列表；否则替换列表
    if (_options.isLoadMore) {
        teamFiles.value = [...teamFiles.value, ...newFiles]
    } else {
        teamFiles.value = newFiles
    }

    Object.assign(page, {
        current: res.data?.current,
        total: res.data?.total,
        pageSize: res.data?.size,
        pages: res.data?.pages,
    })

    // 更新是否还有更多数据的状态
    hasMoreData.value = page.current < page.pages

    if (_options.isShowLoading) {
        isFilesLoading.value = false
    }

    // 确保 processingList 是一个数组
    const processingList = teamFiles.value.filter((d) => d.status !== RepositoryFileStatus.ERROR && d.status !== RepositoryFileStatus.DONE && d.progress > -1 && d.progress < 100) || []
    if (timerId) {
        clearTimeout(timerId)
    }
    // 检查是否有处理中的文件
    if (processingList.length > 0) {
        hasProcessingFiles = true
        timerId = setTimeout(() => {
            getFileType({ isShowLoading: false })
        }, 5000)
    } else if (hasProcessingFiles) {
        // 如果之前有处理中的文件，现在没有了，说明文件都处理完成了
        hasProcessingFiles = false
        spaceStore.loadSpaceInfo()
    }
}

// 点击文件夹
const handleFolderClick = async (folder: RepositoryFolder) => {
    // console.log(folder)
    page.current = 1
    // 重置无限滚动状态
    hasMoreData.value = true
    isLoadingMore.value = false

    // 点击文件夹后切换到文件夹浏览模式
    isSearchMode.value = false
    page.folderId = folder.id.toString()

    // 添加新的面包屑项
    links.value.push({
        label: folder.folderName || folder.name,
        to: folder.folderPath,
        id: folder.id
    })

    getFileType({ isShowLoading: true })
    getFolders({ isShowLoading: true })
}

// 重命名
const handleRename = async (item: string) => {
    if (item === 'folder') {
        await getFolders({ isShowLoading: true })
    } else if (item === 'file') {
        // 重置无限滚动状态
        hasMoreData.value = true
        isLoadingMore.value = false
        await getFileType({ isShowLoading: true })
    }
}

// 移动文件
const handleMove = async (item: RepositoryFolder | NewRepositoryFile, type: 'folder' | 'file') => {
    moveType.value = type
    currentFolder.value = item
    showMoveModal.value = true
}


// 移动文件
const handleMoveConfirm = async () => {
    await getFileType({ isShowLoading: true })
    await getFolders({ isShowLoading: true })
    showMoveModal.value = false
}

// 删除文件
const handleDeleteItem = async (item: string) => {
    if (item === 'folder') {
        await getFolders({ isShowLoading: true })
    } else if (item === 'file') {
        // 重置无限滚动状态
        hasMoreData.value = true
        isLoadingMore.value = false
        await getFileType({ isShowLoading: true })
    }
    // 如果当前页没有数据了,且不是第一页,则跳转到上一页
    if (teamFiles.value.length === 0 && page.current > 1) {
        page.current--
        // 重置无限滚动状态
        hasMoreData.value = true
        isLoadingMore.value = false
        if (item === 'folder') {
            await getFolders({ isShowLoading: true })
        } else if (item === 'file') {
            await getFileType({ isShowLoading: true })
        }
    }
}

const setupData = async () => {
    if (!UserService.isLogined()) {
        isFilesLoading.value = false
        isFoldersLoading.value = false
        return
    }

    // 添加新的面包屑项
    links.value.push({
        label: '个人知识库',
        to: '/library',
        id: '0'
    })

    spaceStore.loadSpaceInfo()
    getFolders({ isShowLoading: true })
    getFileType({ isShowLoading: true })
}


onMounted(() => {
    if (route.query.keyword) {
        searchQuery.value = route.query.keyword as string
        handleSearch(searchQuery.value)
    }
})

</script>