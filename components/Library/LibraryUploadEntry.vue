<template>
    <!-- 上传进度提示 -->
    <div v-if="uploadingFiles.length > 0" class="fixed top-4 right-4 z-[9999]">
        <div v-for="file in uploadingFiles" :key="file.id"
            class="bg-white border border-gray-300 rounded-lg p-4 mb-2 shadow-md w-[300px]">
            <div class="flex items-center justify-between text-sm text-gray-600 mb-2">
                <span class="truncate flex-1 mr-2">{{ file.name }}</span>
                <span class="text-gray-400 whitespace-nowrap">{{ file.progress }}%</span>
            </div>
            <UProgress :value="file.progress" color="blue" />
        </div>
    </div>

    <!-- 隐藏的文件输入框 -->
    <input ref="fileInput" type="file" class="hidden" multiple
        accept=".pdf,.docx,.doc,.ppt,.pptx,.txt,.md,.txt,.jpg,.png,.jpeg,.xlsx,.csv" @change="handleFileSelect" />

    <a-popover placement="rightTop">
        <template #content>
            <div class="flex flex-col space-y-2">
                <!-- 上传弹窗 -->
                <button @click="handleUploadLocal"
                    class="flex-1 flex items-center space-x-2 text-xs sm:text-sm px-3 sm:px-4 py-2 text-[#333333] rounded-lg transition-colors font-medium hover:text-[#2551B5]">
                    <upload theme="outline" size="18" class="text-inherit" :fill="'currentColor'" />
                    <span>本地上传</span>
                </button>

                <LibraryExternal :folderId="folderId" @action="handleAction" />
            </div>
        </template>
        <button
            class="flex-1 text-xs sm:text-sm px-3 sm:px-4 py-2 bg-[#2551B5] text-white rounded-lg transition-colors border border-[#2551B5] font-medium hover:bg-gradient-to-r hover:from-blue-500 hover:to-indigo-600">
            添加
        </button>
    </a-popover>
</template>

<script setup lang="ts">
import { addFiles, checkFileHash } from '@/api/repositoryFile.js';
import { generatePutUrl, uploadByUrl } from '@/api/upload';
import LibraryExternal from '@/components/Library/LibraryExternal.vue';
import { HTTP_STATUS } from '@/utils/constants';
import { Upload } from '@icon-park/vue-next';
import { message } from 'ant-design-vue';
import axios from 'axios';
import { computed, ref } from 'vue';
import { useSpaceStore } from '~/stores/space';
import { useUserStore } from '~/stores/user';
import { getFileSha256, removeQuestionMarkText } from '~/utils/utils';


// 定义组件 props
const props = defineProps<{
    folderId?: string
}>()

// 定义组件 emits
const emit = defineEmits(['action'])

// 定义允许的文件类型
const allowedTypes = [
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // docx
    'application/msword', // doc
    'application/pdf', // pdf
    'application/vnd.ms-powerpoint', // ppt
    'application/vnd.openxmlformats-officedocument.presentationml.presentation', // pptx
    'text/plain', // txt
    'text/markdown', // md
    'text/x-markdown', // md
    'image/jpeg', // jpg, jpeg
    'image/png', // png
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // xlsx
    'text/csv', // csv
]

const user = useUserStore()
const spaceStore = useSpaceStore()
// 获取空间信息
const { spaceQuotaBytes, spaceUsedBytes } = storeToRefs(spaceStore)

// 文件输入框引用
const fileInput = ref<HTMLInputElement | undefined>()
const uploadingFiles = ref<UploadingFile[]>([])

interface UploadingFile {
    id: number
    name: string
    progress: number
}

const spaceId = computed(() => {
    return user.currentLoginInfo?.id || ''
})

// 处理本地上传按钮点击
const handleUploadLocal = () => {
    // 触发文件选择对话框
    if (fileInput.value) {
        fileInput.value.click()
    }
}

// 处理文件选择
const handleFileSelect = (e: Event) => {
    const userStore = useUserStore()
    if (!userStore.isLogined) {
        userStore.openLoginModal()
        return
    }

    const files = (e.target as HTMLInputElement).files
    if (files) {
        const invalidFiles = Array.from(files).filter(file => {
            const fileExtension = file.name.toLowerCase().split('.').pop()
            const isMdFile = fileExtension === 'md' || fileExtension === 'markdown'
            return !(isMdFile || allowedTypes.includes(file.type))
        })

        if (invalidFiles.length > 0) {
            message.error(`以下文件格式不支持：${invalidFiles.map(f => f.name).join(', ')}`)
            return
        }

        handleFiles(Array.from(files))
    }
}

// 修改处理文件上传方法，添加上传限制
const handleFiles = async (files: File[]) => {
    // 检查是否有有效文件需要上传
    const validFiles = files.filter(file => {
        const fileExtension = file.name.toLowerCase().split('.').pop()
        const isMdFile = fileExtension === 'md' || fileExtension === 'markdown'
        return isMdFile || allowedTypes.includes(file.type)
    })

    if (validFiles.length === 0) {
        return
    }

    // 用于跟踪上传成功的文件数量
    let successCount = 0
    const totalValidFiles = validFiles.length

    // 依次处理每个文件
    for (const file of validFiles) {
        if (file.size > spaceQuotaBytes.value - spaceUsedBytes.value) {
            message.error('知识库空间不足，请升级后再添加')
            emit('action', { type: 'uploadComplete' })
            return
        }

        const fileExtension = file.name.toLowerCase().split('.').pop()
        const isMdFile = fileExtension === 'md' || fileExtension === 'markdown'

        // 如果是 Markdown 文件，直接允许
        if (isMdFile) {
            // 继续处理上传
        }
        // 否则检查其他允许的类型
        else if (!allowedTypes.includes(file.type)) {
            message.error(`文件 ${file.name} 格式不支持 (${file.type})`)
            continue
        }

        // 将 uploadingFile 定义在 try 块外部
        const uploadingFile: UploadingFile = {
            id: Date.now() + Math.random(),
            name: file.name,
            progress: 0
        }
        uploadingFiles.value.push(uploadingFile)

        try {
            // 检查文件哈希值
            const sha256 = await getFileSha256(file)

            const checkFileShaResult = await checkFileHash({
                sha256: `${sha256}`,
            })

            if (!checkFileShaResult.ok) {
                uploadingFiles.value = uploadingFiles.value.filter(f => f.id !== uploadingFile.id)
                continue
            }

            let uploadByUrlParams;

            // 如果文件已存在，直接使用已有数据
            if (checkFileShaResult.data != null) {
                uploadByUrlParams = {
                    fileName: checkFileShaResult.data.fileName,
                    fileUrl: checkFileShaResult.data.fileUrl,
                    fileSha256: checkFileShaResult.data.fileSha256
                }
                // 更新进度为100%
                const fileIndex = uploadingFiles.value.findIndex(f => f.id === uploadingFile.id)
                if (fileIndex !== -1) {
                    uploadingFiles.value[fileIndex].progress = 100
                }
            } else {
                const generatePutUrlResult = await generatePutUrl({
                    filename: file.name,
                })
                if (!generatePutUrlResult.ok) {
                    message.error(`文件 ${file.name} 上传失败`)
                    // 从上传列表中移除
                    uploadingFiles.value = uploadingFiles.value.filter(f => f.id !== uploadingFile.id)
                    continue
                }

                // 使用axios发送PUT请求上传文件
                try {

                    console.log('file', file)
                    console.log('generatePutUrlResult', generatePutUrlResult)
                    const response = await axios.put(generatePutUrlResult.data.url, file, {
                        headers: {
                            'Content-Type': generatePutUrlResult.data.contentType
                        },
                        onUploadProgress: (progressEvent) => {
                            // 更新上传进度
                            if (progressEvent.total) {
                                const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
                                const fileIndex = uploadingFiles.value.findIndex(f => f.id === uploadingFile.id);
                                if (fileIndex !== -1) {
                                    uploadingFiles.value[fileIndex].progress = percentCompleted;
                                }
                            }
                        }
                    });

                    if (response.status !== 200) {
                        throw new Error(`上传失败: ${response.status} ${response.statusText}`);
                    }

                    // 确保上传进度为100%
                    const fileIndex = uploadingFiles.value.findIndex(f => f.id === uploadingFile.id);
                    if (fileIndex !== -1) {
                        uploadingFiles.value[fileIndex].progress = 100;
                    }
                } catch (error) {
                    console.error('文件上传错误:', error);
                    message.error(`文件 ${file.name} 上传失败`);
                    // 从上传列表中移除
                    uploadingFiles.value = uploadingFiles.value.filter(f => f.id !== uploadingFile.id);
                    continue;
                }

                uploadByUrlParams = {
                    fileName: file.name,
                    fileUrl: removeQuestionMarkText(generatePutUrlResult.data.url),
                    fileSha256: `${sha256}`,
                }
            }

            const uploadByUrlResult = await uploadByUrl(uploadByUrlParams)

            if (!uploadByUrlResult.ok) {
                message.error(`文件 ${file.name} 上传失败`)
                // 从上传列表中移除
                uploadingFiles.value = uploadingFiles.value.filter(f => f.id !== uploadingFile.id)
                continue
            }

            const res = await addFiles({
                "spaceId": spaceId.value,
                "folderId": props.folderId || '',
                fileIds: [uploadByUrlResult.data.id]
            })

            if (res?.code == HTTP_STATUS.MOBILE_NOT_BOUND) {
                user.setShowPhoneBoundModal({
                    status: BindPhoneModal.SHOW_BINDING,
                })
                return
            }

            if (!res || !res.ok) {
                // 从上传列表中移除
                uploadingFiles.value = uploadingFiles.value.filter(f => f.id !== uploadingFile.id)
                continue
            }

            // 上传成功后延迟移除进度条
            uploadingFiles.value = uploadingFiles.value.filter(f => f.id !== uploadingFile.id)

            // 增加成功计数
            successCount++

            // 如果所有有效文件都上传成功，才通知父组件
            if (successCount === totalValidFiles) {
                message.success('所有文件上传成功')
                emit('action', { type: 'uploadComplete' })
            }

        } catch (error) {
            console.error('Error uploading file:', error)
            message.error(`文件 ${file.name} 上传失败`)
            // 从上传列表中移除
            uploadingFiles.value = uploadingFiles.value.filter(f => f.id !== uploadingFile.id)
        }
    }
}

// 处理 LibraryExternal 组件的 action 事件
const handleAction = (data: any) => {
    if (data.type == 'pasteImported') {
        emit('action', { type: 'uploadComplete' })
    } else if (data.type == 'urlImported') {
        emit('action', { type: 'urlImported', value: data.value })
    }
}
</script>