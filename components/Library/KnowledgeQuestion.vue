<template>
    <!--知识库提问--->
    <div class="flex flex-col flex-1 h-full overflow-hidden">
        <Entrance :is-visible-top="false" :is-knowledge="true" :exampleList="exampleList || []"
            :repository-folder-id-list="repositoryFolderIdList || []" :session-id="sessionId"
            :is-new-session-id="isNewSessionId" :key="sessionId" :session-from="SessionFrom.KnowledgeFolder"
            :is-empty-document="isEmptyDocument" />
    </div>
</template>

<script setup lang="ts">
import { type RepositoryFile } from '@/services/types/repositoryFile';
const Entrance = defineAsyncComponent(() => import('~/components/Chat/Entrance.vue'));
interface Props {
    knowledgeDetail: RepositoryFile | null
    repositoryFolderIdList: string[] | null
    sessionId: string
    exampleList: string[]
    isEmptyDocument: boolean
}
const props = withDefaults(defineProps<Props>(), {
    knowledgeDetail: null,
    sessionId: '',
    isEmptyDocument: false,
})

const isNewSessionId = computed(() => props.sessionId.length < 2 ? true : false)
// const isLoading = ref(true)
// onMounted(() => {
//     const chat = useChatStore()
//     if (props.sessionId) {
//         chat.setIsNewSessionId(false)
//     } else {
//         chat.setIsNewSessionId(true)
//     }
//     isLoading.value = false
// })

</script>