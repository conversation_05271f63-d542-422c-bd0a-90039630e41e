<template>
  <div class="flex flex-col flex-1 h-full overflow-hidden">
    <Entrance v-if="!isLoading" :editorData="props.knowledgeDetail?.processData.summary_data" :is-visible-top="false"
      :key="sessionId" :repositoryFileId="repositoryFileId" :is-knowledge="true" :exampleList="exampleList"
      :session-from="SessionFrom.KnowledgeSingleFile" :session-id="sessionId" :is-new-session-id="isNewSessionId" />
  </div>
</template>

<script setup lang="ts">
import { type RepositoryFile } from '@/services/types/repositoryFile';
import { getMessageSessionList } from '~/api/appMessage';
const Entrance = defineAsyncComponent(() => import('~/components/Chat/Entrance.vue'));
interface Props {
  knowledgeDetail: RepositoryFile | null
}
const props = withDefaults(defineProps<Props>(), {
  knowledgeDetail: null
})


const exampleList = computed(() => {
  if (
    !props.knowledgeDetail ||
    !props.knowledgeDetail.processData ||
    !props.knowledgeDetail.processData.questions
  ) {
    return []
  }
  return props.knowledgeDetail.processData.questions || []
})

console.log(props)


const repositoryFileId = computed(() => {
  if (!props.knowledgeDetail) {
    return ''
  }
  return props.knowledgeDetail.id
})

const getMessageSessionListData = async () => {
  isLoading.value = true;
  const res = await getMessageSessionList({
    repositoryFileId: repositoryFileId.value
  });
  if (!res.success) {
    isLoading.value = false;
    return;
  }
  const list = res.data?.records || []
  if (list.length > 0) {
    sessionId.value = `${list[0].sessionId}`;
  } else {
    sessionId.value = ''
  }
  isLoading.value = false;
};
const isLoading = ref(true)
const sessionId = ref('')
const isNewSessionId = computed(() => {
  return sessionId.value === ''
})

// 这里要获取他的 sessionId
onMounted(() => {
  getMessageSessionListData()
})

</script>