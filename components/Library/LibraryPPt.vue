<template>
    <div class="px-8 py-2">
        <div class="bg-[#F5F7FF] px-16 py-12 space-y-2">
            <div class="flex items-center tase-base text-[#333333] space-x-3">
                <img src="https://static-1256600262.file.myqcloud.com/xiaoin-h5/logo/PPT.png" class="w-[24px] h-[24px]"
                    alt="">
                <span>文档转PPT</span>
            </div>
            <!-- 介绍 -->
            <div class="bg-[#cce0ff] rounded-[10px] p-5">
                <div>
                    <div class="flex items-center">
                        <BookIconfont name="shengshisheng<PERSON>" :size="16" color="#2551B5">
                        </BookIconfont>
                        <span class="text-[15px] font-bold leading-[23px] text-[#2551b5] ml-[9px]">智能匹配</span>
                    </div>
                    <div class="mt-[7px] text-[#4d6fbb] text-[13px] leading-[19px]">
                        100%忠于原文内容生成
                    </div>
                </div>

                <div class="mt-[17px]">
                    <div class="flex items-center">
                        <BookIconfont name="zhuanyepaiban" :size="16" color="#2551B5">
                        </BookIconfont>
                        <span class="text-[15px] font-bold leading-[23px] text-[#2551b5] ml-[9px]">专业排版</span>
                    </div>
                    <div class="mt-[7px] text-[#4d6fbb] text-[13px] leading-[19px]">
                        海量模版选择，专业图示效果
                    </div>
                </div>

                <div class="mt-[17px]">
                    <div class="flex items-center">
                        <BookIconfont name="shengshishengli" :size="16" color="#2551B5">
                        </BookIconfont>
                        <span class="text-[15px] font-bold leading-[23px] text-[#2551b5] ml-[9px]">省时省力</span>
                    </div>
                    <div class="mt-[7px] text-[#4d6fbb] text-[13px] leading-[19px]">
                        只需几分钟，演讲、汇报轻松搞定
                    </div>
                </div>
            </div>

            <!-- 按钮 -->
            <nuxt-link class="block mt-6" to="/create/PPT">
                <img src="https://xiaoin-file.oss-cn-shanghai.aliyuncs.com/xiaoin_images/LirbaryPPT.png"
                    class="w-full max-h-12" />
            </nuxt-link>
        </div>
    </div>

</template>
<script setup lang="ts">




</script>