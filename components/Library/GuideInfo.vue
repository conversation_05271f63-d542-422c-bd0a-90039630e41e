<template>
    <div class="p-6 space-y-4 prose max-w-none " v-if="contentArr.length > 0">
        <div class="border border-blue-100 rounded-lg bg-blue-50">
            <div ref="contentContainer" :class="[
                'content-container',
                { 'collapsed': !isExpanded && shouldShowExpandButton }
            ]">
                <div v-for="(item, index) in contentArr" :key="index">
                    <div class="p-4 ">
                        <div class="flex flex-row items-center justify-between">
                            <h2 class="text-lg font-semibold text-[#2551B5] m-0">{{ item.title }}</h2>

                            <div class="flex flex-row">
                                <button @click="handleCopy(item.content)"
                                    class="flex items-center px-2 py-1 text-sm text-gray-600 transition-colors rounded-full hover:text-blue-700 hover:bg-blue-50">
                                    <copy theme="outline" size="16" class="mr-1" />
                                    复制
                                </button>
                                <button v-if="showNotes" @click="handleAddNote(item.content)"
                                    class="flex items-center px-2 py-1 text-sm text-gray-600 transition-colors rounded-full hover:text-blue-700 hover:bg-blue-50">
                                    <add theme="outline" size="16" class="mr-1" />
                                    添加到笔记

                                </button>
                            </div>
                        </div>
                        <div v-html="getNewsContent(item)"
                            class="flex-grow px-2 overflow-hidden text-sm text-gray-600 guide-info-content-wrapper">
                        </div>
                    </div>
                </div>
            </div>

            <button v-if="shouldShowExpandButton" @click="toggleExpanded"
                class="w-full text-base p-2 text-[#666666] hover:text-[#2551B5] transition-colors flex items-center justify-center gap-1">
                <span>{{ isExpanded ? '收起' : '查看更多' }}</span>
                <Up v-if="isExpanded" theme="outline" size="24" class="text-inherit" :fill="'currentColor'" />
                <Down v-else theme="outline" size="24" class="text-inherit" :fill="'currentColor'" />
            </button>
        </div>
    </div>
</template>

<script setup lang="ts">
import { type RepositoryFile } from '@/services/types/repositoryFile'
import { StarloveConstants } from '@/utils/starloveConstants'
import { Add, Copy, Down, Up } from '@icon-park/vue-next'
import markdownItKatex from '@vscode/markdown-it-katex'
import { message } from 'ant-design-vue'
import hljs from 'highlight.js/lib/core'
import 'highlight.js/styles/ir-black.css'
import 'katex/dist/katex.min.css'
import markdownit from 'markdown-it'
import markdownItContainer from 'markdown-it-container'
import markdownItHighlightjs from 'markdown-it-highlightjs'
import { computed, nextTick, onMounted, ref, watch } from 'vue'
import type { EditorData } from '~/services/types/repositoryFile'

interface Props {
    knowledgeDetail?: RepositoryFile | null
    content?: string
    showNotes?: boolean
    editorData?: EditorData[]
}
const props = withDefaults(defineProps<Props>(), {
    knowledgeDetail: null,
    content: '',
    showNotes: true,
    editorData: () => []
})


console.log(props)

// 展开/折叠相关状态
const isExpanded = ref(false)
const shouldShowExpandButton = ref(false)
const contentContainer = ref<HTMLElement>()

const { $eventBus } = useNuxtApp()


const contentArr = computed(() => {
    let result = []
    // 处理 props.content
    if (props.content) {
        try {
            result = JSON.parse(props.content)
        } catch (e) {
            result = []
        }
    }
    // 如果 result 为空，尝试处理 props.knowledgeDetail.processData.summary_data
    if (result.length === 0 && props.knowledgeDetail?.processData?.summary_data) {
        try {
            result = props.knowledgeDetail.processData.summary_data
        } catch (e) {
            result = []
        }
    }

    if (result.length === 0 && props.editorData) {
        try {
            result = props.editorData
        } catch (e) {
            result = []
        }
    }



    return result
})

const md = markdownit({
    html: true, // 启用 HTML 标签
    linkify: true, // 自动将 URL 转换为链接
    breaks: true,
    xhtmlOut: true,
    langPrefix: 'language-',
    typographer: true,
    highlight: function (str: any, lang: any) {
        if (lang && hljs.getLanguage(lang)) {
            try {
                return '<pre class="hljs"><code>' + hljs.highlight(lang, str, true).value + '</code></pre>'
            } catch (__) { }
        }
        return '<pre class="hljs"><code>' + md.utils.escapeHtml(str) + '</code></pre>'
    },
})
    .use(markdownItKatex)
    .use(markdownItContainer, 'spoiler', {
        validate: function (params: any) {
            return params.trim().match(/^spoiler\s+(.*)$/)
        },
        render: function (tokens: any, idx: any) {
            var m = tokens[idx].info.trim().match(/^spoiler\s+(.*)$/)
            if (tokens[idx].nesting === 1) {
                return '<details><summary>' + md.utils.escapeHtml(m[1]) + '</summary>\n'
            } else {
                return '</details>\n'
            }
        },
    })
    .use(markdownItHighlightjs)

function markdownItPluginAddTarget(md: any) {
    const defaultRender =
        md.renderer.rules.link_open ||
        function (tokens, idx, options, env, self) {
            return self.renderToken(tokens, idx, options)
        }

    md.renderer.rules.link_open = function (tokens, idx, options, env, self) {
        const aIndex = tokens[idx].attrIndex('target')
        if (aIndex < 0) {
            tokens[idx].attrPush(['target', '_blank']) // 添加 target 属性
        }
        return defaultRender(tokens, idx, options, env, self)
    }
}

md.use(markdownItPluginAddTarget)

const getNewsContent = computed(() => (item: any) => {
    let _content = item.content || '' // 确保 _content 为字符串
    if (_content) {
        const regex = /```markdown\n([\s\S]*?)\n```/
        const match = _content.match(regex)
        if (match && match[1]) {
            _content = match[1]
        }

        // 定义正则表达式，用于匹配不带语言标记的代码块
        const extractCodeBlock = /```\n([\s\S]*?)\n```/
        const codeBlockMatch = _content.match(extractCodeBlock)
        if (codeBlockMatch && codeBlockMatch[1]) {
            _content = codeBlockMatch[1]
        }
        _content = processContent(_content)
        return md.render(_content)
    }
    return ''
})

const processContent = (content: string) => {
    return content
        .replace(/\\n/g, '\n') // 将 \n 转换为实际的换行符
        .replace(/\\"/g, '"') // 处理引号的转义
        .replace(/\\'/g, "'") // 处理单引号的转义
        .replace(/\\\\/g, '\\') // 处理反斜杠的转义
        .replace(/\\\{/g, '${')
        .replace(/\\\}/g, '}$')
        .replace(/\\\[/g, '$[')
        .replace(/\\\]/g, ']$')
        .replace(/\\\(/g, '$(')
        .replace(/\\\)/g, ')$')
}

// 新增：文章操作相关的方法
const handleCopy = async (content: string) => {
    try {
        const textToCopy = content.replace(/\\n/g, '\n').replace(/\n/g, '\n')

        // 检查是否支持 Clipboard API
        if (navigator.clipboard && window.isSecureContext) {
            await navigator.clipboard.writeText(textToCopy)
            message.success('复制成功')
        } else {
            // 降级方案：使用传统的复制方法
            const textArea = document.createElement('textarea')
            textArea.value = textToCopy
            textArea.style.position = 'fixed'
            textArea.style.left = '-999999px'
            textArea.style.top = '-999999px'
            document.body.appendChild(textArea)
            textArea.focus()
            textArea.select()

            try {
                document.execCommand('copy')
                message.success('复制成功')
            } catch (err) {
                message.error('复制失败 - 请重试')
            } finally {
                document.body.removeChild(textArea)
            }
        }
    } catch (err) {
        message.error('复制失败 - 请重试')
    }
}

const handleAddNote = (item: string) => {
    $eventBus.emit(
        StarloveConstants.keyOfEventBus.addToNotes,
        item.replace(/#/g, '').replace(/\*/g, '')
    )
}

// 检查内容高度是否超过 380px
const checkContentHeight = async () => {
    await nextTick()
    if (contentContainer.value) {
        const height = contentContainer.value.scrollHeight
        shouldShowExpandButton.value = height > 380
    }
}

// 切换展开/折叠状态
const toggleExpanded = () => {
    isExpanded.value = !isExpanded.value
}

// 监听内容变化，检查是否需要显示展开按钮
onMounted(() => {
    checkContentHeight()
})

// 当内容数组变化时重新检查高度
watch(contentArr, () => {
    checkContentHeight()
}, { deep: true })
</script>


<style lang="scss" scoped>
.guide-info-content-wrapper {
    :deep(p) {
        line-height: 2;
    }
}

.content-container {
    transition: max-height 0.3s ease-in-out;
    overflow: hidden;

    &.collapsed {
        max-height: 300px;
        position: relative;

        &::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: linear-gradient(transparent, rgba(239, 246, 255, 0.9));
            pointer-events: none;
        }
    }

    &:not(.collapsed) {
        max-height: none;
    }
}
</style>
