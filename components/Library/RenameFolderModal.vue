<template>
    <UModal :model-value="modelValue" @update:model-value="emit('update:modelValue', $event)">
        <div class="p-6">
            <div class="space-y-4">
                <div class="space-y-2">
                    <label class="text-sm font-medium text-gray-700">重命名文件夹</label>

                    <UInput v-model="folderName" :placeholder="`请输入新的${props.type === 'folder' ? '文件夹' : '文件'}名称`"
                        size="lg" color="gray" variant="outline" class="w-full"
                        :maxlength="`${props.type === 'folder' ? 30 : null}`">
                        <template #trailing>
                            <span class="text-gray-500">{{ extension }}</span>
                        </template>
                    </UInput>

                </div>
                <div class="flex justify-end space-x-3">
                    <button @click="handleCancel"
                        class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
                        取消
                    </button>
                    <button @click="handleConfirm" :disabled="!folderName.trim()"
                        class="px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 disabled:from-gray-400 disabled:to-gray-500 rounded-lg transition-colors">
                        确定
                    </button>
                </div>
            </div>
        </div>
    </UModal>
</template>

<script setup lang="ts">
import { ref } from 'vue';

interface Props {
    modelValue: boolean
    item: any
    type: string
}

const props = defineProps<Props>()
const emit = defineEmits(['update:modelValue', 'confirm'])

// 分离文件名和扩展名
const getFileNameAndExtension = (fullName: string | undefined) => {
    if (!fullName) {
        return {
            name: '',
            extension: ''
        }
    }
    const match = fullName.match(/^(.+)(\.[^.]+)$/)
    return {
        name: match ? match[1] : fullName,
        extension: match ? match[2] : ''
    }
}

const fileName = props.type === 'folder' ? props.item?.folderName : props.item?.name
const { name: initialName, extension: initialExtension } = getFileNameAndExtension(fileName)
const folderName = ref(initialName || '')
const extension = ref(initialExtension)

const handleCancel = () => {
    const currentFileName = props.type === 'folder' ? props.item?.folderName : props.item?.name
    const { name, extension: newExtension } = getFileNameAndExtension(currentFileName)
    folderName.value = name || ''
    extension.value = newExtension
    emit('update:modelValue', false)
}

const handleConfirm = () => {
    if (folderName.value.trim()) {
        emit('confirm', {
            filename: `${folderName.value.trim()}${extension.value}`,
            id: props.item.id
        })
        handleCancel()
    }
}
</script>