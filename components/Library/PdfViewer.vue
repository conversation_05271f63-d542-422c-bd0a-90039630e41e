<template>
  <div class="flex flex-col flex-1 h-full bg-gray-100">
    <div class="flex-1 overflow-auto">
      <div class="flex justify-center p-8">
        <div class="bg-white w-full shadow-lg rounded-lg">
          <client-only>
            <vue-pdf-embed :source="pdfUrl" :scale="zoom"
              class="w-full [&>div]:mb-16 [&>div]:pb-16 [&>div]:border-b-8 [&>div]:border-gray-100 last:[&>div]:mb-0 last:[&>div]:pb-0 last:[&>div]:border-b-0"
              @rendered="onPdfRendered" @loaded="onPdfLoaded" @document-loaded="onPdfDocumentLoaded"
              :style="{ minHeight: '100vh' }" />
          </client-only>
        </div>
      </div>
    </div>

    <!-- 页码控制 -->
    <div class="flex items-center justify-center space-x-4 p-4 bg-white border-t border-gray-200">
      <div v-if="totalPages" class="flex items-center space-x-4">
        <button @click="prevPage"
          class="w-10 h-10 flex items-center justify-center bg-gray-100 rounded-full hover:bg-gray-200 disabled:opacity-50"
          :disabled="currentPage <= 1">
          <left theme="outline" size="24" fill="#333" :strokeWidth="2" />
        </button>
        <span class="text-gray-600">第 {{ currentPage }} 页 / 共 {{ totalPages }} 页</span>
        <button @click="nextPage"
          class="w-10 h-10 flex items-center justify-center bg-gray-100 rounded-full hover:bg-gray-200 disabled:opacity-50"
          :disabled="currentPage >= totalPages">
          <right theme="outline" size="24" fill="#333" :strokeWidth="2" />
        </button>
      </div>
      <div v-else>
        <span class="text-gray-600">加载中...</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Left, Right } from '@icon-park/vue-next';
import { defineAsyncComponent, ref } from 'vue';

const props = defineProps({
  pdfUrl: {
    type: String,
    required: true
  }
})

// 页面控制相关的响应式变量
const currentPage = ref(1)
const totalPages = ref(undefined)
const zoom = ref(1)

const VuePdfEmbed = defineAsyncComponent(() =>
  import('vue-pdf-embed')
)

const onPdfRendered = (e) => {
  // PDF渲染完成后的回调
  console.log('PDF渲染完成', e)
  // console.log(`当前页码: ${currentPage.value}`)
  // console.log(`总页数: ${totalPages.value}`)
}

const onPdfLoaded = (e) => {
  console.log('onPdfLoaded 事件触发:', e._pdfInfo)
  if (e._pdfInfo) {
    totalPages.value = e._pdfInfo.numPages
  }
  // 详细打印加载事件对象的内容
  // console.log('加载事件对象详情:', {
  //   pagesCount: e.pagesCount,
  //   eventType: e.type,
  //   eventTarget: e.target,
  //   raw: e
  // })

  // if (e.pagesCount) {
  //   console.log('设置总页数:', e.pagesCount)
  //   totalPages.value = e.pagesCount
  // } else {
  //   console.warn('未能获取到PDF总页数')
  //   // 尝试从其他属性获取页数
  //   if (e.target && e.target._pdfInfo) {
  //     totalPages.value = e.target._pdfInfo.numPages
  //     console.log('从_pdfInfo获取总页数:', totalPages.value)
  //   }
  // }
}

const onPdfDocumentLoaded = (pdf) => {
  console.log('PDF文档加载完成:', pdf)
  // if (pdf && pdf.numPages) {
  //   console.log('获取到PDF总页数:', pdf.numPages)
  //   totalPages.value = pdf.numPages
  // }
}

// 翻页方法
const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
  }
}

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++
  }
}
</script>