<template>
    <!-- 知识库文件夹提问 -->
    <ClientOnly>
        <KnowledgeQuestion v-if="!isLoading" :key="sessionId" :repository-folder-id-list="repositoryFolderIdList"
            :knowledgeDetail="null" :sessionId="sessionId" :example-list="exampleList"
            :is-empty-document="isEmptyDocument" />
    </ClientOnly>
</template>
<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { getMessageSessionList } from '~/api/appMessage';
import KnowledgeQuestion from './KnowledgeQuestion.vue';

const props = defineProps({
    currentFolderId: {
        type: String,
        default: '0',
    },
    exampleList: {
        type: Array as PropType<string[]>,
        default: [],
    },
    isEmptyDocument: {
        type: Boolean,
        default: false,
    },
})
const repositoryFolderIdList = computed(() => {
    return [props.currentFolderId];
});
const sessionId = ref('')
const isLoading = ref(true);
const getMessageSessionListData = async () => {
    isLoading.value = true;
    const res = await getMessageSessionList({
        repositoryFolderId: props.currentFolderId,
    });
    if (!res.success) {
        isLoading.value = false;
        return;
    }
    const list = res.data?.records || []
    if (list.length > 0) {
        sessionId.value = `${list[0].sessionId}`;
    } else {
        sessionId.value = ''
    }
    isLoading.value = false;
};
watch(() => props.currentFolderId, (newVal, oldValue) => {
    if (newVal != oldValue) {
        getMessageSessionListData()
    }
})
onMounted(() => {
    getMessageSessionListData()
})

</script>