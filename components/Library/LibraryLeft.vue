<template>
    <div class="flex flex-col h-screen space-y-2 border-r border-gray-200/70 ">
        <!-- 头部 -->
        <div v-if="links.length <= 1" class=" p-2 sm:p-4
            h-20 flex items-center justify-between bg-gradient-to-l from-[#E1E6FF] to-[#DBEAFB]">
            <div class="flex items-center ">
                <div
                    class="flex items-center justify-center w-10 h-10 mr-2 bg-blue-700 shadow-inner rounded-xl sm:mr-4">
                    <brain theme="outline" size="24" fill="#FFFFFF" />
                </div>
                <div>
                    <h2
                        class="text-lg font-medium text-[#2551B5] bg-gradient-to-r from-blue-700 to-indigo-700 bg-clip-text">
                        知识库</h2>
                    <!-- <p class="text-sm text-gray-500">我的AI外脑</p> -->
                </div>
            </div>
            <!-- 存储空间信息 -->
            <div
                class="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm text-[#333333] font-medium whitespace-nowrap">
                <save-one theme="outline" size="18" fill="#333333" />
                <span class="text-[#333333] px-1 sm:px-2">
                    {{ formatStorageSize(spaceUsedBytes, 0) }}/{{
                        formatStorageSize(spaceQuotaBytes, 0) }}
                </span>
            </div>
        </div>
        <!-- 面包屑 -->
        <div v-if="links.length > 1" class="flex items-center flex-shrink-0 h-20 px-2 overflow-x-auto sm:px-4">
            <div class="flex flex-wrap items-center gap-1 sm:gap-2">

                <button @click="handleBreadcrumbClick(links[0])"
                    class="flex items-center space-x-2 cursor-pointer hover:text-blue-600 whitespace-nowrap">
                    <!-- <img src="https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/library/large-folders.png"
                        class="w-6 h-6 sm:w-8 sm:h-8" alt="folder"> -->
                    <Iconfont name="folder" :size="26" />
                    <span class="text-xs text-gray-500 sm:text-lg">{{ links[0]?.label || '根目录' }}</span>
                </button>
                <template v-for="(link, index) in links.slice(1)" :key="link.id">
                    <div class="text-gray-400">
                        <right theme="outline" size="24" class="sm:size-16" />
                    </div>
                    <button @click="index < links.length - 2 && handleBreadcrumbClick(link)" :class="[
                        'text-xs sm:text-lg transition-colors whitespace-nowrap max-w-[100px] sm:max-w-[150px] md:max-w-none overflow-hidden text-ellipsis',
                        index === links.length - 2
                            ? 'text-blue-600 font-medium cursor-default'
                            : 'text-gray-500 hover:text-blue-600 cursor-pointer'
                    ]">
                        {{ link.label }}
                    </button>
                </template>
            </div>
        </div>

        <!-- 搜索框 -->
        <div
            class="flex items-center transition-all duration-200 bg-white border shadow-md sm:mx-4 md:flex-none border-blue-200/50 rounded-xl focus-within:border-blue-300/50 focus-within:ring-2 focus-within:ring-blue-200/50">
            <!-- 搜索图标 -->
            <div class="px-3 text-blue-500">
                <search theme="outline" size="24" fill="#3b82f6" />
            </div>

            <!-- 输入框 -->
            <input v-model="searchQuery" type="text" placeholder="搜索文件名、文件夹名称"
                class="w-full h-9 bg-transparent text-[#333] placeholder-[#999] focus:outline-none text-base "
                @keyup.enter="handleSearch" />
            <!-- 搜索按钮 -->
            <button @click="handleSearch"
                class="bg-[#2551B5] h-full px-4 rounded-lg text-white text-sm font-medium transition-all duration-200 hover:bg-blue-600 whitespace-nowrap">
                <span>搜索</span>
            </button>

        </div>

        <!-- 新建文件夹 -->
        <div class="flex items-center w-full gap-6 px-2 sm:px-4">
            <button v-if="canCreateFolder" @click="handleCreateFolderClick"
                class="flex-1 text-xs sm:text-sm px-3 sm:px-4 py-2 bg-[#E7EDFE] text-[#2551B5] rounded-lg transition-colors border border-[#2551B5] font-medium">
                新建文件夹
            </button>
            <LibraryUploadEntry :folderId="page.folderId" @action="handleAction"></LibraryUploadEntry>
        </div>

        <!-- 文件和文件夹 -->
        <div class="flex-1  overflow-y-auto ">
            <div class="flex items-center flex-1 h-full overflow-hidden">
                <!-- 加载状态 -->
                <div v-if="isFilesLoading || isFoldersLoading" class="flex items-center justify-center w-full py-10">
                    <loading theme="outline" size="40" class="text-gray-400 animate-spin" />
                </div>

                <!-- 内容区域：文件夹和文件列表 -->
                <div v-if="(folders.length > 0 || teamFiles.length > 0) && !isFilesLoading && !isFoldersLoading"
                    class="flex flex-col justify-between flex-1 w-full h-full">
                    <!-- 文件夹和文件列表容器 -->
                    <div ref="scrollContainer" class="flex-1 min-h-0 overflow-y-auto" @scroll="handleScroll">
                        <!-- 文件夹列表 -->
                        <div v-if="folders.length > 0" class="px-2 sm:px-4 mb-3 sm:mb-4">
                            <FolderItem :folders="folders" @select="handleFolderClick" @rename="handleRename('folder')"
                                @move="handleMove" @delete="handleDeleteItem('folder')" />
                        </div>

                        <!-- 文件列表 -->
                        <div v-if="teamFiles.length > 0" class="px-2 sm:px-4">
                            <KnowledgeItem :items="teamFiles" @delete="handleDeleteItem('file')"
                                @rename="handleRename('file')" @move="handleMove" :links="links" />
                        </div>

                        <!-- 加载更多指示器 -->
                        <div v-if="isLoadingMore" class="flex items-center justify-center py-4">
                            <loading theme="outline" size="24" class="mr-2 text-gray-400 animate-spin" />
                            <span class="text-sm text-gray-500">加载中...</span>
                        </div>

                        <!-- 没有更多数据提示 -->
                        <div v-if="teamFiles.length > 0 && !hasMoreData && !isLoadingMore"
                            class="flex items-center justify-center py-4">
                            <span class="text-sm text-gray-400">没有更多数据了</span>
                        </div>
                    </div>
                </div>

                <!-- 空状态 -->
                <div v-if="!isFilesLoading && !isFoldersLoading && folders.length === 0 && teamFiles.length === 0"
                    class="flex flex-col items-center justify-center w-full py-12 sm:py-20">
                    <div class="flex flex-col items-center">
                        <div
                            class="flex items-center justify-center w-16 h-16 mb-3 rounded-full sm:w-20 sm:h-20 sm:mb-4 bg-gray-50">
                            <folder-open theme="outline" size="36" class="text-gray-400" />
                        </div>
                        <h3 class="mb-1 text-base font-medium text-gray-600 sm:mb-2 sm:text-lg">知识库中暂无文档
                        </h3>
                        <p class="text-xs text-gray-500 sm:text-sm">上传一个文档，开始使用您的知识库吧</p>
                    </div>
                </div>
            </div>

            <!-- 新建文件夹弹窗 -->
            <CreateFolderModal v-model="showCreateFolderModal" @confirm="handleCreateFolder" />
            <!-- 移动文件弹窗 -->
            <MoveFileModal v-if="showMoveModal" :type="moveType" v-model="showMoveModal" @confirm="handleMoveConfirm"
                :currentItem="currentFolder" />
        </div>
    </div>
</template>


<script setup lang="ts">
import { addFolder, addHtml, getFolder, getSubFolders, listFile } from '@/api/repositoryFile';
import FolderItem from '@/components/Library/FolderItem.vue';
import KnowledgeItem from '@/components/Library/KnowledgeItem.vue';
import LibraryUploadEntry from '@/components/Library/LibraryUploadEntry.vue';
import type { NewRepositoryFile, RepositoryFolder } from '@/services/types/repositoryFile';
import { useUserStore } from '@/stores/user';
import { formatStorageSize } from '@/utils/utils';
import { Brain, FolderOpen, Loading, Right, SaveOne, Search } from '@icon-park/vue-next';
import { message } from 'ant-design-vue';
import { onMounted, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { useTracking } from '~/composables/useTracking';
import { UserService } from '~/services/user';
import { useSpaceStore } from '~/stores/space';

interface Folder {
    id: number
    name: string
    fileCount: number
    updateTime: string
}
interface Options {
    isShowLoading?: boolean
    isLoadMore?: boolean
}

const props = defineProps({
    currentFolderId: {
        type: String,
        default: '0',
    },
    exampleList: {
        type: Array as PropType<string[]>,
        default: [],
    },
    isEmptyDocument: {
        type: Boolean,
        default: false,
    },
})
const emit = defineEmits(['update:currentFolderId', 'update:exampleList', 'update:isEmptyDocument'])

const { track } = useTracking();
const user = useUserStore()
const spaceStore = useSpaceStore()

// 获取空间信息
const { spaceQuotaBytes, spaceUsedBytes } = storeToRefs(spaceStore)


const showMoveModal = ref(false)
const currentFolder = ref<RepositoryFolder | NewRepositoryFile | null>(null)
const moveType = ref<'folder' | 'file'>('folder')
// 添加一个 ref 来跟踪是否有处理中的文件
let hasProcessingFiles = false
let timerId: NodeJS.Timeout | undefined = undefined
const isFilesLoading = ref(true)
const isFoldersLoading = ref(true)
const showCreateFolderModal = ref(false)
const teamFiles = ref<NewRepositoryFile[]>([])
const folders = ref<RepositoryFolder[]>([])

// 无限滚动相关状态
const isLoadingMore = ref(false)
const hasMoreData = ref(true)
const scrollContainer = ref<HTMLElement | null>(null)


// 面包屑
const links = ref<Array<{
    label: string;
    to: string;
    id: string | number;
}>>([])
const route = useRoute()

const page = reactive({
    current: 1,
    pageSize: 15,
    total: 0,
    pages: 1,
    folderId: route.query.folderId || '0'
})

const spaceId = computed(() => {
    return user.currentLoginInfo?.id || ''
})


const searchQuery = ref('');
const handleSearch = () => {
    const userStore = useUserStore()
    if (!userStore.isLogined) {
        userStore.openLoginModal()
        return
    }
    const trimmedQuery = searchQuery.value.trim();
    if (!trimmedQuery) {
        message.warning('请输入搜索关键词');
        return;
    }
    navigateTo(`/library/search?keyword=${encodeURIComponent(trimmedQuery)}`);
};

// 滚动处理函数
const handleScroll = () => {
    if (!scrollContainer.value || isLoadingMore.value || !hasMoreData.value) return

    const { scrollTop, scrollHeight, clientHeight } = scrollContainer.value
    const isBottom = scrollTop + clientHeight >= scrollHeight - 50 // 距离底部 50px 内触发

    if (isBottom && page.current < page.pages) {
        loadMoreFiles()
    }
}

// 加载更多文件
const loadMoreFiles = async () => {
    if (isLoadingMore.value || !hasMoreData.value) return

    isLoadingMore.value = true
    page.current++

    try {
        await getFileType({ isShowLoading: false, isLoadMore: true })
    } catch (error) {
        console.error('加载更多文件失败:', error)
        page.current-- // 回退页码
    } finally {
        isLoadingMore.value = false
    }
}

// 添加计算属性判断是否可以创建文件夹
const canCreateFolder = computed(() => {
    // 条件1：文件夹层级不超过三级（不包括根目录）
    const levelCondition = links.value.length <= 3;
    // 条件2：当前目录下的文件夹数量不超过10个
    let folderCountCondition = false
    folderCountCondition = folders.value.length < 30;
    return levelCondition && folderCountCondition;
})


// 面包屑点击处理
const handleBreadcrumbClick = async (link: { id: string | number; to: string; label: string }) => {
    page.current = 1
    // 重置无限滚动状态
    hasMoreData.value = true
    isLoadingMore.value = false

    // 设置当前文件夹ID
    page.folderId = link.id.toString()

    // 更新面包屑导航（删除当前点击项之后的所有项）
    const index = links.value.findIndex(item => item.id === link.id)
    if (index !== -1) {
        links.value = links.value.slice(0, index + 1)
    }

    // 重新加载文件夹内容
    await getFileType({ isShowLoading: true })
    await getFolders({ isShowLoading: true })
    if (folders.value.length === 0 && teamFiles.value.length === 0) {
        emit('update:isEmptyDocument', true)
    }
}

// 新建文件夹
const handleCreateFolderClick = () => {
    const userStore = useUserStore()
    if (!userStore.isLogined) {
        userStore.openLoginModal()
        return
    }
    showCreateFolderModal.value = true;
}

// 创建文件夹
const handleCreateFolder = async (data: { name: string }) => {

    const params = {
        folderName: data.name,
        parentId: page.folderId
    }
    const res = await addFolder(spaceId.value, params)
    if (!res.ok) {
        // console.error('创建文件夹失败')
        return
    }
    // 重置无限滚动状态
    hasMoreData.value = true
    isLoadingMore.value = false

    await getFileType({ isShowLoading: true })
    await getFolders({ isShowLoading: true })
    if (folders.value.length === 0 && teamFiles.value.length === 0) {
        emit('update:isEmptyDocument', true)
    }
}


// 上传文件
const handleAction = async (payload: any) => {
    if (payload.type === 'uploadComplete') {
        page.current = 1
        // 重置无限滚动状态
        hasMoreData.value = true
        isLoadingMore.value = false

        await getFileType({ isShowLoading: true })
        // await getFolders({ isShowLoading: true })
        // spaceStore.loadSpaceInfo()

        track('library_upload',
            payload.postId,
            '文件上传'
        );
    } else if (payload.type === 'urlImported') {
        const res = await addHtml({
            "spaceId": spaceId.value,
            "folderId": page.folderId,
            url: payload.value,
        })
        if (!res.ok) {
            // console.error('网页抓取失败')
            return
        }
        message.success(res.message || '导入成功')
        page.current = 1
        // 重置无限滚动状态
        hasMoreData.value = true
        isLoadingMore.value = false

        await getFileType({ isShowLoading: true })
        // await getFolders({ isShowLoading: true })
        // spaceStore.loadSpaceInfo()

        track('library_import',
            payload.value,
            '网页导入'
        );
    }
}

// 获取文件进度
const getProgress = (record: NewRepositoryFile) => {
    if (record?.processData?.error) {
        return -1
    }
    if (record.status == RepositoryFileStatus.DONE) {
        return 100
    }
    return record.processData?.summary_status || 0
}


// 获取文件夹
const getFolders = async (options: Options | null = null) => {
    const defaultOptions: Options = { isShowLoading: true }
    const _options = options ?? defaultOptions

    if (_options.isShowLoading) {
        isFoldersLoading.value = true
    }
    if (!spaceId.value) {
        return
    }
    const params = {
        folderId: page.folderId,
    }
    const res = await getSubFolders(spaceId.value, params)
    if (!res.ok) {
        // console.error('获取文件夹列表失败')
        if (_options.isShowLoading) {
            isFoldersLoading.value = false
        }
        return
    }
    folders.value = res.data?.map((d: Folder) => ({
        ...d,
        folderId: page.folderId
    }))

    if (_options.isShowLoading) {
        isFoldersLoading.value = false
    }
}

// 获取文件
const getFileType = async (options: Options | null = null) => {
    const defaultOptions: Options = { isShowLoading: true, isLoadMore: false }
    const _options = options ?? defaultOptions

    if (_options.isShowLoading) {
        isFilesLoading.value = true
    }
    if (!spaceId.value) {
        console.error('spaceId.value==>', spaceId.value)
        return
    }

    const params = {
        folderId: page.folderId,
        pageNo: page.current,
        pageSize: page.pageSize
    }

    const res = await listFile(spaceId.value, params)

    if (!res.ok) {
        // console.error('获取文档列表失败')
        isFilesLoading.value = false
        return
    }

    // 如果当前页没有数据且不是第一页,自动跳转到上一页
    if (res.data?.records?.length === 0 && page.current > 1 && !_options.isLoadMore) {
        page.current--
        isFilesLoading.value = false
        return await getFileType(options)
    }

    const newFiles = res.data?.records?.map((d: NewRepositoryFile) => ({
        ...d,
        progress: getProgress(d),
        folderId: page.folderId
    })) || []

    // 如果是加载更多，追加到现有列表；否则替换列表
    if (_options.isLoadMore) {
        teamFiles.value = [...teamFiles.value, ...newFiles]
    } else {
        teamFiles.value = newFiles
    }
    emit('update:exampleList', teamFiles.value.length > 0 ? teamFiles.value[0].processData?.questions || [] : [])

    Object.assign(page, {
        current: res.data?.current,
        total: res.data?.total,
        pageSize: res.data?.size,
        pages: res.data?.pages,
    })

    // 更新是否还有更多数据的状态
    hasMoreData.value = page.current < page.pages

    if (_options.isShowLoading) {
        isFilesLoading.value = false
    }

    // 确保 processingList 是一个数组
    const processingList = teamFiles.value.filter((d) => d.status !== RepositoryFileStatus.ERROR && d.status !== RepositoryFileStatus.DONE && d.progress > -1 && d.progress < 100) || []
    if (timerId) {
        clearTimeout(timerId)
    }
    // 检查是否有处理中的文件
    if (processingList.length > 0) {
        hasProcessingFiles = true
        timerId = setTimeout(() => {
            getFileType({ isShowLoading: false })
        }, 5000)
    } else if (hasProcessingFiles) {
        // 如果之前有处理中的文件，现在没有了，说明文件都处理完成了
        hasProcessingFiles = false
        spaceStore.loadSpaceInfo()
    }
}

// 点击文件夹
const handleFolderClick = async (folder: RepositoryFolder) => {
    // console.log(folder)
    page.current = 1
    // 重置无限滚动状态
    hasMoreData.value = true
    isLoadingMore.value = false

    page.folderId = folder.id.toString()

    // 添加新的面包屑项
    links.value.push({
        label: folder.folderName,
        to: folder.folderPath,
        id: folder.id
    })

    await getFileType({ isShowLoading: true })
    await getFolders({ isShowLoading: true })
    console.log(folders.value.length, 'folders.value.length', teamFiles.value.length)
    if (folders.value.length === 0 && teamFiles.value.length === 0) {
        emit('update:isEmptyDocument', true)
    }
}

// 重命名
const handleRename = async (item: string) => {
    if (item === 'folder') {
        await getFolders({ isShowLoading: true })
    } else if (item === 'file') {
        // 重置无限滚动状态
        hasMoreData.value = true
        isLoadingMore.value = false
        await getFileType({ isShowLoading: true })
    }
}

// 移动文件
const handleMove = async (item: RepositoryFolder | NewRepositoryFile, type: 'folder' | 'file') => {
    moveType.value = type
    currentFolder.value = item
    showMoveModal.value = true
}


// 移动文件
const handleMoveConfirm = async () => {
    await getFileType({ isShowLoading: true })
    await getFolders({ isShowLoading: true })
    showMoveModal.value = false
    if (folders.value.length === 0 && teamFiles.value.length === 0) {
        emit('update:isEmptyDocument', true)
    }
}

// 删除文件
const handleDeleteItem = async (item: string) => {
    if (item === 'folder') {
        await getFolders({ isShowLoading: true })
    } else if (item === 'file') {
        // 重置无限滚动状态
        hasMoreData.value = true
        isLoadingMore.value = false
        await getFileType({ isShowLoading: true })
    }
    // 如果当前页没有数据了,且不是第一页,则跳转到上一页
    if (teamFiles.value.length === 0 && page.current > 1) {
        page.current--
        // 重置无限滚动状态
        hasMoreData.value = true
        isLoadingMore.value = false
        if (item === 'folder') {
            await getFolders({ isShowLoading: true })
        } else if (item === 'file') {
            await getFileType({ isShowLoading: true })
        }
    }
}

// 用户登录成功  加载数据
watch(() => user.isLogined, (isLogined) => {
    if (isLogined) {
        // console.log('用户登录成功，重新加载数据')
        setupData()
    }
})

// 初始化面包屑
const initBreadcrumb = async () => {
    const res = await getFolder(spaceId.value, { folderId: page.folderId.toString() })
    if (!res.ok) {
        // console.error('获取文件夹列表失败')
        return
    }
    // 如果是根目录，只显示个人知识库
    if (page.folderId === '0' || !res.data) {
        links.value = [{
            label: '个人知识库',
            to: '/library',
            id: '0'
        }]
        return
    }

    // 重置面包屑
    links.value = []

    // 添加根目录
    links.value.push({
        label: '个人知识库',
        to: '/library',
        id: '0'
    })

    // 解析 parentIds 和 folderPath 来构建面包屑
    const folderData = res.data
    const parentIds = folderData.parentIds ? folderData.parentIds.split(',') : []
    const folderPath = folderData.folderPath || ''

    // 分割路径，去除空字符串
    const pathSegments = folderPath.split('/').filter(segment => segment.trim() !== '')
    // 构建面包屑链
    if (parentIds.length > 0 && pathSegments.length > 0) {
        // parentIds 包含所有父级ID，包括当前文件夹的ID
        // pathSegments 包含路径的各个部分

        // 为每个路径段创建面包屑项
        for (let i = 0; i < pathSegments.length; i++) {
            const pathToHere = '/' + pathSegments.slice(0, i + 1).join('/')
            // parentIds[i] 对应 pathSegments[i] 的ID
            const folderId = i < parentIds.length ? parentIds[i] : folderData.id

            links.value.push({
                label: pathSegments[i],
                to: pathToHere,
                id: folderId
            })
            console.log(`添加面包屑项 ${i}:`, {
                label: pathSegments[i],
                to: pathToHere,
                id: folderId
            })
        }
    }
}




const setupData = async () => {
    if (!UserService.isLogined()) {
        isFilesLoading.value = false
        isFoldersLoading.value = false
        return
    }

    spaceStore.loadSpaceInfo()
    await getFolders({ isShowLoading: true })
    await getFileType({ isShowLoading: true })
    if (folders.value.length === 0 && teamFiles.value.length === 0) {
        emit('update:isEmptyDocument', true)
    }
    initBreadcrumb()
}

watch(() => page.folderId, (newValue) => {
    console.log(page.folderId, 'page.folderId')
    emit('update:currentFolderId', newValue)
})


onMounted(() => {
    setupData()
})

</script>