const THUMBNAIL_SCROLL_MARGIN = -19
const THUMBNAIL_SELECTED_CLASS = 'selected'
class PDFThumbnailViewer {
  constructor({
    container,
    eventBus,
    linkService,
    renderingQueue,
    pageColors,
    abortSignal,
    enableHWA,
  }) {
    this.container = container
    this.eventBus = eventBus
    this.linkService = linkService
    this.renderingQueue = renderingQueue
    this.pageColors = pageColors || null
    this.enableHWA = enableHWA || false
    this.scroll = watchScroll(this.container, this.#scrollUpdated.bind(this), abortSignal)
    this.#resetView()
  }
  #scrollUpdated() {
    this.renderingQueue.renderHighestPriority()
  }
  getThumbnail(index) {
    return this._thumbnails[index]
  }
  #getVisibleThumbs() {
    return getVisibleElements({
      scrollEl: this.container,
      views: this._thumbnails,
    })
  }
  scrollThumbnailIntoView(pageNumber) {
    if (!this.pdfDocument) {
      return
    }
    const thumbnailView = this._thumbnails[pageNumber - 1]
    if (!thumbnailView) {
      console.error('scrollThumbnailIntoView: Invalid "pageNumber" parameter.')
      return
    }
    if (pageNumber !== this._currentPageNumber) {
      const prevThumbnailView = this._thumbnails[this._currentPageNumber - 1]
      prevThumbnailView.div.classList.remove(THUMBNAIL_SELECTED_CLASS)
      thumbnailView.div.classList.add(THUMBNAIL_SELECTED_CLASS)
    }
    const { first, last, views } = this.#getVisibleThumbs()
    if (views.length > 0) {
      let shouldScroll = false
      if (pageNumber <= first.id || pageNumber >= last.id) {
        shouldScroll = true
      } else {
        for (const { id, percent } of views) {
          if (id !== pageNumber) {
            continue
          }
          shouldScroll = percent < 100
          break
        }
      }
      if (shouldScroll) {
        scrollIntoView(thumbnailView.div, {
          top: THUMBNAIL_SCROLL_MARGIN,
        })
      }
    }
    this._currentPageNumber = pageNumber
  }
  get pagesRotation() {
    return this._pagesRotation
  }
  set pagesRotation(rotation) {
    if (!isValidRotation(rotation)) {
      throw new Error('Invalid thumbnails rotation angle.')
    }
    if (!this.pdfDocument) {
      return
    }
    if (this._pagesRotation === rotation) {
      return
    }
    this._pagesRotation = rotation
    const updateArgs = {
      rotation,
    }
    for (const thumbnail of this._thumbnails) {
      thumbnail.update(updateArgs)
    }
  }
  cleanup() {
    for (const thumbnail of this._thumbnails) {
      if (thumbnail.renderingState !== RenderingStates.FINISHED) {
        thumbnail.reset()
      }
    }
    TempImageFactory.destroyCanvas()
  }
  #resetView() {
    this._thumbnails = []
    this._currentPageNumber = 1
    this._pageLabels = null
    this._pagesRotation = 0
    this.container.textContent = ''
  }
  setDocument(pdfDocument) {
    if (this.pdfDocument) {
      this.#cancelRendering()
      this.#resetView()
    }
    this.pdfDocument = pdfDocument
    if (!pdfDocument) {
      return
    }
    const firstPagePromise = pdfDocument.getPage(1)
    const optionalContentConfigPromise = pdfDocument.getOptionalContentConfig({
      intent: 'display',
    })
    firstPagePromise
      .then((firstPdfPage) => {
        const pagesCount = pdfDocument.numPages
        const viewport = firstPdfPage.getViewport({
          scale: 1,
        })
        for (let pageNum = 1; pageNum <= pagesCount; ++pageNum) {
          const thumbnail = new PDFThumbnailView({
            container: this.container,
            eventBus: this.eventBus,
            id: pageNum,
            defaultViewport: viewport.clone(),
            optionalContentConfigPromise,
            linkService: this.linkService,
            renderingQueue: this.renderingQueue,
            pageColors: this.pageColors,
            enableHWA: this.enableHWA,
          })
          this._thumbnails.push(thumbnail)
        }
        this._thumbnails[0]?.setPdfPage(firstPdfPage)
        const thumbnailView = this._thumbnails[this._currentPageNumber - 1]
        thumbnailView.div.classList.add(THUMBNAIL_SELECTED_CLASS)
      })
      .catch((reason) => {
        console.error('Unable to initialize thumbnail viewer', reason)
      })
  }
  #cancelRendering() {
    for (const thumbnail of this._thumbnails) {
      thumbnail.cancelRendering()
    }
  }
  setPageLabels(labels) {
    if (!this.pdfDocument) {
      return
    }
    if (!labels) {
      this._pageLabels = null
    } else if (!(Array.isArray(labels) && this.pdfDocument.numPages === labels.length)) {
      this._pageLabels = null
      console.error('PDFThumbnailViewer_setPageLabels: Invalid page labels.')
    } else {
      this._pageLabels = labels
    }
    for (let i = 0, ii = this._thumbnails.length; i < ii; i++) {
      this._thumbnails[i].setPageLabel(this._pageLabels?.[i] ?? null)
    }
  }
  async #ensurePdfPageLoaded(thumbView) {
    if (thumbView.pdfPage) {
      return thumbView.pdfPage
    }
    try {
      const pdfPage = await this.pdfDocument.getPage(thumbView.id)
      if (!thumbView.pdfPage) {
        thumbView.setPdfPage(pdfPage)
      }
      return pdfPage
    } catch (reason) {
      console.error('Unable to get page for thumb view', reason)
      return null
    }
  }
  #getScrollAhead(visible) {
    if (visible.first?.id === 1) {
      return true
    } else if (visible.last?.id === this._thumbnails.length) {
      return false
    }
    return this.scroll.down
  }
  forceRendering() {
    const visibleThumbs = this.#getVisibleThumbs()
    const scrollAhead = this.#getScrollAhead(visibleThumbs)
    const thumbView = this.renderingQueue.getHighestPriority(
      visibleThumbs,
      this._thumbnails,
      scrollAhead
    )
    if (thumbView) {
      this.#ensurePdfPageLoaded(thumbView).then(() => {
        this.renderingQueue.renderView(thumbView)
      })
      return true
    }
    return false
  }
}
function watchScroll(viewAreaElement, callback, abortSignal = undefined) {
  const debounceScroll = function (evt) {
    if (rAF) {
      return
    }
    rAF = window.requestAnimationFrame(function viewAreaElementScrolled() {
      rAF = null
      const currentX = viewAreaElement.scrollLeft
      const lastX = state.lastX
      if (currentX !== lastX) {
        state.right = currentX > lastX
      }
      state.lastX = currentX
      const currentY = viewAreaElement.scrollTop
      const lastY = state.lastY
      if (currentY !== lastY) {
        state.down = currentY > lastY
      }
      state.lastY = currentY
      callback(state)
    })
  }
  const state = {
    right: true,
    down: true,
    lastX: viewAreaElement.scrollLeft,
    lastY: viewAreaElement.scrollTop,
    _eventHandler: debounceScroll,
  }
  let rAF = null
  viewAreaElement.addEventListener('scroll', debounceScroll, {
    useCapture: true,
    signal: abortSignal,
  })
  abortSignal?.addEventListener('abort', () => window.cancelAnimationFrame(rAF), {
    once: true,
  })
  return state
}
export function scrollIntoViewNew(element, spot, scrollMatches = false) {
  let parent = element.parent
  if (!parent) {
    console.error('parent is not set -- cannot scroll')
    return
  }
  let offsetY = element.offsetTop + element.clientTop
  let offsetX = element.offsetLeft + element.clientLeft
  while (
    (parent.clientHeight === parent.scrollHeight && parent.clientWidth === parent.scrollWidth) ||
    (scrollMatches &&
      (parent.classList.contains('markedContent') ||
        getComputedStyle(parent).overflow === 'hidden'))
  ) {
    offsetY += parent.offsetTop
    offsetX += parent.offsetLeft
    parent = parent.parent
    if (!parent) {
      return
    }
  }
  if (spot) {
    if (spot.top !== undefined) {
      offsetY += spot.top
    }
    if (spot.left !== undefined) {
      offsetX += spot.left
      parent.scrollLeft = offsetX
    }
  }
  parent.scrollTop = offsetY
}
export function scrollIntoView(element, spot, scrollMatches = false) {
  let parent = element.offsetParent
  if (!parent) {
    console.error('offsetParent is not set -- cannot scroll')
    return
  }
  let offsetY = element.offsetTop + element.clientTop
  let offsetX = element.offsetLeft + element.clientLeft
  while (
    (parent.clientHeight === parent.scrollHeight && parent.clientWidth === parent.scrollWidth) ||
    (scrollMatches &&
      (parent.classList.contains('markedContent') ||
        getComputedStyle(parent).overflow === 'hidden'))
  ) {
    offsetY += parent.offsetTop
    offsetX += parent.offsetLeft
    parent = parent.offsetParent
    if (!parent) {
      return
    }
  }
  if (spot) {
    if (spot.top !== undefined) {
      offsetY += spot.top
    }
    if (spot.left !== undefined) {
      offsetX += spot.left
      parent.scrollLeft = offsetX
    }
  }
  parent.scrollTop = offsetY
}
function isValidRotation(angle) {
  return Number.isInteger(angle) && angle % 90 === 0
}

function getVisibleElements({
  scrollEl,
  views,
  sortByVisibility = false,
  horizontal = false,
  rtl = false,
}) {
  const top = scrollEl.scrollTop,
    bottom = top + scrollEl.clientHeight
  const left = scrollEl.scrollLeft,
    right = left + scrollEl.clientWidth
  function isElementBottomAfterViewTop(view) {
    const element = view.div
    const elementBottom = element.offsetTop + element.clientTop + element.clientHeight
    return elementBottom > top
  }
  function isElementNextAfterViewHorizontally(view) {
    const element = view.div
    const elementLeft = element.offsetLeft + element.clientLeft
    const elementRight = elementLeft + element.clientWidth
    return rtl ? elementLeft < right : elementRight > left
  }
  const visible = [],
    ids = new Set(),
    numViews = views.length
  let firstVisibleElementInd = binarySearchFirstItem(
    views,
    horizontal ? isElementNextAfterViewHorizontally : isElementBottomAfterViewTop
  )
  if (firstVisibleElementInd > 0 && firstVisibleElementInd < numViews && !horizontal) {
    firstVisibleElementInd = backtrackBeforeAllVisibleElements(firstVisibleElementInd, views, top)
  }
  let lastEdge = horizontal ? right : -1
  for (let i = firstVisibleElementInd; i < numViews; i++) {
    const view = views[i],
      element = view.div
    const currentWidth = element.offsetLeft + element.clientLeft
    const currentHeight = element.offsetTop + element.clientTop
    const viewWidth = element.clientWidth,
      viewHeight = element.clientHeight
    const viewRight = currentWidth + viewWidth
    const viewBottom = currentHeight + viewHeight
    if (lastEdge === -1) {
      if (viewBottom >= bottom) {
        lastEdge = viewBottom
      }
    } else if ((horizontal ? currentWidth : currentHeight) > lastEdge) {
      break
    }
    if (
      viewBottom <= top ||
      currentHeight >= bottom ||
      viewRight <= left ||
      currentWidth >= right
    ) {
      continue
    }
    const hiddenHeight = Math.max(0, top - currentHeight) + Math.max(0, viewBottom - bottom)
    const hiddenWidth = Math.max(0, left - currentWidth) + Math.max(0, viewRight - right)
    const fractionHeight = (viewHeight - hiddenHeight) / viewHeight,
      fractionWidth = (viewWidth - hiddenWidth) / viewWidth
    const percent = (fractionHeight * fractionWidth * 100) | 0
    visible.push({
      id: view.id,
      x: currentWidth,
      y: currentHeight,
      view,
      percent,
      widthPercent: (fractionWidth * 100) | 0,
    })
    ids.add(view.id)
  }
  const first = visible[0],
    last = visible.at(-1)
  if (sortByVisibility) {
    visible.sort(function (a, b) {
      const pc = a.percent - b.percent
      if (Math.abs(pc) > 0.001) {
        return -pc
      }
      return a.id - b.id
    })
  }
  return {
    first,
    last,
    views: visible,
    ids,
  }
}
