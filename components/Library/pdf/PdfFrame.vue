<template>
    <div class="knowledge-pdf-preview">
        <div v-if="loading" class="knowledge-pdf-preview-loading"><a-spin /></div>
        <iframe id="iframe_pdf" :src="webSiteUrl" class="knowledge-pdf-preview-iframe" @load="onIframeLoad"></iframe>
    </div>
</template>
<script lang="ts" setup>
const { $eventBus } = useNuxtApp();
import { StarloveConstants } from '@/utils/starloveConstants';
import { computed, onMounted, onUnmounted, ref } from 'vue';

interface Props {
    pdfUrl?: string
    pageNum?: number
    meta?: string
}
const props = withDefaults(defineProps<Props>(), {
    pdfUrl: '',
    pageNum: 0,
    meta: ''
})
const webSiteUrl = computed(() => {
    const pageName = '/pdf-preview'
    if (props.meta) {
        return `${pageName}?pdfUrl=${encodeURIComponent(props.pdfUrl)}&meta=${props.meta}`
    }
    if (props.pageNum) {
        return `${pageName}?pdfUrl=${encodeURIComponent(props.pdfUrl)}&pageNum=${props.pageNum}`
    }

    return `${pageName}?pdfUrl=${encodeURIComponent(props.pdfUrl)}`
})
// 定义加载状态
const loading = ref(true)
const onIframeLoad = () => {
    loading.value = false // 加载完成后，改变 loading 状态为 false
}
let frameWindow: any;
function sendMessage(command: string, value = {}) {
    if (!frameWindow) {
        const dd = document.getElementById('iframe_pdf') as any
        if (dd) {
            frameWindow = dd.contentWindow
        }
    }

    if (!frameWindow) {
        return
    }

    frameWindow.postMessage(
        {
            command,
            value
        },
        '*'
    )
}

const onMessage = (event: any) => {
    if (event.data.type === 'pdf-preview-page-change') {
        $eventBus.emit(StarloveConstants.keyOfEventBus.pdfPreviewPageChange, event.data.value)
    }
}

onMounted(() => {
    $eventBus.on(StarloveConstants.keyOfEventBus.beUpdateKnowledgeFilePage, (options) => {
        if (options.meta.bboxs) {
            sendMessage('renderHightLightQuestion', options)
        } else {
            sendMessage('changePageNum', { pageNum: options.meta.page })
        }
    })
    $eventBus.on(
        StarloveConstants.keyOfEventBus.highLightTranslate,
        ({ bbox, page, sourceContent }) => {
            sendMessage('highLightTranslate', { bbox: { ...bbox }, page, sourceContent })
        }
    )
    window.addEventListener('message', onMessage)
})

onUnmounted(() => {
    $eventBus.off(StarloveConstants.keyOfEventBus.beUpdateKnowledgeFilePage)
    $eventBus.off(StarloveConstants.keyOfEventBus.highLightTranslate)
    window.removeEventListener('message', onMessage)
})
</script>
<style lang="scss" scoped>
.knowledge-pdf-preview {
    position: relative;
    overflow: hidden;
    border: none;
    width: 100%;
    height: 100%;

    &-iframe {
        width: 100%;
        height: 100%;
        overflow: auto;
        border: none;
    }

    &-loading {
        position: absolute;
        left: 50%;
        top: 50px;
        transform: translateX(-50%);
    }
}
</style>