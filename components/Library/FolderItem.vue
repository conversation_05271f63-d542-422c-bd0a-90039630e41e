<template>
    <div class="pb-7 border-b border-[#D0DCFA] justify-center sm:justify-start space-y-2">
        <div v-for="folder in folders" :key="folder.id"
            class="w-full py-2 sm:py-4 sm:px-2 rounded-[8px] cursor-pointer bg-[#FFFFFF] hover:bg-[#EAF0FF] group/item transition-all duration-200"
            @click="handleFolderClick(folder)">

            <div class="flex-1 flex flex-row items-center justify-between mx-2">
                <div class="flex items-center space-x-2">
                    <!-- <img src="https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/library/large-folders.png"
                        class="w-6 h-6 sm:w-8 sm:h-8" alt="folder"> -->
                    <Iconfont name="folder" :size="28"></Iconfont>
                    <h3 class="text-xs sm:text-sm text-[#333333] font-medium text-center truncate mt-1">
                        {{ folder.folderName || folder.name }}
                    </h3>
                </div>

                <div>
                    <a-popover trigger="hover">
                        <template #content>
                            <div class="flex flex-col gap-2 items-center">
                                <button
                                    class="box-border inline-flex px-2 py-1 text-sm text-[#333333] font-normal rounded-md hover:bg-blue-50 hover:text-blue-700 transition-colors"
                                    @click="handleRenameClick(folder)">
                                    <span class="inline-block text-[#333333]">重命名</span>
                                </button>
                                <button
                                    class="box-border inline-flex px-2 py-1 text-sm text-[#333333] font-normal rounded-md hover:bg-blue-50 hover:text-blue-700 transition-colors"
                                    @click="handleMoveClick(folder)">
                                    <span class="inline-block text-[#333333]">移动</span>
                                </button>

                                <a-popconfirm @confirm="handleDeleteClick(folder.id)" title="删除文件不会减少已使用空间，是否确认删除?"
                                    ok-text="确定" cancel-text="取消">
                                    <button
                                        class="box-border inline-flex px-2 py-1 text-sm rounded-md hover:bg-red-50 text-gray-600 transition-colors ">
                                        删除
                                    </button>
                                </a-popconfirm>
                            </div>
                        </template>
                        <button @click.stop>
                            <more theme="outline" size="24" fill="#999999" />
                            <!-- <img src="https://static-1256600262.cos.ap-shanghai.myqcloud.com/xiaoin-h5/image/more-icon.png"
                                class="w-4 h-4"> -->
                        </button>
                    </a-popover>
                </div>
            </div>
        </div>

        <!-- 重命名和移动文件的弹窗 -->
        <RenameFolderModal v-if="showRenameModal" v-model="showRenameModal" :item="currentRenameItem"
            @confirm="handleRenameConfirm" type="folder" />
    </div>
</template>

<script setup lang="ts">
import { deleteFolder, updateFolder } from '@/api/repositoryFile';
import RenameFolderModal from '@/components/Library/RenameFolderModal.vue';
import type { RepositoryFolder } from '@/services/types/repositoryFile';
import { More } from "@icon-park/vue-next";
import { message } from 'ant-design-vue';
import { computed, ref } from 'vue';
import { useUserStore } from '~/stores/user';

const props = defineProps<{
    folders: RepositoryFolder[]
}>()

const emit = defineEmits<{
    (e: 'rename'): void
    (e: 'move', folder: RepositoryFolder, type: 'folder'): void
    (e: 'select', folder: RepositoryFolder): void
    (e: 'delete', id: string): void
}>()



const user = useUserStore()

const spaceId = computed(() => {
    return user.currentLoginInfo?.id || ''
})

// 移动文件夹
const handleMoveClick = (folder: RepositoryFolder) => {
    emit('move', folder, 'folder')
}

// 删除文件夹
const handleDeleteClick = async (id: string) => {
    // console.log('删除文件夹：', id)

    const params = {
        folderId: id
    }

    const res = await deleteFolder(spaceId.value, params)
    if (!res.ok) {
        // message.error(res.message || '删除失败')
        return
    }
    message.success(res.message || '删除成功')
    emit('delete', id)
}

// 重命名-------------------------------------------------
const showRenameModal = ref(false)
const currentRenameItem = ref<any>(null)


const handleRenameClick = (item: RepositoryFolder) => {
    currentRenameItem.value = item
    showRenameModal.value = true
}

const handleRenameConfirm = async (item: { id: number, filename: string }) => {

    const params = {
        folderId: item.id.toString(),
        folderName: item.filename
    }
    const res = await updateFolder(spaceId.value, params)
    if (!res.ok) {
        // message.error(res.message || '修改失败')
        return
    }
    message.success(res.message || '修改成功')
    emit('rename')
}

// 文件夹点击处理
const handleFolderClick = (folder: RepositoryFolder) => {
    emit('select', folder)
}
</script>