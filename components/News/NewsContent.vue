<template>
  <div class="knowledge-website-preview">
    <div class="knowledge-website-preview-title">{{ content?.meta?.title || '' }}</div>
    <div class="knowledge-website-preview-content">
      <div class="knowledge-website-preview-original-link">
        原文链接：
        <a :href="content?.meta?.url" target="_blank">{{ content?.meta?.url || '' }}</a>
      </div>
      <div class="knowledge-website-preview-content1">
        <div v-html="getNewsContent" class="h-full"></div>
      </div>
    </div>
  </div>
</template>


<script lang="ts" setup>
import markdownItKatex from '@vscode/markdown-it-katex'
import hljs from 'highlight.js/lib/core'
import 'highlight.js/styles/ir-black.css'
import 'katex/dist/katex.min.css'
import markdownit from 'markdown-it'
import markdownItContainer from 'markdown-it-container'
import markdownItHighlightjs from 'markdown-it-highlightjs'
import { computed } from 'vue'


interface ContentMeta {
  url: string;
  title: string;
  description: string;
}

interface Article {
  content: string;
  meta: ContentMeta;
}



interface Props {
  content?: Article
}

const props = withDefaults(defineProps<Props>(), {})


const addNoReferrerMeta = () => {
  // 创建一个新的meta元素
  const meta = document.createElement('meta')

  // 设置meta元素的name和content属性
  meta.name = 'referrer'
  meta.content = 'never'

  // 获取head元素
  const head = document.getElementsByTagName('head')[0]

  // 将新的meta元素添加到head中
  head.appendChild(meta)
}
const insertMetaTags = () => {
  // <meta name="referrer" content="never">
  // 使用 querySelectorAll 和 Array.prototype.some 方法
  const metaTags = document.querySelectorAll('meta[name="referrer"]')
  console.log('metaTags', metaTags)
  const hasNeverReferrer = Array.from(metaTags).some((tag: any) => tag.content === 'never')

  if (!hasNeverReferrer) {
    addNoReferrerMeta()
  }
}




const md = markdownit({
  html: true,
  linkify: true,
  breaks: true,
  xhtmlOut: true,
  langPrefix: 'language-',
  typographer: true,
  highlight: function (str, lang) {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return '<pre class="hljs"><code>' + hljs.highlight(lang, str, true).value + '</code></pre>'
      } catch (__) { }
    }
    return '<pre class="hljs"><code>' + md.utils.escapeHtml(str) + '</code></pre>'
  }
})
  .use(markdownItKatex)
  .use(markdownItContainer, 'spoiler', {
    validate: function (params) {
      return params.trim().match(/^spoiler\s+(.*)$/)
    },
    render: function (tokens, idx) {
      var m = tokens[idx].info.trim().match(/^spoiler\s+(.*)$/)
      if (tokens[idx].nesting === 1) {
        return '<details><summary>' + md.utils.escapeHtml(m[1]) + '</summary>\n'
      } else {
        return '</details>\n'
      }
    }
  })
  .use(markdownItHighlightjs)

function markdownItPluginAddTarget(md) {
  const defaultRender = md.renderer.rules.link_open || function (tokens, idx, options, env, self) {
    return self.renderToken(tokens, idx, options)
  }

  md.renderer.rules.link_open = function (tokens, idx, options, env, self) {
    const aIndex = tokens[idx].attrIndex('target')
    if (aIndex < 0) {
      tokens[idx].attrPush(['target', '_blank']) // Add target="_blank"
    }
    return defaultRender(tokens, idx, options, env, self)
  }
}

md.use(markdownItPluginAddTarget)

const getNewsContent = computed(() => {
  let _content = props.content?.content || ''  // Ensure _content is a string

  // console.log(_content)

  if (_content) {
    const regex = /```markdown\n([\s\S]*?)\n```/
    const match = _content.match(regex)
    if (match && match[1]) {
      _content = match[1]
    }

    const extractCodeBlock = /```\n([\s\S]*?)\n```/
    const codeBlockMatch = _content.match(extractCodeBlock)
    if (codeBlockMatch && codeBlockMatch[1]) {
      _content = codeBlockMatch[1]
    }
    _content = processContent(_content)

    return md.render(_content)
  }
  return ''
})

const processContent = (content: string) => {
  return content
    .replace(/\\n/g, '\n')
    .replace(/\\"/g, '"')
    .replace(/\\'/g, "'")
    .replace(/\\\\/g, '\\')
    .replace(/\\\{/g, '${')
    .replace(/\\\}/g, '}$')
    .replace(/\\\[/g, '$[')
    .replace(/\\\]/g, ']$')
    .replace(/\\\(/g, '$(')
    .replace(/\\\)/g, ')$')
}


onMounted(() => {
  // insertMetaTags()
})



</script>

<style lang="scss" scoped>
.knowledge-website-preview {
  position: relative;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 95px);
  border: none;

  &-original-link {
    color: rgb(153, 153, 153);
    font-size: 14px;
  }

  &-title {
    padding: 15px 10px;
    text-align: center;
    font-weight: 800;
    font-size: 16px;
    line-height: 25px;
  }

  &-content {
    flex: 1;
    padding: 10px 30px;
    font-weight: 400;
    font-size: 16px;
    color: #555555;
    line-height: 31px;
  }

  &-content1 {
    width: 100%;
    height: 100%;
  }



}
</style>
