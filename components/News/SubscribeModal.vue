<template>
    <common-modal :model-value="modelValue" title="个性化订阅" subtitle="选择您喜欢的推荐方式，获取感兴趣的知识资讯" :width="900" :maxHeight="600"
        @confirm="saveSubscription" @cancel="handleCancel">
        <div class="p-6">
            <!-- 加载状态 -->
            <div v-if="typeLoading" class="flex justify-center items-center py-20">
                <div class="text-gray-500">正在加载...</div>
            </div>

            <template v-else>
                <!-- 基于知识库推荐 -->
                <div class="mb-6">
                    <div @click="selectSubscriptionType('knowledge')"
                        class="flex items-center p-4 rounded-xl border cursor-pointer transition-all duration-200"
                        :class="[subscriptionType === 'knowledge' ? 'border-blue-700 bg-blue-50' : 'border-gray-200 hover:border-blue-700']">
                        <book-one theme="outline" size="24"
                            :fill="subscriptionType === 'knowledge' ? '#3B82F6' : '#666'" class="mr-3" />
                        <div>
                            <div class="font-medium"
                                :class="{ 'text-blue-700': subscriptionType === 'knowledge', 'text-gray-700': subscriptionType !== 'knowledge' }">
                                基于知识库推荐
                            </div>
                            <div class="text-sm text-gray-500 mt-1">
                                小in将依据你的知识库内容，向你推荐你可能感兴趣的知识资讯
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 自定义兴趣标签 -->
                <div>
                    <div @click="selectSubscriptionType('tags')"
                        class="flex items-center p-4 rounded-xl border cursor-pointer transition-all duration-200 mb-4"
                        :class="[subscriptionType === 'tags' ? 'border-blue-700 bg-blue-50' : 'border-gray-200 hover:border-blue-700']">
                        <tag-one theme="outline" size="24" :fill="subscriptionType === 'tags' ? '#3B82F6' : '#666'"
                            class="mr-3" />
                        <div>
                            <div class="font-medium"
                                :class="{ 'text-blue-700': subscriptionType === 'tags', 'text-gray-700': subscriptionType !== 'tags' }">
                                自定义兴趣标签
                            </div>
                            <div class="text-sm text-gray-500 mt-1">
                                小in将依据你设置的兴趣标签，向你推荐满足这些标签的知识资讯
                            </div>
                        </div>
                    </div>

                    <!-- 标签选择区域 -->
                    <div v-if="subscriptionType === 'tags'">
                        <!-- 加载中状态 -->
                        <div v-if="isLoading" class="text-gray-500 text-center col-span-5">
                            加载中...
                        </div>

                        <!-- 标签展示 -->
                        <div v-else class="grid grid-cols-5 gap-4 mt-4">
                            <div v-if="loading" class="text-gray-500 text-center col-span-5">加载中...</div>
                            <div v-else-if="subscriptionTags.length === 0" class="text-gray-500 text-center col-span-5">
                                未找到标签数据
                            </div>
                            <div v-else v-for="tag in subscriptionTags" :key="tag.id" @click="toggleTag(tag.id)"
                                class="flex justify-center items-center p-3 rounded-xl border cursor-pointer transition-all duration-200"
                                :class="[selectedTags.includes(tag.id) ? 'border-blue-700 bg-blue-50' : 'border-gray-200 hover:border-blue-700']">
                                <span class="text-center"
                                    :class="{ 'text-blue-700': selectedTags.includes(tag.id), 'text-gray-600': !selectedTags.includes(tag.id) }">
                                    {{ tag.name }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        </div>
    </common-modal>
</template>

<script setup lang="ts">
import { getMyTagList, getTagList, getTagType, newsUserTagAdd, newsUserTagRemove, setTagType } from '@/api/repositoryFile';
import { BookOne, TagOne } from '@icon-park/vue-next';
import { onMounted, ref } from 'vue';
import CommonModal from '../Common/Modal.vue';

const props = defineProps({
    modelValue: Boolean,
});


const emit = defineEmits(['save', 'cancel']);

// 状态变量
const selectedTags = ref<number[]>([]); // 选中的标签 ID 列表
const subscriptionType = ref<'knowledge' | 'tags' | null>(null);
const originalSubscriptionType = ref<'knowledge' | 'tags' | null>(null);
const subscriptionTags = ref<{ id: number; name: string }[]>([]); // 所有标签
const userSubscribedTags = ref<number[]>([]); // 用户已关注的标签
const loading = ref(false); // 数据加载状态
const isLoading = ref(false); // 自定义标签加载状态
const typeLoading = ref(true); // 订阅类型加载状态

// 更新订阅类型并加载已关注的标签
const selectSubscriptionType = async (type: 'knowledge' | 'tags') => {
    // 如果当前选择的订阅类型与点击的类型相同，直接返回，不做接口请求
    if (subscriptionType.value === type) {
        return;
    }

    subscriptionType.value = type;
    if (type === 'tags') {
        isLoading.value = true; // 显示加载中状态
        try {
            await fetchMyTags();
            setSelectedTags();
        } finally {
            isLoading.value = false; // 结束加载
        }
    }
};

// 获取所有标签
const fetchAllTags = async () => {
    loading.value = true;
    try {
        const result = await getTagList();
        // 截取前9个标签
        subscriptionTags.value = result.ok && result.data ? result.data : [];
    } catch (error) {
        console.error('获取标签失败', error);
        subscriptionTags.value = [];
    } finally {
        loading.value = false;
    }
};

// 获取用户已关注的标签
const fetchMyTags = async () => {
    try {
        const result = await getMyTagList();
        // 截取前9个已关注标签
        userSubscribedTags.value = result.ok && result.data ? result.data.map(tag => tag.id) : [];
    } catch (error) {
        console.error('获取我的标签失败', error);
        userSubscribedTags.value = [];
    }
};





// 设置默认选中的标签
const setSelectedTags = () => {
    selectedTags.value = [...userSubscribedTags.value];
};

// 切换标签选中状态
const toggleTag = (tagId: number) => {
    const index = selectedTags.value.indexOf(tagId);
    if (index === -1) {
        selectedTags.value.push(tagId);
    } else {
        selectedTags.value.splice(index, 1);
    }
};

// 保存订阅设置
const saveSubscription = async () => {
    try {
        // 无论什么类型都需要更新订阅类型
        const params = { type: subscriptionType.value === 'knowledge' ? 1 : 2 };
        await setUserTagType(params);

        // 如果是标签类型，还需要处理标签的添加和删除
        if (subscriptionType.value === 'tags') {
            const tagsToAdd = selectedTags.value.filter(tag => !userSubscribedTags.value.includes(tag));
            const tagsToDelete = userSubscribedTags.value.filter(tag => !selectedTags.value.includes(tag));

            if (tagsToDelete.length > 0) {
                await deleteTags(tagsToDelete.map(String));
            }
            if (tagsToAdd.length > 0) {
                await addTags(tagsToAdd.map(String));
            }
        }

        console.log('订阅设置保存成功');
    } catch (error) {
        console.error('保存订阅设置失败', error);
    }

    emit('save', {
        type: subscriptionType.value,
        tags: subscriptionType.value === 'tags' ? selectedTags.value : []
    });
};


const setUserTagType = async (params: { type: number }) => {
    // 改变类型
    const result = await setTagType(params)
    if (!result.ok) {
        return
    }
    console.log(result)
}

// 添加标签
const addTags = async (tagIds: string[]): Promise<void> => {
    const params = { tagIds };
    console.log(params)
    try {
        const result = await newsUserTagAdd(params);
        if (!result.ok) {
            console.error('添加标签失败', result);
        }
    } catch (error) {
        console.error('添加标签失败', error);
    }
};

// 删除标签
const deleteTags = async (tagIds: string[]): Promise<void> => {
    const params = { tagIds };
    console.log(params)
    try {
        const result = await newsUserTagRemove(params);
        if (!result.ok) {
            console.error('删除标签失败', result);
        }
    } catch (error) {
        console.error('删除标签失败', error);
    }
};

// 取消操作，恢复默认标签状态
const handleCancel = async () => {
    subscriptionType.value = originalSubscriptionType.value;  // 恢复到原始订阅类型
    await fetchMyTags();  // 重新获取用户的订阅标签
    setSelectedTags();  // 恢复选中的标签
    emit('cancel');
};

// 获取标签类型
const getSubscriptionType = async () => {
    typeLoading.value = true;
    try {
        const res = await getTagType();
        if (!res.ok) {
            return;
        }
        const data = res.data;
        const newType = data === 0 || data === 1 ? 'knowledge' : 'tags';
        subscriptionType.value = newType;
        originalSubscriptionType.value = newType;
    } catch (error) {
        console.error('获取标签类型失败', error);
    } finally {
        typeLoading.value = false;
    }
};

// 初始化数据
const initData = async () => {
    loading.value = true;
    try {
        // 1. 获取标签类型
        await getSubscriptionType();
        // 2. 获取全部标签
        await fetchAllTags();
        // 3. 获取用户已关注的标签
        await fetchMyTags();
        // 4. 设置选中的标签
        setSelectedTags();
    } catch (error) {
        console.error('初始化数据失败', error);
    } finally {
        loading.value = false;
    }
};

onMounted(() => {
    initData();
});
</script>
