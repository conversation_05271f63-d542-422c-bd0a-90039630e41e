<template>
  <div class="w-full">
    <div class="flex flex-wrap gap-6">
      <div v-for="(item, index) in contentArr" :key="index" :class="[
        'rounded-lg',
        contentArr.length === 1 ? 'w-full' : 'w-full md:w-[calc(50%-12px)]'
      ]">
        <div class="flex flex-col h-full">
          <div class="flex items-center mb-4">
            <span class="inline-flex items-center px-2 bg-blue-100 text-gray-700 text-sm rounded-full ">
              {{ item.title }}
            </span>
          </div>
          <div v-html="getNewsContent(item)"
            class="text-gray-600 text-sm leading-relaxed flex-grow overflow-hidden line-clamp-6">
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import markdownItKatex from '@vscode/markdown-it-katex'
import hljs from 'highlight.js/lib/core'
import 'highlight.js/styles/ir-black.css'
import 'katex/dist/katex.min.css'
import markdownit from 'markdown-it'
import markdownItContainer from 'markdown-it-container'
import markdownItHighlightjs from 'markdown-it-highlightjs'
import { computed } from 'vue'

interface Props {
  content?: string
}

const props = withDefaults(defineProps<Props>(), {
  content: ''
})

const contentArr = computed(() => {
  if (!props.content) {
    return []
  }
  try {
    return JSON.parse(props.content)
  } catch (e) {
    return []
  }
})

const md = markdownit({
  html: true, // 启用 HTML 标签
  linkify: true, // 自动将 URL 转换为链接
  breaks: true,
  xhtmlOut: true,
  langPrefix: 'language-',
  typographer: true,
  highlight: function (str, lang) {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return '<pre class="hljs"><code>' + hljs.highlight(lang, str, true).value + '</code></pre>'
      } catch (__) { }
    }
    return '<pre class="hljs"><code>' + md.utils.escapeHtml(str) + '</code></pre>'
  }
})
  .use(markdownItKatex)
  .use(markdownItContainer, 'spoiler', {
    validate: function (params) {
      return params.trim().match(/^spoiler\s+(.*)$/)
    },
    render: function (tokens, idx) {
      var m = tokens[idx].info.trim().match(/^spoiler\s+(.*)$/)
      if (tokens[idx].nesting === 1) {
        return '<details><summary>' + md.utils.escapeHtml(m[1]) + '</summary>\n'
      } else {
        return '</details>\n'
      }
    }
  })
  .use(markdownItHighlightjs)

function markdownItPluginAddTarget(md) {
  const defaultRender = md.renderer.rules.link_open || function (tokens, idx, options, env, self) {
    return self.renderToken(tokens, idx, options)
  }

  md.renderer.rules.link_open = function (tokens, idx, options, env, self) {
    const aIndex = tokens[idx].attrIndex('target')
    if (aIndex < 0) {
      tokens[idx].attrPush(['target', '_blank']) // 添加 target 属性
    }
    return defaultRender(tokens, idx, options, env, self)
  }
}

md.use(markdownItPluginAddTarget)

const getNewsContent = computed(() => (item: any) => {
  let _content = item.content || ''  // 确保 _content 为字符串
  if (_content) {
    const regex = /```markdown\n([\s\S]*?)\n```/
    const match = _content.match(regex)
    if (match && match[1]) {
      _content = match[1]
    }

    // 定义正则表达式，用于匹配不带语言标记的代码块
    const extractCodeBlock = /```\n([\s\S]*?)\n```/
    const codeBlockMatch = _content.match(extractCodeBlock)
    if (codeBlockMatch && codeBlockMatch[1]) {
      _content = codeBlockMatch[1]
    }
    _content = processContent(_content)
    return md.render(_content)
  }
  return ''
})

const processContent = (content: string) => {
  return content
    .replace(/\\n/g, '\n') // 将 \n 转换为实际的换行符
    .replace(/\\"/g, '"') // 处理引号的转义
    .replace(/\\'/g, "'") // 处理单引号的转义
    .replace(/\\\\/g, '\\') // 处理反斜杠的转义
    .replace(/\\\{/g, '${')
    .replace(/\\\}/g, '}$')
    .replace(/\\\[/g, '$[')
    .replace(/\\\]/g, ']$')
    .replace(/\\\(/g, '$(')
    .replace(/\\\)/g, ')$')
}
</script>
