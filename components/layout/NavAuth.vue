<template>
  <div class="w-full">
    <!-- 未登录状态 -->
    <button v-if="!isLoggedIn" @click="handlePressLogin" class="w-full">
      <div
        class="text-white flex items-center justify-center bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 px-4 py-2 rounded-lg hover:shadow-md">
        登录
      </div>
    </button>

    <!-- 已登录状态 -->
    <div v-else>
      <!-- 白色卡片区域 -->
      <!-- <div class="bg-purple-200 rounded-lg p-3 space-y-3"> -->
      <!-- 第一行：个人信息 -->
      <div class="flex items-center justify-between cursor-pointer" @click="handlePressProfile">
        <div class="flex items-center space-x-2">
          <UserAvatar />
          <!-- <img :src="avatar || '/default-avatar.png'" class="w-8 h-8 rounded-full object-cover" alt="avatar" /> -->
          <span class="text-sm text-gray-700">{{ nickname || '用户昵称' }}</span>
        </div>
        <Right theme="outline" size="16" class="text-gray-400" />
      </div>

      <!-- 第二行：会员信息 -->
      <!-- <UserInfoArea /> -->

      <!-- 第三行：硬币信息 -->
      <!-- <div class="flex items-center justify-between space-x-2">
          <div class="flex items-center bg-white border border-blue-100 p-2 rounded-lg space-x-1">
            <Funds theme="outline" size="20" class="text-yellow-500" />
            <span class="text-sm text-blue-600">{{ coins }}</span>
          </div>
          <button @click="handlePressRecharge"
            class="px-2 py-1 text-sm text-white bg-gradient-to-r from-orange-500 to-red-600 rounded-lg hover:from-orange-600 hover:to-red-700 border border-red-400 hover:border-red-300 transition-all duration-300">
            充值
          </button>
        </div> -->
      <!-- </div> -->
    </div>
  </div>
</template>

<script setup>
import UserAvatar from '@/components/Auth/UserAvatar.vue';
import { Right } from '@icon-park/vue-next';
import { useRouter } from 'vue-router';

const router = useRouter();

defineProps({
  isLoggedIn: {
    type: Boolean,
    default: false,
  },
  coins: {
    type: Number,
    default: 0,
  },
  avatar: {
    type: String,
    default: '',
  },
  nickname: {
    type: String,
    default: '',
  },
  vipLevel: {
    type: String,
    default: '',
  },
});

defineEmits(['show-recharge']);

const handlePressLogin = () => {
  const userStore = useUserStore();
  userStore.openLoginModal();
};

// const handlePressRecharge = () => {
//   const rechargeStore = useRechargeStore();
//   rechargeStore.openRechargeModal();
// };

const handlePressProfile = () => {
  router.push('/profile');
};

const handlePressVip = () => {
  router.push('/vip');
};
</script>
