<template>
    <!-- 导航链接容器 -->
    <NuxtLink :to="to" class="relative group block cursor-pointer">
        <!-- 背景光晕效果 -->
        <div class="absolute -inset-0.5 bg-gradient-to-r from-blue-500/20 to-blue-500/20 rounded-lg blur-sm"
            :class="{ 'opacity-100': isActive, 'opacity-0': !isActive }">
        </div>

        <!-- 导航项主体内容 -->
        <div class="relative flex items-center rounded-lg px-4 py-3" :class="[
            isActive
                ? 'bg-gradient-to-r from-blue-50 to-blue-50 border border-blue-200/50'
                : 'hover:bg-gradient-to-r hover:from-blue-50/50 hover:to-blue-100/50  border border-transparent'
        ]">
            <!-- 图标容器 -->
            <div class="w-7 h-7 rounded-lg flex items-center justify-center mr-3">
                <component :is="icon" size="20" :fill="isActive ? '#2563eb' : '#64748b'" />
            </div>

            <!-- 导航标题文本 -->
            <span class="text-base" :class="[
                isActive
                    ? 'text-blue-800'
                    : 'text-gray-600 group-hover:text-blue-600'
            ]">{{ title }}</span>

            <!-- 热门标签 (可选) -->
            <div v-if="hot" class="absolute right-3 flex items-center">
                <div class="relative">
                    <!-- 热门标签的光晕效果 -->
                    <!-- <div
                        class="absolute -inset-0.5 bg-gradient-to-r from-orange-500 to-red-500 rounded-full blur-[2px] opacity-40 animate-pulse">
                    </div> -->
                    <!-- 热门标签图标 -->
                    <div class="relative  text-white p-0.5 rounded-full flex items-center justify-center">
                        <img src="https://static-1256600262.cos.ap-shanghai.myqcloud.com/xiaoin-sy/create-hot.png"
                            alt="hot" class="w-5 h-5">
                    </div>
                </div>
            </div>
        </div>
    </NuxtLink>
</template>

<script setup>
import { markRaw } from 'vue';

// 组件属性定义
const props = defineProps({
    to: {
        type: String,
        required: true
    },
    title: {
        type: String,
        required: true
    },
    icon: {
        type: Object,
        required: true,
        // 添加自定义转换器来确保 icon 不会变成响应式
        transform: (icon) => markRaw(icon)
    },
    isActive: {
        type: Boolean,
        required: true
    },
    hot: {
        type: Boolean,
        default: false
    }
})
</script>