<template>
    <div class="w-full h-full flex flex-col items-center justify-center">
        <div class="w-20 h-20 mx-auto mb-4 bg-gray-50 rounded-full flex items-center justify-center">
            <loading theme="outline" size="40" class="text-gray-400 animate-spin" />
        </div>
        <h3 class="text-lg font-medium text-gray-600 mb-2 text-[13px]">加载中...</h3>
    </div>
</template>
<script setup lang="ts">
import { Loading } from '@icon-park/vue-next'
</script>