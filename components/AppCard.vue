<template>
  <NuxtLink :to="to" :title="altFn(code, title)" class="block bg-white/80 backdrop-blur-sm rounded-xl p-3 sm:p-4 border hover:border-blue-300/50 
           hover:shadow-lg transition-all duration-200 group cursor-pointer border-blue-200/50">
    <!-- 垂直模式-->
    <div v-if="layout === 'vertical'" class="h-full flex flex-col items-center justify-center gap-3">
      <div class="w-[70px]  aspect-square rounded-lg 
                  flex items-center justify-center shrink-0 overflow-hidden 
                 ">
        <img :src="icon" :alt="altFn(code, title)" class="w-full h-full object-cover" />
      </div>

      <h3 class="text-lg text-gray-800 truncate group-hover:text-blue-600 transition-colors">
        {{ title }}
      </h3>

      <p class="text-sm text-gray-500 line-clamp-2 text-center">{{ desc }}</p>


      <!-- 特性区域 -->
      <!-- <div v-if="showFeatures" class="mt-4 flex-1 grid-cols-1 sm:grid-cols-3 gap-4 hidden xl:grid">
        <div class="col-span-1 sm:col-span-3">
          <ul class="text-sm text-gray-600 space-y-3">
            <li class="flex items-center">
              <check-one theme="filled" size="16" fill="#10b981" class="mr-2" />
              摘要、目录、正文、文献
            </li>
            <li class="flex items-center">
              <check-one theme="filled" size="16" fill="#10b981" class="mr-2" />
              知网查重率超20%免费重写
            </li>
            <li class="flex items-center">
              <check-one theme="filled" size="16" fill="#10b981" class="mr-2" />
              指定大纲支持三级大纲输入
            </li>
            <li class="flex items-center">
              <check-one theme="filled" size="16" fill="#10b981" class="mr-2" />
              优化语言,提升论文质量
            </li>
          </ul>
        </div>
      </div> -->
    </div>

    <!-- 水平模式 -->
    <div v-else class="block sm:flex sm:items-start sm:gap-3 py-4">
      <div class="w-[50px] mx-auto aspect-square rounded-lg 
                  flex items-center justify-center shrink-0 overflow-hidden 
                  transition-transform group-hover:scale-105">
        <img :src="icon" :alt="altFn(code, title)" class="w-full h-full object-cover" />
      </div>

      <div class="flex-1 min-w-0 text-center sm:text-left mt-2 sm:mt-0">
        <h3 class="text-base mb-1 text-gray-800 truncate group-hover:text-blue-600 transition-colors">
          {{ title }}
        </h3>
        <p class="text-xs text-gray-500 line-clamp-2 mt-2">{{ desc }}</p>
      </div>
    </div>

  </NuxtLink>
</template>

<script setup lang="ts">


const props = defineProps({
  icon: {
    type: String,
    default: 'https://static-1256600262.file.myqcloud.com/xzkc/icon/course-paper.png'
  },
  title: {
    type: String,
    required: true
  },
  desc: {
    type: String,
    default: ''
  },
  to: {
    type: String,
    required: true
  },
  // tag: {
  //   type: String,
  //   required: false
  // },
  layout: {
    type: String,
    required: false,
    default: 'default'
  },
  showFeatures: {
    type: Boolean,
    default: false
  },
  code: {
    type: String,
    default: ''
  }
})

const altFn = (code: string, title: string): string => {
  if (code === 'rewrite-document') {
    return '文档改写AI降重'
  }

  if (code === 'literature_review') {
    return `${title}AI`
  }

  const altCodes = [
    'paper', // 论文助手
    'thesis_report', // 论文开题报告
    'thesis_correction', //论文批改
    'proposal', // 论文大纲
    'book', // 万能小in AI智能写书软件
    'zhixie', // 论文致谢
    'task', // 论文任务书
    'paper_title', // 论文题目
    'abstract', // 论文摘要
    'paper_middle', // 论文中期报告
    'yiju', // 论文选题依据
  ]

  return altCodes.includes(code) ? `AI${title}` : title
}



</script>
