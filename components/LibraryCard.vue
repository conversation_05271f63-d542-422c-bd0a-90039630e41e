<template>
  <div 
    :class="[
      'bg-white/80 backdrop-blur-sm rounded-xl p-5 border transition-colors group cursor-pointer',
      getBorderColorClass(library.type)
    ]"
  >
    <!-- 原有的卡片内容结构 -->
  </div>
</template>

<script setup>
const props = defineProps({
  library: {
    type: Object,
    required: true
  },
  viewMode: {
    type: String,
    default: 'grid'
  }
})

const getBorderColorClass = (type) => {
  const classes = {
    default: 'border-emerald-200/50 hover:border-emerald-300/50',
    research: 'border-blue-200/50 hover:border-blue-300/50',
    course: 'border-purple-200/50 hover:border-purple-300/50'
  }
  return classes[type] || classes.default
}
</script> 