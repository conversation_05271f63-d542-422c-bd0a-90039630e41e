<template>
    <pdf-from-word-viewer v-if="pdfFromFileType === 'word'" :pdf-source="pdfSource" :total-pages="totalPages" />
    <pdf-from-slides-viewer v-else :pdf-source="pdfSource" :total-pages="totalPages" />
</template>

<script setup lang="ts">
interface Props {
    pdfFromFileType?: 'word' | 'slides'
    pdfSource: string
    totalPages?: number
}

const props = withDefaults(defineProps<Props>(), {
    pdfFromFileType: 'word'
})
</script>