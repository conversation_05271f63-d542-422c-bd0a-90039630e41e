<template>
    <div class="word-content-result-viewer flex-1 p-6 overflow-y-auto bg-white">
        <div class="max-w-2xl mx-auto" v-if="fileItems.length > 0 && !answerContent">
            <h3 class="mb-4 text-lg font-medium text-gray-900">生成的文件</h3>
            <div class="space-y-3">
                <div v-for="file in fileItems" :key="file.path"
                    class="flex items-center justify-between p-4 rounded-lg bg-gray-50">
                    <div class="flex items-center flex-1 min-w-0 space-x-3">
                        <download theme="outline" size="20" fill="#666666" class="flex-shrink-0" />
                        <span class="text-gray-700 truncate">{{ getFileName(file.path) }}</span>
                    </div>

                    <!-- 下载按钮  :href="appendTimestampToURL(file.path)" target="_blank"-->
                    <a @click="forceDownloadFile(file.path)" class="px-3 py-1.5 text-sm text-gray-600 bg-white border border-gray-300 
                            rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 
                            focus:ring-blue-500 flex-shrink-0 ml-4">
                        下载{{ file.type }}
                    </a>
                </div>
            </div>
        </div>

        <div v-html="answerContent"></div>
    </div>
</template>

<script setup lang="ts">
import { Download } from '@icon-park/vue-next';
import { getFileName } from '~/utils/utils';


interface Props {
    fileItems: any[]

    answerContent?: string,

}
const props = withDefaults(defineProps<Props>(), {})
const appendTimestampToURL = (url: string) => {
    if (!url) {
        return
    }
    return url
    // const timestamp = new Date().getTime().toString()
    // return url.includes('?') ? `${url}&ts=${timestamp}` : `${url}?ts=${timestamp}`
}

const forceDownloadFile = (url: string) => {
    if (!url) {
        return
    }
    try {
        fetch(url)
            .then(res => res.blob())
            .then(blob => {
                const blobUrl = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = blobUrl;
                a.download = getFileName(url);
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(blobUrl);
                document.body.removeChild(a);
            });

    } catch (error) {
        console.error('下载失败:', error);
    }
}


</script>

<style scoped lang="scss">
.word-content-result-viewer {
    .shadow-inner {
        box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.1);
    }

    /* 添加内容区域的样式 */
    :deep(div) {
        line-height: 1.8;
        font-size: 16px;
        color: #333;
    }

    :deep(p) {
        margin-bottom: 1em;
    }

    :deep(h1) {
        font-size: 1.6em;
        font-weight: 700;
        margin: 1.8em 0 1em;
        color: #1a1a1a;
    }

    :deep(h2) {
        font-size: 1.4em;
        font-weight: 700;
        margin: 1.6em 0 0.8em;
        color: #1a1a1a;
    }

    :deep(h3) {
        font-size: 1.6em;
        font-weight: 600;
        margin: 1.4em 0 0.7em;
        color: #1a1a1a;
    }

    :deep(h4) {
        font-size: 1.0em;
        font-weight: 600;
        margin: 1.2em 0 0.6em;
        color: #1a1a1a;
    }

    :deep(h5) {
        font-size: 0.8em;
        font-weight: 600;
        margin: 1em 0 0.5em;
        color: #1a1a1a;
    }

    :deep(h6) {
        font-size: 0.7em;
        font-weight: 600;
        margin: 1em 0 0.5em;
        color: #1a1a1a;
    }

    /* 添加水平线样式 */
    :deep(hr) {
        margin: 2em 0;
        border: none;
        border-top: 1px solid #e5e7eb;
    }

    /* 移除原来的通用标题样式 */
    :deep(h1, h2, h3, h4, h5, h6) {
        line-height: 1.4;
    }

}
</style>