<template>
    <div class="flex h-screen flex-1">
        <!-- 左侧导航栏和右侧知识库部分保持与 paper.vue 一致 -->

        <!-- 主内容区域 -->
        <div class="flex-1 flex flex-col h-screen">
            <!-- 顶部标题区域保持一致 -->
            <div class="p-4 bg-gradient-to-r from-sky-50/90 via-indigo-50/80 to-blue-50/90">
                <div class="flex items-center justify-between">
                    <!-- 左侧标题部分 -->
                    <div class="flex items-center">
                        <div
                            class="w-10 h-10 bg-gradient-to-br from-sky-100 to-indigo-100 rounded-lg flex items-center justify-center mr-3 shadow-inner">
                            <file-editing theme="outline" size="24" fill="#3b82f6" />
                        </div>
                        <div>
                            <h1
                                class="text-lg font-bold bg-gradient-to-r from-sky-600 via-blue-600 to-indigo-600 bg-clip-text text-transparent">
                                写作中
                            </h1>
                            <p class="text-xs text-gray-500 mt-0.5">请耐心等待，AI正在为您写作</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分隔线 -->
            <div class="border-t border-blue-100/50"></div>

            <!-- 主体内容区域 -->
            <div class="flex-1 overflow-y-auto p-4 bg-white">
                <div class="max-w-4xl mx-auto">
                    <!-- 写作进度卡片 -->
                    <div class="mb-6 p-6 bg-white rounded-xl border border-gray-200">
                        <h2 class="text-lg font-medium text-gray-800 mb-4">写作进度</h2>
                        <div class="space-y-4" v-if="currentSubmission && currentCreator">
                            <template
                                v-if="!currentCreator?.creator?.config?.isUseLargeWorker && currentCreator.creator.code != 'ppt'">
                                <!-- 短文展示 -->
                                <div class="flex flex-col items-center space-y-4">
                                    <!-- <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div> -->
                                    <a-spin :spinning="true" class="text-gray-600"></a-spin>
                                    <p class="text-gray-600">写作中...</p>
                                </div>
                            </template>
                            <template v-else>
                                <!-- 长文展示-->
                                <Creating :submission="currentSubmission" :creator-data="currentCreator" />
                            </template>
                        </div>
                    </div>

                    <!-- 写作预览卡片 -->
                    <div class="mb-6 p-6 bg-white rounded-xl border border-gray-200">
                        <!-- <h2 class="text-lg font-medium text-gray-800 mb-4">写作预览</h2> -->

                        <div class="space-y-6 text-center">
                            <!-- 第一行文字 -->
                            <p class="text-gray-600">
                                完成写作大约需要10分钟左右，后续可在
                                <span class="font-bold text-[#3B82F6]">个人中心</span>-<span
                                    class='font-bold text-[#3B82F6]'>写作记录</span>
                                中查看；遇到长文写作，排队等情况可能需要等待更久，感谢理解~
                                关注公众号将第一时间获得提醒哦！
                            </p>

                            <!-- 二维码图片 -->
                            <div class="flex justify-center" v-if="!userStore.currentLoginInfo?.isSubscribe">
                                <img :src="wechatOfficialAccountsQrcode" alt="公众号二维码" class="w-32 h-32 object-cover">
                            </div>

                            <!-- 第二行文字 -->
                            <!-- <p class="text-gray-600">
                                关注公众号将第一时间获得提醒哦！
                            </p> -->
                        </div>
                    </div>

                    <!-- <div class="flex justify-center mb-10" v-if="currentSubmission?.status == 'error'">
                        <button
                            class="mt-4 px-16 py-3 bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 text-white rounded-xl text-base font-medium transition duration-200"
                            @click="viewFullPaper">
                            写作失败，查看原因
                        </button>
                    </div> -->
                    <!-- <div class="flex justify-center mb-10" v-if="currentSubmission?.status == 'done'">
                        <button
                            class="mt-4 px-16 py-3 bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 text-white rounded-xl text-base font-medium transition duration-200"
                            @click="viewFullPaper">
                            查看写作成果
                        </button>
                    </div> -->
                </div>
            </div>
        </div>

        <!-- 右侧知识库边栏保持 paper.vue 一致 -->
    </div>
</template>

<script setup lang="ts">
import Creating from '@/components/Creating.vue';
import {
    FileEditing
} from '@icon-park/vue-next';
import { storeToRefs } from 'pinia';
import { onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useSubmissionStore } from '~/stores/submission';
import { wechatOfficialAccountsQrcode } from '~/utils/constants';

const userStore = useUserStore()

const route = useRoute()

const router = useRouter()

const submissionStore = useSubmissionStore()
const { currentSubmission, currentCreator } = storeToRefs(submissionStore)

const emit = defineEmits(['creation-complete'])

onMounted(async () => {
    // console.log('route.query = ', route.query)
})

</script>

<style scoped>
/* 保持与 paper.vue 相同的样式 */
</style>