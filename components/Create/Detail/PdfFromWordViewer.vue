<template>
    <div class="flex-1 flex-col min-h-0 overflow-y-auto relative">
        <!-- 左翻页按钮 -->
        <button @click="prevPage"
            class="absolute left-4 top-1/2 -translate-y-1/2 bg-white rounded-full p-2 shadow-md hover:shadow-lg transition-shadow z-10">
            <left theme="outline" size="24" fill="#333" />
        </button>

        <div class="p-8 pb-16 h-full overflow-auto">
            <!-- PDF显示区域 -->
            <div class="flex justify-center items-center min-h-full">
                <div class="flex bg-white shadow-xl transform origin-center" :style="`scale(${zoom * 2 / 3})`">
                    <!-- 左页 -->
                    <div class="w-[397px] h-[561px] relative">
                        <client-only>
                            <vue-pdf-embed :source="pdfSource" :page="currentPage" :scale="zoom" class="w-full h-full"
                                @rendered="onPageRendered" @loaded="handlePDFLoaded" />
                        </client-only>
                    </div>

                    <!-- 分隔线 -->
                    <div class="w-[1px] bg-gray-200 mx-8"></div>

                    <!-- 右页 -->
                    <div class="w-[397px] h-[561px] relative">
                        <client-only>
                            <vue-pdf-embed :source="pdfSource" :page="currentPage + 1" :scale="zoom"
                                class="w-full h-full" @rendered="onPageRendered" />
                        </client-only>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右翻页按钮 -->
        <button @click="nextPage"
            class="absolute right-4 top-1/2 -translate-y-1/2 bg-white rounded-full p-2 shadow-md hover:shadow-lg transition-shadow z-10">
            <right theme="outline" size="24" fill="#333" />
        </button>
    </div>
</template>

<script setup lang="ts">
import { Left, Right } from '@icon-park/vue-next';
import { defineAsyncComponent, ref } from 'vue';

interface Props {
    pdfSource: string
    totalPages?: number
}

const props = withDefaults(defineProps<Props>(), {
    totalPages: 10
})

const VuePdfEmbed = defineAsyncComponent(() =>
    import('vue-pdf-embed')
)

const currentPage = ref(1)
const totalPages = ref(props.totalPages)
const zoom = ref(1)

const handlePDFLoaded = (events: any) => {
    totalPages.value = events._pdfInfo.numPages
}

const onPageRendered = () => {
    console.log('onPageRendered')
}

const prevPage = () => {
    if (currentPage.value > 1) {
        currentPage.value -= 2
    }
}

const nextPage = () => {
    if (currentPage.value < totalPages.value - 1) {
        currentPage.value += 2
    }
}
</script>