<template>
    <div class="flex flex-1 h-screen">
        <!-- 左侧边栏 -->
        <div class="overflow-y-auto bg-white">
            <CreatePaperLeftPanel />
        </div>

        <!-- 右侧主体内容区域 -->
        <div class="flex flex-col flex-1 min-h-0" v-if="currentSubmission">
            <!-- 顶部操作栏 -->
            <div class="flex items-center justify-between p-8 bg-white border-b border-gray-200">
                <div class="flex">
                    <button @click="router.back()"
                        class="mr-2 lg:mr-4 p-1.5 lg:p-2 hover:bg-gray-100 rounded-lg transition-colors">
                        <left-small theme="outline" size="24" fill="#666" />
                    </button>
                    <div class="flex items-center p-0 text-lg font-medium text-gray-800">
                        {{ currentSubmission.formData.topic }}
                    </div>
                </div>

                <div class="flex items-center space-x-3 text-xs" style="color: #6D7EA5;font-size: 12px;">
                    内容由AI生成，仅供参考，无版权风险
                </div>

            </div>

            <!-- 错误信息区域 -->
            <div v-if="hasError" class="flex-1 p-8 overflow-y-auto">
                <div class="max-w-2xl mx-auto text-center">
                    <!-- 错误图标 -->
                    <div class="flex justify-center mb-6">
                        <close-one theme="outline" size="48" fill="#DC2626" />
                    </div>

                    <!-- 错误标题 -->
                    <h2 class="mb-4 text-2xl font-semibold text-gray-900">
                        写作失败
                    </h2>

                    <!-- 错误详细信息 -->
                    <div class="p-6 mb-6 border border-red-200 rounded-lg bg-red-50">
                        <div class="text-base text-red-700" v-html="answerContent"></div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="flex justify-center space-x-4">
                        <button @click="router.back()"
                            class="px-4 py-2 text-sm font-medium text-gray-700 transition-colors bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                            返回上一页
                        </button>
                        <button @click="handlePressEditCreate"
                            class="px-4 py-2 text-sm font-medium text-white transition-colors bg-blue-600 rounded-md hover:bg-blue-700">
                            重新写作
                        </button>
                    </div>
                </div>
            </div>

            <ClientOnly>
                <MindMapResultViewer v-if="isMindMapSubmission" :currentMindmapType="currentMindmapType">
                </MindMapResultViewer>
            </ClientOnly>

            <PdfResultViewer v-if="shouldShowPdfViewer" :pdf-source="pdfPath" :pdf-from-file-type="pdfFromFileType" />

            <WordAndContentResultViewer v-if="shouldShowWordAndContent" :fileItems="fileItems"
                :answer-content="answerContent" />

            <ClientOnly>
                <!-- 底部固定区域 -->
                <div class="flex items-center  p-4 bg-white border-t border-gray-200"
                    v-if="currentSubmission.creatorCode != 'mind_map'">

                    <!-- <template v-if="currentSubmission.status != SubmissionStatus.error"> -->

                    <!-- <div> -->
                    <!-- 只在有Word文档时显示导出Word按钮 -->
                    <!-- <a v-if="answer?.finalDocxPath" style="margin-left: 10px"
                                class="download-area-button download-area-normal">
                                <button
                                    @click="forceDownloadFile(answer.finalDocxPath, `${currentSubmission.formData.topic}.word` || '万能小in.word')"
                                    class="px-4 py-2 text-sm font-bold text-gray-600 bg-white border border-gray-300 rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <download theme="outline" size="16" fill="currentColor"
                                        class="inline-block mr-1 align-text-bottom" />
                                    下载Word
                                </button>
                            </a> -->
                    <!-- <a style="margin-left: 10px" class="download-area-button download-area-normal">
                                <button
                                    @click="forceDownloadFile(answer.pptxPath, `${currentSubmission.formData.topic}.pptx` || '万能小in-演示文稿.pptx')"
                                    class="px-4 py-2 text-sm text-gray-600 bg-white border border-gray-300 
                        rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 
                        focus:ring-blue-500 font-bold">
                                    <download theme="outline" size="16" fill="currentColor"
                                        class="inline-block mr-1 align-text-bottom" />
                                    下载PPT
                                </button>
                            </a> -->
                    <!-- </div> -->
                    <!-- 修改编辑按钮，添加flex布局和备注文字 -->
                    <!-- <div class="flex justify-center items-center"
                        v-if="currentSubmission.status != SubmissionStatus.error && currentSubmission.isEditable"> -->
                    <!-- </div> -->

                    <!-- 修改这部分为响应式布局，使用TailwindCSS类 -->
                    <div class="w-full flex flex-col md:flex-row justify-between items-center gap-4 md:gap-2">
                        <div class="w-full md:w-1/3">
                            <a v-if="answer.pptxPath && currentSubmission.creatorCode == 'ppt'"
                                class="ml-[10px] download-area-button download-area-normal">
                                <button
                                    @click="forceDownloadFile(answer.pptxPath, `${currentSubmission.formData.topic}.pptx` || '万能小in-演示文稿.pptx')"
                                    class="px-4 py-2 text-sm text-gray-600 bg-white border border-gray-300 
                        rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 
                        focus:ring-blue-500 font-bold">
                                    <DownloadOutlined theme="outline" size="16" fill="currentColor"
                                        class="inline-block mr-1 align-text-bottom" />
                                    下载PPT
                                </button>
                            </a>
                        </div>
                        <div class="w-full md:w-1/3 flex justify-center">
                            <div v-if="currentSubmission.status != SubmissionStatus.error && currentSubmission.isEditable"
                                class="flex justify-center items-center">
                                <a style="margin-left: 10px" class="download-area-button" :href="getAiEditorUrl">
                                    <button
                                        class="px-6 py-2.5 bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 text-white text-lg font-semibold rounded-md shadow-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition duration-200">
                                        查看详情
                                    </button>
                                </a>
                                <span class="mt-1 text-xs text-gray-500 ml-[5px]">人机协作，编辑下载</span>
                            </div>
                        </div>
                        <div class="w-full md:w-1/3 flex justify-center md:justify-end items-center">
                            <button @click="handlePressEditCreate"
                                class="px-4 py-2 text-sm text-gray-600 bg-white border border-gray-300 rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <copy theme="outline" size="16" fill="currentColor"
                                    class="inline-block mr-1 align-text-bottom" />
                                复制写作任务
                            </button>
                        </div>
                    </div>
                </div>
            </ClientOnly>
        </div>
    </div>
</template>

<script setup lang="ts">
import CreatePaperLeftPanel from '@/components/Create/Paper/LeftPanel.vue'
import { StarloveUtil } from '@/utils/util'
import { DownloadOutlined } from '@ant-design/icons-vue'
import { CloseOne, Copy, LeftSmall } from '@icon-park/vue-next'
import markdownit from 'markdown-it'
import { storeToRefs } from 'pinia'
import { computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useApp } from '~/composables/useApp'
import { UserService } from '~/services/user'
import { useSubmissionStore } from '~/stores/submission'
import { MindmapAnswerType, SubmissionStatus } from '~/utils/constants'
import { saveSubmissionDraft } from '~/utils/pc_utils'
import { getAttachmentsFromSubmission, getFileDownloadUel, getFileName } from '~/utils/utils'
import MindMapResultViewer from './MindMapResultViewer.vue'
import PdfResultViewer from './PdfResultViewer.vue'
import WordAndContentResultViewer from './WordAndContentResultViewer.vue'
const app = useApp()
const router = useRouter()

const submissionStore = useSubmissionStore()
const { currentSubmission, currentCreator } = storeToRefs(submissionStore)

const currentMindmapType = ref(MindmapAnswerType.mindmap)

// const changeMindmapIcon = computed(() => {
//     if (currentMindmapType.value == MindmapAnswerType.mindmap) {
//         return 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/icons/new-pc/result-change-mindmap-active.png'
//     }
//     return 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/icons/new-pc/result-change-mindmap.png'
// })

// const changeOutlineIcon = computed(() => {
//     if (currentMindmapType.value == MindmapAnswerType.outline) {
//         return 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/icons/new-pc/result-change-outline-active.png'
//     }
//     return 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/icons/new-pc/result-change-outline.png'
// })

const getAiEditorUrl = computed(() => {
    if (!currentSubmission.value?.id) {
        return ''
    }
    if (app.value?.isDesktop) {
        return `${StarloveUtil.getAIEditorBaseUrl()}/?id=${currentSubmission.value?.id}&t=${UserService.getToken()}`
    }
    return `${StarloveUtil.getAIEditorBaseUrl()}/?id=${currentSubmission.value?.id}`
})
const getError = (answer: any) => {
    if (answer.error) {
        return `写作失败已退还硬币。<br />${answer.error}`
    }
    return undefined
}

const hasError = computed(() => {
    if (!currentSubmission.value) {
        return false
    }
    if (currentSubmission.value.status == 'error') {
        return true
    }
    return false
})

const answer = computed(() => {
    if (!currentSubmission.value || !currentSubmission.value.answer) {
        return undefined
    }

    // if (currentSubmission.value.status == SubmissionStatus.error) {
    //     return undefined
    // }

    try {
        const answer = JSON.parse(currentSubmission.value.answer)
        return answer
    } catch (error) {
        console.error('JSON parse error:', error)
        return {
            content: currentSubmission.value.answer
        }
        // return undefined
    }

})

const pdfPath = computed(() => {
    if (!answer.value) {
        return undefined
    }
    return answer.value.finalPdfPath || answer.value.pdfPath
})

const shouldShowPdfViewer = computed(() => {
    if (!pdfPath.value) {
        return false
    }

    const downloadUrl = getFileDownloadUel(pdfPath.value)
    if (!downloadUrl) {
        return false
    }

    const lowerCaseUrl = downloadUrl.toLowerCase()
    return lowerCaseUrl.endsWith('.pdf')
})

const pdfFromFileType = computed(() => {
    if (!answer.value) {
        return undefined
    }
    if (answer.value.pptxPath) {
        return 'slides'
    }
    return 'word'
})

const shouldShowWordAndContent = computed(() => {
    if (hasError.value) {
        return false
    }
    if (isMindMapSubmission.value) {
        return false
    }
    if (shouldShowPdfViewer.value) {
        return false
    }
    if (!answer.value) {
        return false
    }
    if (fileItems.value.length > 0 || answerContent.value) {
        return true
    }
    return true
})

const isMindMapSubmission = computed(() => {
    if (!currentSubmission.value) {
        return false
    }
    if (currentSubmission.value.creatorCode == 'mind_map') {
        return true
    }
    return false
})

const isMarkdown = (text: string) => {
    // 检查是否包含标题（#）、列表（*）、加粗/斜体（*或_）、链接（[]()等Markdown特征
    // 增强检测能力，特别是对标题和横线的检测
    const markdownPatterns = [
        /^#{1,6}\s+.+$/m,                // 标题
        /^(?:-{3,}|\*{3,}|_{3,})$/m,     // 横线
        /^[-*+]\s+.+$/m,                 // 无序列表
        /^\d+\.\s+.+$/m,                 // 有序列表
        /\*\*.*?\*\*|__.*?__/,           // 加粗
        /\*.*?\*|_.*?_/,                 // 斜体
        /\[.*?\]\(.*?\)/,                // 链接
        /`.*?`/,                         // 行内代码
        /^```[\s\S]*?```$/m,             // 代码块
        /^>\s+.+$/m                      // 引用
    ]
    return markdownPatterns.some(pattern => pattern.test(text))
}

const answerContent = computed(() => {
    if (!currentSubmission.value || !currentSubmission.value.answer) {
        return '抱歉，系统繁忙，请返回重试~本次写作不消耗任何硬币'
    }
    if (answer.value) {
        let _answerContent = answer.value.content || getError(answer.value) || ''

        // console.log('isMarkdown(_answerContent)==>', isMarkdown(_answerContent))
        if (isMarkdown(_answerContent)) {
            const md = markdownit({
                html: true, // 启用 HTML 标签
                linkify: true, // 自动将 URL 转换为链接
                breaks: true, // 将 \n 转换为 <br>
                typographer: true // 启用一些语言中立的替换和引号美化
            })
            // 直接渲染原始 Markdown 文本，不预先替换换行符
            return md.render(_answerContent)
        }
        return _answerContent.replace(/\n/g, '<br/>')
    }
    return currentSubmission.value.answer.replace(/\n/g, '<br/>')
})

// const handlePressChangeType = () => {
//     const data = currentMindmapType.value
//     if (data == MindmapAnswerType.mindmap) {
//         currentMindmapType.value = MindmapAnswerType.outline
//     } else {
//         currentMindmapType.value = MindmapAnswerType.mindmap
//     }
//     // setOutlineData()
//     // 当前是自定义大纲切换到脑图时
//     if (data == MindmapAnswerType.outline) {
//         // updateAnswer()
//     }
// }

const handlePressEditCreate = () => {
    const params: {
        creatorCode: any;
        formData: any;
        attachments?: any;
    } = {
        creatorCode: currentCreator.value?.creator.code,
        formData: currentSubmission.value?.formData,
        // console.log('handlePressEdit props.submission ==>', props.submission)
    }
    if (currentSubmission.value?.formData?.attachments) {
        params['attachments'] = getAttachmentsFromSubmission(currentSubmission.value)
    }
    // console.log("saveSubmissionDraft ==>", params)
    saveSubmissionDraft(params)
    router.replace({ path: `/create/${currentCreator.value?.creator.code}` })
    // router.replace({ path: `/create/ppt` })
}

const forceDownloadFile = (url: string, title: string) => {
    const downloadUrl = getFileDownloadUel(url)
    if (!downloadUrl) {
        return url
    }

    try {
        fetch(downloadUrl)
            .then(res => res.blob())
            .then(blob => {
                const blobUrl = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = blobUrl;
                a.download = getFileName(url) || title;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(blobUrl);
                document.body.removeChild(a);
            });

    } catch (error) {
        console.error('下载失败:', error);
    }
}

const appendTimestampToURL = (url: string) => {

    const downloadUrl = getFileDownloadUel(url)
    if (!downloadUrl) {
        return url
    }
    // 添加时间戳以避免缓存问题
    const timestamp = new Date().getTime()
    const separator = downloadUrl.includes('?') ? '&' : '?'
    return `${downloadUrl}${separator}t=${timestamp}`
}

// const handlePressDownloadMindMap = () => {
//     console.log("handlePressDownloadMindMap ==>")
// }

onMounted(() => {
    // 获取PDF总页数
    // totalPages.value = 10 // 这里假设总页数为10,实际可通过PDF库获取

    // answer.value.finalPdfPath = '/demo.pdf'
    // answer.value.finalDocxPath = 'https://file.xiaoin.com.cn/quick_paper/None/paper/86f6eb40-0d6b-4983-bb87-a28c00bfc0bf/健全全过程人民民主制度体系建设更高水平的.pdf'
    // answer.value.finalPdfPath = 'https://www.bcspanthers.org/uploaded/event_forms/Guidance_Handbook.pdf'
})

// 添加一个新的计算属性来处理文件名的展示
// const getFileName = (url: string) => {
//     url = url.split('/').pop() || 'document.docx';
//     if (url.indexOf('?') > -1) {
//         url = url.split('?')[0]
//     }
//     return decodeURIComponent(url);
// }
const fileItems = computed(() => {
    if (!answer.value) return []

    const files = []
    const { pptxPath, finalDocxPath, checkDocxPath } = answer.value

    if (pptxPath) {
        files.push({
            path: pptxPath,
            name: getFileName(pptxPath) || 'presentation.pptx',//pptxPath.split('/').pop() || 'presentation.pptx',
            type: 'PPT'
        })
    }

    if (finalDocxPath) {
        files.push({
            path: finalDocxPath,
            name: getFileName(finalDocxPath) || 'document.docx',
            type: 'Word',
        })
    }

    if (checkDocxPath) {
        files.push({
            path: checkDocxPath,
            name: getFileName(checkDocxPath) || 'document.docx',
            type: 'Word',
        })
    }

    return files
})
</script>


<style scoped lang="scss">
.shadow-inner {
    box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.1);
}

/* 添加内容区域的样式 */
:deep(div) {
    line-height: 1.8;
    font-size: 16px;
    color: #333;
}


/* 移除原来的通用标题样式 */
:deep(h1, h2, h3, h4, h5, h6) {
    line-height: 1.4;
}

/* 添加滚动条样式优化 */
.overflow-y-auto {
    scrollbar-width: thin;
    scrollbar-color: #E5E7EB transparent;
}

.overflow-y-auto::-webkit-scrollbar {
    width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
    background: transparent;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
    background-color: #E5E7EB;
    border-radius: 3px;
}

/* 添加滚动时的平滑效果 */
.overflow-y-auto {
    scroll-behavior: smooth;
}

/* 移动端滚动优化 */
@media (max-width: 768px) {
    .overflow-y-auto {
        -webkit-overflow-scrolling: touch;
    }
}

.mindmap-switch-item {
    padding: 6px 12px;
    transition: all 0.3s ease;
}

.mindmap-switch-item.active {
    background: #ecf6ff;
    border-radius: 5px;
    color: #249cff;
}
</style>