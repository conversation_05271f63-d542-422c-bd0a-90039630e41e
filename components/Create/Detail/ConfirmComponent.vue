<template>
  <div class="flex h-screen flex-1">


    <!-- 主内容区域 -->
    <div class="flex-1 flex flex-col h-screen">
      <!-- 顶部标题区域保持一致 -->
      <div class="flex items-center justify-between p-4 bg-gradient-to-r from-sky-50/90 via-indigo-50/80 to-blue-50/90">
        <div class="flex items-center justify-between">
          <!-- 左侧标题部分 -->
          <div class="flex items-center">
            <div
              class="w-10 h-10 bg-gradient-to-br from-sky-100 to-indigo-100 rounded-lg flex items-center justify-center mr-3 shadow-inner">
              <file-editing theme="outline" size="24" fill="#3b82f6" />
            </div>
            <div>
              <h1
                class="text-lg font-bold bg-gradient-to-r from-sky-600 via-blue-600 to-indigo-600 bg-clip-text text-transparent">
                确认订单
              </h1>
              <p class="text-xs text-gray-500 mt-0.5">请确认订单信息并立即开始写作任务</p>
            </div>
          </div>
        </div>


      </div>

      <!-- 分隔线 -->
      <div class="border-t border-blue-100/50"></div>

      <!-- 主体内容区域 -->
      <div class="flex-1 overflow-y-auto p-4 bg-white" v-if="currentSubmission">
        <div class="max-w-4xl mx-auto">
          <!-- 订单信息卡片 -->
          <div class="mb-6 p-6 bg-white rounded-xl border border-gray-200">
            <div class="flex items-center justify-between mb-4">
              <h2 class="text-lg font-medium text-gray-800">订单信息</h2>
              <button @click="handleCallback" class="flex items-center text-sm text-blue-500 hover:text-blue-600">
                <span class="ml-1">返回修改</span>
              </button>
            </div>

            <div class="space-y-4">
              <!-- 订单信息区域 -->
              <OrderInformation v-if="currentCreator && currentSubmission" :current-submission="currentSubmission"
                :current-creator="currentCreator" />

              <!-- 参考文献 -->
              <div class="space-y-2" v-if="currentSubmission?.attachments && currentSubmission?.attachments.length > 0">
                <div class="text-sm font-medium text-gray-700">{{ attachmentsFieldName }}</div>
                <div class="space-y-2">
                  <div class="flex items-center gap-2 p-3 bg-gray-50 rounded-lg">
                    <div class="flex-1 min-w-0">
                      <AttachmentsTable ref="literatureReviewRefs" :submission="currentSubmission"
                        v-model:isNeedGetTheWordCount="isNeedGetTheWordCount"
                        :knowledgeAssistantMemberInfo="knowledgeAssistantMemberInfo"
                        @getSelectedFileIdsAndAttachments="getSelectedFileIdsAndAttachments">
                      </AttachmentsTable>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>


          <div v-if="jiagouList.length > 0" class="mb-6 px-6 py-4 bg-white rounded-xl border border-gray-200">
            <div class="flex flex-row items-center gap-2">
              <file-editing theme="outline" size="16" fill="#3b82f6" />
              <h2 class="text-sm text-[#2551B5] ">更多应用组合写作，内容关联更高、一键生成更省事、打包购买更优惠!</h2>
            </div>
            <div class="flex flex-col sm:flex-row gap-3 sm:gap-4 pt-3 sm:pt-4 pb-4 sm:pb-6">
              <button v-for="item in jiagouList" :key="item.creatorCode" @click="handleJiagouChange(item)" :style="{
                backgroundImage: item.isSelected
                  ? 'url(https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/add-ons-bgtwo.png)'
                  : 'url(https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/add-ons-bg.png)',
                backgroundSize: '105% 105%',
                boxShadow: item.isSelected
                  ? '0 0 0 1px #FFCF96'
                  : '0 0 0 1px #FFE6B6'
              }"
                class="w-full h-[110px] sm:w-1/3 p-3 sm:p-4 flex flex-col justify-between rounded-lg cursor-pointer bg-cover bg-center bg-no-repeat mb-3 sm:mb-0">
                <div class="flex flex-col justify-between h-full">
                  <!-- 上部分 -->
                  <div class="flex justify-between items-center">
                    <!-- 上左 - 图标、名称和标签 -->
                    <div class="flex flex-col space-y-2">
                      <div class="flex items-center space-x-2">
                        <img :src="item.icon" class="w-5 h-5 sm:w-6 sm:h-6 flex-shrink-0" />
                        <span class="text-base font-medium truncate max-w-[100px]">{{
                          item.creatorName }}</span>
                        <div class="px-2 py-1 text-xs text-[#6959E5] bg-[#ECEAFF] rounded-[5px] truncate max-w-[120px]">
                          {{ item.tag }}
                        </div>
                      </div>
                    </div>
                    <!-- 上右 - 复选框 -->
                    <div>
                      <input type="checkbox" :checked="item.isSelected" @click.stop="handleJiagouChange(item)"
                        class="flex-shrink-0" />
                    </div>
                  </div>

                  <!-- 下部分 -->
                  <div class="flex justify-between items-center">
                    <!-- 下左 - 价格信息 -->
                    <div>
                      <span class="text-[#FF4242] text-base font-medium truncate">
                        {{ (getSelectedSizeInfo(item).tokens || 0) / 10000 }}万硬币
                      </span>
                      <span class="text-gray-500 line-through text-sm truncate">
                        {{ (getSelectedSizeInfo(item).originTokens || 0) / 10000 }}万硬币
                      </span>
                    </div>
                    <!-- 下右 - 选择框 -->
                    <div class="inline-block ">
                      <select
                        v-if="item.prices.length > 0 && getSelectedSizeInfo(item).name && getSelectedSizeInfo(item).name.trim() !== ''"
                        @change="(e) => handleSelectChange(e, item)" @click.stop
                        class="px-2 py-1 text-sm bg-white border-none rounded-md cursor-pointer focus:outline-none"
                        :value="item.selectedSize">
                        <option v-for="size in item.prices" :key="size.size" :value="size.size">
                          {{ getWordCount(size.size) }}字
                        </option>
                      </select>
                      <!-- 空白占位元素，保持布局 -->
                      <div v-else class="py-1 text-sm invisible">占位元素</div>
                    </div>
                  </div>
                </div>
              </button>
            </div>
          </div>


          <!-- 费用明细卡片 -->
          <div class="mb-6 p-6 bg-white rounded-xl border border-gray-200">
            <h2 class="text-lg font-medium text-gray-800 mb-4">费用明细</h2>

            <div class="space-y-3">
              <!-- 文档学习费用 -->
              <div class="space-y-3 p-3 bg-[#e6f4ff] rounded-lg">
                <div class="flex justify-between items-center text-sm" v-if="isShowFreeTotal && creatorCoin > 0">
                  <span class="text-gray-600">文档学习费用</span>
                  <span class="text-gray-800">
                    <div class="flex items-center">
                      <div class="vip-promotion flex items-center cursor-pointer mr-3" v-if="isUpgradeVip"
                        @click="handleOpenVipModal">
                        <span class="text-red-500">升级年会员，<strong>每次</strong>写作免学习费用</span>
                        <Right theme="outline" size="16" fill="#EF4444" class="ml-1" />
                      </div>
                      <template v-if="isNeedGetTheWordCount">-</template>
                      <template v-else>{{ footerTotal == 0 ? '-' : `${footerTotal.toFixed(1)}万硬币`
                      }}</template>
                    </div>
                  </span>
                </div>
                <div class="flex justify-between items-center text-sm" v-if="creatorCoin > 0">
                  <span class="text-gray-600">写作费用</span>
                  <div class="flex flex-row gap-2 items-center">
                    <span class="text-[#FF2442] bg-[#FFF2F2] px-2.5 py-1 rounded-[7px]"
                      v-if="selectLectureCoin > 0">组合写作已省{{ savedCoins
                      }}万硬币</span>
                    <span class="text-gray-800 px-2.5 py-1">{{ writingCost }}万硬币</span>
                  </div>
                </div>
              </div>
              <div class="border-t border-gray-100 my-2"></div>
              <div class="flex justify-between items-center">
                <span class="text-gray-800 font-medium">合计</span>
                <span class="flex justify-between items-center text-lg text-right font-medium text-blue-600">
                  <div>
                    <template v-if="isNeedGetTheWordCount">-</template>
                    <template v-else>
                      <div>{{ submissionTokens || '-' }}万硬币</div>
                      <div class="flex items-center justify-end price">{{ expendMoney }}
                        <PopoverHelp
                          content="最低折合是按平台提供的硬币购买选项，以最高一档298元（iOS为398元）对应的低至5折的最低折扣价格来折算的。每笔写作任务，对应的实际硬币消耗价格，会视用户的硬币购买选项、对应硬币折扣不同而有差异。" />
                      </div>
                    </template>
                  </div>
                </span>
              </div>
            </div>
          </div>
          <div class="example-title" v-if="imageExampleList.length > 0" @click="handleShowExample">提前看看生成格式示例
          </div>
        </div>
      </div>

      <!-- 确认按钮 -->
      <ClientOnly>
        <div class="flex justify-center items-center gap-4 mt-6 mb-6" v-if="!isTheFileHasBeenReadSuccess">
          <div v-if="isShowCallbackButton" class="text-red-500 text-center">
            <div class="mb-2">由于文件错误，请返回上一步重新上传文件</div>
            <button @click="handleCallback"
              class="px-10 py-2 bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 text-white rounded-xl text-base font-medium relative group overflow-hidden">
              <span class="relative z-10">返回修改</span>
            </button>
          </div>
          <div v-else>
            <button v-if="isRequireRecharge" @click="handleOpenVipModal"
              class="px-16 py-3 bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 text-white rounded-xl text-base font-medium relative group overflow-hidden">
              <span class="relative z-10">充值</span>
            </button>
            <button v-else @click="handlePay" :disabled="isSubmitting"
              class="px-16 py-3 bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 text-white rounded-xl text-base font-medium relative group overflow-hidden disabled:opacity-50 disabled:cursor-not-allowed">
              <span class="relative z-10 flex items-center justify-center">
                <loading v-if="isSubmitting" theme="outline" size="20" fill="#fff" class="animate-spin mr-2" />
                确认写作
              </span>
            </button>
          </div>
        </div>
      </ClientOnly>
    </div>

    <EssayExampleModal v-if="showExampleVisible" v-model:show-modal="showExampleVisible"
      :imageList="imageExampleList" />


  </div>
</template>

<script lang="ts" setup>
import { HTTP_STATUS } from '@/utils/constants';
import {
  FileEditing,
  Loading,
  Right
} from '@icon-park/vue-next';
import { message } from 'ant-design-vue';
import { storeToRefs } from 'pinia';
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { paySubmission, preparePay } from '~/api/create';
import { getBalance, saveActionLog } from '~/api/user';
import PopoverHelp from '~/components/Common/PopoverHelp.vue';
import AttachmentsTable from '~/components/Confirm/AttachmentsTable.vue';
import OrderInformation from '~/components/Create/Detail/OrderInformation.vue';
import EssayExampleModal from '~/components/Modal/EssayExampleModal.vue';
import { useTracking } from '~/composables/useTracking';
import type { combos, prices } from '~/services/types/submission';
import { UserService } from '~/services/user';
import { useRechargeStore } from '~/stores/recharge';
import { useSubmissionStore } from '~/stores/submission';
import { useUserStore } from '~/stores/user';
import { BindPhoneModal, MIN_WORDS_COUNT, SubmissionHelpStatus } from '~/utils/constants';
import { saveSubmissionDraft } from '~/utils/pc_utils';
import { StarloveConstants } from '~/utils/starloveConstants';
import { StarloveUtil } from '~/utils/util';
import { getPlatform, getWordCount } from '~/utils/utils';

interface FileIdInfo {
  fileId: string
}

interface SubmissionParams {
  submissionId: string;
  attachments?: Array<{ fileId: string }>;
  combos?: Array<{
    creatorCode: string;
    size: string;
  }>;
}

const { track } = useTracking();
const submissionStore = useSubmissionStore()
const { currentSubmission, currentCreator } = storeToRefs(submissionStore)

const { $eventBus } = useNuxtApp();

const rechargeStore = useRechargeStore()


const emit = defineEmits(['paySuccess'])

const router = useRouter()
const user = useUserStore()

const isAcceptInstallationApp = ref(false)

const selectLectureCoin = ref(0)
const literatureReviewRefs = ref()

const isNeedGetTheWordCount = ref(false)
const showExampleVisible = ref(false)
const fileIdList = ref<FileIdInfo[]>([])

const isTheFileHasBeenReadSuccess = ref(false)


let timerId: NodeJS.Timeout | undefined = undefined

const attachmentsList = ref(currentSubmission.value?.attachments || [])

const ids = computed(() => {
  const list: string[] = []
  attachmentsList.value.map((item) => {
    if (item.wordCount && item.wordCount != 0 && item.wordCount != -1) {
      list.push(item.id)
    }
  })
  return list
})

const attachmentsFieldName = computed(() => {
  const list = currentCreator.value?.details.filter((item) => item.fieldCode == 'references')
  if (!list || list?.length == 0) {
    if (currentCreator.value?.creator?.config?.isUseLargeWorker) {
      return '背景文档'
    }
    return currentSubmission.value?.creatorCode.includes('paper') ? '参考文献' : '背景文档'
  }
  return list[0].fieldName || '参考文献'
})

const selectedRowKeys = ref(ids.value)

const isUpgradeVip = computed(() => {

  if ((knowledgeAssistantMemberInfo.value?.vipLevel || 0) < 1) {
    return true
  }
  return false
})

const knowledgeAssistantMemberInfo = ref(UserService.getKnowledgeAssistantMemberInfo())

watchEffect(async () => {
  if (user.isAcceptInstallationApp) {
    isAcceptInstallationApp.value = true
  }

  if (rechargeStore.rechargeStatus == RECHARGE_STATUS.SUCCESS) {
    knowledgeAssistantMemberInfo.value = UserService.getKnowledgeAssistantMemberInfo()
    isNeedGetTheWordCount.value = false
  }
})

const currentCoinBalance = computed(() => {
  return user.currentLoginInfo?.coinBalance || 0
})

const expendTokens = computed(() => {
  if (fileIdList.value.length > 0 && !isNeedGetTheWordCount.value) {
    // console.log('currentSubmission.value ==>', currentSubmission.value?.attachments)
    if (!currentSubmission.value?.attachments?.length) {
      return currentSubmission.value?.creatorCoin || 0;
    }

    const total = currentSubmission.value.attachments.reduce((sum, item) => {
      const isSelected = fileIdList.value.some(file => file.fileId === item.id);
      return isSelected && typeof item.coinNum === 'number' ? sum + item.coinNum : sum;
    }, 0);

    return total + (currentSubmission.value?.creatorCoin || 0);
  }
  return currentSubmission.value?.tokens || 0;
})

const isRequireRecharge = computed(() => {
  if (!expendTokens.value) {
    return false
  }
  if (currentCoinBalance.value == undefined) {
    return false
  }
  if (currentSubmission.value?.helpStatus == SubmissionHelpStatus.successHelp) {
    return false
  }

  // 计算总消耗硬币，包含加购项（selectLectureCoin是以万硬币为单位，需要转换为硬币）
  const totalExpendTokens = expendTokens.value + (selectLectureCoin.value * 10000)

  if ((currentCoinBalance.value || 0) - totalExpendTokens < 0) {
    return true
  }

  return false
})


const getSelectedFileIdsAndAttachments = (params: any) => {
  if (params.list.length > 0) {
    fileIdList.value = params.list
  }
  if (params.attachmentsList.length > 0) {
    attachmentsList.value = params.attachmentsList
  }
  if (params.selectedRowKeys.length > 0) {
    selectedRowKeys.value = params.selectedRowKeys
  }
}

const expendMoney = computed(() => {
  const tokens: any = submissionTokens.value
  if (!tokens || tokens == 0) {
    return 0
  }
  const total = (Math.ceil(tokens * 0.5 * 10) / 10).toFixed(1)
  return `最低折合${total}元`
})

const footerTotal = computed(() => {
  if (knowledgeAssistantMemberInfo.value && knowledgeAssistantMemberInfo.value.vipLevel >= 1) {
    return 0
  }
  if (attachmentsList.value.length == 0) {
    return 0
  }
  let total = 0

  attachmentsList.value.map((item) => {
    const list = selectedRowKeys.value.filter((child) => child == item.id)
    if (list.length > 0) {
      total += studyingFees(item) //Math.ceil((item.coinNum / 10000) * 10) / 10
    }
  })
  return total
})

const creatorCoin = computed(() => {
  return (currentSubmission.value?.creatorCoin || 0) + (currentSubmission.value?.formData?.creatorLearnCoin || 0)
})

const isShowFreeTotal = computed(() => {
  if (currentSubmission.value?.attachments && currentSubmission.value?.attachments.length > 0) {
    return true
  }
  return false
})

// 计算总写作费用
const submissionTokens = computed(() => {
  let data = (currentSubmission.value?.tokens ?? 0) / 10000

  if (selectedRowKeys.value.length == 0) {
    return data + selectLectureCoin.value || 0
  }
  data = creatorCoin.value / 10000

  return (data + footerTotal.value + selectLectureCoin.value).toFixed(1) || 0
})

const isShowCallbackButton = computed(() => {
  if (!attachmentsList.value) {
    return false
  }
  const list = attachmentsList.value || []
  if (list.length == 0) {
    return false
  }
  const newList = list.filter((item) => item.wordCount < MIN_WORDS_COUNT)
  // console.log('newList.length == list.length ==>', newList.length == list.length)
  if (newList.length == list.length) {
    return true
  }
  return false
})

const fieldOutline = computed(() => {
  if (!currentCreator.value) {
    return []
  }
  return currentCreator.value?.details.filter((item) => item.fieldCode == 'outline')
})

const predictWordsNumber = computed(() => {
  const list = fieldOutline.value
  if (list.length == 0) {
    return 0
  }
  return list[0].maxLength || 0
})


const studyingFees = (record: any) => {
  const fees = Math.floor((record.coinNum / 10000) * 10) / 10
  if (fees <= 0) {
    return 0
  }
  return fees
}


const imageExampleList = computed(() => {
  if (!currentCreator.value) {
    return []
  }
  const list = currentCreator.value.details.filter((item) => item.fieldCode == 'example')
  if (list.length == 0 || !list[0].options) {
    return []
  }
  return list[0].options.split(',')
})



const hasParamValue = (code: string) => {
  if (!currentSubmission.value || !currentSubmission.value.formData || !currentSubmission.value.formData.params) {
    return false
  }
  if (!currentSubmission.value?.formData?.params?.[code]) {
    return false
  }
  const paramValue = `${currentSubmission.value.formData.params[code]}`
  if (paramValue.length == 0) {
    return false
  }
  return true
}



const handleShowExample = () => {
  showExampleVisible.value = true
}

// 添加 loading 状态变量
const isSubmitting = ref(false)

// 修改 handlePay 函数
const handlePay = async () => {
  if (!UserService.isLogined()) {
    track('create_confirm', currentSubmission.value?.id.toString() || '', '确认写作')
    return
  }
  if (!currentSubmission.value) {
    return
  }

  // 计算总消耗硬币，包含加购项（selectLectureCoin是以万硬币为单位，需要转换为硬币）
  const totalExpendTokens = expendTokens.value + (selectLectureCoin.value * 10000)

  if ((user.currentLoginInfo?.coinBalance || 0) - totalExpendTokens < 0) {
    rechargeStore.openRechargeModal(RechargeModalTab.coin, totalExpendTokens)
    return
  }

  isSubmitting.value = true

  try {
    const params: SubmissionParams = {
      submissionId: currentSubmission.value.id
    }
    if (currentSubmission.value.creatorCode == 'ppt') {
      if (currentSubmission.value.attachments) {
        const list = (currentSubmission.value.attachments || []).map((item) => {
          return {
            fileId: item.id
          }
        })
        params['attachments'] = list
      }
    }
    if (fileIdList.value.length > 0) {
      params['attachments'] = fileIdList.value
    }

    // console.log('params ==>', params)
    // 添加选中的加购项
    const selectedCombos = jiagouList.value
      .filter(item => item.isSelected)
      .map(item => ({
        creatorCode: item.creatorCode,
        size: item.selectedSize
      }))

    if (selectedCombos.length > 0) {
      params.combos = selectedCombos
    }

    const res = await paySubmission(params)
    if (!res.ok) {
      if (res?.code === HTTP_STATUS.MOBILE_NOT_BOUND) {
        user.setShowPhoneBoundModal({
          status: BindPhoneModal.SHOW_BINDING
        })
        return
      }
      message.error(res.message || '支付失败')
      return
    }
    saveUserActionLog('submission_confirm', '确认写作')

    deleteSubmissionDraft(currentSubmission.value?.creatorCode)

    // 更新个人版硬币余额
    const resultBalance = await getBalance()
    if (resultBalance.ok && user.currentLoginInfo) {
      user.currentLoginInfo.coinBalance = resultBalance.data
    }


    message.success('提交成功')
    emit('paySuccess')
    track('create_confirm', currentSubmission.value?.id.toString() || '', '确认写作')
  } finally {
    isSubmitting.value = false
  }
}


const saveUserActionLog = async (action: String, valueText: String) => {
  if (!UserService.getSelfUserId()) {
    return
  }
  if (!currentSubmission.value?.creatorCode.includes('paper')) {
    return
  }
  const params = {
    action: action,
    platform: getPlatform(),
    userId: UserService.getSelfUserId(),
    content: JSON.stringify({
      uid: UserService.getSelfUserId(),
      group: UserService.isLastDigitOfTheUserIDAnOddNumber() ? 'B' : 'A',
      creatorCode: currentSubmission.value?.creatorCode,
      time: StarloveUtil.formatTime(),
      value: valueText,
      source: currentSubmission.value?.creatorCode
    })
  }
  await saveActionLog(params)
}

const handleCallback = () => {
  saveDraft()
  router.replace(`/create/${currentSubmission.value?.creatorCode}`)
}

const handleOpenVipModal = () => {
  // 计算总消耗硬币，包含加购项
  const totalExpendTokens = expendTokens.value + (selectLectureCoin.value * 10000)

  rechargeStore.openRechargeModal(RechargeModalTab.vip, totalExpendTokens)
}


const saveDraft = async () => {

  saveSubmissionDraft(currentSubmission.value)
}
const jiagouList = ref<combos[]>([])

const getSubmissionAttachments = async () => {
  const res = await preparePay({ submissionId: currentSubmission.value?.id })
  if (!res.ok) {
    message.error(res.message || '获取附件失败')
    return
  }
  const list = res.data?.combos || []
  jiagouList.value = list.map((item: any) => {
    return {
      creatorCode: item.creatorCode,
      creatorName: item.creatorName,
      tag: item.tag,
      prices: item.prices,
      icon: item.icon,
      isSelected: false,
      selectedSize: item.creatorCode == 'paper-graduate' ? item.prices[1]?.size : item.prices[0]?.size
    }
  })
}


const handleSizeSelect = (item: combos, selectedSize: prices) => {
  item.selectedSize = selectedSize.size
}

const getSelectedSizeInfo = (item: combos): prices => {
  return item.prices.find(s => s.size === item.selectedSize) || item.prices[0] || { size: '', name: '', tokens: 0, originTokens: 0 }
}

// 加购选择
const handleJiagouChange = (item: combos) => {
  item.isSelected = !item.isSelected
  selectLectureCoin.value = Number(writingCost.value) - (creatorCoin.value / 10000)
}

// 加购选择字数
const handleSelectChange = (event: Event, item: combos) => {
  const selectElement = event.target as HTMLSelectElement;
  const selectedSize = selectElement.value;
  handleSizeSelect(item, item.prices.find(size => size.size === selectedSize) || item.prices[0]);

  if (item.isSelected) {
    selectLectureCoin.value = Number(writingCost.value) - (creatorCoin.value / 10000)
  }
}

// 组合节省的硬币数
const savedCoins = computed(() => {
  return (jiagouList.value
    .filter(item => item.isSelected)
    .reduce((total, item) => {
      const selectedSize = getSelectedSizeInfo(item)
      return total + ((selectedSize.originTokens || 0) - (selectedSize.tokens || 0))
    }, 0) / 10000).toFixed(1)
})

// 写作费用硬币数（基础写作费用 + 加购项）
const writingCost = computed(() => {
  const selectedCoins = jiagouList.value
    .filter(item => item.isSelected)
    .reduce((total, item) => {
      const selectedSize = getSelectedSizeInfo(item)
      return total + (selectedSize.tokens || 0)
    }, 0) / 10000

  return ((creatorCoin.value / 10000) + selectedCoins).toFixed(1)
})

onBeforeUnmount(() => {
  if (timerId) {
    clearTimeout(timerId)
  }
})

onMounted(async () => {
  $eventBus.on(StarloveConstants.keyOfEventBus.updateTheSubmissionAttachmentInformation, (value) => {
    isTheFileHasBeenReadSuccess.value = value
  })
  // 获取一下用户信息，用户在多端充值后，这边没有体现
  await UserService.loadUserInfoAndAssistantMemberInfo()

  knowledgeAssistantMemberInfo.value = UserService.getKnowledgeAssistantMemberInfo()

  // 获取加购项
  await getSubmissionAttachments()
})
</script>
<style lang="scss" scoped>
/* 保持与 paper.vue 相同的样式 */

.price {
  font-size: 12px;
  color: #999999;
  line-height: 19px;
  font-weight: 400;
}

.desktop-text {
  padding: 4px 10px;
  border-radius: 7px;
  border: 1px solid #ff4242;
  font-weight: 400;
  font-size: 14px;
  color: #ff4242;
  line-height: 21px;
  cursor: pointer;
  display: flex;
  align-items: center;
}


.example-title {
  text-align: center;
  font-size: 16px;
  font-weight: 400;
  color: #3681da;
  line-height: 25px;
  margin-bottom: 0;
  margin-top: 10px;
  cursor: pointer;
}

.vip-promotion {
  border: 1px solid #EF4444;
  padding: 2px 8px;
  border-radius: 4px;
  transition: all 0.2s;
}

.vip-promotion:hover {
  background-color: rgba(239, 68, 68, 0.05);
}
</style>