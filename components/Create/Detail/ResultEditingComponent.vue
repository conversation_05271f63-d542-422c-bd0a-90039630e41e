<template>
    <div class="flex h-screen flex-1">
        <!-- 左侧边栏 -->
        <div class="bg-white overflow-y-auto">
            <CreatePaperLeftPanel />
        </div>
        <div class="flex-1">
            <!-- 顶部操作栏 -->
            <div class="p-8 bg-white border-b border-gray-200 flex items-center justify-between">
                <div class="flex">
                    <button @click="router.back()"
                        class="mr-2 lg:mr-4 p-1.5 lg:p-2 hover:bg-gray-100 rounded-lg transition-colors">
                        <left-small theme="outline" size="24" fill="#666" />
                    </button>
                    <div class="flex items-center p-0 text-lg font-medium text-gray-800">
                        {{ currentSubmission?.formData.topic }}
                    </div>
                </div>
                <!-- 头像区域 -->
                <div class="flex items-center space-x-3 text-xs" style="color: #6d7ea5; font-size: 12px">
                    内容由AI生成，仅供参考，无版权风险
                </div>
            </div>

            <div class="flex justify-center items-center h-full pb-20"
                v-if="currentSubmission?.status === SubmissionStatus.done">
                <a-result status="success" title="转换完成" :sub-title="getFileName">
                    <template #extra>
                        <a style="margin-left: 10px" class="download-area-button" :href="getAiEditorUrl"
                            target="_blank">
                            <button
                                class="px-6 py-2.5 bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 text-white text-lg font-semibold rounded-md shadow-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition duration-200">
                                <!-- AI在线编辑 -->查看详情
                            </button></a>
                    </template>
                </a-result>
            </div>

            <div class="flex justify-center items-center h-full pb-20"
                v-if="currentSubmission?.status === SubmissionStatus.error">
                <a-result status="error" title="转换失败" :sub-title="getFileName">
                    <template #extra>
                        <a style="margin-left: 10px" class="download-area-button" @click="handleRetry">
                            <button
                                class="px-6 py-2.5 bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 text-white text-lg font-semibold rounded-md shadow-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition duration-200">
                                重试
                            </button></a>
                    </template>
                </a-result>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import CreatePaperLeftPanel from '@/components/Create/Paper/LeftPanel.vue'
import { Result as AResult } from 'ant-design-vue'
import { useRouter } from 'vue-router'
import { useApp } from '~/composables/useApp'
import { UserService } from '~/services/user'
import { useSubmissionStore } from '~/stores/submission'
import { SubmissionStatus } from '~/utils/constants'
import { StarloveUtil } from '~/utils/util'

import { LeftSmall } from '@icon-park/vue-next'

const router = useRouter()
const app = useApp()
const submissionStore = useSubmissionStore()
const { currentSubmission, currentCreator } = storeToRefs(submissionStore)
const getAiEditorUrl = computed(() => {
    if (!currentSubmission.value?.id) {
        return ''
    }
    if (app.value?.isDesktop) {
        return `${StarloveUtil.getAIEditorBaseUrl()}/?id=${currentSubmission.value?.id}&t=${UserService.getToken()}`
    }
    return `${StarloveUtil.getAIEditorBaseUrl()}/?id=${currentSubmission.value?.id}`
})
const getFileName = computed(() => {
    if (!currentSubmission.value) {
        return '';
    }
    if (!currentSubmission.value?.id) {
        return '';
    }
    if (!currentSubmission.value?.formData) {
        return '';
    }
    if (Array.isArray(currentSubmission.value?.formData.attachments) && currentSubmission.value?.formData.attachments.length > 0) {
        return currentSubmission.value?.formData.attachments[0]?.fileName || '';
    }
    return '';
})

const handleRetry = () => {
    router.push(`/create/${currentSubmission.value?.creatorCode}`)
}




</script>
