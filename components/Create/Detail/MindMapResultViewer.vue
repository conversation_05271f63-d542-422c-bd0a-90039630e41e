<template>
    <div class="flex-1">
        <ClientOnly class="h-full">

            <!-- 思维导图区域 -->
            <MindForKnowledge class="w-full h-full bg-white" :mindMapData="inspirationData" :ref="(el: any) => {
                if (el) simpleMindMapRef = el
            }" :title="currentSubmission?.formData.topic" :isReadOnly="false" :submissionId="currentSubmission?.id">
            </MindForKnowledge>


        </ClientOnly>
    </div>
</template>

<script setup lang="ts">
import MindForKnowledge from '@/components/Library/MindForKnowledge.vue'
import markdown from 'simple-mind-map/src/parse/markdown.js'
import { ref } from 'vue'
import type { Chapter } from '~/services/types/appMessage'
import { useSubmissionStore } from '~/stores/submission'
import { convertPListByMarkdownList, } from '~/utils/mind-map'

// const props = defineProps<{
//     currentMindmapType: MindmapAnswerType
// }>()

const submissionStore = useSubmissionStore()
const { currentSubmission, currentCreator } = storeToRefs(submissionStore)

const inspirationData = ref(null)
// const outlineTextareaRefs = ref()
const isShowSimpleMindMap = ref(false)


const simpleMindMapRef = ref()


// const saveMindmapContentLoaded = ref(false)

const outlineList = ref<Chapter[]>([])

const getInspirationData = () => {
    if (!currentSubmission.value) {
        return
    }

    let _inspiration_data = currentSubmission.value.answer

    try {
        const jsonMatch = JSON.parse(_inspiration_data)
        if (jsonMatch.content) {
            _inspiration_data = jsonMatch.content
        }
    } catch (e) { }

    // 然后处理 markdown 代码块
    const regex = /```markdown\n([\s\S]*?)\n```/
    _inspiration_data = _inspiration_data.replace(/\\n/g, '\n')
    const match = _inspiration_data.match(regex)
    if (match && match[1]) {
        _inspiration_data = match[1]
    }

    // console.log('_inspiration_data newdata 111==>', newdata)
    const dd = markdown.transformMarkdownTo(_inspiration_data)
    console.log('dd ==>', dd)
    inspirationData.value = dd
    outlineList.value = convertPListByMarkdownList(dd || [])
    isShowSimpleMindMap.value = true
}

// const onOutlineBlur = (list: any) => {
//     console.log('onOutlineBlur ==>', list)
//     if (list.length == 0) {
//         return
//     }
//     outlineList.value = formatOutlineTextContent(list)

//     updateAnswer()
// }

// const generateMarkdown = (data: any) => {
//     // console.log('generateMarkdown data ==>', data)
//     let markdown = '```markdown\n# ' + currentSubmission.value?.formData.topic + '\n'
//     data.forEach((chapter: { chapter_title: any; sections: any[] }) => {
//         markdown += `## ${chapter.chapter_title}\n\n`
//         chapter.sections.forEach((section: { section_title: any; nodes: any[] }) => {
//             markdown += `### ${section.section_title}\n`
//             section.nodes.forEach((node: { node_title: any }) => {
//                 markdown += `- ${node.node_title}\n`
//             })
//             markdown += '\n'
//         })
//     })
//     markdown += '\n```'
//     // console.log('generateMarkdown markdown ==>', markdown)

//     return markdown
// }


// const updateAnswer = async () => {
//     if (!UserService.isLogined()) {
//         return
//     }
//     try {
//         saveMindmapContentLoaded.value = true
//         const content = generateMarkdown(outlineList.value)
//         const params = {
//             submissionId: currentSubmission.value?.id,
//             content: content
//         }
//         // console.log('params ==>', params)
//         await updateAnswerContent(params)
//         submissionStore.loadSubmissionById(currentSubmission.value?.id || '')
//         const dd = markdown.transformMarkdownTo(content)
//         // // console.log('dd ==>', dd)
//         // // inspirationData.value = dd

//         console.log('outlineList.value ==>', outlineList.value)

//         if (simpleMindMapRef.value) {
//             simpleMindMapRef.value.renderMindMapData({
//                 children: outlineList.value,
//                 data: {
//                     text: currentSubmission.value?.formData.topic
//                 }
//             })
//         }

//         saveMindmapContentLoaded.value = false
//     } catch (error) {
//         saveMindmapContentLoaded.value = false
//     }
// }

// watch(() => props.currentMindmapType, (newVal, oldVal) => {
//     console.log('newVal ==>', oldVal)
//     if (oldVal == MindmapAnswerType.outline) {
//         // updateAnswer()
//     }
// })

onMounted(() => {
    // console.log('currentCreator ==>', currentCreator.value)

    getInspirationData()
})
</script>
