<template>
    <div class="flex-1 flex-col min-h-0 overflow-y-auto relative">
        <!-- 左翻页按钮 -->
        <button @click="prevPage"
            class="absolute left-4 top-1/2 -translate-y-1/2 bg-white rounded-full p-2 shadow-md hover:shadow-lg transition-shadow z-10">
            <left theme="outline" size="24" fill="#333" />
        </button>

        <div class="p-8 pb-16 h-full overflow-auto">
            <!-- PDF显示区域 -->
            <div class="flex justify-center items-center min-h-full">
                <div class="bg-white shadow-xl transform origin-center" :style="`scale(${zoom * 2 / 3})`">
                    <!-- 单页显示,16:9比例 -->
                    <div class="w-[960px] h-[540px] relative">
                        <div class="absolute flex items-center justify-center h-full w-full" v-if="isLoading">
                            <UProgress :value="progress" class="w-[80%] m-auto" />
                        </div>
                        <client-only>
                            <vue-pdf-embed :source="pdfSource" :page="currentPage" :scale="zoom" class="w-full h-full"
                                @rendered="onPageRendered" @loaded="handlePDFLoaded" @progress="onProgress"
                                @onLoading-failed="onLoadingFailed" v-if="!isError" />
                        </client-only>
                        <div class="absolute flex items-center justify-center h-full w-full" v-if="isError">加载失败</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右翻页按钮 -->
        <button @click="nextPage"
            class="absolute right-4 top-1/2 -translate-y-1/2 bg-white rounded-full p-2 shadow-md hover:shadow-lg transition-shadow z-10">
            <right theme="outline" size="24" fill="#333" />
        </button>
    </div>
</template>

<script setup lang="ts">
import { Left, Right } from '@icon-park/vue-next';
import { defineAsyncComponent, ref } from 'vue';

interface Props {
    pdfSource: string
    totalPages?: number
}

const props = withDefaults(defineProps<Props>(), {
    totalPages: 10
})

const VuePdfEmbed = defineAsyncComponent(() =>
    import('vue-pdf-embed')
)

const currentPage = ref(1)
const totalPages = ref(props.totalPages)
const zoom = ref(1)

// 加载进度
const progress = ref(0);
// 是否正在加载
const isLoading = ref(true);
const isError = ref(false);
// 处理进度更新事件
const onProgress = (event: any) => {
    if (event.loaded) {
        const percentComplete = (event.loaded / event.total) * 100;
        progress.value = Math.round(percentComplete);
    }
};

const handlePDFLoaded = (events: any) => {
    totalPages.value = events._pdfInfo.numPages
    setTimeout(() => {
        isLoading.value = false;
        progress.value = 100;
    }, 200);
}
const onLoadingFailed = () => {
    isLoading.value = false;
    isError.value = true
}

const onPageRendered = () => {
    console.log('onPageRendered')
}

const prevPage = () => {
    if (currentPage.value > 1) {
        currentPage.value--
    }
}

const nextPage = () => {
    if (currentPage.value < totalPages.value) {
        currentPage.value++
    }
}
</script>