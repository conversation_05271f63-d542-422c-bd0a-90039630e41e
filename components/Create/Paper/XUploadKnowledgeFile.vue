<template>
    <a-upload v-model:file-list="fileList" :before-upload="beforeUpload" :customRequest="httpUpload" list-type="text"
        :showUploadList="false" class="upload-list-inline" :accept="accept">
        <slot></slot>
    </a-upload>
</template>
<script lang="ts" setup>
import { addFiles, checkFileHash } from '@/api/repositoryFile.js';
import { generatePutUrl, uploadByUrl } from '@/api/upload';
import { getFileSha256 } from '@/utils/utils';
import { Upload as AUpload, message } from 'ant-design-vue';
import axios from 'axios';
import { watch } from 'vue';
import { UserService } from '~/services/user';
import { useUserStore } from '~/stores/user';
import { removeQuestionMarkText } from '~/utils/utils';

const props = defineProps({
    files: {
        type: Array,
        default: [],
    },
    pasteFileInfo: {
        type: Object as () => any,
        default: null
    },
    accept: {
        type: String,
        default: '.pdf,.docx,.doc,.ppt,.pptx,.txt,.md,.jpg,.png,.jpeg,.xlsx,.csv'
    },
    folderId: {
        type: String,
        default: '0'
    },
})
const emit = defineEmits(['update:files', 'ok'])
const fileList = computed({
    get: () => props.files,
    set: (val) => {
        emit('update:files', val)
    }
})
const triggerUpload = (file: any) => {
    const uploadOptions = {
        file: file.originFileObj,
        onSuccess: (response: any, _file) => {
            file.status = 'done'
            console.log(response, 'response', file)
            file.response = {
                ...response
            }
            _file.status = 'done'
            _file.response = {
                ...response
            }
            fileList.value = [{
                ..._file,
            }];
            // message.success('图片上传成功');
        },
        onProgress: (progressEvent: { percent: number }) => {
            file.percent = progressEvent.percent
        },
        onError: (error: any) => {
            file.status = 'error'
            // message.error('图片上传失败');
        }
    }
    httpUpload(uploadOptions)
}
watch(() => props.pasteFileInfo, (newVal) => {
    const _pasteFileInfo = newVal
    if (_pasteFileInfo && _pasteFileInfo.uid) {
        if (!accept.includes(_pasteFileInfo.name.split('.')[1])) {
            message.warning('不支持的图片格式')
            return
        }
        fileList.value.push(_pasteFileInfo)
        const isBeforeUpload = beforeUpload(_pasteFileInfo.originFileObj, 1)
        if (!isBeforeUpload) {
            return
        }
        triggerUpload(_pasteFileInfo)
    }

})

interface FileInfo {
    uid: string
    webkitRelativePath: string
    name: string
    size: number
    type: string
    status: string
    percent: number
    response: {
        fileUrl?: string
        fileId?: string
    },
    url: string
}
const beforeUpload = (file: any, _fileList: any) => {
    const reader = new FileReader();
    reader.onload = (e) => {
        // 获取 Base64 格式的结果并保存
        if (fileList.value.length) {
            fileList.value = [{
                ...fileList.value[0],
                url: e.target.result
            }];
        }
    };
    // 读取文件为 DataURL(Base64)
    reader.readAsDataURL(file);
    const isLt10M = file.size / 1024 / 1024 < 10
    if (!isLt10M) {
        message.error(`文件:${file.name}大小不能超过10M!`)
    }
    return isLt10M || AUpload.LIST_IGNORE
}
// const removeQuestionMarkText = (str: string) => {
//     return str.replace(/\?.*$/, '')
// }
const saveUploadByUrl = async (params: { fileName: string, fileUrl: string, fileSha256: string }) => {

    const res = await uploadByUrl({
        fileName: params.fileName,
        fileUrl: params.fileUrl,
        fileSha256: params.fileSha256
    })
    if (!res.success) {
        message.error(params.fileName + '上传失败')
        return null
    }

    const res1 = await addFiles({
        "spaceId": UserService.getSelfUserId(),
        "folderId": props.folderId,
        fileIds: [res.data.id]
    })

    if (res1?.code == HTTP_STATUS.MOBILE_NOT_BOUND) {
        const user = useUserStore()
        user.setShowPhoneBoundModal({
            status: BindPhoneModal.SHOW_BINDING,
        })
        return
    }
    emit('ok')
    return res.data
}
const handleUploadFileByFile = async (file: FileInfo, onProgress: any, onSuccess: any) => {
    try {
        // 文件需要上传
        const item = file
        console.log(item, 'item')

        const sha256 = await getFileSha256(item as any)
        const checkFileShaResult = await checkFileHash({
            sha256: `${sha256}`,
        });

        if (!checkFileShaResult.ok) {
            message.error(checkFileShaResult.message || '图片上传失败，请重试')
            return
        }

        if (checkFileShaResult.data != null) {
            const params = {
                fileName: checkFileShaResult.data.fileName,
                fileUrl: checkFileShaResult.data.fileUrl,
                fileSha256: checkFileShaResult.data.fileSha256
            }
            const fileData = await saveUploadByUrl(params)
            const _response = { fileUrl: params.fileUrl, fileId: fileData.id, name: file.name, status: 'done' }
            file.url = params.fileUrl
            onSuccess(_response, file)
            return
        } else {
            // console.log(fileList.value, 'fileList')
            const cosClientAndParams = await generatePutUrl({
                filename: item.name

            })
            // console.log('cosClientAndParams ==>', cosClientAndParams)
            if (!cosClientAndParams.ok || !cosClientAndParams.data) {
                throw new Error(cosClientAndParams?.message)
            }

            const response = await axios.put(cosClientAndParams.data.url, file, {
                headers: {
                    'Content-Type': cosClientAndParams.data.contentType
                },
                onUploadProgress: (progressEvent: any) => {
                    // 更新上传进度
                    if (progressEvent.total) {
                        const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
                        onProgress({ percent: percentCompleted });
                    }
                }
            });

            if (response.status !== 200) {
                throw new Error(`上传失败: ${response.status} ${response.statusText}`);
            }
            const params = {
                fileName: item.name,
                fileUrl: removeQuestionMarkText(cosClientAndParams.data.url),
                fileSha256: sha256
            }

            const fileData = await saveUploadByUrl(params)
            // 使用处理后的URL
            const _response = { fileUrl: params.fileUrl, fileId: fileData.id, name: item.name, status: 'done' }
            item.url = params.fileUrl
            onSuccess(_response, item)
            return
        }

    } catch (error: any) {
        console.log(error, 'error')
        message.error('文件上传失败，请重试')
    }
}


const httpUpload = async (data: { file: any; onProgress?: any; onSuccess?: any }) => {
    const { file, onProgress, onSuccess } = data

    setTimeout(() => {
        handleUploadFileByFile(file, onProgress, onSuccess)
    }, 300)
}
</script>