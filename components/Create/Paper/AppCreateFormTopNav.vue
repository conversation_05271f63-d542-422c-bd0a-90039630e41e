<template>
  <div class="flex-shrink-0 p-4 bg-gradient-to-r from-sky-50/100 via-indigo-50/100 to-blue-50/100">
    <div class="flex items-center justify-between" v-if="creator">
      <!-- 左侧标题部分 -->
      <div class="flex items-center">
        <div
          class="w-10 h-10 bg-gradient-to-br from-sky-100 to-indigo-100 rounded-lg flex items-center justify-center mr-3 shadow-inner">
          <file-editing theme="outline" size="24" fill="#3b82f6" />
        </div>
        <div>
          <h1
            class="text-lg font-bold bg-gradient-to-r from-sky-600 via-blue-600 to-indigo-600 bg-clip-text text-transparent">
            {{ creator.name }}
          </h1>
          <p class="text-xs text-gray-500 mt-0.5">{{ creator.description }}</p>
        </div>
      </div>
      <!-- 右侧区域 -->
      <div v-if="creator.code != 'book'"
        class="text-sm text-purple-700 mt-0.5 bg-purple-100/80 px-5 py-2.5 rounded-lg shadow-sm">
        写作质量大升级，已全面接入Deepseek R1满血版推理模型
      </div>
      <div v-else>
        <BookCreateStep :step="0"></BookCreateStep>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>

import { FileEditing } from '@icon-park/vue-next';
import type { CreatorsCategoryInfo } from '~/services/types/appMessage';

defineProps({
  creator: {
    type: Object as PropType<CreatorsCategoryInfo>,
    required: true
  }
})


</script>