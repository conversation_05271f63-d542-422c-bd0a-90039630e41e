<template><!-- 右侧边栏知识库文档列表 -->

    <div class="border-l border-gray-200 bg-white/80 backdrop-blur-sm flex flex-col shrink-0">
        <div class="relative group">

            <div class="relative flex justify-between items-center bg-gradient-to-r from-blue-50 to-indigo-50 p-3">
                <div class="flex items-center">
                    <div
                        class="w-10 h-10 bg-gradient-to-br from-blue-200 to-indigo-200 rounded-xl flex items-center justify-center mr-3 shadow-inner group-hover:scale-105 transition-transform duration-300">
                        <brain theme="outline" size="24" fill="#3B82F6" />
                    </div>
                    <div>
                        <h2
                            class="text-xl font-bold text-transparent bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text">
                            我的知识库</h2>
                        <p class="text-xs text-blue-600/90">My Knowledge Base</p>
                    </div>
                </div>
                <div class="flex items-center gap-2">
                    <button
                        class="relative bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-3 py-1.5 rounded-lg leading-none flex items-center justify-center hover:opacity-90 transition-opacity">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"
                            class="w-4 h-4 mr-1">
                            <path fill-rule="evenodd"
                                d="M12 3.75a.75.75 0 0 1 .75.75v6.75h6.75a.75.75 0 0 1 0 1.5h-6.75v6.75a.75.75 0 0 1-1.5 0v-6.75H4.5a.75.75 0 0 1 0-1.5h6.75V4.5a.75.75 0 0 1 .75-.75Z"
                                clip-rule="evenodd" />
                        </svg>
                        <span class="text-sm font-medium">添加</span>
                    </button>
                    <button
                        class="relative bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-3 py-1.5 rounded-lg leading-none flex items-center justify-center hover:opacity-90 transition-opacity">
                        <search theme="outline" size="16" fill="currentColor" />
                        <span class="text-sm font-medium ml-1">搜索</span>
                    </button>
                </div>
            </div>
        </div>

        <div class="flex border-b border-gray-200">
            <button v-for="tab in tabs" :key="tab.name" class="px-4 py-2 text-sm font-medium"
                :class="{ 'text-blue-600 border-b-2 border-blue-600': activeTab === tab.name }"
                @click="activeTab = tab.name">
                {{ tab.label }}
            </button>
        </div>

        <div class="flex-1 overflow-y-auto">
            <div v-show="activeTab === 'all'">
                <!-- 文件夹部分 -->
                <div class="p-4 space-y-3">
                    <div v-for="folder in folders" :key="folder.id"
                        class="p-3 bg-gray-50/70 rounded-lg hover:bg-gray-100/70 cursor-pointer group"
                        @click="openFolder(folder)">
                        <div class="flex items-center">
                            <folder-close theme="outline" size="18" fill="#6B7280"
                                class="mr-2 group-hover:fill-blue-600 transition duration-150" />
                            <span
                                class="text-sm text-gray-700 font-medium group-hover:text-blue-600 transition duration-150">{{
                                    folder.name }}</span>
                        </div>
                    </div>
                </div>

                <!-- 知识库文档列表 -->
                <div class="p-4 space-y-3">
                    <div v-for="doc in knowledgeList" :key="doc.id"
                        class="p-3 bg-gray-50/70 rounded-lg hover:bg-gray-100/70 group">
                        <div class="flex items-center">
                            <input type="checkbox"
                                class="form-checkbox h-4 w-4 text-blue-600 transition duration-150 ease-in-out mr-2" />
                            <component :is="getFileIcon(doc.type)" size="18" fill="#3B82F6"
                                class="mr-2 group-hover:fill-blue-600 transition duration-150" />
                            <h4
                                class="text-sm font-medium text-gray-700 truncate group-hover:text-blue-600 transition duration-150">
                                {{ doc.title }}</h4>
                        </div>
                        <div class="mt-2 space-y-2">
                            <div class="flex items-center text-xs text-gray-500">
                                <span>{{ doc.type }}</span>
                                <span class="mx-1">·</span>
                                <span>{{ doc.wordCount }}</span>
                                <span class="mx-1">·</span>
                                <span>{{ doc.date }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div v-show="activeTab === 'search'" class="flex flex-col h-full">
                <div class="p-4 border-b border-gray-200">


                    <button
                        class="w-full py-2 rounded-lg bg-blue-500 text-white text-sm font-medium hover:bg-blue-600 transition duration-300"
                        @click="searchByPaperTitle">
                        按论文题目搜索
                    </button>

                    <div class="flex gap-2 mb-4 mt-4">
                        <div class="relative flex-1">
                            <input type="text" v-model="searchKeyword" placeholder="输入关键字搜索"
                                class="w-full pl-3 pr-10 py-2 rounded-lg bg-gray-100 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                @keyup.enter="searchByKeyword">
                            <!-- <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                                <FolderSearch size="18" class="text-gray-400 cursor-pointer hover:text-blue-500 transition duration-300" @click="searchByKeyword"/>
                            </div> -->
                        </div>
                        <button
                            class="px-4 py-2 rounded-lg bg-blue-500 text-white text-sm font-medium hover:bg-blue-600 transition duration-300"
                            @click="searchByKeyword">
                            搜索
                        </button>
                    </div>
                </div>
                <div class="flex-1 overflow-y-auto p-4 space-y-3">
                    <div v-for="item in searchResults" :key="item.id"
                        class="p-3 bg-gray-50/70 rounded-lg hover:bg-gray-100/70 group">
                        <div class="flex items-center">
                            <input type="checkbox"
                                class="form-checkbox h-4 w-4 text-blue-600 transition duration-150 ease-in-out mr-2" />
                            <component :is="getFileIcon(item.type)" size="18" fill="#3B82F6"
                                class="mr-2 group-hover:fill-blue-600 transition duration-150" />
                            <h4
                                class="text-sm font-medium text-gray-700 truncate group-hover:text-blue-600 transition duration-150">
                                {{ item.title }}</h4>
                        </div>
                        <div class="mt-2 space-y-2">
                            <div class="flex items-center text-xs text-gray-500">
                                <span>{{ item.type }}</span>
                                <span class="mx-1">·</span>
                                <span>{{ item.wordCount }}</span>
                                <span class="mx-1">·</span>
                                <span>{{ item.date }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部加载更多按钮保持不变 -->
    </div>
</template>

<script setup>
import {
    Brain,
    FileText,
    FolderClose,
    Search
} from '@icon-park/vue-next'
import { ref } from 'vue'

const knowledgeList = ref([
    {
        id: 1,
        title: '人工智能在教育领域的应用研究',
        type: '学术论文',
        wordCount: '12000字',
        date: '2024-03-01'
    },
    {
        id: 2,
        title: '基于深度学习的自然语言处理技术研究',
        type: '学术论文',
        wordCount: '15000字',
        date: '2024-02-28'
    },
    {
        id: 3,
        title: '元宇宙术发展现状与趋势分析',
        type: '研究报告',
        wordCount: '8000字',
        date: '2024-02-25'
    },
    {
        id: 4,
        title: '区块链在供应链金融中的应用研究',
        type: '学术论文',
        wordCount: '10000字',
        date: '2024-02-20'
    },
    {
        id: 5,
        title: '智慧城市建设中的数据安全问题研究',
        type: '研究报告',
        wordCount: '9500字',
        date: '2024-02-15'
    },
    {
        id: 6,
        title: '新能源汽车行业发展趋势分析',
        type: '行业报告',
        wordCount: '11000字',
        date: '2024-02-10'
    },
    {
        id: 7,
        title: '中国数字经济发展研究报告',
        type: '研究报告',
        wordCount: '13000字',
        date: '2024-02-05'
    },
    {
        id: 8,
        title: '人工智能伦理问题探讨',
        type: '学术论文',
        wordCount: '9000字',
        date: '2024-01-30'
    },
    {
        id: 9,
        title: '大数据时代个人隐私保护研究',
        type: '学术论文',
        wordCount: '11500字',
        date: '2024-01-25'
    },
    {
        id: 10,
        title: '碳中和背景下绿色金融发展研究',
        type: '研究报告',
        wordCount: '14000字',
        date: '2024-01-20'
    }
])

const folders = ref([
    { id: 1, name: '学术论文' },
    { id: 2, name: '行业报告' },
    { id: 3, name: '研究报告' }
])

const openFolder = (folder) => {
    console.log(`进入文件夹: ${folder.name}`)
    // TODO: 实现进入文件夹逻辑
}

const tabs = [
    { name: 'all', label: '全部' },
    { name: 'search', label: '搜索结果' }
]

const activeTab = ref('all')

const searchResults = ref([
    {
        id: 1,
        title: '搜索结果1',
        type: '学术论文',
        wordCount: '8000字',
        date: '2024-04-01'
    },
    {
        id: 2,
        title: '搜索结果2',
        type: '行业报告',
        wordCount: '5000字',
        date: '2024-03-15'
    },
    {
        id: 3,
        title: '搜索结果3',
        type: '学术论文',
        wordCount: '10000字',
        date: '2024-03-10'
    },
    {
        id: 4,
        title: '搜索结果4',
        type: '研究报告',
        wordCount: '12000字',
        date: '2024-02-20'
    }
])

const getFileIcon = (type) => {
    switch (type) {
        case '学术论文':
        case '行业报告':
        case '研究报告':
            return FileText
        default:
            return FolderClose
    }
}

const searchKeyword = ref('')
const searchByTitle = ref(false)

const searchByKeyword = () => {
    console.log(`搜索关键字: ${searchKeyword.value}`)
    // TODO: 执行关键字搜索逻辑
}

const searchByPaperTitle = () => {
    // 这里假设论文题目存储在一个叫 paperTitle 的变量中
    searchKeyword.value = paperTitle
    // TODO: 执行按论文题目搜索逻辑,与关键字搜索逻辑可能有所不同
}
</script>