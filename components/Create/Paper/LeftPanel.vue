<template>
  <!-- 修改定位类，使用 sticky 定位 -->
  <div
    class="fixed md:sticky md:top-0 md:w-[240px] w-[280px] h-screen border-r border-gray-200 bg-white/80 backdrop-blur-sm flex flex-col transform transition-transform duration-300 z-30"
    :class="[isOpen ? 'translate-x-0' : '-translate-x-full md:translate-x-0']">

    <!-- 添加关闭按钮，仅在移动端显示 -->
    <button @click="toggleMenu" class="md:hidden absolute right-2 top-2 p-2 rounded-lg hover:bg-gray-100">
      <Close theme="outline" size="20" fill="#666" />
    </button>

    <!-- 顶部搜索框 -->
    <div class="p-4 space-y-3">
      <NuxtLink to="/create" class="block">
        <button
          class="w-full flex items-center justify-center px-4 py-2 text-sm text-blue-600 bg-blue-50 hover:bg-blue-100 rounded-xl border border-blue-200 transition-colors">
          <left theme="outline" size="16" fill="#2563eb" class="mr-2" />
          返回写作中心
        </button>
      </NuxtLink>
      <!-- 写作详情搜索区域 -->
      <div class="relative">
        <input v-model="searchQuery" type="text" placeholder="搜索其它写作类型"
          class="w-full pl-9 pr-4 py-2 text-sm bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:border-blue-300/50 focus:ring-1 focus:ring-blue-300/50"
          @keyup.enter="handleSearch" />
        <search theme="outline" size="18" fill="#9CA3AF" class="absolute left-3 top-1/2 -translate-y-1/2" />
      </div>
    </div>

    <!-- 最近使用 -->
    <RecentUsed :maxItems="3" />


    <!-- 推荐区域 -->
    <div class="border-gray-200 px-4 py-2" v-if="recommendsList.length > 0">
      <p class="text-gray-600 text-sm font-medium mb-2">你可能需要</p>
      <NuxtLink v-for="item in recommendsList" :key="item.code" :to="`/create/${item.code}`"
        class="flex items-center p-2 rounded-lg hover:bg-gray-50 cursor-pointer group">
        <div class="w-5 h-5 rounded-lg flex items-center justify-center mr-2 shrink-0">
          <img :src="item.avatar" class="w-5 h-5" />
        </div>
        <span class="text-sm text-gray-700">{{ item.name }}</span>
      </NuxtLink>
    </div>

    <!-- 新增最近写作记录部分 -->
    <div class="px-4 py-2 " v-if="recentlyCreatedList.length > 0">
      <div class="flex items-center justify-between mb-2">
        <h3 class="text-sm font-medium text-gray-600">最近写作</h3>
        <NuxtLink to="/create/history">
          <button class="text-xs text-blue-500 hover:text-blue-600 flex items-center">更多
            <right theme="outline" size="14" fill="currentColor" class="ml-0.5" />
          </button>
        </NuxtLink>
      </div>
      <div class="space-y-2">
        <div v-for="item in recentlyCreatedList" :key="item.id"
          class="p-2 bg-gray-50/70 rounded-lg hover:bg-gray-100/70 cursor-pointer">
          <NuxtLink :target="item.creatorCode === 'online_editing' ? '_blank' : ''"
            :to="item.creatorCode === 'online_editing' ? `${getAIEditorBaseUrl()}/?id=${item.id}&t=${app?.isDesktop ? UserService.getToken() : ''}` : `/create/detail?id=${item.id}&code=${item.creatorCode}`">
            <div class="flex items-center gap-2">
              <div class="flex-1 min-w-0">
                <h4 class="text-sm text-gray-800 font-medium truncate">{{ item.formData.topic ||
                  item.creatorName }}</h4>
                <div class="flex items-center text-xs text-gray-500 mt-0.5">
                </div>
              </div>
            </div>
          </NuxtLink>
        </div>
      </div>
    </div>
  </div>

  <!-- 添加遮罩层，仅在移动端菜单打开时显示 -->
  <div v-if="isOpen" @click="toggleMenu" class="fixed inset-0 bg-black/20 backdrop-blur-sm z-20 md:hidden">
  </div>
</template>

<script setup lang="ts">
import { getSubmissionLastDone } from '@/api/order';
import type { SubmissionOrderInfo } from '@/services/types/order';
import { UserService } from '@/services/user';
import { getAIEditorBaseUrl } from '@/utils/utils';
import {
  Close,
  Left,
  Right,
  Search,
} from '@icon-park/vue-next';
import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import { useApp } from '~/composables/useApp';
import { useSearchStore } from '~/stores/search';
import RecentUsed from './RecentUsed.vue';

const app = useApp()

const props = defineProps({
  creatorData: {
    type: Object,
    required: false
  }
})

const router = useRouter();
const searchStore = useSearchStore();
const searchQuery = ref('');
const recentlyCreatedList = ref<SubmissionOrderInfo[]>([]);

// 添加菜单开关状态
const isOpen = ref(false);

// 切换菜单方法
const toggleMenu = () => {
  isOpen.value = !isOpen.value;
};

// 处理搜索
const handleSearch = async () => {
  if (!searchQuery.value.trim()) return;

  // 更新 search store 中的查询
  searchStore.searchQuery = searchQuery.value;

  // 跳转到 create/index 页面
  await router.push('/create');

  // 触发搜索
  await searchStore.handleSearch();
};

const loadCreateRecords = async () => {
  if (!UserService.isLogined()) {
    return;
  }

  const res = await getSubmissionLastDone();
  if (!res.ok || !res.data) {
    return;
  }
  recentlyCreatedList.value = res.data.records || [];
};
const recommendsList = computed(() => {
  const list = props.creatorData?.recommends || []
  if (list.length == 0) {
    return []
  }
  return list.filter((item: any) => !!item).slice(0, 3)
})

onMounted(() => {

});

// 向父组件暴露方法
defineExpose({
  toggleMenu
});
</script>

<style scoped>
/* 防止菜单展开时页面滚动 */
:deep(body.menu-open) {
  overflow: hidden;
}
</style>
