<template>
    <div v-if="displayList.length > 0" class="px-4 py-2">
        <h3 class="text-sm font-medium text-gray-600 mb-2">最近使用</h3>
        <div class="space-y-1">
            <NuxtLink v-for="item in displayList" :key="item.code"
                :to="item.code == 'book' ? '/book' : `/create/${item.code}`"
                class="flex items-center p-2 rounded-lg hover:bg-gray-50 cursor-pointer group">
                <div class="w-5 h-5 rounded-lg flex items-center justify-center mr-2 shrink-0">
                    <img :src="item.avatar" class="w-5 h-5" />
                </div>
                <span class="text-sm text-gray-700">{{ item.name }}</span>
            </NuxtLink>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { computed, onMounted } from 'vue';
import { useRecentAppsStore } from '~/stores/recentApps';

const props = defineProps({
    maxItems: {
        type: Number,
        required: false
    }
})

const store = useRecentAppsStore()
const { recentList } = storeToRefs(store)

const displayList = computed(() => {
    if (!props.maxItems) return recentList.value
    return recentList.value.slice(0, props.maxItems)
})

onMounted(() => {
    store.loadFromServer()
})
</script>