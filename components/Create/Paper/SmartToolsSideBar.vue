<template>
  <div class="smart-tools-sidebar p-4">
    <!-- AI 工具标题 -->
    <div class="tools-header flex items-center mb-4">
      <Brain theme="filled" size="24" class="text-primary-600 mr-2"/>
      <h2 class="text-lg font-medium">AI 写作助手</h2>
    </div>

    <!-- AI 工具卡片列表 -->
    <div class="tools-list space-y-3">
      <!-- 标题优化工具 -->
      <div class="tool-card bg-white rounded-lg p-3 shadow-sm hover:shadow-md transition-shadow">
        <div class="flex items-center justify-between mb-2">
          <span class="font-medium">标题优化</span>
          <button class="btn-primary text-sm px-3 py-1 rounded">
            优化
          </button>
        </div>
        <p class="text-sm text-gray-600">智能分析标题，提供学术化表达建议</p>
      </div>

      <!-- 文献推荐工具 -->
      <div class="tool-card bg-white rounded-lg p-3 shadow-sm hover:shadow-md transition-shadow">
        <div class="flex items-center justify-between mb-2">
          <span class="font-medium">文献推荐</span>
          <button class="btn-primary text-sm px-3 py-1 rounded">
            查找
          </button>
        </div>
        <p class="text-sm text-gray-600">从知识库匹配相关参考文献</p>
      </div>

      <!-- 网络文献搜索 -->
      <div class="tool-card bg-white rounded-lg p-3 shadow-sm hover:shadow-md transition-shadow">
        <div class="flex items-center justify-between mb-2">
          <span class="font-medium">文献搜索</span>
          <button class="btn-primary text-sm px-3 py-1 rounded">
            搜索
          </button>
        </div>
        <p class="text-sm text-gray-600">从学术网站搜索最新研究文献</p>
      </div>

      <!-- 摘要生成工具 -->
      <div class="tool-card bg-white rounded-lg p-3 shadow-sm hover:shadow-md transition-shadow">
        <div class="flex items-center justify-between mb-2">
          <span class="font-medium">摘要生成</span>
          <button class="btn-primary text-sm px-3 py-1 rounded">
            生成
          </button>
        </div>
        <p class="text-sm text-gray-600">基于论文内容智能生成摘要</p>
      </div>

      <!-- 学术润色工具 -->
      <div class="tool-card bg-white rounded-lg p-3 shadow-sm hover:shadow-md transition-shadow">
        <div class="flex items-center justify-between mb-2">
          <span class="font-medium">学术润色</span>
          <button class="btn-primary text-sm px-3 py-1 rounded">
            润色
          </button>
        </div>
        <p class="text-sm text-gray-600">提升文章的学术性和专业度</p>
      </div>

      <!-- 格式检查工具 -->
      <div class="tool-card bg-white rounded-lg p-3 shadow-sm hover:shadow-md transition-shadow">
        <div class="flex items-center justify-between mb-2">
          <span class="font-medium">格式检查</span>
          <button class="btn-primary text-sm px-3 py-1 rounded">
            检查
          </button>
        </div>
        <p class="text-sm text-gray-600">检查引用格式与学术规范</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import {
    Search,
    FolderOpen,
    FolderBlockOne,
    FolderClose,
    Add,
    FileText,
    FolderSearch,
    Brain
} from '@icon-park/vue-next'
import { ref } from 'vue'

// 这里可以添加工具卡片的点击处理函数
</script>

<style scoped>
.smart-tools-sidebar {
  background-color: #f8fafc;
  border-left: 1px solid #e2e8f0;
  height: 100%;
}

.btn-primary {
  background-color: #4f46e5;
  color: white;
}

.btn-primary:hover {
  background-color: #4338ca;
}

.tool-card {
  border: 1px solid #e5e7eb;
}
</style>