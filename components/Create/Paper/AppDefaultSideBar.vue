<template>
  <div class="p-4" v-if="creatorData">
    <!-- 顶部图标和标题 -->
    <div class="flex items-center justify-center flex-col mb-6" v-if="creatorData.creator">
      <div class="w-16 h-16 bg-blue-50 rounded-2xl flex items-center justify-center mb-2">
        <!-- <FileText theme="outline" size="32" class="text-blue-500" /> -->
        <img :src="creatorData.creator.avatar || creatorData.creator.icon"
          :alt="altFn(creatorData.creator.code, creatorData.creator.name)"
          :title="altFn(creatorData.creator.code, creatorData.creator.name)"
          class="w-12 h-12 rounded-xl hover:shadow-xl transition-all duration-300 hover:scale-105" />
      </div>
      <h2 class="text-lg font-medium">{{ creatorData.creator.name }}</h2>
      <p class="text-sm text-gray-500 mt-1">{{ creatorData.creator.description }}</p>
    </div>
    <!-- 功能列表 -->
    <div class="bot-html-description" v-if="creatorData.creator.descriptionHtml">
      <div class="html" v-html="creatorData.creator.descriptionHtml"></div>
    </div>
  </div>
</template>

<script setup lang="ts">

const props = defineProps({
  creatorData: {
    type: Object,
    required: true
  }
})


const altFn = (code: string, title: string): string => {
  if (code === 'rewrite-document') {
    return '文档改写AI降重'
  }

  if (code === 'literature_review') {
    return `${title}AI`
  }

  const altCodes = [
    'paper', // 论文助手
    'thesis_report', // 论文开题报告
    'thesis_correction', //论文批改
    'proposal', // 论文大纲
    'book', // 万能小in AI智能写书软件
    'zhixie', // 论文致谢
    'task', // 论文任务书
    'paper_title', // 论文题目
    'abstract', // 论文摘要
    'paper_middle', // 论文中期报告
    'yiju', // 论文选题依据
  ]

  return altCodes.includes(code) ? `AI${title}` : title
}



onMounted(() => {
  // console.log("creatorData ==>", props.creatorData)
})

</script>

<style>
.bot-html-description {
  margin-top: 16px;
  margin-bottom: 32px;
  text-align: left;
  font-size: 14px;
  line-height: 1.5;
  display: flex;
  flex-wrap: wrap;

  .html {
    margin-top: 16px;
    width: 100%;
  }

  ul {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    padding-left: 0;
    list-style: none;
    gap: 12px;

    li {
      box-sizing: border-box;
      width: 100%;
      color: #374151;
      font-size: 14px;
      display: flex;
      align-items: center;
      word-break: break-word;
      white-space: normal;
      line-height: 1.5;

      &::before {
        content: "";
        display: block;
        flex-shrink: 0;
        width: 8px;
        height: 8px;
        background-color: #3B82F6;
        border-radius: 50%;
        margin-right: 8px;
        margin-top: 0;
      }
    }
  }
}
</style>