<template>
    <!-- 右侧产品特点和使用攻略 -->
    <div class="flex items-center gap-4">
            <!-- 产品特点 -->
            <div class="relative group">
              <button class="px-3 py-1.5 text-sm text-gray-600 hover:text-gray-800 flex items-center gap-1">
                产品特点
                <down theme="outline" size="14" fill="currentColor" />
              </button>
              <!-- 弹出的产品特点详情 -->
              <div
                class="hidden group-hover:block absolute right-0 top-full mt-2 w-72 bg-white rounded-xl shadow-lg border border-gray-100 p-4 z-10">
                <div class="space-y-3">
                  <div class="flex items-start gap-3">
                    <check-one theme="outline" size="18" fill="#10B981" class="mt-0.5 shrink-0" />
                    <span class="text-sm text-gray-600">封面、目录、摘要</span>
                  </div>
                  <div class="flex items-start gap-3">
                    <check-one theme="outline" size="18" fill="#10B981" class="mt-0.5 shrink-0" />
                    <span class="text-sm text-gray-600">正文、致谢、文献</span>
                  </div>
                  <div class="flex items-start gap-3">
                    <check-one theme="outline" size="18" fill="#10B981" class="mt-0.5 shrink-0" />
                    <span class="text-sm text-gray-600">知网查重率超20%免费重写</span>
                  </div>
                  <div class="flex items-start gap-3">
                    <check-one theme="outline" size="18" fill="#10B981" class="mt-0.5 shrink-0" />
                    <span class="text-sm text-gray-600">指定大纲支持三级大纲输入</span>
                  </div>
                  <div class="flex items-start gap-3">
                    <check-one theme="outline" size="18" fill="#10B981" class="mt-0.5 shrink-0" />
                    <span class="text-sm text-gray-600">优化语言，提升论文表达质量</span>
                  </div>
                  <div class="flex items-start gap-3">
                    <check-one theme="outline" size="18" fill="#10B981" class="mt-0.5 shrink-0" />
                    <span class="text-sm text-gray-600">格式规范，满足学术要求</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 使用攻略 -->
            <a href="#" class="px-3 py-1.5 text-sm text-gray-600 hover:text-gray-800 flex items-center gap-1">
              <book-open theme="outline" size="16" fill="currentColor" class="mr-1" />
              使用攻略
            </a>
          </div>
</template>

<script setup>

import {
  FileEditing, Help, Upload, Right, Search,
  Book, Write, Code, Video, Music, ActivitySource,
  CheckOne,
  BookOpen, Down, Add, Close,
  Crown, ChartLineArea, Cycle, Timer
} from '@icon-park/vue-next'

</script>