<template>
  <div class="flex-shrink-0 p-4 bg-gradient-to-r from-sky-50/100 via-indigo-50/100 to-blue-50/100">
    <div class="flex items-center justify-between">
      <!-- 左侧标题部分 -->
      <div class="flex items-center">
        <div
          class="w-10 h-10 bg-gradient-to-br from-sky-100 to-indigo-100 rounded-lg flex items-center justify-center mr-3 shadow-inner">
          <file-editing theme="outline" size="24" fill="#3b82f6" />
        </div>
        <div>
          <h1
            class="text-lg font-bold bg-gradient-to-r from-sky-600 via-blue-600 to-indigo-600 bg-clip-text text-transparent">
            专业论文
          </h1>
          <p class="text-xs text-gray-500 mt-0.5">几分钟数万字，格式规范，内容专业</p>
        </div>
      </div>

      <!-- 头像区域 -->
      <UserAvatar />

    </div>
  </div>
</template>

<script lang="ts" setup>
import { FileEditing } from '@icon-park/vue-next';
import UserAvatar from '~/components/Auth/UserAvatar.vue';
</script>