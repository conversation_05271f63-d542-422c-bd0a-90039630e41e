<template>
  <a-modal v-model:open="modelValue" :title="null" :width="1000" :footer="null" :closable="true" :centered="true"
    :bodyStyle="{ height: modalHeight + 'px' }" :destroyOnClose="true" @cancel="handleCancel">
    <div class="flex flex-col h-full">
      <!-- 头部标题区域 -->
      <div class="">
        <h3 class="text-lg font-medium text-gray-900">从知识库选择文档</h3>
        <p class="mt-1 text-sm text-gray-500">选择合适的文档作为论文参考资料</p>
      </div>

      <!-- 内容区域 -->
      <div class="flex flex-1 min-h-0">
        <!-- 左侧区域 -->
        <div class="w-[60%] flex flex-col min-h-0">
          <!-- 搜索框固定区域 -->
          <div class="pt-4 bg-white shrink-0">
            <div class="flex items-center">
              <div
                class="flex-1 flex items-center h-[43px] rounded-[10px] border border-[#2551B5] overflow-hidden mr-2">
                <div class="flex items-center flex-1 px-3">
                  <search theme="outline" size="18" fill="#2551B5" class="mr-2" />
                  <input type="text" placeholder="搜索文档标题、内容..."
                    class="w-full h-full text-sm bg-transparent border-none outline-none" v-model="searchQuery"
                    @keyup.enter="handleKnowledgeSearchFileData" />
                </div>
                <button
                  class="h-full px-4 bg-[#2551B5] text-white text-sm font-medium hover:bg-[#2551B5]/90 transition-colors"
                  @click="handleKnowledgeSearchFileData">
                  搜索
                </button>
              </div>
              <div class="pr-4">


                <XUploadKnowledgeFile :folder-id="currentFolderId" :files="knowledgefiles"
                  @ok="loadStoreKnowledgeFileData">
                  <a-tooltip placement="top" title="上传本地文件">
                    <button
                      class="h-full p-1 text-sm font-medium text-gray-500 transition-colors border border-gray-300 rounded-md hover:bg-gray-100">
                      <plus size="24" class="cursor-pointer" />
                    </button>
                  </a-tooltip>
                </XUploadKnowledgeFile>

              </div>
            </div>

            <!-- 面包屑 -->
            <Breadcrumb v-if="breadcrumbPaths.length > 1" :paths="breadcrumbPaths" @click="handleBreadcrumbClick"
              class="mt-2" />
          </div>

          <!-- 文件列表可滚动区域 -->
          <div class="flex-1 pr-2 mt-4 overflow-y-auto">
            <!-- Loading 状态 -->
            <div v-if="loading" class="absolute inset-0 z-10 flex items-center justify-center bg-white/80">
              <div class="flex flex-col items-center">
                <!-- <div class="w-8 h-8 border-b-2 border-blue-500 rounded-full animate-spin"></div> -->
                <a-spin :spinning="true" class="text-gray-600"></a-spin>
                <span class="mt-2 text-sm text-gray-500">加载中...</span>
              </div>
            </div>

            <!-- 空状态 -->
            <div class="flex items-center justify-center h-full" v-if="!loading && knowledgeFileOptions.length === 0">
              <EmptyState />
            </div>

            <!-- 文件列表 -->
            <template v-else>
              <div v-for="item in knowledgeFileOptions" :key="item.id" class="file-tree-item"
                :style="{ paddingLeft: `${item.level * 20}px` }">
                <div
                  class="flex items-center p-2 mb-2 rounded-lg cursor-pointer bg-gray-50/50 hover:bg-gray-100/80 group"
                  @click="toggleDocument(item)">
                  <!-- Checkbox -->
                  <a-checkbox :checked="isSelected(item.id)" :indeterminate="item.isFolder && isPartiallySelected(item)"
                    @change="(e) => handleCheckboxChange(e, item)" class="mr-2" @click.stop>
                  </a-checkbox>

                  <!-- 文件/文件夹图标 -->
                  <div class="flex items-center justify-center mx-2 rounded-lg w-7 h-7 bg-blue-50">
                    <img v-if="item.isFolder"
                      src="https://static-1256600262.file.myqcloud.com/xiaoin-h5/icons/knowledge-folder-icon.png"
                      class="w-4 h-4" />
                    <img v-else
                      src="https://static-1256600262.file.myqcloud.com/xiaoin-h5/icons/knowledge-file-icon.png"
                      class="w-4 h-4" />
                  </div>

                  <!-- 文件信息 -->
                  <div class="flex-1 min-w-0" @click="item.isFolder && toggleFolder(item)">
                    <h4 class="text-sm font-medium text-gray-700 truncate">{{ item.title }}</h4>
                  </div>

                  <!-- 文件夹右侧箭头 -->
                  <div v-if="item.isFolder" class="ml-2 transition-transform duration-200 transform"
                    :class="[item.isExpanded ? 'rotate-90' : '']">
                    <right theme="outline" size="16" fill="#6B7280" />
                  </div>
                </div>
              </div>
            </template>
          </div>

          <!-- 分页控件固定区域 -->
          <div v-if="isMobile" class="py-3 text-center bg-white shrink-0">
            <a-pagination v-model:current="knowledgeStore.currentPage" :total="knowledgeStore.total" show-less-items
              :showSizeChanger="false" simple @change="handlePageChange" />
          </div>
          <div v-else class="px-2 pb-3 bg-white shrink-0">
            <Pagination v-model:current="knowledgeStore.currentPage" :page-count="knowledgeStore.pageSize"
              :total="knowledgeStore.total" @change="handlePageChange"></Pagination>
          </div>
        </div>

        <!-- 右侧区域 -->
        <div class="w-[40%] flex flex-col min-h-0 border-l border-gray-200">
          <!-- 右侧标题固定区域 -->
          <div class="px-4 pt-4 bg-white shrink-0">
            <div class="flex items-center justify-between">
              <h3 class="text-sm font-medium text-gray-700">
                已选择文档 ({{ selectedDocs.length }}/{{ maxLength }})
              </h3>
              <button v-if="selectedDocs.length > 0" class="text-sm text-gray-500 hover:text-blue-500"
                @click="clearSelection">
                清空
              </button>
            </div>
          </div>

          <!-- 右侧已选列表可滚动区域 -->
          <div class="flex-1 px-4 mt-4 overflow-y-auto">
            <div v-if="selectedDocs.length === 0"
              class="flex flex-col items-center justify-center h-full text-gray-400">
              <inbox theme="outline" size="32" fill="currentColor" class="mb-2" />
              <span class="text-sm">暂未选择文档</span>
            </div>

            <div v-else class="space-y-2">
              <div v-for="doc in selectedDocsList" :key="doc.id"
                class="flex items-center p-3 rounded-lg bg-gray-50 group">
                <div class="flex items-center justify-center w-8 h-8 mr-3 rounded-lg bg-blue-50">
                  <img
                    src="https://static-1256600262.cos.ap-shanghai.myqcloud.com/xiaoin-h5/icons/knowledge-file-icon.png"
                    class="w-4 h-4" />
                </div>
                <div class="flex-1 min-w-0">
                  <h4 class="text-sm font-medium text-gray-700 truncate">{{ doc.title }}</h4>
                </div>
                <button class="ml-2 text-gray-400 opacity-0 group-hover:opacity-100 hover:text-red-500"
                  @click="removeSelected(doc.id)">
                  <delete theme="outline" size="16" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部区域 -->
      <div class="h-[60px] shrink-0 px-6 flex items-center justify-end space-x-4 border-t border-gray-200 bg-white">
        <a-button class="px-8" @click="handleCancel">取消</a-button>
        <a-button type="primary" class="px-8" @click="handleConfirmSelect">
          确认
        </a-button>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import Breadcrumb from '@/components/Common/Breadcrumb.vue';
import EmptyState from '@/components/EmptyState.vue';
import { Delete, Inbox, Plus, Right, Search } from '@icon-park/vue-next';
import { Button as AButton, Checkbox as ACheckbox, Modal as AModal, message } from 'ant-design-vue';
import { computed, onBeforeUnmount, onMounted, ref, watch } from 'vue';
import { useMobileDetection } from '~/composables/useMobileDetection';
import { useKnowledgeStore } from '~/stores/knowledgeStore';
import XUploadKnowledgeFile from './XUploadKnowledgeFile.vue';

const props = defineProps({

  appCode: {
    type: String,
    default: () => ''
  },
  selectedIds: {
    type: Array,
    default: () => []
  },
  options: {
    type: String,
    default: () => ''
  },
  maxLength: {
    type: Number,
    default: () => 20
  },
  isChat: {
    type: Boolean,
    default: () => false
  }
})

const emit = defineEmits(['update:modelValue', 'select'])

const { isMobile } = useMobileDetection()

const modelValue = ref(true)

const searchQuery = ref('')
const loading = ref(false)
// 使用 ref 来跟踪选中状态
const selectedDocs = ref([...props.selectedIds])
// 移除本地的 selectedFolderIds，使用 store 中的状态
// const selectedFolderIds = ref([]) //用于保存选中文件夹的id，只做展示
// 新增的状态和方法
const breadcrumbPaths = ref([{ id: 0, title: '知识库' }]);
const defaultParams = props.isChat ? { status: '' } : {}

const currentFolderId = ref('0')
const knowledgefiles = ref([])
const knowledgeFileOptions = ref([])

const knowledgeStore = useKnowledgeStore()

const screenHeight = ref(window.innerHeight); // 用于存储当前屏幕高度

const MODAL_PADDING = 80; // Modal 上下内边距总和
const MIN_HEIGHT = 500; // 最小高度
const MAX_HEIGHT = 780; // 最大高度

// 计算 Modal 高度
const modalHeight = computed(() => {
  const height = screenHeight.value - MODAL_PADDING;
  return Math.min(Math.max(height, MIN_HEIGHT), MAX_HEIGHT);
});

// 监听窗口大小变化
const updateScreenHeight = () => {
  screenHeight.value = window.innerHeight;
};

const handleKnowledgeSearchFileData = async () => {
  const keywords = searchQuery.value.trim();
  knowledgeStore.reqParams.keywords = keywords;
  knowledgeStore.isSearchMode = true;
  knowledgeStore.hasSearchKeyword = !!keywords;
  knowledgeStore.reqParams.fileNames = props.options

  knowledgeStore.total = 0
  knowledgeStore.totalPages = 0
  knowledgeStore.currentPage = 1

  if (!keywords) {
    // 如果搜索框为空，恢复到普通列表模式
    knowledgeStore.currentPage = 1;
    await loadStoreKnowledgeFileData();
    return;
  }

  loading.value = true;
  try {
    await knowledgeStore.loadKnowledgeSearchFileData();
    knowledgeFileOptions.value = knowledgeStore.knowledgeFileOptions;
  } catch (error) {
    message.error('搜索失败');
  } finally {
    loading.value = false;
  }
}


// 更新选择状态的方法
const toggleDocument = async (doc) => {
  if (doc.isFolder) {
    // 如果是文件夹，加载文件夹内容
    try {
      loading.value = true;
      // 更新面包屑路径
      breadcrumbPaths.value = [...breadcrumbPaths.value, { id: doc.id, title: doc.title }];
      currentFolderId.value = `${doc.id}`;
      await knowledgeStore.loadKnowledgeFileData(doc.id, defaultParams);
      knowledgeFileOptions.value = knowledgeStore.knowledgeFileOptions;
    } catch (error) {
      message.error('加载文件夹内容失败');
    } finally {
      loading.value = false;
    }
    return;
  }

  const index = selectedDocs.value.indexOf(doc.id);
  if (index === -1) {
    selectedDocs.value.push(doc.id);
  } else {
    selectedDocs.value.splice(index, 1);
  }
}

// 检查是否选中
const isSelected = (id) => {
  // 使用 store 中的 selectedFolderIds
  return [...selectedDocs.value, ...knowledgeStore.selectedFolderIds].includes(id)
}

// 监听父组件传入的 selectedIds 变化
watch(() => props.selectedIds, (newIds) => {
  selectedDocs.value = [...newIds]
}, { deep: true })

const handleClose = () => {
  emit('update:modelValue', false)
  knowledgeStore.resetData()
  // 重置面包屑路径
  breadcrumbPaths.value = [{ id: 0, title: '知识库' }];
  // 重置搜索相关状态
  searchQuery.value = '';
  knowledgeStore.isSearchMode = false;
  knowledgeStore.hasSearchKeyword = false;
  // 清空选中状态
  selectedDocs.value = [];
}

const handleCancel = () => {
  handleClose()
}

const handleConfirmSelect = () => {

  if (!props.isChat && selectedDocs.value.length == 0) {
    message.warning('请选择文档')
    return
  }
  const list = knowledgeStore.allKnowledgeFileOptions.filter(item => selectedDocs.value.includes(item.id))
  if (list.length > props.maxLength) {
    message.warning(`最大选择${props.maxLength}个文件`)
    return
  }
  emit('select', list)
  emit('update:modelValue', false)
}

const handlePageChange = async (page) => {
  // if (knowledgeStore.currentPage === page) return;
  // knowledgeStore.currentPage = page;
  await loadStoreKnowledgeFileData();
}

const loadStoreKnowledgeFileData = async () => {
  loading.value = true;
  try {
    const folderId = breadcrumbPaths.value[breadcrumbPaths.value.length - 1].id
    if (props.appCode == 'book') {
      knowledgeStore.reqParams.fileType = ''
    }
    currentFolderId.value = `${folderId}`
    await knowledgeStore.loadKnowledgeFileData(folderId, defaultParams);
    knowledgeFileOptions.value = knowledgeStore.knowledgeFileOptions;

    // console.log("knowledgeFileOptions 456 ==>", knowledgeFileOptions.value)
  } catch (error) {
    message.error('加载知识库列表失败');
  } finally {
    loading.value = false;
  }
}

// watchEffect(() => {
// if (!knowledgeStore.appCode) {
//   return
// }
// if (knowledgeStore.appCode != props.appCode) {
//   knowledgeStore.initKnowledgeFileData()
//   console.log("knowledgeStore.appCode != props.appCode");
//   loadStoreKnowledgeFileData()
// }
// })

onMounted(() => {
  window.addEventListener('resize', updateScreenHeight);
  updateScreenHeight(); // 初始化高度

  knowledgeStore.setAppCode(props.appCode)
  knowledgeStore.setOptions(props.options)


  if (knowledgeStore.knowledgeFileOptions.length === 0) {
    console.log("knowledgeStore.knowledgeFileOptions.length == 0");
    loadStoreKnowledgeFileData()
  } else {
    knowledgeFileOptions.value = knowledgeStore.knowledgeFileOptions
  }
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', updateScreenHeight);
})

// 新增的状态和方法
const selectedDocsList = computed(() => {
  const list = knowledgeStore.allKnowledgeFileOptions.filter(item => selectedDocs.value.includes(item.id))

  return list
})

const clearSelection = () => {
  selectedDocs.value = []
  // 清空 store 中的选中文件夹
  knowledgeStore.selectedFolderIds = []
}

const removeSelected = (id) => {
  const index = selectedDocs.value.indexOf(id)
  if (index > -1) {
    selectedDocs.value.splice(index, 1)
  }
}

const toggleFolder = (item) => {
  item.isExpanded = !item.isExpanded
}

const isPartiallySelected = (folder) => {
  if (!folder.children?.length) return false;
  const selectedChildren = folder.children.filter(child => isSelected(child.id));
  return selectedChildren.length > 0 && selectedChildren.length < folder.children.length;
};

const handleCheckboxChange = async (e, item) => {
  const checked = e.target.checked;

  if (item.isFolder) {
    loading.value = true;
    try {
      // 获取文件夹下所有文件
      const files = await knowledgeStore.listAllFiles(item.id);

      if (checked) {
        // 将文件夹下所有文件ID添加到选中列表
        const fileIds = files.map(file => file.id);
        // console.log("fileIds 456 ==>", fileIds)
        // 使用 store 的方法添加选中文件夹
        knowledgeStore.addSelectedFolder(item.id);
        selectedDocs.value = [...new Set([...selectedDocs.value, ...fileIds])];

        // 使用Map进行去重处理
        const uniqueFiles = new Map();
        // 先添加现有的文件
        knowledgeStore.allKnowledgeFileOptions.forEach(file => uniqueFiles.set(file.id, file));
        // 添加新的文件，如果ID相同会自动覆盖
        files.forEach(file => uniqueFiles.set(file.id, file));
        // 转换回数组
        knowledgeStore.allKnowledgeFileOptions = Array.from(uniqueFiles.values());
      } else {
        // 从选中列表中移除文件夹下所有文件ID
        const fileIds = files.map(file => file.id);
        // 使用 store 的方法移除选中文件夹
        knowledgeStore.removeSelectedFolder(item.id);
        selectedDocs.value = selectedDocs.value.filter(id => !fileIds.includes(id));
        knowledgeStore.allKnowledgeFileOptions = knowledgeStore.allKnowledgeFileOptions.filter(item => !fileIds.includes(item.id));
      }
    } catch (error) {
      message.error('获取文件夹内容失败');
    } finally {
      loading.value = false;
    }
  } else {
    // 如果是文件，直接切换选中状态
    if (checked && !selectedDocs.value.includes(item.id)) {
      selectedDocs.value.push(item.id);
    } else if (!checked) {
      const index = selectedDocs.value.indexOf(item.id);
      if (index > -1) {
        selectedDocs.value.splice(index, 1);
      }
    }
  }
};

const getAllChildrenIds = (folder) => {
  const ids = [folder.id];
  if (folder.children) {
    folder.children.forEach(child => {
      if (child.isFolder) {
        ids.push(...getAllChildrenIds(child));
      } else {
        ids.push(child.id);
      }
    });
  }
  return ids;
};

const handleBreadcrumbClick = async (item, index) => {

  try {
    loading.value = true;
    // 更新面包屑路径
    breadcrumbPaths.value = breadcrumbPaths.value.slice(0, index + 1);
    currentFolderId.value = `${item.id}`;
    await knowledgeStore.loadKnowledgeFileData(item.id, defaultParams);
    knowledgeFileOptions.value = knowledgeStore.knowledgeFileOptions;
  } catch (error) {
    message.error('加载文件夹内容失败');
  } finally {
    loading.value = false;
  }
}

// 更新 watch 监听
watch(() => searchQuery.value, (newValue) => {
  // 当输入框清空时，不自动触发搜索
  if (!newValue.trim()) {
    knowledgeStore.hasSearchKeyword = false;
  }
})
</script>

<style scoped>
/* 修改 ant-design-vue 的默认样式 */
:deep(.ant-modal-content) {
  padding: 0;
  overflow: hidden;
}

:deep(.ant-modal-body) {
  height: 100%;
}

/* 其他样式保持不变 */
.overflow-y-auto {
  scrollbar-width: thin;
  scrollbar-color: #E5E7EB transparent;
  -webkit-overflow-scrolling: touch;
}

.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: transparent;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background-color: #E5E7EB;
  border-radius: 3px;
}

/* 文件树项动画 */
.file-tree-item {
  transition: all 0.2s ease-in-out;
}

.file-tree-item:hover {
  transform: translateX(2px);
}

/* 优化checkbox样式 */
:deep(.ant-checkbox-wrapper) {
  font-size: 14px;
}

:deep(.ant-checkbox) {
  top: 0;
}

/* 优化hover效果 */
.group:hover .transform {
  opacity: 1;
}
</style>