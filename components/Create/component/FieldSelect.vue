<template>
    <div class="mb-6 ">
        <label class="flex items-center text-sm font-medium text-gray-700 mb-2">
            <span class="text-red-500 mr-1" v-if="fieldItem.isRequired == 'Y'">*</span>
            {{ fieldItem.fieldName }}
            <PopoverHelp :content="fieldItem.description" />
        </label>

        <select :value="modelValue" @change="handleChange"
            class="min-w-[200px]  px-4 py-2.5 text-sm bg-white border border-gray-200 rounded-xl focus:outline-none focus:border-blue-300/50 focus:ring-1 focus:ring-blue-300/50"
            style="appearance: none; background-image: url('data:image/svg+xml;charset=UTF-8,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%2216%22 height=%2216%22 viewBox=%220 0 16 16%22><path fill=%22%23000000%22 d=%22M4.293 5.293a1 1 0 011.414 0L8 7.586l2.293-2.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z%22/></svg>'); background-repeat: no-repeat; background-position: right 10px center; background-size: 18px;">
            <option value="" disabled>请选择</option>
            <option v-for="item in fieldItemList" :key="item.name" :value="item.name">
                {{ item.name }}
            </option>
        </select>

    </div>
</template>

<script lang="ts" setup>
import { computed, onMounted } from 'vue';
import PopoverHelp from '~/components/Common/PopoverHelp.vue';

const props = defineProps({
    fieldItem: {
        type: Object,
        required: true
    },
    modelValue: {
        type: String,
        required: true
    }
})

const emit = defineEmits(['update:modelValue'])

const handleChange = (event: Event) => {
    const target = event.target as HTMLSelectElement
    emit('update:modelValue', target.value)
}

// 计算属性获取选项列表
const fieldItemList = computed(() => {
    const item = props.fieldItem
    if (!item.options) {
        return []
    }
    const list = item.options.split(',')
    return list.map((child: string) => ({
        name: child.trim()
    }))
})

// 设置默认值
onMounted(() => {
    if (props.fieldItem.defaultValue && !props.modelValue) {
        emit('update:modelValue', props.fieldItem.defaultValue)
    }
})

</script>
