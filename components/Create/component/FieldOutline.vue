<template>
  <div class="outline-selector">
    <div class="mb-6 ">
      <!-- 表单字段名称区域 -->
      <label class="flex items-center text-sm font-medium text-gray-700 mb-2">
        <span class="text-red-500 mr-1" v-if="fieldItem.isRequired == 'Y'">*</span>
        {{ fieldItem.fieldName }}
        <span class="flex items-center">
          <PopoverHelp :content="fieldItem.description" />
        </span>
      </label>
      <div class="mb-4">
        <div class="flex gap-3 mb-4 max-w-md">
          <div v-for="item in outlineTypeList" :key="item.value" @click="handleOutlineTypeChange(item.value)" :class="[
            'flex-1 border rounded-lg p-2 cursor-pointer transition-colors text-center',
            outlineType === item.value ? 'border-blue-500 bg-blue-50/50' : 'border-gray-200',
          ]">
            <span class="text-sm" :class="outlineType === item.value ? 'text-blue-600' : 'text-gray-600'">{{ item.label
            }}</span>
          </div>
        </div>
      </div>
    </div>

    <div class="mb-6">
      <div class="mb-4">
        <div v-if="outlineType === CreateSubmissionAssistType.custom" class="p-4 bg-gray-50/70 rounded-xl">
          <OutlineExplain :fieldItem="fieldItem" :maxLevel="getMaxLevelByCreatorRequireFieldInfoList(fieldItem) || 1"
            :code="creatorData.creator.code" @playVideo="onPlayVideo">
          </OutlineExplain>

          <!-- <div class="mt-2">
            <button class="p-2 rounded-lg hover:bg-gray-100 text-gray-500 bg-blue-100 text-[14px]"
              :class="{ 'opacity-50 cursor-not-allowed': outlineGenerationCount >= 3, 'hover:bg-gray-100': outlineGenerationCount < 3 }"
              @click="handleOutlineButtonClick">
              自由输入
            </button>
          </div> -->

          <OutlineTextarea :ref="(el) => { if (el) outlineTextareaRefs = el }"
            :maxLevel="getMaxLevelByCreatorRequireFieldInfoList(fieldItem) || 1" :fieldOutline="fieldItem"
            :isHighlight="isHighlight" :outlineList="outlineList"
            :notDisplayTheWordCountOfTheOutline="creatorData.creator.code == CreateContentType.information"
            @onBlur="onOutlineBlur">
          </OutlineTextarea>

        </div>
      </div>
    </div>

    <OutlineVideoTutorialModal v-model="showOutlineVideoModal" :maxLevel="maxLevel"></OutlineVideoTutorialModal>

    <OutlineModal></OutlineModal>
  </div>
</template>

<script lang="ts" setup>
import OutlineModal from '@/components/Create/component/OutlineModal.vue'
import { useOutlineModalStore } from '@/stores/outlineModal'
import { CreateSubmissionAssistType } from '@/utils/constants'
import { message, Modal } from 'ant-design-vue'
import { ref } from 'vue'
import { loadOutlineData } from '~/api/create'
import PopoverHelp from '~/components/Common/PopoverHelp.vue'
import type { CreatorRequireFieldInfo } from '~/services/types/appMessage'
import { CreateContentType } from '~/utils/constants'
import { convertChapterData } from '~/utils/outline'
import { getMaxLevelByCreatorRequireFieldInfoList } from '~/utils/utils'
import OutlineExplain from './OutlineExplain.vue'
import OutlineVideoTutorialModal from './OutlineVideoTutorialModal.vue'

const props = defineProps({
  creatorData: {
    type: Object,
    required: true,
  },
  fieldItem: {
    type: Object as PropType<CreatorRequireFieldInfo>,
    // type: Object,
    required: true,
  },
  outlineType: {
    type: String,
    required: true,
  },
  isHighlight: {
    type: Boolean,
    required: false
  }
})

const emit = defineEmits(['update:outlineType', 'onOutlineBlur'])
const outlineType = computed({
  get: () => props.outlineType,
  set: (val) => {
    emit('update:outlineType', val)
  }
})

// const outlineGenerationCount = ref(Number(storage.get(StarloveConstants.keyOflocalStorage.outlineGenerationCount, 0)))
const outlineTextareaRefs = ref()
const pListValue = ref<Array<any>>([])
const outlineTypeList = computed(() => {
  // if (props.creatorData.creator.code == 'book') {
  //   const data = props.fieldItem.options?.split(',')
  //   return data.map((item, index) => {
  //     return {
  //       label: item,
  //       value: index == 0 ? CreateSubmissionAssistType.ai : CreateSubmissionAssistType.custom,
  //     }
  //   })
  // }
  return [
    {
      label: 'AI智能',
      value: CreateSubmissionAssistType.ai,
    },
    {
      label: '指定大纲',
      value: CreateSubmissionAssistType.custom,
    },
  ]
})

const showOutlineVideoModal = ref(false)
const maxLevel = ref(getMaxLevelByCreatorRequireFieldInfoList(props.fieldItem) || 1)

const outlineList = ref(props.fieldItem?.againEditOutlineList || [])

const onOutlineBlur = (list: any[], sizeLength: number) => {
  if (list.length == 0) {
    return
  }
  pListValue.value = list
  emit('onOutlineBlur', list, sizeLength)
}

const onPlayVideo = (level: number) => {
  showOutlineVideoModal.value = true
  maxLevel.value = level
}


// const outlineTree = ref<any[]>([
//   {
//     level: 1,
//     name: '',
//     // children: [{
//     //   level: 2,
//     //   name: '',
//     //   children: [{
//     //     level: 3,
//     //     name: '',
//     //     children: [],
//     //   }],
//     // }],
//   }
// ])
// const version = ref(0)
// const currentFocus = ref()

// const predictWordsNumber = computed(() => {
//   // const list = fieldOutline.value
//   if (!props.fieldItem) {
//     return 0
//   }
//   return props.fieldItem.maxLength || 0
// })


// const singleChapterWordsNumber = computed(() => {
//   if (!props.fieldItem) {
//     return 500
//   }
//   const count = props.fieldItem.nodeWordCount || '500'
//   return parseInt(count)
// })

// const chapterWordsNumber = ref(predictWordsNumber.value + singleChapterWordsNumber.value)

// // 使用OutlineModal store
const outlineModalStore = useOutlineModalStore()

// watch(() => outlineType.value, (newVal) => {
// console.log("newVal ==>", newVal)
// if (newVal === CreateSubmissionAssistType.custom) {
//   // 使用store打开模态框
//   outlineModalStore.openModal('', handleOk, handleCancel)
// } else {
//   if (outlineList.value.length == 0) {
//     outlineModalStore.closeModal()
//     return
//   }
//   Modal.confirm({
//     title: '切换至AI智能会清空当前指定大纲的内容，确定切换吗？',
//     onOk: () => {
//       outlineList.value = []
//       outlineType.value = CreateSubmissionAssistType.ai
//     },
//     onCancel: () => {
//       outlineType.value = CreateSubmissionAssistType.custom
//     },
//   })
// }
// })

const handleOutlineTypeChange = (val: string) => {

  if (val == CreateSubmissionAssistType.custom) {
    if (outlineType.value == CreateSubmissionAssistType.custom) {
      return
    }
    // 使用store打开模态框
    outlineModalStore.openModal('', handleOk, handleCancel)
  } else {
    if (outlineList.value.length == 0) {
      outlineType.value = val
      return
    }
    Modal.confirm({
      title: '切换至AI智能会清空当前指定大纲的内容，确定切换吗？',
      okText: '切换',
      cancelText: '取消',
      onOk: () => {
        outlineList.value = []
        outlineType.value = CreateSubmissionAssistType.ai
        const draftData = readSubmissionDraft(props.creatorData.creator.code)
        if (!draftData) return
        if (draftData.formData.userOutline) {
          draftData.formData.userOutline = ''
          saveSubmissionDraft({ ...draftData, creatorCode: props.creatorData.creator.code })
        }
      },
      onCancel: () => {
        outlineType.value = CreateSubmissionAssistType.custom
      },
    })
  }

}

const handleOk = async () => {
  try {
    outlineModalStore.setLoading(true)

    const params = {
      keywords: outlineModalStore.content,
      creatorName: props.creatorData.creator.name,
      category: maxLevel.value == 1 ? CreateOutlineCategory.wordOutline1 : maxLevel.value == 2 ? CreateOutlineCategory.wordOutline2 : CreateOutlineCategory.wordOutline3
    }

    const res = await loadOutlineData(params)

    if (!res.ok || !res.data) {
      message.error(res.message || '大纲润色失败，请重试')
      return
    }
    outlineType.value = CreateSubmissionAssistType.custom
    outlineList.value = convertChapterData(res.data.chapter_titles || [], maxLevel.value)

    // outlineGenerationCount.value++

    // // 将生成次数保存到本地存储
    // storage.set(StarloveConstants.keyOflocalStorage.outlineGenerationCount, outlineGenerationCount.value)

    // 关闭模态框 并上传大纲
    outlineModalStore.closeModal()

    if (outlineTextareaRefs.value) {
      outlineTextareaRefs.value.currentOutlineList = []
      // outlineTextareaRefs.value.clearDivContent()
      setTimeout(() => {
        // outlineTextareaRefs.value.currentOutlineList = outlineList.value
        outlineTextareaRefs.value.uploadOutlineList(outlineList.value)
      }, 500)

    }

    message.success('大纲生成成功')
  } catch (error) {
    console.error('生成大纲失败:', error)
    message.error('大纲润色失败，请重试')
  } finally {
    outlineModalStore.setLoading(false)
  }
}

const handleCancel = () => {
  outlineModalStore.closeModal()
}

// const chapterWordsNumberChange = () => {
//   let lv1Number = 0
//   let lv2Number = 0
//   let lv3Number = 0
//   chapterWordsNumber.value = predictWordsNumber.value

//   if (outlineTree.value.length == 0) {
//     return 0
//   }
//   lv1Number = outlineTree.value.length
//   outlineTree.value.map((item) => {
//     if (item.children && item.children.length > 0) {
//       lv2Number += item.children.length - 1
//       item.children.map((child: any) => {
//         if (child.children && child.children.length > 0) {
//           lv3Number += child.children.length - 1
//         }
//       })
//     }
//   })
//   chapterWordsNumber.value = predictWordsNumber.value + (lv1Number + lv2Number + lv3Number) * singleChapterWordsNumber.value
// }


// const update = () => {
//   let outlineMd = ''
//   // outlineMd += '# ' + topic.value + '\n'
//   outlineMd += appendMd(outlineTree.value)
//   version.value++

//   chapterWordsNumberChange()
// }

// function appendMd(children: any) {
//   let str = ''
//   for (let i = 0; i < children.length; i++) {
//     let level = children[i].level
//     if (level == 0) {
//       str += '- '
//     } else {
//       for (let j = 0; j < level; j++) {
//         str += '#'
//       }
//       str += ' '
//     }
//     str += children[i].name + '\n'
//     if (children[i].children) {
//       str += appendMd(children[i].children)
//     }
//   }
//   return str
// }

// const operate = (children: any, idx: number, type: number) => {
//   let current = children[idx]
//   if (type == 1) {
//     if (idx == children.length) {
//       children.push({
//         level: 1,
//         name: '',
//         children: [],
//       })
//     } else {
//       children.splice(idx, 0, {
//         level: current.level,
//         name: '',
//         children: [],
//       })
//     }
//   } else if (type == 2) {
//     ; (current.children = current.children || []).splice(0, 0, {
//       level: current.level + 1,
//       name: '',
//       children: [],
//     })
//   } else if (type == 3) {
//     // 删除
//     children.splice(idx, 1)
//   }
//   update()
// }

// const onFocus = (value: any) => {
//   currentFocus.value = value
// }
// const onBlur = () => {
//   currentFocus.value = ''
// }

// const getOutlineAndCustomSizeLength = () => {
//   return {
//     outlineList: transformToUserOutline(outlineTree.value),
//     customSizeLength: chapterWordsNumber.value,
//   }
// }

// const copyOutline = async () => {
//   if (outlineTree.value.length === 0) {
//     message.warning('大纲内容为空，无法复制')
//     return
//   }

//   let result = ''

//   // 遍历大纲树，生成格式化的文本
//   outlineTree.value.forEach((chapter, chapterIndex) => {
//     // 第一级：章标题
//     result += `${chapter.name}\n`

//     // 第二级：节标题
//     if (chapter.children && chapter.children.length > 0) {
//       chapter.children.forEach((section: TreeNode) => {
//         result += `  ${section.name}\n`

//         // 第三级：小节标题
//         if (section.children && section.children.length > 0) {
//           section.children.forEach((node: TreeNode) => {
//             result += `    ${node.name}\n`
//           })
//         }
//       })
//     }

//     // 章与章之间添加空行
//     if (chapterIndex < outlineTree.value.length - 1) {
//       result += '\n'
//     }
//   })
//   if (!result || result.length == 0 || result.trim() == '') {
//     return
//   }
//   // 复制到剪贴板
//   try {
//     copyToClipboard(result)
//     message.success('大纲已复制到剪贴板')
//   } catch (error) {
//     // 如果navigator.clipboard不可用，使用备用方法
//     // fallbackCopyTextToClipboard(result)
//   }
// }


// const handleOutlineButtonClick = () => {
//   if (outlineGenerationCount.value >= 3) {
//     // message.warning('您已达到大纲生成次数上限（3次）')
//     return
//   }
//   // 使用store打开模态框
//   outlineModalStore.openModal('', handleOk, handleCancel)
// }

onMounted(() => {
  // 组件初始化时清除大纲生成次数
  // storage.remove(StarloveConstants.keyOflocalStorage.outlineGenerationCount)
  // outlineGenerationCount.value = 0

  // outlineList.value = props.fieldItem?.againEditOutlineList || []

  watch(() => props.fieldItem?.againEditOutlineList, (newVal: any) => {
    outlineList.value = newVal
  })
  // console.log("outlineTextareaRefs.value ==>", outlineTextareaRefs.value)
})

onBeforeUnmount(() => {
  // storage.remove(StarloveConstants.keyOflocalStorage.outlineGenerationCount)
})

defineExpose({
  onOutlineBlur,
  // getOutlineAndCustomSizeLength
})
</script>