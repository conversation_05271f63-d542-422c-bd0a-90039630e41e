<template>
    <div>
        <div class="title-area" style="margin-bottom: 10px" v-if="props.maxLevel > 1">
            <div class="title">{{ currentParagraphTitle }}</div>
            <div class="title-button">
                <div class="level" @click="handleUpdataLevel(1)" :class="{ active: currentTitleLevel == 1 }">
                    一级标题
                </div>
                <div v-if="props.maxLevel > 1" class="level" style="margin-left: 10px"
                    :class="{ active: currentTitleLevel == 2 }" @click="handleUpdataLevel(2)">
                    二级标题
                </div>
                <div v-if="props.maxLevel > 2" class="level" style="margin-left: 10px"
                    :class="{ active: currentTitleLevel == 3 }" @click="handleUpdataLevel(3)">
                    三级标题
                </div>
            </div>
        </div>
        <a-spin :spinning="isShowOutlineTextLoaded">
            <div class="textarea">
                <div class="outline" id="outline" :empty="`${empty}`" :placeholder="outlinePlaceholder"
                    ref="contentDivRef" contenteditable="true" style="caret-color: blue" @paste="onPaste"
                    @keydown="handleKeyDown" @keyup="handleKeyUp" @input="onInput" @blur="onBlur" @click="handelClick">
                    <p class="outline-content-paragraph outline-lv1"></p>
                </div>
            </div>
        </a-spin>
    </div>
</template>

<script setup lang="ts">
import type { Chapter, Section } from '@/services/types/appMessage';
import { arabicNumberToChinese, chineseNumberToArabic } from '@/utils/utils';
import { computed, nextTick, onBeforeUnmount, onMounted, onUnmounted, ref, watch } from 'vue';
interface Props {
    outlineList?: Chapter[]
    isH5?: boolean
    maxLevel: number
    placeholder: string
}
const props = withDefaults(defineProps<Props>(), {})

const contentDivRef = ref()
const contentTextOutline = ref()

const currentOldParagraph = ref<HTMLElement | null>(null)

const empty = ref(true)

const isShowOutlineTextLoaded = ref(false)

const emit = defineEmits(['onBlur'])

const oldParagraph = ref()

const outlinePlaceholder = computed(() => {
    if (!props.placeholder) {
        return '请输入大纲内容，并按大纲格式进行编辑修改'
    }
    const data = props.placeholder.replace(/\\n/g, '\n')
    return data
})

const paragraphTitle = () => {
    if (empty.value) {
        return
    }
    const data = oldParagraph.value
    if (!data) {
        return
    }
    const datalvt = data.getAttribute('data-lvt')
    if (!datalvt) {
        return
    }
    const content = `${datalvt}：${data.textContent}`
    console.log('current Title content ==>', content)
    return content
}

const titleLevel = () => {
    if (empty.value) {
        return 0
    }
    const data = oldParagraph.value
    if (!data) {
        return 0
    }
    const datalv = data.getAttribute('data-lv')
    if (!datalv) {
        return 0
    }
    if (parseInt(datalv) == 1) {
        return 1
    }
    if (parseInt(datalv) == 2) {
        return 2
    }
    if (parseInt(datalv) == 3) {
        return 3
    }
    return 0
}

const findParagraph = (node: Node | null): HTMLParagraphElement | null => {
    // 递归查找包含p标签的父节点
    if (node) {
        if (
            node.nodeType === Node.ELEMENT_NODE &&
            (node as HTMLElement).tagName.toLowerCase() === 'p'
        ) {
            return node as HTMLParagraphElement
        } else {
            return findParagraph(node?.parentNode)
        }
    }
    return null
}

const currentParagraphTitle = ref(paragraphTitle())
const currentTitleLevel = ref(titleLevel())

const onBlur = () => {
    const pList = getContentDivAllPTagAndContent()
    const currentP = getCurrentParagraph()
    if (currentP) {
        oldParagraph.value = currentP
        currentParagraphTitle.value = paragraphTitle()
        currentTitleLevel.value = titleLevel()
    }
    emit('onBlur', pList)
}

const handelClick = () => {
    if (empty.value) {
        return
    }
    const currentP = getCurrentParagraph()
    if (!currentP) {
        return
    }
    if (currentP.textContent?.trim().length == 0) {
        return
    }
    oldParagraph.value = currentP
    currentParagraphTitle.value = paragraphTitle()
    currentTitleLevel.value = titleLevel()
}

const handleUpdataLevel = (level: number) => {
    if (level == 1) {
        updataTitleToLevel1()
    } else {
        updataTitleToLevel2(level)
    }
}

const onPaste = (event: ClipboardEvent) => {
    event.preventDefault() // 阻止默认粘贴

    if (event.clipboardData) {
        let text = event.clipboardData.getData('text/plain') // 获取纯文本
        text = text.trim() // 去除首尾空格

        const pList = getContentDivAllPTagAndContent()
        const currentP = getCurrentParagraph()
        let currentPIndex = 0

        if (currentP) {
            pList.forEach((element: HTMLElement, index: any) => {
                if (element.getAttribute('data-lvl') == currentP.getAttribute('data-lvl')) {
                    currentPIndex = index + 1
                }
            })
        }

        text.split(/\n+/).map((para, index) => {
            // 当输入框没有任何内容时，需要现有一个p标签，否则伪元素不显示
            if (currentP && !currentP.getAttribute('data-lvt')) {
                currentP.setAttribute('data-lvt', `第一章`)
                currentP.setAttribute('data-lv', `1`)
                currentP.setAttribute('data-id', new Date().getTime().toString())
                currentP.setAttribute('data-lvl', `${index + 1}`)
                currentP.textContent = para
                // currentP.focus()
            } else {
                const selection = window.getSelection()

                let newParagraph;
                if (currentP) {
                    newParagraph = currentP.cloneNode(true)
                } else {
                    if (pList && pList.length > 0) {
                        newParagraph = pList[0].cloneNode(true)
                    }
                }
                // 判断粘贴时，如果有焦点，且焦点行有文字时，只有第一行

                if (selection && selection.rangeCount > 0 && index == 0) {
                    const range = selection.getRangeAt(0)
                    const currentParagraph = findParagraph(range.startContainer)

                    // 获取焦点位置的偏移量
                    const offset = range.startOffset
                    // console.log('offset ==>', offset)
                    // 检查当前行是否有文字，如果有，则累加粘贴的文字
                    if (currentParagraph) {
                        const currentText = currentParagraph.textContent || ''
                        const newText = currentText.slice(0, offset) + para + currentText.slice(offset)
                        // console.log('newText ==>', newText)
                        // 更新当前行的内容
                        currentParagraph.textContent = newText

                        // 设置焦点到插入文本的末尾
                        range.setStart(currentParagraph.firstChild || currentParagraph, offset + para.length)
                        range.setEnd(currentParagraph.firstChild || currentParagraph, offset + para.length)

                        // 更新选区
                        selection.removeAllRanges()
                        selection.addRange(range)
                        return
                    }
                }

                if (currentP && currentP.getAttribute('data-lv') == '2' && newParagraph) {
                    newParagraph.setAttribute('data-lv', `2`)
                    newParagraph.className = `outline-content-paragraph outline-lv2`
                    const lvTitleList = currentP.getAttribute('data-lvt')?.split('.') || ['1.1']
                    newParagraph.setAttribute('data-lvt', `${lvTitleList[0]}.${parseInt(lvTitleList[1]) + 1}`)
                }
                if (currentP && currentP.getAttribute('data-lv') == '3' && newParagraph) {
                    newParagraph.setAttribute('data-lv', `3`)
                    newParagraph.className = `outline-content-paragraph outline-lv3`
                    const lvTitleList = currentP.getAttribute('data-lvt')?.split('.') || ['1.1.1']
                    newParagraph.setAttribute(
                        'data-lvt',
                        `${lvTitleList[0]}.${lvTitleList[1]}.${parseInt(lvTitleList[2]) + 1}`
                    )
                }
                if (currentP && currentP?.getAttribute('data-lv') == '1' && newParagraph) {
                    newParagraph.setAttribute('data-lv', `1`)
                    newParagraph.setAttribute(
                        'data-lvt',
                        `第${arabicNumberToChinese(pList.length + index)}章`
                    )
                }
                if (newParagraph) {
                    newParagraph.setAttribute('data-lvl', `${pList.length + index}`)
                    newParagraph.setAttribute('data-id', new Date().getTime().toString())
                    newParagraph.textContent = para
                }

                const secondParagraph = contentDivRef.value.querySelector(
                    `p:nth-child(${currentPIndex + index})`
                )

                // 在plist中找到了就插入，没找到就appendChild添加
                if (newParagraph) {
                    if (secondParagraph && secondParagraph?.parentNode) {
                        secondParagraph?.parentNode.insertBefore(newParagraph, secondParagraph);
                    } else {
                        contentDivRef.value.appendChild(newParagraph);
                    }
                }
            }
        })
        formatPTagDataTitle()
    }
}

const onInput = () => {
    contentTextOutline.value = contentDivRef.value.textContent.trim()
    // 当输入文字时，检查内容是否为空，如果为空则插入第一个<p>元素
    const currentP = getCurrentParagraph()
    const lvt = currentP?.getAttribute('data-lvt')

    if (!lvt) {
        nextTick(() => {
            currentP?.setAttribute('data-lvt', `第一章`)
            currentP?.setAttribute('data-lv', `1`)
            currentP?.setAttribute('data-id', new Date().getTime().toString())
        })
        // 此处不要注释，ios第一行输入时不显示"第一章"
        // ToastService.info(lvt + '')
        // Taro.hideLoading()
    }
}

const handleKeyDown = (event: KeyboardEvent) => {
    if (event.shiftKey && event.key === 'Tab') {
        event.preventDefault()
        updataTitleToLevel1()
    }
    // 监听Ctrl + Z 或 Command + Z
    if ((event.ctrlKey || event.metaKey) && event.key === 'z') {
        // 执行撤销操作，可以根据需要进行自定义撤销逻辑
        document.execCommand('undo', false, '')

        formatPTagDataTitle()
        // event.preventDefault()
    }
    if (event.key === 'Tab' && props.maxLevel > 1) {
        if (event.shiftKey && event.key === 'Tab') {
            return
        }

        event.preventDefault()
        updataTitleToLevel2(undefined)
    }

    if (event.key == 'Enter') {
        const paragraph = getCurrentParagraph()
        currentOldParagraph.value = paragraph
    }

    if (event.key === 'Backspace' && contentDivRef.value.textContent === '') {
        const currentP = getCurrentParagraph()
        const pList = getContentDivAllPTagAndContent()
        if (pList.length > 1) {
            return
        }
        if (currentP) {
            if (!currentP.getAttribute('data-lvt')) {
                event.preventDefault()
            }
            currentP.className = 'outline-content-paragraph outline-lv1'
            currentP.removeAttribute('data-lv')
            currentP.removeAttribute('data-lvt')
            currentP.removeAttribute('data-lvl')
            currentP.removeAttribute('data-id')
            currentP.focus()

            event.preventDefault()
        } else {
            // createEmptyParagraph()
        }
    }
}

// 更新为下一级
const updataTitleToLevel2 = (level: number | undefined) => {
    // 获取当前段落的级别
    const currentLevel = getCurrentParagraphLevel()
    // console.log('props.maxLevel ==>', props.maxLevel)
    if (currentLevel >= props.maxLevel) {
        return
    }
    let currentP = getCurrentParagraph() || oldParagraph.value
    if (!currentP) {
        return
    }
    currentP.className = `outline-content-paragraph outline-lv${level || currentLevel}`
    // const dataLv = currentP.getAttribute('data-lv') || '1'
    // console.log('currentP ==>', currentP)
    const dataLvt = currentP.getAttribute('data-lvt') //|| '第一章'

    const number = chineseNumberToArabic(dataLvt?.slice(1, dataLvt.length - 1))

    if (/[\u4e00-\u9fa5]/.test(dataLvt?.slice(0, 1))) {
        currentP.setAttribute('data-lvt', `${number - 1}.1`)
    } else {
        currentP.setAttribute('data-lvt', `${dataLvt}.1`)
    }
    currentP.setAttribute('data-lv', `${level || currentLevel}`)
    // console.log('currentP ==>', currentP)

    oldParagraph.value = currentP
    currentParagraphTitle.value = paragraphTitle()
    currentTitleLevel.value = level || currentLevel

    formatPTagDataTitle()
}

// 更新为上一级
const updataTitleToLevel1 = () => {
    const currentLevel = getCurrentParagraphLevel()

    // if (currentLevel == 1) {
    //   return
    // }
    const currentP = getCurrentParagraph() || oldParagraph.value
    if (!currentP) {
        return
    }

    const dataLv = currentP.getAttribute('data-lv') //|| '2'
    if (!dataLv || parseInt(dataLv) <= 1) {
        return
    }

    currentP.className = `outline-content-paragraph outline-lv${parseInt(dataLv) - 1}`
    const dataLvt = currentP.getAttribute('data-lvt') //|| '1.1'
    // console.log('dataLvt ==>', dataLvt)
    // console.log('dataLvt slice ==>', dataLvt?.substring(1, dataLvt.length - 1))
    const number = chineseNumberToArabic(dataLvt?.substring(0, 1))
    // console.log('number ==>', parseInt(number))
    if (parseInt(dataLv) == 2) {
        const currentLvt = '第' + arabicNumberToChinese((number + 1).toString()) + '章'
        //   console.log('currentLvt ==>', currentLvt)
        currentP.setAttribute('data-lvt', `${currentLvt}`)
        //   // TODO 还需要将此标签后面的所有章节数字 +1
    }
    if (parseInt(dataLv) == 3) {
        //   // TODO 减1后，需查找此章节下的二级标签后批量操作
        const data = dataLvt?.split('.') || []
        // console.log('data ==>', data)
        currentP.setAttribute('data-lvt', `${data[0]}.${data[1]}`)
    }

    currentP.setAttribute('data-lv', `${parseInt(dataLv) - 1}`)

    oldParagraph.value = currentP

    formatPTagDataTitle()
    console.log('currentP ==>', currentP.getAttribute('data-lvt'))
    currentParagraphTitle.value = paragraphTitle()
    currentTitleLevel.value = titleLevel()

}

const handleKeyUp = (event: KeyboardEvent) => {
    if (event.key == 'Enter') {
        // 获取当前段落的级别
        const currentLevel = getCurrentParagraphLevel()

        const newParagraph = getCurrentParagraph()

        if (!newParagraph || !currentOldParagraph.value) {
            return
        }
        const dataLvt = currentOldParagraph.value.getAttribute('data-lvt') || '第一章'

        newParagraph.className = `outline-content-paragraph outline-lv${currentLevel}`
        newParagraph.setAttribute('data-lv', currentLevel.toString())

        const number = chineseNumberToArabic(dataLvt?.slice(1, dataLvt.length - 1))
        const currentLvt = '第' + arabicNumberToChinese((number + 1).toString()) + '章'

        if (/[\u4e00-\u9fa5]/.test(dataLvt?.slice(0, 1))) {
            newParagraph.setAttribute('data-lvt', currentLvt)
        } else {
            const data = dataLvt.split('.')
            if (data.length == 2) {
                newParagraph.setAttribute('data-lvt', `${data[0]}.${parseInt(data[1]) + 1}`)
            } else {
                newParagraph.setAttribute('data-lvt', `${data[0]}.${data[1]}.${parseInt(data[2]) + 1}`)
            }
        }
        // newParagraph.setAttribute('data-lvl', (currentLevel - 1).toString()) //此值错误。需修改
        newParagraph.setAttribute('data-id', new Date().getTime().toString())

        formatPTagDataTitle()
    }

    if (event.key === 'Backspace') {
        const currentP = getCurrentParagraph()
        if (!currentP || !currentP.getAttribute('data-lv')) {
            return
        }
        formatPTagDataTitle()
    }
}

const formatOutlineByUserOutline = (list: any) => {
    const pList = getContentDivAllPTagAndContent()
    const getPListFirst = () => {
        if (pList && pList.length > 0 && pList[0]) {
            return pList[0].cloneNode(true)
        }
        return null
    }
    if (Array.isArray(list)) {
        list.forEach((chapterItem: Chapter) => {
            let chapter = getCurrentParagraph() || getPListFirst()
            chapter.setAttribute('data-lv', `1`)
            chapter.setAttribute('data-id', new Date().getTime().toString())
            chapter.textContent = chapterItem.chapter_title
            contentDivRef.value.appendChild(chapter)
            if (chapterItem.sections && Array.isArray(chapterItem.sections)) {
                chapterItem.sections.forEach((sectionItem) => {
                    let section = getPListFirst()
                    section.setAttribute('data-lv', `2`)
                    section.className = `outline-content-paragraph outline-lv2`
                    section.setAttribute('data-id', new Date().getTime().toString())
                    section.textContent = sectionItem.section_title
                    contentDivRef.value.appendChild(section)
                    if (sectionItem.nodes && Array.isArray(sectionItem.nodes)) {
                        sectionItem.nodes.forEach((nodeItem) => {
                            let node = getPListFirst()
                            node.setAttribute('data-lv', `3`)
                            node.className = `outline-content-paragraph outline-lv3`
                            node.setAttribute('data-id', new Date().getTime().toString())
                            node.textContent = nodeItem.node_title
                            contentDivRef.value.appendChild(node)
                        })
                    }
                })
            }

        })
    }

    const firstParagraph = contentDivRef.value.querySelector('p:first-child')
    if (!firstParagraph.textContent) {
        firstParagraph.remove()
    }

    formatPTagDataTitle()
}

const formatPTagDataTitle = () => {
    if (!contentDivRef.value) {
        return
    }
    const pList = getContentDivAllPTagAndContent()
    let prevLv1 = '0'
    let prevLv2 = '0'
    let prevLv3 = '0'
    // console.log('pList ==>', pList)
    pList.forEach((p: HTMLElement, index: number) => {
        if (!p) return; // 检查 p 是否为 null 或 undefined
        const lv = parseInt(p.getAttribute('data-lv') || '1')
        if (lv === 0 || lv === 1) {
            prevLv2 = '0'
            prevLv3 = '0'
            prevLv1 = incrementLvt(prevLv1)
            p.setAttribute('data-lvt', '第' + arabicNumberToChinese(prevLv1) + '章')
            p.setAttribute('data-lvl', index.toString())
            p.setAttribute('data-lv', '1')
            p.setAttribute('data-id', new Date().getTime().toString())
        } else if (lv === 2) {
            prevLv3 = '0'
            prevLv2 = incrementLvt(prevLv2)
            p.setAttribute('data-lvt', `${prevLv1 == '0' ? 1 : prevLv1}.${prevLv2}`)
            p.setAttribute('data-lvl', index.toString())
            p.setAttribute('data-lv', '2')
            p.setAttribute('data-id', new Date().getTime().toString())
        } else if (lv === 3) {
            prevLv3 = incrementLvt(prevLv3)
            p.setAttribute('data-lvt', `${prevLv1}.${prevLv2 == '0' ? 1 : prevLv2}.${prevLv3}`)
            p.setAttribute('data-lvl', index.toString())
            p.setAttribute('data-lv', '3')
            p.setAttribute('data-id', new Date().getTime().toString())
        }
    })
    // console.log('againEditOutlineList.value pList ==>', pList)
}

const formatOutlineTextContent = () => {
    const pList = getContentDivAllPTagAndContent()
    const chapters: Chapter[] = []
    let currentChapter: Chapter | null = null
    let currentSection: Section | null = null

    let isThereHierarchyError = false

    pList.forEach((p: HTMLElement) => {
        const level = parseInt(p.getAttribute('data-lv') || '')
        const title = p.textContent?.trim() || ''

        if (level === 1) {
            // New chapter
            currentChapter = { chapter_title: title, sections: [] }
            chapters.push(currentChapter)
            currentSection = null
        } else if (level === 2) {
            // New section
            currentSection = { section_title: title, nodes: [] }
            if (!currentChapter) {
                currentChapter = { chapter_title: '', sections: [] }
            }
            if (!currentChapter.sections) currentChapter.sections = []
            currentChapter.sections.push(currentSection)
        } else if (level === 3) {
            // New node
            const node = { node_title: title }
            if (currentSection) {
                currentSection.nodes = currentSection.nodes || []
                currentSection.nodes.push(node)
            } else {
                currentSection = { section_title: '', nodes: [node] }
                if (!currentChapter) {
                    currentChapter = { chapter_title: '', sections: [] }
                }
                if (!currentChapter.sections) currentChapter.sections = []
                currentChapter!.sections.push(currentSection)

                isThereHierarchyError = true
            }
        }
    })
    if (isThereHierarchyError) {
        return []
    }
    // console.log('chapters ==>', chapters)
    return chapters
}

const incrementLvt = (lvt: string): string => {
    const parts = lvt.split('.')
    const lastPart = parseInt(parts[parts.length - 1]) + 1
    parts[parts.length - 1] = lastPart.toString()
    return parts.join('.')
}

const getCurrentParagraphLevel = () => {
    // 获取当前焦点位置的父级<p>元素的级别
    const selection = window.getSelection()
    if (selection && selection.focusNode) {
        let node: Node | null = selection.focusNode as Node
        while (node) {
            if (node instanceof HTMLElement && node.tagName === 'P') {
                const lv = node.getAttribute('data-lv')
                return lv ? parseInt(lv, 10) : 1
            }
            node = node?.parentNode
        }
    }
    return 1
}

const getCurrentParagraph = () => {
    const selection = window.getSelection()
    if (selection && selection.focusNode) {
        let node: Node | null = selection.focusNode as Node
        while (node) {
            if (node instanceof HTMLElement && node.tagName === 'P') {
                return node
            }
            node = node?.parentNode
        }
    }
    return null // 没有找到包含焦点的 <p> 标签
}

const clearDivContent = () => {
    const divRef = contentDivRef.value
    divRef.textContent = ''
    divRef.insertHTML = `<p class="outline-content-paragraph outline-lv1"></p>`

    contentDivRef.value = divRef

    empty.value = true
}

const getContentDivAllPTagAndContent = () => {
    if (!contentDivRef.value) {
        return []
    }
    const pList = contentDivRef.value.querySelectorAll('p')
    return pList
}

const setEmptyValue = () => {
    const currentP = getCurrentParagraph()
    if (!currentP) {
        return
    }
    const pList = getContentDivAllPTagAndContent()
    if (!currentP.getAttribute('data-lvt') && pList.length == 1) {
        empty.value = true
        return
    }
    empty.value = false
}

const setupData = () => {
    // console.log('againEditOutlineList.value props.outlineList==>', props.outlineList)
    if (Array.isArray(props.outlineList) && props.outlineList.length > 0) {
        isShowOutlineTextLoaded.value = true

        setTimeout(() => {
            formatOutlineByUserOutline(props.outlineList)
            empty.value = false
            isShowOutlineTextLoaded.value = false
        }, 1000)
    }
    // 监听 DOM 变化
    const observer = new MutationObserver(() => {
        // console.log('DOM 变化了')
        // 在这里执行你的逻辑
        setEmptyValue()
    })

    // 重新打开时清除内容和伪元素
    if (contentDivRef.value) {
        getContentDivAllPTagAndContent().forEach((element: HTMLElement) => {
            element.textContent = ''
            if (element.getAttribute('data-lvt')) {
                element.removeAttribute('data-lvt')
            }
            if (element.getAttribute('data-lv')) {
                element.removeAttribute('data-lv')
            }
            if (element.getAttribute('data-lvl')) {
                element.removeAttribute('data-lvl')
            }
            if (element.getAttribute('data-id')) {
                element.removeAttribute('data-id')
            }
        })
    }

    // 配置 MutationObserver
    const config = { attributes: true, childList: true, subtree: true }

    // 开始监听
    observer.observe(contentDivRef.value as Node, config)

    // 在组件卸载时停止监听
    return () => {
        observer.disconnect()
    }
}

watch(
    () => {
        return props.outlineList
    },
    (_newValue, _oldValue) => {
        // console.log('newValue ->', newValue)
        if (!props.isH5) {
            return
        }

        setupData()
    }
)

onUnmounted(() => { })

onMounted(() => {
    // 当编辑时outlineList有值
    setupData()
})

onBeforeUnmount(() => { })

defineExpose({
    clearDivContent,
    getContentDivAllPTagAndContent,
    formatOutlineTextContent
})
</script>

<style scoped>
[contenteditable]>div {
    display: inline;
}

.title-area {
    .title {
        font-size: 14px;
        color: #333333;
        max-width: 40%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
}

.title-button {
    display: flex;
    justify-content: flex-end;
    align-items: center;

    .level {
        background-color: #ffffff;
        border-radius: 5px;
        border: 1px solid #dddddd;
        padding: 5px 15px;
        font-size: 14px;

        font-weight: 400;
        color: #777777;
        line-height: 21px;
    }

    .active {
        border: 1px solid #1e99ff;
        color: #1e99ff;
        background: #ecf6ff;
    }
}

.textarea {
    width: 100%;
    height: 500px;
    position: relative;
    box-sizing: border-box;
    border: 1px solid #eeeeee;
}

.outline {
    width: 100%;
    height: 500px;
    border-radius: 5px;
    border: 1px solid #eeeeee !important;
    padding: 20px;
    overflow-y: auto;
    background-color: #ffffff;
}

.outline-content-paragraph {
    font-size: 15px;
    color: #000;
    font-weight: 400;
    line-height: 240%;
    padding-top: 10px;
    border: none;
    text-align: left;
    border-radius: 10px;
}

.outline[empty='true']::before {
    content: attr(placeholder);
    font-weight: 400;
    font-size: 15px;
    color: rgba(27, 35, 55, 0.48);
    position: absolute;
    left: 35px;
    right: 35px;
    top: 33px;
    white-space: pre-wrap;
}

.outline-lv1 {
    font-size: 17px;

    font-weight: bold;
    color: #333333;
    line-height: 27px;
    margin: 0 15px;
}

.outline-lv1::before {
    content: attr(data-lvt);
    font-size: 17px;

    font-weight: bold;
    color: #999999;
    line-height: 27px;
    margin: 20px 15px 15px 0;
    margin-right: 10px;
}

.outline-lv2 {
    font-size: 16px;

    font-weight: 400;
    color: #333333;
    line-height: 23px;
    margin: 0 35px;
}

.outline-lv2::before {
    content: attr(data-lvt);
    font-size: 16px;

    font-weight: semi-bold;
    color: #999999;
    line-height: 23px;
    padding: 0 15px 15px 50x;
    padding-right: 10px;
}

.outline-lv3 {
    font-size: 15px;

    font-weight: 400;
    color: #333333;
    line-height: 23px;
    margin: 0 55px;
}

.outline-lv3::before {
    content: attr(data-lvt);
    font-size: 15px;

    font-weight: 400;
    color: #999999;
    line-height: 23px;
    padding: 0 15px 15px 70x;
    padding-right: 10px;
}
</style>