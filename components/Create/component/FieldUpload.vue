<template>
  <div>
    <div class="mb-6">
      <label class="flex items-center text-sm font-medium text-gray-700 mb-2 h-6" v-if="isShowLabel">
        <span class="text-red-500 mr-1 flex items-center">*</span>
        <div v-if="referencesList.length > 0" class="flex items-center justify-start">
          <span class="flex items-center">{{ referencesList[0]?.fieldName || '参考文献' }}</span>
          <div class="flex items-center ml-1">
            <PopoverHelp :content="referencesList[0]?.description || ''" />
          </div>
        </div>
        <div v-else class="flex items-center justify-start">
          <span class="flex items-center">{{ fieldItem.fieldName || '参考文献' }}</span>
          <div class="flex items-center ml-1">
            <PopoverHelp :content="fieldItem.description" />
          </div>
        </div>
      </label>

      <!-- 选择参考文献的按钮 -->
      <div :class="['grid', `grid-cols-${referenceOpeions.length}`, 'gap-3', 'mb-4', 'max-w-md', 'flex-wrap']"
        v-if="referenceOpeions.length > 0">
        <div v-for="item in referenceOpeions" :key="item.value" @click="handleReferenceTypeChange(item.value)" :class="[
          'border rounded-lg p-2 cursor-pointer transition-colors text-center',
          referenceValueType === item.value ? 'border-blue-500 bg-blue-50/50' : 'border-gray-200'
        ]">
          <span class="text-sm" :class="referenceValueType === item.value ? 'text-blue-600' : 'text-gray-600'">{{
            item.label
          }}</span>
        </div>
      </div>

      <!-- 内容区域 -->
      <div
        v-if="referenceValueType === CreateSubmissionAssistType.document || referenceValueType === CreateSubmissionAssistType.custom || referenceValueType === CreateSubmissionInformationType.upload || referenceValueType === CreateSubmissionAssistChineseType.custom">
        <div class="p-4 bg-gray-50/70 rounded-xl">
          <!-- 更新权益说明样式 -->

          <div class="text-[13px] flex items-center text-[#333333]">为保证学习质量，所选文件内如包含有图片、图表、音视频等不会进行学习；</div>
          <div class="text-[13px] flex items-center text-[#333333] my-1">最多可选{{ fieldItem.maxLength }}个文件，支持{{
            fieldItem.options
            }}格式</div>
          <div class="text-sm flex items-center text-[#333333]">
            <div class="flex items-center cursor-pointer" @click="handleOpenRechargeVipModal">
              <img src="//static-1256600262.file.myqcloud.com/xiaoin-h5/image/knowledge-assistant-level4.png"
                class="w-5 h-5 mr-1" />
              <span class="font-medium">小in年会员专属权益：文档学习费用</span>
              <span class="text-red-500">全免费</span>
              <right theme="outline" size="14" fill="currentColor" class="ml-0.5" />
            </div>
          </div>
        </div>
        <div class="p-4 bg-gray-50/70 rounded-xl">
          <!-- 手动上传参考文献 -->
          <div class="flex gap-4">
            <!-- 左侧手动上传区域 -->
            <div class="flex-1">
              <div class="flex flex-col h-full">
                <!-- 手动上传区域 
                 selectedKnowledgeList.length > 0
                    ? 'border-gray-200 cursor-not-allowed'
                    isDragging
                    ? 'border-blue-400 bg-blue-50/30'
                    : -->
                <!-- selectedKnowledgeList.length === 0 &&  -->
                <div :ref="(el) => {
                  if (el) referenceUpload = el
                }" class="flex-1 border-2 border-dashed rounded-xl text-center transition-colors relative" :class="[
                  'border-gray-200 hover:border-blue-300/50 cursor-pointer'
                ]" @click="handleFileInputClick" @dragover.prevent="handleDragOver"
                  @dragleave.prevent="handleDragLeave" @drop.prevent="handleDrop">

                  <!-- 禁用遮罩 -->
                  <!-- <div v-if="selectedKnowledgeList.length > 0"
                    class="absolute inset-0 bg-gray-100/80 rounded-xl z-10 flex items-center justify-center">
                    <p class="text-sm text-gray-500">手动上传与知识库选择只能二选一</p>
                  </div> -->
                  <!-- :disabled="selectedKnowledgeList.length > 0" -->
                  <div class="flex items-center justify-center h-full p-4">
                    <input ref="fileInput" type="file" class="hidden" multiple
                      :accept="formatAcceptValue(fieldItem.options)" @change="handleFileUpload" />
                    <div class="space-y-2">
                      <div class="w-12 h-12 mx-auto rounded-xl bg-blue-50 flex items-center justify-center">
                        <upload theme="outline" size="24" :fill="'#3B82F6'" />
                        <!-- selectedKnowledgeList.length > 0 ? '#9CA3AF' :  -->
                      </div>
                      <!-- selectedKnowledgeList.length > 0 ? 'text-gray-400' :  -->
                      <div class="text-sm" :class="'text-gray-600'">
                        <span v-if="appCode && (appCode.includes('paper') || appCode == 'literature_review' || appCode == 'thesis_report')
                        ">点击上传文献原文</span>
                        <span v-else>点击进行文件上传</span>
                      </div>
                      <!-- <div class="text-xs text-gray-400">为保证学习质量，暂不支持图片图表、音视频等</div> -->
                      <!-- <div class="text-xs text-gray-400">单次最多可传{{ fieldItem.maxLength }}个文件；支持 {{ fieldItem.options }}
                        格式
                      </div> -->
                      <div class="text-xs text-gray-400">支持拖拽文件到此处上传</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 从知识库选择文档入口 -->
            <div class="flex-1" v-if="knowledgeUploadList.length > 0">
              <!-- <p class="text-sm text-gray-500 mb-3">从知识库中选择</p> -->
              <!-- fileList.length > 0
                  ? 'bg-gray-50 border-gray-200/80 cursor-not-allowed'
                  :  -->
              <div class="flex-1 text-center p-6 transition-colors relative rounded-xl border" :class="[
                'bg-gray-50 border-gray-200/80 cursor-pointer hover:bg-gray-100/80'
              ]" @click="handleKnowledgeSelect">
                <!-- 禁用遮罩 -->
                <!-- <div v-if="fileList.length > 0"
                  class="absolute inset-0 bg-gray-100/80 rounded-xl z-10 flex items-center justify-center">
                  <p class="text-sm text-gray-500">手动上传和知识库选择只能二选一</p>
                </div> -->

                <!-- fileList.length > 0 ? '#9CA3AF' :  -->
                <div class="w-12 h-12 mx-auto mb-3 rounded-xl bg-blue-50 flex items-center justify-center">
                  <brain theme="outline" size="24" :fill="'#3B82F6'" />
                </div>
                <!-- fileList.length > 0 ? 'text-gray-400' :  -->
                <p class="text-sm mb-1" :class="'text-gray-600'">
                  从知识库中选择文档
                </p>
                <!-- <div class="text-xs text-gray-400">单次最多可传{{ knowledgeUploadList[0].maxLength }}个文件；支持{{
                  knowledgeUploadList[0].options }}格式</div> -->
              </div>
            </div>

          </div>
        </div>
      </div>


      <div class="max-h-[300px] overflow-y-auto">
        <!-- 已选参考文献列表 -->
        <div v-if="fileList.length > 0" class="mb-3 max-w-[500px]">
          <div class="space-y-2">
            <div v-for="(doc, index) in fileList" :key="index"
              class="flex items-center justify-between p-2.5 bg-gray-50 rounded-lg border border-gray-100 group">
              <div class="flex-1 min-w-0">
                <div class="flex items-center">
                  <div class="w-6 h-6 rounded-md bg-blue-50 flex items-center justify-center mr-2">
                    <file-editing theme="outline" size="16" fill="#3B82F6" />
                  </div>
                  <div class="flex-1 min-w-0">
                    <h5 class="text-sm font-medium text-gray-700 truncate">{{ doc.name || doc.title }}</h5>
                    <div class="flex items-center text-xs text-gray-500 mt-0.5">
                      <span v-if="doc.fileType">{{ (getKnowledgeFileType as Record<string, string>)[doc.fileType]
                          }}</span>
                      <template v-if="doc.wordCount && doc.wordCount > 0">
                        <!-- <span class="mx-1">·</span> -->
                        <span>{{ (doc.wordCount / 10000).toFixed(2) }}万字</span>
                      </template>
                    </div>
                  </div>
                </div>
              </div>
              <button @click="removeReference(doc)" class="p-1.5 text-gray-400 hover:text-gray-600 ml-2">
                <close theme="outline" size="16" fill="currentColor" />
              </button>
            </div>
          </div>
        </div>

        <div v-if="selectedKnowledgeList.length > 0" class="mb-3 max-w-[500px]">
          <div class="space-y-2">
            <div v-for="(doc, index) in selectedKnowledgeList" :key="index"
              class="flex items-center justify-between p-2.5 bg-gray-50 rounded-lg border border-gray-100 group">
              <div class="flex-1 min-w-0" v-if="doc">
                <div class="flex items-center">
                  <div class="w-6 h-6 rounded-md bg-blue-50 flex items-center justify-center mr-2">
                    <file-editing theme="outline" size="16" fill="#3B82F6" />
                  </div>
                  <div class="flex-1 min-w-0">
                    <h5 class="text-sm font-medium text-gray-700 truncate">{{ doc.title }}</h5>
                    <div class="flex items-center text-xs text-gray-500 mt-0.5">
                      <span v-if="doc.fileType">{{ (getKnowledgeFileType as Record<string, string>)[doc.fileType]
                          }}</span>
                      <template v-if="doc.wordCount && doc.wordCount > 0">
                        <!-- <span class="mx-1">·</span> -->
                        <span>{{ (doc.wordCount / 10000).toFixed(2) }}万字</span>
                      </template>
                    </div>
                  </div>
                </div>
              </div>
              <button @click="removeKnowledgeReference(doc)" class="p-1.5 text-gray-400 hover:text-gray-600 ml-2">
                <close theme="outline" size="16" fill="currentColor" />
              </button>
            </div>
          </div>
        </div>


      </div>

      <!-- 在 template 底部添加弹窗组件 -->
      <SelectKnowledgeDocumentModal v-if="showSelectModal" v-model:modelValue="showSelectModal" :appCode="appCode"
        :selected-ids="selectedKnowledgeFileIds" :options="knowledgeUploadList[0].options"
        :maxLength="knowledgeUploadList[0].maxLength - fileList.length" @select="onKnowledgeSelect" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { UserService } from '@/services/user';
import { Brain, Close, FileEditing, Right, Upload } from '@icon-park/vue-next';
import { message } from 'ant-design-vue';
import { computed, ref } from 'vue';
import PopoverHelp from '~/components/Common/PopoverHelp.vue';
import SelectKnowledgeDocumentModal from '~/components/Create/Paper/SelectKnowledgeDocumentModal.vue';
import type { SelectRequireInfo } from '~/services/types/appMessage';
import { useRechargeStore } from '~/stores/recharge';
import { useUserStore } from '~/stores/user';
import { CreateSubmissionAssistChineseType, CreateSubmissionAssistType, CreateSubmissionInformationType, getKnowledgeFileType } from '~/utils/constants';

interface UploadDocument {
  name: string;
  title?: string;
  fileType?: string;
  wordCount?: number;
  id?: string | number;
}

interface UploadFileRepositoryInfo {
  fileId: string;
  repositoryFileId?: string;
  referencesValue?: string;
}

const props = defineProps({
  referenceType: {
    type: String,
    required: true
  },
  creatorRequireFieldInfoList: {
    type: Array as PropType<SelectRequireInfo[]>,
    required: false
  },
  fieldItemInfo: {
    type: Object as PropType<SelectRequireInfo>,
    required: true
  },
  knowledgeUploadList: {
    type: Array as PropType<SelectRequireInfo[]>,
    required: true
  },
  referencesList: {
    type: Array as PropType<SelectRequireInfo[]>,
    required: true
  },
  appCode: {
    type: String,
    required: true
  },
  isShowLabel: {
    type: Boolean,
    default: true
  },
  attachments: {
    type: Array,
    required: false,
    default: []
  }
})

const userStore = useUserStore()

const fieldItem: any = computed(() => {
  const list = props.creatorRequireFieldInfoList?.filter(item => item.fieldCode === 'upload')
  if (list && list.length > 0) {
    return list[0]
  }
  return props.fieldItemInfo
})

const emit = defineEmits(['update:referenceType', 'rechargeVip'])
const referenceValueType = computed({
  get: () => props.referenceType,
  set: (val) => {
    emit('update:referenceType', val)
  }
})

const fileList = ref<UploadDocument[]>([])
const selectedKnowledgeList = ref<UploadDocument[]>([])

// const referenceType = ref()
const showSelectModal = ref(false)

const selectedKnowledgeFileIds = ref<(string | number | undefined)[]>([])


const fileRepositoryInfoList = ref<UploadFileRepositoryInfo[]>([])

const referenceOpeions = ref<any[]>([])
const referenceUpload = ref()
const fileInput = ref<HTMLInputElement>()

const isDragging = ref(false)


// const knowledgeAssistantMemberInfo = computed(() => {
//   return UserService.getKnowledgeAssistantMemberInfo()
// })

// 格式化accept属性值的函数
const formatAcceptValue = (options: string) => {
  if (!options) return ''

  // 如果已经是正确的格式（以.或application/开头），直接返回
  // console.log("options ==>", options)
  // if (/^(\.|application\/)/.test(options)) return options

  // 处理常见文件类型
  const formatMap: Record<string, string> = {
    'pdf': '.pdf',
    'docx': '.docx',
    'doc': '.doc',
    'xlsx': '.xlsx',
    'xls': '.xls',
    'pptx': '.pptx',
    'ppt': '.ppt',
    'txt': '.txt'
  }
  // 拆分可能的多种格式
  const formats = options.split(/[,，、\s]+/)
  const acceptValues = formats.map(format => {
    const cleanFormat = format.toLowerCase().trim()
    return formatMap[cleanFormat] || (cleanFormat.startsWith('.') ? cleanFormat : `.${cleanFormat}`)
  })

  return acceptValues.join(',')
}

// 文件类型与MIME类型的映射关系
const fileExtToMimeType: Record<string, string[]> = {
  '.pdf': ['application/pdf'],
  '.doc': ['application/msword'],
  '.docx': ['application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
  '.xls': ['application/vnd.ms-excel'],
  '.xlsx': ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],
  '.ppt': ['application/vnd.ms-powerpoint'],
  '.pptx': ['application/vnd.openxmlformats-officedocument.presentationml.presentation'],
  '.txt': ['text/plain'],
  // 可以根据需要添加更多类型
}

// 检查文件格式是否在允许的列表中 - 增强版，同时检查扩展名和MIME类型
const isFileFormatAllowed = (file: File, allowedFormats: string) => {
  if (!allowedFormats) return false

  // 获取文件MIME类型
  const fileType = file.type

  // 获取文件扩展名并转为小写
  const fileName = file.name
  const fileExt = fileName.split('.').pop()?.toLowerCase() || ''


  // 处理允许的格式列表
  const allowedExtList = allowedFormats.toLowerCase()
    .split(/[,，、\s]+/)
    .map(ext => ext.trim())
    .filter(ext => ext !== '')

  // 步骤1: 基于MIME类型判断
  if (fileType) {
    for (const ext of allowedExtList) {
      // 获取该扩展名对应的MIME类型列表
      const validMimeTypes = fileExtToMimeType[ext] || []

      // 如果文件的MIME类型在列表中，直接返回true
      if (validMimeTypes.includes(fileType)) {
        return true
      }
    }
  }

  // 步骤2: 基于扩展名判断
  const isExtensionMatched = allowedExtList.includes(fileExt)
  if (isExtensionMatched) {
    return true
  }

  return false
}


// 从参考文献表移除
const removeReference = (doc: UploadDocument) => {
  fileList.value = fileList.value.filter(item => item !== doc)

  // 重置 input value 以允许重新选择相同文件
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

const removeKnowledgeReference = (doc: UploadDocument) => {
  selectedKnowledgeList.value = selectedKnowledgeList.value.filter(item => item.id !== doc.id)
  selectedKnowledgeFileIds.value = selectedKnowledgeFileIds.value.filter(item => item !== doc.id)

  // console.log("selectedKnowledgeList  ==>", selectedKnowledgeList.value)
  setFileRepositoryInfoList()
}

const handleFileInputClick = () => {
  fileInput.value?.click()
}

// 添加件上传处理函数
const handleFileUpload = (event: Event) => {
  const target = event.target as HTMLInputElement

  if (!target.files) return


  const remainingSlots = fieldItem.value.maxLength - (fileList.value.length + selectedKnowledgeList.value.length)
  if (remainingSlots <= 0) {
    message.warning(`最多只能上传${fieldItem.value.maxLength}个文件`)
    return
  }

  // 检查每个文件的大小
  const oversizedFiles = Array.from(target.files).filter(file => file.size > 100 * 1024 * 1024)
  if (oversizedFiles.length > 0) {
    message.error('单个文件大小不能超过100M')
    return
  }

  // 过滤出符合格式要求的文件
  const allowedFiles = Array.from(target.files).filter(file =>
    isFileFormatAllowed(file, fieldItem.value.options)
  )
  // console.log("allowedFiles ==>", allowedFiles)
  // 如果有不支持的格式，提示用户
  if (allowedFiles.length < target.files.length) {
    message.warning(`已过滤掉${target.files.length - allowedFiles.length}个不支持的文件格式，支持的格式为：${fieldItem.value.options}`)
  }

  // 获取所有符合条件的文件，但只取剩余槽位数量
  const files = allowedFiles
    .slice(0, remainingSlots)
    .map(file => ({
      name: file.name,
      file
    }))

  // 如果选择的文件数量超过剩余槽位
  if (allowedFiles.length > remainingSlots) {
    message.warning(`已超出上传限制，仅采用前${remainingSlots}个文件`)
  }

  fileList.value.push(...files)

  // 重置 input value 以允许重新选择相同文件
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

const handleKnowledgeSelect = () => {
  if (!UserService.isLogined()) {
    userStore.openLoginModal()
    return
  }
  // if ((knowledgeAssistantMemberInfo.value?.vipLevel || 0) < 1) {
  //   message.warning('您当前无法使用基于知识库写作')
  //   return
  // }
  if (fileList.value.length + selectedKnowledgeList.value.length > fieldItem.value.maxLength) {
    message.warning(`最多只能上传${fieldItem.value.maxLength}个文件`)
    return
  }
  showSelectModal.value = true
}

const onKnowledgeSelect = (list: UploadDocument[]) => {

  selectedKnowledgeList.value = list
  selectedKnowledgeFileIds.value = list.map(item => item.id)
  setFileRepositoryInfoList()
}

const setFileRepositoryInfoList = () => {
  const newList = selectedKnowledgeList.value
    .filter((item): item is (typeof item & { fileId: string }) => {
      return selectedKnowledgeFileIds.value.includes(item.id) && !!item.id
    })
  if (newList.length == 0) {
    fileRepositoryInfoList.value = []
    return
  }
  fileRepositoryInfoList.value = newList.map(item => ({
    fileId: item.fileId,
    repositoryFileId: item.id as string
  }))
}

const rechargeStore = useRechargeStore()
const handleOpenRechargeVipModal = () => {
  if (!UserService.isLogined()) {
    userStore.openLoginModal()
    return
  }

  rechargeStore.openRechargeModal()
}

const handleDragOver = (event: DragEvent) => {
  if (selectedKnowledgeList.value.length > 0) return
  isDragging.value = true
}

const handleDragLeave = (event: DragEvent) => {
  isDragging.value = false
}

const handleDrop = (event: DragEvent) => {
  if (selectedKnowledgeList.value.length > 0) return
  isDragging.value = false

  const files = event.dataTransfer?.files
  if (!files) return

  const remainingSlots = fieldItem.value.maxLength - fileList.value.length
  if (remainingSlots <= 0) {
    message.warning(`最多只能上传${fieldItem.value.maxLength}个文件`)
    return
  }

  // 检查文件大小
  const oversizedFiles = Array.from(files).filter(file => file.size > 100 * 1024 * 1024)
  if (oversizedFiles.length > 0) {
    message.error('单个文件大小不能超过100M')
    return
  }

  // 过滤出符合格式要求的文件
  const allowedFiles = Array.from(files).filter(file =>
    isFileFormatAllowed(file, fieldItem.value.options)
  )

  // 如果有不支持的格式，提示用户
  if (allowedFiles.length < files.length) {
    message.warning(`已过滤掉${files.length - allowedFiles.length}个不支持的文件格式，支持的格式为：${fieldItem.value.options}`)
  }

  // 获取符合条件的文件
  const newFiles = allowedFiles
    .slice(0, remainingSlots)
    .map(file => ({
      name: file.name,
      file
    }))

  // 如果拖入的文件数量超过剩余槽位
  if (allowedFiles.length > remainingSlots) {
    message.warning(`已超出上传限制，仅采用前${remainingSlots}个文件`)
  }

  fileList.value.push(...newFiles)

  // 重置 input value 以允许重新选择相同文件
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

const setOptionsData = () => {
  if (props.referencesList.length > 0) {
    let _optionsList: any[] = []
    if (isJSON(props.referencesList[0]?.options)) {
      referenceOpeions.value = JSON.parse(props.referencesList[0].options)
    } else {
      const _options = props.referencesList[0]?.options || ''
      _optionsList = _options.split(',').map(item => ({
        label: item,
        value: item
      }))
      referenceOpeions.value = _optionsList;
    }

    if (props.referencesList[0]?.defaultValue) {
      referenceValueType.value = props.referencesList[0]?.defaultValue
      return
    }
    if (_optionsList.length > 0) {
      referenceValueType.value = _optionsList[0]?.value
      return
    }
  }
  // 用于一些特殊应用，可直接上传文件，显示参考文献区域，没有ai智能和手动上传的选择
  referenceValueType.value = CreateSubmissionAssistType.document
}

const handleReferenceTypeChange = (value: string) => {
  referenceValueType.value = value
  fileRepositoryInfoList.value = []
  selectedKnowledgeList.value = []
  fileList.value = []

  // 重置 input value 以允许重新选择相同文件
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

onMounted(() => {
  // console.log("fieldItem ==>", fieldItem.value)

  // console.log("props.referencesList ==>", props.referencesList)
  nextTick(() => {

    setOptionsData()

    if (props.attachments.length > 0) {
      fileList.value = [...props.attachments as UploadDocument[]]
      referenceValueType.value = CreateSubmissionAssistChineseType.custom
      // console.log("props.referencesList.value ==>", props.referencesList)
    }
    // console.log("referenceType.value =>", referenceType.value)

  })


})

defineExpose({
  setOptionsData,
  selectedKnowledgeFileIds: computed(() => fileRepositoryInfoList || []) || [],
  fileList: computed(() => fileList.value || []) || [],
  selectedKnowledgeList,
})
</script>
