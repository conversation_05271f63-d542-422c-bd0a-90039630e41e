<template>
    <div class="field-input">
        <div class="mb-6">
            <label class="flex items-center text-sm font-medium text-gray-700 mb-2">
                <span class="text-red-500 mr-1" v-if="fieldItem.isRequired == 'Y'">*</span>
                {{ fieldItem.fieldName }}
                <PopoverHelp :content="fieldItem.description" />
            </label>

            <div class="relative">
                <!-- Math.min(5, Math.max(3, (inputValue?.split('\n').length || 3))) -->
                <textarea v-model="inputValue" :rows="fieldItem.options?.rows || 5" type="text"
                    :placeholder="placeholder" :maxlength="fieldItem.maxLength || 1000"
                    class="w-full pl-4 pr-16 py-2.5 text-sm bg-white border border-gray-200 rounded-xl focus:outline-none focus:border-blue-300/50 focus:ring-1 focus:ring-blue-300/50"></textarea>
                <button @click="clearField"
                    class="absolute right-3.5 bottom-3.5 px-2 py-1 text-xs text-gray-400 hover:text-gray-600">
                    清空
                </button>
            </div>

            <div class="flex justify-end mt-1">
                <span class="text-xs text-gray-400">{{ inputValue?.length || 0 }} / {{ fieldItem.maxLength || 1000
                    }}</span>
            </div>
        </div>
    </div>
</template>

<script setup>
import { watch } from 'vue';
import PopoverHelp from '~/components/Common/PopoverHelp.vue';

const props = defineProps({
    fieldItem: {
        type: Object,
        required: true
    },
    inputValue: {
        type: String,
        required: true
    }
})

const emit = defineEmits(['update:inputValue'])
const inputValue = computed({
    get: () => props.inputValue,
    set: (val) => {
        emit('update:inputValue', val)
    }
})

const placeholder = computed(() => {
    // return '1、如您已有清晰完整的大纲：请在下方输入或粘贴大纲内容。 \n2、如您已有部分大纲，需要AI对大纲内容进行扩充、润色或修改：请先输入清晰具体的补充要求，如：“请帮我扩充完善以下大纲”；然后再输入/粘贴您已有的大纲内容。 \n3、请按大纲序号格式输入，AI会进行格式整理及润色。您也可以在后续大纲编辑页面，进行新的修改或完善。'
    return props.fieldItem.placeholder || `请输入${props.fieldItem.fieldName}`
})

// 清空输入内容
const clearField = () => {
    inputValue.value = ''
}

// 监听输入内容变化
watch(inputValue, (newVal) => {
    emit('update:inputValue', newVal)
})

onMounted(() => {
    // console.log(props.fieldItem)
})
</script>

<style>
/* 添加到你的样式文件中 */
textarea::placeholder {
    white-space: pre-line;
}

/* 如果需要兼容旧版浏览器，可以添加前缀 */
textarea::-webkit-input-placeholder {
    /* Chrome/Safari */
    white-space: pre-line;
}

textarea::-moz-placeholder {
    /* Firefox */
    white-space: pre-line;
}

textarea:-ms-input-placeholder {
    /* IE 10+ */
    white-space: pre-line;
}
</style>
