<template>
    <div class="predict-words" v-if="!isResult">
        <div v-if="!empty && !notDisplayTheWordCountOfTheOutline">
            文章预估字数：约
            <text style="color: #1e99ff">{{ chapterWordsNumber }}</text>
            字
            <text style="color: #777">(英文减半)</text>
        </div>
    </div>
    <a-spin :spinning="isShowOutlineTextLoaded" class="h-full">
        <div class="textarea h-full">
            <div class="outline" id="outline" tabindex="0" :empty="`${empty}`" :placeholder="outlinePlaceholder"
                ref="contentDivRef" contenteditable="true" style="caret-color: blue" @paste="onPaste"
                @keydown="handleKeyDown" @keyup="handleKeyUp" @input="onInput" @blur="onBlur">
                <p class="outline-content-paragraph outline-lv1"></p>
            </div>
        </div>
    </a-spin>
</template>

<script setup lang="ts">

import { arabicNumberToChinese, chineseNumberToArabic } from '@/utils/utils';
import { computed, onMounted, onUnmounted, ref, watch } from 'vue';
import type { Chapter, CreatorRequireFieldInfo, Nodes, Section } from '~/services/types/appMessage';
import { useOutlineStore } from '~/stores/outline';

interface Props {
    isHighlight: boolean
    isH5?: boolean
    notDisplayTheWordCountOfTheOutline?: boolean
    maxLevel: number
    isResult?: boolean
    fieldOutline?: CreatorRequireFieldInfo
    outlineList?: Chapter[]
}
const props = withDefaults(defineProps<Props>(), {})

const contentDivRef = ref()
const contentTextOutline = ref()

const currentOldParagraph = ref<HTMLElement | null>(null)

const empty = ref(true)

const isShowOutlineTextLoaded = ref(false)

const emit = defineEmits(['onBlur'])

const currentOutlineList = ref(props.outlineList || [])

const oldParagraph = ref()

// const outlineStyle = {
//     caretColor: 'blue'
// }

// const outlineStyleAnimation = {
//     caretColor: 'red'
// }

// const creatorInfo = computed(() => {
//     return props.creatorDetail
// })

const fieldOutline = computed(() => {
    return props.fieldOutline
})

const predictWordsNumber = computed(() => {
    // const list = fieldOutline.value
    if (!fieldOutline.value) {
        return 0
    }
    return fieldOutline.value.maxLength || 0
})

const singleChapterWordsNumber = computed(() => {
    if (!fieldOutline.value) {
        return 500
    }
    const count = fieldOutline.value.nodeWordCount || '500'
    return parseInt(count)
})

const chapterWordsNumber = ref(predictWordsNumber.value + singleChapterWordsNumber.value)


const outlinePlaceholder = computed(() => {
    // const list = fieldOutline.value
    if (!fieldOutline.value) {
        return '请输入大纲内容，并按大纲格式进行编辑修改'
    }
    if (!fieldOutline.value.placeholder || fieldOutline.value.placeholder.length == 0) {
        return '请输入大纲内容，并按大纲格式进行编辑修改'
    }
    const data = fieldOutline.value.placeholder.replace(/\\n/g, '\n')
    return data || '请输入大纲内容，并按大纲格式进行编辑修改'
})

const findParagraph = (node: Node | null): HTMLParagraphElement | null => {
    // 递归查找包含p标签的父节点
    if (node) {
        if (
            node.nodeType == Node.ELEMENT_NODE &&
            (node as HTMLElement).tagName.toLowerCase() == 'p'
        ) {
            return node as HTMLParagraphElement
        } else {
            return findParagraph(node?.parentNode)
        }
    }
    return null
}

const outlineStore = useOutlineStore()

const onBlur = () => {
    const pList = getContentDivAllPTagAndContent()
    const currentP = getCurrentParagraph()
    if (currentP) {
        oldParagraph.value = currentP
    }
    // outlineStore.updateOutlineContent(pList)
    emit('onBlur', pList, chapterWordsNumberChange())
}

const parseContent = (content: string) => {
    const lines = content.split('\n')
    // console.log('lines ==>', lines)
    let currentChapter: Chapter | null = null
    let currentSection: Section | null = null
    let currentNode: Nodes | null = null

    lines.forEach((line) => {
        const isLv1Title =
            line.trim().slice(0, 5).includes('第') && line.trim().slice(0, 5).includes('章')
        const isLv2Title = /^\d+\.\d+/.test(`${line.trim().slice(0, 5)}`)
        const isLv3Title = /^\d+\.\d+\.\d+/.test(`${line.trim().slice(0, 5)}`)
        console.log('isLv1Title ==>', isLv1Title)
        if (isLv1Title) {
            const title = line
                .trim()
                .replace(/^\s*第?[\d一二三四五六七八九十A-Za-z]+[章、. : ：]*\s*/, '')
                .replace(/[\s\.\d]*$/, '')
            // 判断是否是章标题
            currentChapter = { chapter_title: title, sections: [] }
            currentOutlineList.value.push(currentChapter)
        } else if (isLv2Title && !isLv3Title && props.maxLevel > 1) {
            // 判断是否是小节标题
            if (currentChapter) {
                const title = line
                    .trim()
                    .replace(/^(\d+\.\d+)\s*：?\s*/, '')
                    .replace(/[\s\.\d]*$/, '')

                currentSection = { section_title: title, nodes: [] }
                currentChapter.sections.push(currentSection)
            }
        } else if (isLv3Title && props.maxLevel > 2) {
            // 判断是否是小节标题
            if (currentSection) {
                const title = line
                    .trim()
                    .replace(/^(\d+\.\d+\.\d+)\s*：?\s*/, '')
                    .replace(/[\s\.\d]*$/, '')
                currentNode = { node_title: title }
                currentSection.nodes.push(currentNode)
            }
        } else {
            // 其他情况视为章内容
            currentChapter = { chapter_title: line.replace(/[\s\.\d]*$/, ''), sections: [] }
            currentOutlineList.value.push(currentChapter)
        }
    })

    // console.log('currentOutlineList 112233 ==>', currentOutlineList.value)
}

const onPaste = (event: ClipboardEvent) => {
    event.preventDefault() // 阻止默认粘贴

    if (event.clipboardData) {
        let text = event.clipboardData.getData('text/plain') // 获取纯文本
        text = text.trim() // 去除首尾空格

        // console.log('text 112233 ==>', text)

        const pList = getContentDivAllPTagAndContent()

        const currentP = getCurrentParagraph()
        if (currentP && !currentP.getAttribute('data-lvt')) {
            currentOutlineList.value = []
            parseContent(text)
            setupData()
            if (currentOutlineList.value.length > 0) {
                return
            }
        }

        let currentPIndex = 0

        if (currentP) {
            pList.forEach((element: { getAttribute: (arg0: string) => string | null; }, index: number) => {
                if (element.getAttribute('data-lvl') == currentP.getAttribute('data-lvl')) {
                    currentPIndex = index + 1
                }
            })
        }

        text.split(/\n+/).map((para, index) => {
            // 当输入框没有任何内容时，需要现有一个p标签，否则伪元素不显示
            if (currentP && !currentP.getAttribute('data-lvt')) {
                currentP.setAttribute('data-lvt', `第一章`)
                currentP.setAttribute('data-lv', `1`)
                currentP.setAttribute('data-result', `${props.isResult}` || 'false')
                currentP.setAttribute('data-id', new Date().getTime().toString())
                currentP.setAttribute('data-lvl', `${index + 1}`)
                currentP.textContent = para
                // currentP.focus()
            } else {
                const selection = window.getSelection()

                let newParagraph;
                if (currentP) {
                    newParagraph = currentP.cloneNode(true)
                } else {
                    if (pList && pList.length > 0) {
                        newParagraph = pList[0].cloneNode(true)
                    }
                }
                // 判断粘贴时，如果有焦点，且焦点行有文字时，只有第一行

                if (selection && selection.rangeCount > 0 && index == 0) {
                    const range = selection.getRangeAt(0)
                    const currentParagraph = findParagraph(range.startContainer)

                    // 获取焦点位置的偏移量
                    const offset = range.startOffset
                    // console.log('offset ==>', offset)
                    // 检查当前行是否有文字，如果有，则累加粘贴的文字
                    if (currentParagraph) {
                        const currentText = currentParagraph.textContent || ''
                        const newText = currentText.slice(0, offset) + para + currentText.slice(offset)
                        // console.log('newText ==>', newText)
                        // 更新当前行的内容
                        currentParagraph.textContent = newText

                        // 设置焦点到插入文本的末尾
                        range.setStart(currentParagraph.firstChild || currentParagraph, offset + para.length)
                        range.setEnd(currentParagraph.firstChild || currentParagraph, offset + para.length)

                        // 更新选区
                        selection.removeAllRanges()
                        selection.addRange(range)
                        return
                    }
                }

                if (currentP && currentP.getAttribute('data-lv') == '2' && newParagraph) {
                    newParagraph.setAttribute('data-lv', `2`)
                    newParagraph.className = `outline-content-paragraph outline-lv2`
                    const lvTitleList = currentP.getAttribute('data-lvt')?.split('.') || ['1.1']
                    newParagraph.setAttribute('data-lvt', `${lvTitleList[0]}.${parseInt(lvTitleList[1]) + 1}`)
                }
                if (currentP && currentP.getAttribute('data-lv') == '3' && newParagraph) {
                    newParagraph.setAttribute('data-lv', `3`)

                    newParagraph.className = `outline-content-paragraph outline-lv3`
                    const lvTitleList = currentP.getAttribute('data-lvt')?.split('.') || ['1.1.1']
                    newParagraph.setAttribute(
                        'data-lvt',
                        `${lvTitleList[0]}.${lvTitleList[1]}.${parseInt(lvTitleList[2]) + 1}`
                    )
                }
                if (currentP && currentP?.getAttribute('data-lv') == '1' && newParagraph) {
                    newParagraph.setAttribute('data-lv', `1`)

                    newParagraph.setAttribute(
                        'data-lvt',
                        `第${arabicNumberToChinese(pList.length + index)}章`
                    )
                }
                if (newParagraph) {
                    newParagraph.setAttribute('data-lvl', `${pList.length + index}`)
                    newParagraph.setAttribute('data-result', `${props.isResult}` || 'false')
                    newParagraph.setAttribute('data-id', new Date().getTime().toString())
                    newParagraph.textContent = para
                }

                const secondParagraph = contentDivRef.value.querySelector(
                    `p:nth-child(${currentPIndex + index})`
                )

                // 在plist中找到了就插入，没找到就appendChild添加
                if (newParagraph) {
                    if (secondParagraph && secondParagraph?.parentNode) {
                        secondParagraph?.parentNode.insertBefore(newParagraph, secondParagraph);
                    } else {
                        contentDivRef.value.appendChild(newParagraph)
                    }
                }
            }
        })
        formatPTagDataTitle()
    }
}

const onInput = () => {
    // 当输入文字时，检查内容是否为空，如果为空则插入第一个<p>元素
    const currentP = getCurrentParagraph()
    if (currentP && !currentP?.getAttribute('data-lvt')) {
        currentP.setAttribute('data-lvt', `第一章`)
        currentP.setAttribute('data-lv', `1`)
        currentP.setAttribute('data-result', `${props.isResult}` || 'false')
        currentP.setAttribute('data-id', new Date().getTime().toString())
    }
    contentTextOutline.value = contentDivRef.value.textContent
}

const handleKeyDown = (event: KeyboardEvent) => {
    if (event.shiftKey && event.key == 'Tab') {
        event.preventDefault()
        updataTitleToLevel1()
    }
    // 监听Ctrl + Z 或 Command + Z
    if ((event.ctrlKey || event.metaKey) && event.key == 'z') {
        // 执行撤销操作，可以根据需要进行自定义撤销逻辑
        document.execCommand('undo', false, '')

        formatPTagDataTitle()
        // event.preventDefault()
    }
    if (event.key == 'Tab' && props.maxLevel > 1) {
        if (event.shiftKey && event.key == 'Tab') {
            return
        }

        event.preventDefault()
        updataTitleToLevel2()
    }

    if (event.key == 'Enter') {
        const paragraph = getCurrentParagraph()
        currentOldParagraph.value = paragraph
    }

    if (event.key == 'Backspace' && contentDivRef.value.textContent == '') {
        const currentP = getCurrentParagraph()
        const pList = getContentDivAllPTagAndContent()
        if (pList.length > 1) {
            return
        }
        if (currentP) {
            if (!currentP.getAttribute('data-lvt')) {
                event.preventDefault()
            }
            currentP.className = 'outline-content-paragraph outline-lv1'
            currentP.removeAttribute('data-lv')
            currentP.removeAttribute('data-lvt')
            currentP.removeAttribute('data-result')
            currentP.removeAttribute('data-lvl')
            currentP.removeAttribute('data-id')
            currentP.focus()

            event.preventDefault()
        } else {
            // createEmptyParagraph()
        }
    }
}

// 更新为下一级
const updataTitleToLevel2 = () => {
    // 获取当前段落的级别
    const currentLevel = getCurrentParagraphLevel()

    if (currentLevel >= props.maxLevel) {
        return
    }
    const currentP = getCurrentParagraph() || oldParagraph.value
    if (!currentP) {
        return
    }
    const dataLv = currentP.getAttribute('data-lv') || '2'
    if (!dataLv || parseInt(dataLv) >= 3) {
        return
    }
    currentP.className = `outline-content-paragraph outline-lv${currentLevel + 1}`
    // const dataLv = currentP.getAttribute('data-lv') || '1'
    const dataLvt = currentP.getAttribute('data-lvt') || '第一章'

    const number = chineseNumberToArabic(dataLvt?.slice(1, dataLvt.length - 1))

    if (/[\u4e00-\u9fa5]/.test(dataLvt?.slice(0, 1))) {
        currentP.setAttribute('data-lvt', `${number - 1}.1`)
    } else {
        currentP.setAttribute('data-lvt', `${dataLvt}.1`)
    }
    currentP.setAttribute('data-lv', `${parseInt(dataLv) + 1}`)
    currentP.setAttribute('data-result', `${props.isResult}` || 'false')

    oldParagraph.value = currentP

    formatPTagDataTitle()
}

// 更新为上一级
const updataTitleToLevel1 = () => {
    const currentLevel = getCurrentParagraphLevel()

    // if (currentLevel == 1) {
    //   return
    // }
    const currentP = getCurrentParagraph() || oldParagraph.value
    if (!currentP) {
        return
    }

    const dataLv = currentP.getAttribute('data-lv') //|| '2'
    if (!dataLv || parseInt(dataLv) <= 1) {
        return
    }

    currentP.className = `outline-content-paragraph outline-lv${parseInt(dataLv) - 1}`
    const dataLvt = currentP.getAttribute('data-lvt') //|| '1.1'

    const number = chineseNumberToArabic(dataLvt?.substring(0, 1))

    if (parseInt(dataLv) == 2) {
        const currentLvt = '第' + arabicNumberToChinese((number + 1).toString()) + '章'

        currentP.setAttribute('data-lvt', `${currentLvt}`)
        //   // TODO 还需要将此标签后面的所有章节数字 +1
    }
    if (parseInt(dataLv) == 3) {
        //   // TODO 减1后，需查找此章节下的二级标签后批量操作
        const data = dataLvt?.split('.') || []

        currentP.setAttribute('data-lvt', `${data[0]}.${data[1]}`)
    }

    currentP.setAttribute('data-lv', `${parseInt(dataLv) - 1}`)
    currentP.setAttribute('data-result', `${props.isResult}` || 'false')

    oldParagraph.value = currentP

    formatPTagDataTitle()
}

const handleKeyUp = (event: KeyboardEvent) => {
    if (event.key == 'Enter') {
        // 获取当前段落的级别
        const currentLevel = getCurrentParagraphLevel()

        const newParagraph = getCurrentParagraph()

        if (!newParagraph || !currentOldParagraph.value) {
            return
        }
        const dataLvt = currentOldParagraph.value.getAttribute('data-lvt') || '第一章'

        newParagraph.className = `outline-content-paragraph outline-lv${currentLevel}`
        newParagraph.setAttribute('data-lv', currentLevel.toString())

        const number = chineseNumberToArabic(dataLvt?.slice(1, dataLvt.length - 1))
        const currentLvt = '第' + arabicNumberToChinese((number + 1).toString()) + '章'

        if (/[\u4e00-\u9fa5]/.test(dataLvt?.slice(0, 1))) {
            newParagraph.setAttribute('data-lvt', currentLvt)
        } else {
            const data = dataLvt.split('.')
            if (data.length == 2) {
                newParagraph.setAttribute('data-lvt', `${data[0]}.${parseInt(data[1]) + 1}`)
            } else {
                newParagraph.setAttribute('data-lvt', `${data[0]}.${data[1]}.${parseInt(data[2]) + 1}`)
            }
        }
        // newParagraph.setAttribute('data-lvl', (currentLevel - 1).toString()) //此值错误。需修改
        newParagraph.setAttribute('data-id', new Date().getTime().toString())
        newParagraph.setAttribute('data-result', `${props.isResult}` || 'false')

        formatPTagDataTitle()
    }

    if (event.key == 'Backspace') {
        const currentP = getCurrentParagraph()
        if (!currentP || !currentP.getAttribute('data-lv')) {
            return
        }
        formatPTagDataTitle()
    }
}

const formatOutlineByUserOutline = (list: any) => {
    const pList = getContentDivAllPTagAndContent()


    // 首先清空所有内容
    if (contentDivRef.value) {
        // 清空所有子元素
        while (contentDivRef.value.firstChild) {
            contentDivRef.value.removeChild(contentDivRef.value.firstChild);
        }
    }

    const getPListFirst = () => {
        if (pList && pList.length > 0 && pList[0]) {
            return pList[0].cloneNode(true)
        }

        // 如果没有找到元素，创建一个新的p元素
        const newP = document.createElement('p');
        newP.className = 'outline-content-paragraph outline-lv1';
        return newP;
    }

    if (Array.isArray(list)) {
        list.map((chapterItem: Chapter, index: number) => {
            // 跳过空标题的章节
            if (!chapterItem.chapter_title || chapterItem.chapter_title.trim() === '') {
                return;
            }

            const chapter = getPListFirst()

            if (!chapter) {
                return
            }

            chapter.setAttribute('data-lv', `1`)
            chapter.setAttribute('data-id', new Date().getTime().toString())
            chapter.textContent = chapterItem.chapter_title

            contentDivRef.value.appendChild(chapter)

            if (chapterItem.sections && Array.isArray(chapterItem.sections)) {
                chapterItem.sections.forEach((sectionItem) => {
                    // 跳过空标题的小节
                    if (!sectionItem.section_title || sectionItem.section_title.trim() == '') {
                        return;
                    }

                    const section = getPListFirst()
                    section.setAttribute('data-lv', `2`)
                    section.className = `outline-content-paragraph outline-lv2`
                    section.setAttribute('data-id', new Date().getTime().toString())
                    section.textContent = sectionItem.section_title
                    contentDivRef.value.appendChild(section)

                    if (sectionItem.nodes && Array.isArray(sectionItem.nodes)) {
                        sectionItem.nodes.forEach((nodeItem) => {
                            // 跳过空标题的节点
                            if (!nodeItem.node_title || nodeItem.node_title.trim() == '') {
                                return;
                            }

                            const node = getPListFirst()
                            node.setAttribute('data-lv', `3`)
                            node.className = `outline-content-paragraph outline-lv3`
                            node.setAttribute('data-id', new Date().getTime().toString())
                            node.textContent = nodeItem.node_title
                            contentDivRef.value.appendChild(node)
                        })
                    }
                })
            }
        })
    }

    // if (contentDivRef.value) {
    //     const firstParagraph = contentDivRef.value.querySelector('p:first-child')
    //     if (firstParagraph && !firstParagraph.textContent) {
    //         firstParagraph.remove()
    //     }
    // 检查是否有内容被添加，如果没有则添加一个空的段落
    if (contentDivRef.value && !contentDivRef.value.hasChildNodes()) {
        const newP = document.createElement('p');
        newP.className = 'outline-content-paragraph outline-lv1';
        contentDivRef.value.appendChild(newP);
    }
}

const formatPTagDataTitle = () => {
    if (!contentDivRef.value) {
        return
    }
    const pList = getContentDivAllPTagAndContent()
    let prevLv1 = '0'
    let prevLv2 = '0'
    let prevLv3 = '0'
    pList.forEach((p: any, index: { toString: () => any; }) => {
        if (!p) return; // 检查 p 是否为 null 或 undefined
        const lv = parseInt(p.getAttribute('data-lv') || '1')

        if (lv == 0 || lv == 1) {
            prevLv2 = '0'
            prevLv3 = '0'
            prevLv1 = incrementLvt(prevLv1)
            const title = '第' + arabicNumberToChinese(prevLv1) + '章'
            p.setAttribute('data-lvt', title)
            p.setAttribute('data-lvl', index.toString())
            p.setAttribute('data-lv', '1')
            p.setAttribute('data-id', new Date().getTime().toString())
        } else if (lv == 2) {
            prevLv3 = '0'
            prevLv2 = incrementLvt(prevLv2)
            p.setAttribute('data-lvt', `${prevLv1 == '0' ? 1 : prevLv1}.${prevLv2}`)
            p.setAttribute('data-lvl', index.toString())
            p.setAttribute('data-lv', '2')
            p.setAttribute('data-id', new Date().getTime().toString())
        } else if (lv == 3) {
            prevLv3 = incrementLvt(prevLv3)
            p.setAttribute('data-lvt', `${prevLv1}.${prevLv2 == '0' ? 1 : prevLv2}.${prevLv3}`)
            p.setAttribute('data-lvl', index.toString())
            p.setAttribute('data-lv', '3')
            p.setAttribute('data-id', new Date().getTime().toString())
        }
        p.setAttribute('data-result', `${props.isResult}` || 'false')
    })
    // console.log('againEditOutlineList.value pList ==>', pList)
    chapterWordsNumberChange()
}

const chapterWordsNumberChange = () => {
    let lv1Number = 0
    let lv2Number = 0
    let lv3Number = 0
    chapterWordsNumber.value = predictWordsNumber.value

    const list = formatOutlineTextContent()
    if (list.length == 0) {
        return 0
    }
    lv1Number = list.length
    list.map((item) => {
        if (item.sections && item.sections.length > 0) {
            lv2Number += item.sections.length - 1
            item.sections.map((child) => {
                if (child.nodes && child.nodes.length > 0) {
                    lv3Number += child.nodes.length - 1
                }
            })
        }
    })
    const number =
        predictWordsNumber.value + (lv1Number + lv2Number + lv3Number) * singleChapterWordsNumber.value
    chapterWordsNumber.value = number
    return lv1Number + lv2Number + lv3Number
}

const formatOutlineTextContent = () => {
    const pList = getContentDivAllPTagAndContent()
    const chapters: Chapter[] = []
    let currentChapter: Chapter | null = null
    let currentSection: Section | null = null

    let isThereHierarchyError = false

    pList.forEach((p: any) => {
        const level = parseInt(p.getAttribute('data-lv'))
        const title = p.textContent.trim()

        if (level == 1) {
            // New chapter
            currentChapter = { chapter_title: title, sections: [] }
            chapters.push(currentChapter)
            currentSection = null
        } else if (level == 2) {
            // New section
            currentSection = { section_title: title, nodes: [] }
            if (!currentChapter) {
                currentChapter = { chapter_title: '', sections: [] }
            }
            if (!currentChapter.sections) currentChapter.sections = []
            currentChapter.sections.push(currentSection)
        } else if (level == 3) {
            // New node
            const node = { node_title: title }
            if (currentSection) {
                currentSection.nodes = currentSection.nodes || []
                currentSection.nodes.push(node)
            } else {
                currentSection = { section_title: '', nodes: [node] }
                // if (!currentChapter || !currentChapter.sections) currentChapter!.sections = []
                if (!currentChapter) {
                    currentChapter = { chapter_title: '', sections: [] }
                }
                if (!currentChapter.sections) currentChapter.sections = []
                currentChapter!.sections.push(currentSection)

                isThereHierarchyError = true
            }
        }
    })
    if (isThereHierarchyError) {
        return []
    }
    // console.log('chapters ==>', chapters)
    return chapters
}

const incrementLvt = (lvt: string): string => {
    const parts = lvt.split('.')
    const lastPart = parseInt(parts[parts.length - 1]) + 1
    parts[parts.length - 1] = lastPart.toString()
    return parts.join('.')
}

const getCurrentParagraphLevel = () => {
    // 获取当前焦点位置的父级<p>元素的级别
    const selection = window.getSelection()
    if (selection && selection.focusNode) {
        let node = selection.focusNode as Node
        while (node) {
            if (node instanceof HTMLElement && node.tagName == 'P') {
                const lv = node.getAttribute('data-lv')
                return lv ? parseInt(lv, 10) : 1
            }
            node = node?.parentNode as Node
        }
    }
    return 1
}

const getCurrentParagraph = () => {
    const selection = window.getSelection()
    if (selection && selection.focusNode) {
        let node = selection.focusNode as Node
        while (node) {
            if (node instanceof HTMLElement && node.tagName == 'P') {
                return node
            }
            node = node?.parentNode as Node
        }
    }
    return null // 没有找到包含焦点的 <p> 标签
}

const clearDivContent = () => {
    const divRef = contentDivRef.value
    divRef.textContent = ''
    divRef.insertHTML = `<p class="outline-content-paragraph outline-lv1"></p>`

    contentDivRef.value = divRef

    empty.value = true
}

const getContentDivAllPTagAndContent = () => {
    if (!contentDivRef.value) {
        return []
    }
    const pList = contentDivRef.value.querySelectorAll('p')
    outlineStore.updateOutlineContent(pList)
    return pList
}

const setEmptyValue = () => {
    const currentP = getCurrentParagraph()
    if (!currentP) {
        return
    }
    const pList = getContentDivAllPTagAndContent()
    if (!currentP.getAttribute('data-lvt') && pList.length == 1) {
        empty.value = true
        return
    }
    empty.value = false
}

const setupData = () => {

    // console.log('againEditOutlineList.value props.outlineList==>', currentOutlineList.value)
    if (currentOutlineList.value && currentOutlineList.value.length > 0) {
        isShowOutlineTextLoaded.value = true

        setTimeout(() => {
            formatOutlineByUserOutline(currentOutlineList.value)
            empty.value = false
            isShowOutlineTextLoaded.value = false
            formatPTagDataTitle()
        }, 1000)
    }
    // 监听 DOM 变化
    const observer = new MutationObserver(() => {
        // console.log('DOM 变化了')
        // 在这里执行你的逻辑
        setEmptyValue()
    })

    // 重新打开时清除内容和伪元素
    if (contentDivRef.value) {
        getContentDivAllPTagAndContent().forEach((element: any) => {
            element.textContent = ''
            if (element.getAttribute('data-lvt')) {
                element.removeAttribute('data-lvt')
            }
            if (element.getAttribute('data-lv')) {
                element.removeAttribute('data-lv')
            }
            if (element.getAttribute('data-lvl')) {
                element.removeAttribute('data-lvl')
            }
            if (element.getAttribute('data-id')) {
                element.removeAttribute('data-id')
            }
            if (element.getAttribute('data-result')) {
                element.removeAttribute('data-result')
            }
        })
    }

    outlineStore.updateOutlineLength(chapterWordsNumberChange())

    // 配置 MutationObserver
    const config = { attributes: true, childList: true, subtree: true }

    // 开始监听
    observer?.observe(contentDivRef.value as Node, config)

    // 在组件卸载时停止监听
    return () => {
        observer?.disconnect()
    }
}

watch(
    () => {
        return currentOutlineList.value
    },
    (_newValue, _oldValue) => {
        if (!props.isH5) {
            return
        }

        setupData()

        const pList = getContentDivAllPTagAndContent()
        outlineStore.updateOutlineContent(pList,)
    }
)

const uploadOutlineList = async (list: any) => {
    currentOutlineList.value = list

    setupData()
    let pList = getContentDivAllPTagAndContent()
    outlineStore.updateOutlineContent(pList)
}

onUnmounted(() => { })

onMounted(() => {
    // 当编辑时outlineList有值
    setupData()
})

defineExpose({
    // setupData,
    currentOutlineList: () => currentOutlineList.value,
    uploadOutlineList,
    clearDivContent,
    getContentDivAllPTagAndContent,
    formatOutlineTextContent,
    chapterWordsNumberChange
})


</script>

<style lang="scss" scoped>
[contenteditable]>div {
    display: inline;
}

[contenteditable] {
    -webkit-user-select: text;
    user-select: text;
    border: none;
    outline: none;
}


.predict-words {
    margin: 10px 0;
    font-weight: 500;
    font-size: 16px;
    color: #333333;
    line-height: 25px;
}

.title-area {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .title {
        font-size: 14px;
        color: #333333;
        max-width: 40%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
}

.title-button {
    display: flex;
    justify-content: flex-end;
    align-items: center;

    .level {
        background-color: #ffffff;
        border-radius: 5px;
        border: 1px solid #dddddd;
        padding: 5px 15px;
        font-size: 14px;

        font-weight: 400;
        color: #777777;
        line-height: 21px;
    }

    .active {
        border: 1px solid #1e99ff;
        color: #1e99ff;
        background: #ecf6ff;
    }
}

.textarea {
    width: 100%;
    height: 500px;
    position: relative;
    box-sizing: border-box;
}

.outline {
    width: 100%;
    height: 500px;
    border-radius: 5px;
    border: 1px solid #eeeeee !important;
    padding: 20px;
    overflow-y: auto;
    background-color: #ffffff;
    position: relative;
}


.outline[empty='true']::before {
    content: attr(placeholder);
    font-weight: 400;
    font-size: 15px;
    color: rgba(27, 35, 55, 0.48);
    position: absolute;
    left: 35px;
    right: 35px;
    // top: 8px;
    top: 33px;
    white-space: pre-wrap;
    // transform: translateY(100%);
    // -webkit-transform: translateY(100%);
}

.outline-content-paragraph {
    font-size: 15px;
    color: #000;
    font-weight: 400;
    line-height: 240%;
    padding-top: 10px;
    border: none;
    text-align: left;
    border-radius: 10px;
}

.outline-lv1 {
    font-size: 17px;
    font-weight: bold;
    color: #333333;
    line-height: 27px;
    margin: 0 15px;
}

.outline-lv1[data-result='false']::before {
    content: attr(data-lvt);
    font-size: 17px;
    font-weight: bold;
    color: #999999;
    line-height: 27px;
    margin: 20px 15px 15px 0;
    margin-right: 10px;
}

.outline-lv1[data-result='true']::before {
    content: '';
    display: inline-block;
    width: 8px;
    height: 8px;
    background-color: #1e99ff;
    border-radius: 50%;
    margin-right: 10px;
    vertical-align: middle;
}

.outline-lv2 {
    font-size: 16px;
    font-weight: 400;
    color: #333333;
    line-height: 23px;
    margin: 0 35px;
}

.outline-lv2[data-result='false']::before {
    content: attr(data-lvt);
    font-size: 16px;
    font-weight: semi-bold;
    color: #999999;
    line-height: 23px;
    padding: 0 15px 15px 0;
    padding-right: 10px;
}

.outline-lv2[data-result='true']::before {
    content: '';
    display: inline-block;
    width: 5px;
    height: 5px;
    border: 2px solid #1e99ff;
    border-radius: 50%;
    margin-right: 10px;
    vertical-align: middle;
}

.outline-lv3 {
    font-size: 15px;
    font-weight: 400;
    color: #333333;
    line-height: 23px;
    margin: 0 55px;
}

.outline-lv3[data-result='false']::before {
    content: attr(data-lvt);
    font-size: 15px;
    font-weight: 400;
    color: #999999;
    line-height: 23px;
    padding: 0 15px 15px 0;
    padding-right: 10px;
}

.outline-lv3[data-result='true']::before {
    content: '';
    display: inline-block;
    width: 7px;
    height: 7px;
    background-color: #1e99ff;
    margin-right: 10px;
    vertical-align: middle;
}
</style>