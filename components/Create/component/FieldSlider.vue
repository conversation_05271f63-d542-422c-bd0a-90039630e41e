<template>
    <div class="mb-6 ">
        <label class="flex items-center text-sm font-medium text-gray-700 mb-2">
            <span class="text-red-500 mr-1" v-if="fieldItem.isRequired == 'Y'">*</span>
            {{ fieldItem.fieldName }}
            <PopoverHelp :content="fieldItem.description" />
        </label>

        <div class="flex item-center justify-start">
            <div class="flex-1">
                <a-slider :value="modelValue" :min="4" :max="100" :range="false" @change="handleChange" />
            </div>
            <div class="flex item-center leading-[32px] w-[150px]">
                <a-input-number :value="modelValue" :min="4" :max="100" style="margin-left: 16px"
                    @change="handleNumberChange" />
                <span class="ml-1">万字</span>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { onMounted } from 'vue';
import PopoverHelp from '~/components/Common/PopoverHelp.vue';

const props = defineProps({
    fieldItem: {
        type: Object,
        required: true
    },
    modelValue: {
        type: Number,
        required: true,
        default: 4
    }
})

const emit = defineEmits(['update:modelValue'])

const handleChange = (value: number | number[]) => {
    if (Array.isArray(value)) {
        emit('update:modelValue', value[0])
    } else {
        emit('update:modelValue', value)
    }
}

const handleNumberChange = (value: number | string) => {
    emit('update:modelValue', value)
}

// 设置默认值
onMounted(() => {
    if (props.fieldItem.defaultValue && !props.modelValue) {
        emit('update:modelValue', props.fieldItem.defaultValue || 4)
    }
})

</script>
