<template>
    <div class="w-full mx-10 pt-50">
        <div class="text-center py-10 pb-20">应用升级中，暂时无法使用</div>
        <div class="recommend">
            <div v-if="categoryList && categoryList?.recommendList">
                <div class="text-lg font-bold text-black-800 mt-10 mb-4">或者试试以下应用吧~</div>
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                    <template v-for="(app, index) in categoryList?.recommendList" :key="index">
                        <AppCard v-if="app?.code" :icon="app.avatar" :title="app.name" :desc="app.description"
                            :to="toCreatePage(app.code)" :code="app.code" border-color="border-blue-200/50"
                            icon-bg-color="bg-blue-50" icon-color="#3b82f6" title-color="text-blue-800" />
                    </template>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { searchCategory } from '@/api/appCategory';
import { ToastService } from '@/services/toast';
import type { SearchCategoryInfo } from '@/services/types/appMessage';
import { onMounted, ref } from 'vue';
import AppCard from '~/components/AppCard.vue';

interface Props {
    keyword: string
}
const props = withDefaults(defineProps<Props>(), {})

const categoryList = ref<SearchCategoryInfo>()

const loadCategoryData = async () => {
    const res = await searchCategory({
        keyword: props.keyword
    })
    if (!res.ok) {
        ToastService.error(res.message || '加载失败')
        return
    }
    categoryList.value = res.data
}
const toCreatePage = (code: string) => {
    if (code == 'paper') {
        return '/create/paper'
    }
    if (code == 'ppt') {
        return '/create/ppt'
    }
    return `/create/${code}`
}

const setupData = () => {
    loadCategoryData()
}

onMounted(() => {
    setupData()
})
</script>

<style>
.submission-close-page {
    width: 100%;
    text-align: center;
    padding-top: 50px;

    .title {
        padding: 6px 19px;
        text-align: center;
        font-size: 18px;

        font-weight: 500;
        color: #333333;
        line-height: 29px;
    }
}
</style>