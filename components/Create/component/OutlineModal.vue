<template>
    <a-modal v-model:open="outlineModalStore.isVisible" title="输入指定大纲" :width="700" centered cancel-text="取消"
        ok-text="确定" @ok="handleOk" @cancel="handleCancel" :confirm-loading="outlineModalStore.isLoading">
        <div class="text-[14px] text-gray-500 mt-3">
            1、如您已有清晰完整的大纲：请在下方输入或粘贴大纲内容。
            <br />
            2、如您已有部分大纲：需要AI对大纲内容进行扩充、润色或修改，请先输入清晰具体的补充要求，如：“请帮我扩充完善以下大纲”；然后再输入/粘贴您已有的大纲内容。
        </div>
        <div class="py-[20px]">
            <a-textarea v-model:value="contentValue" :auto-size="{ minRows: 15, maxRows: 20 }" :maxlength="5000"
                placeholder="请输入大纲内容" class="outline-textarea" show-count />
        </div>
    </a-modal>
</template>

<script lang="ts" setup>
import { message } from 'ant-design-vue'
import { computed } from 'vue'
import { useOutlineModalStore } from '~/stores/outlineModal'

// 使用store
const outlineModalStore = useOutlineModalStore()

// 使用计算属性直接绑定到store的content
const contentValue = computed({
    get: () => outlineModalStore.content,
    set: (val) => outlineModalStore.updateContent(val)
})

// 直接调用store的方法，但增加非空验证
const handleOk = () => {
    // 验证内容是否为空或只有空格
    if (!outlineModalStore.content || outlineModalStore.content.trim() == '') {
        message.error('请输入大纲内容，不能为空')
        return
    }

    outlineModalStore.handleOk()
}

const handleCancel = () => {
    outlineModalStore.handleCancel()
}
</script>

<style scoped>
.outline-textarea {
    resize: none;
}
</style>