<template>
    <div class="mb-8">
        <div class="flex gap-3 flex-wrap">
            <div v-for="(item, index) in getFieldItemList()" :key="item.value" class="flex-1 min-w-[120px]">
                <div class="rounded-lg p-2.5 cursor-pointer transition-colors">
                    <div class="w-full text-left mb-2">
                        <span class="text-xs text-gray-500 truncate">示例</span>
                    </div>
                    <img :src="item.value" :alt="'示例图' + index" class="w-full object-contain rounded" />
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
const props = defineProps({
    fieldItem: {
        type: Object,
        required: true
    }
})

const getFieldItemList = () => {
    const item = props.fieldItem
    if (!item.options) return []

    // 提取以 http/https 开头的 URL，忽略中间的逗号
    const list = item.options.match(/https?:\/\/[^,\s]+/g) || []


    return list.map((url: string) => ({
        name: url,
        value: url,
        fieldCode: item.fieldCode
    }))
}
</script>
