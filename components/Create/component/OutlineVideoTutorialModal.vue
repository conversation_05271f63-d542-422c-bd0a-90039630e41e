<template>
    <Modal :model-value="modelValue" @update:model-value="$emit('update:modelValue', $event)" title="视频演示" :width="1000"
        :max-height="maxModalHeight" :show-footer="false">
        <div class="flex items-center justify-center w-full h-full bg-black rounded-lg"
            :style="{ height: `${maxVideoHeight}px` }">
            <div v-if="loading" class="absolute inset-0 flex items-center justify-center bg-gray-50 rounded-lg">
                <div class="animate-pulse text-gray-400">视频加载中...</div>
            </div>
            <video ref="videoRef" class="w-full h-full rounded-lg" style="object-fit: contain;" :src="videoSrc" controls
                preload="auto" @loadstart="loading = true" @loadeddata="loading = false" @ended="onVideoEnded">
            </video>
        </div>
    </Modal>
</template>

<script setup>
import { computed, onBeforeUnmount, ref } from 'vue';
import Modal from '~/components/Common/Modal.vue';

const props = defineProps({
    modelValue: Boolean,
    code: {
        type: String,
        default: ''
    },
    maxLevel: {
        type: Number,
        default: 1
    }
})

const emit = defineEmits(['update:modelValue'])
const videoRef = ref(null)
const loading = ref(true)

// 计算最大模态框高度（屏幕高度的90%）
const maxModalHeight = computed(() => {
    if (typeof window !== 'undefined') {
        return window.innerHeight * 0.9
    }
    return 700
})

// 计算最大视频容器高度（模态框高度减去标题栏高度）
const maxVideoHeight = computed(() => {
    return maxModalHeight.value - 80 // 标题栏大约占80px
})

const videoSrc = computed(() => {
    // if (props.code == 'ppt') {
    return 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/video/video4.mp4'
    // }
    // if (props.maxLevel == 1) {
    //     return 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/video/video1.mp4'
    // }
    // if (props.maxLevel == 2) {
    //     return 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/video/video2.mp4'
    // }
    // return 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/video/video3.mp4'
})

// 视频播放结束处理
const onVideoEnded = () => {
    emit('update:modelValue', false)
}

// 组件销毁前停止视频播放
onBeforeUnmount(() => {
    if (videoRef.value) {
        videoRef.value.pause()
    }
})
</script>

<style scoped></style>