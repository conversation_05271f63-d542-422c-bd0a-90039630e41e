<template>
    <div class="outline-body">
        <!-- ppt的操作说明 v-if="code == 'ppt'"-->
        <!-- <template> -->
        <div class="flex flex-wrap justify-start items-center gap-1 w-full outline-title-desc" v-if="code == 'ppt'">
            <span class="whitespace-nowrap">&middot; 操作说明：点</span>
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-500 flex-shrink-0" viewBox="0 0 20 20"
                fill="currentColor">
                <path fill-rule="evenodd"
                    d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z"
                    clip-rule="evenodd" />
            </svg>
            <span class="whitespace-nowrap">新增同级和新增子级，最多支持输入三级子标题；</span>
            <div class="flex items-center justify-start cursor-pointer" @click="handleOpenVideo">
                <a class="video whitespace-nowrap">视频演示</a>
                <img class="w-[23px] h-[18px] pl-[5px] cursor-pointer"
                    src="https://static-1256600262.file.myqcloud.com/h5/icon/video-icon.png" />
            </div>
        </div>
        <!-- </template> -->
        <template v-else>
            <div class="flex flex-wrap items-center justify-start gap-1 w-full outline-title-desc" v-if="maxLevel == 1">
                &middot; 操作说明：
                <a-tag :bordered="false" class="title-tag">Enter</a-tag>
                可新增同一级别；
                <a class="video" @click="handleOpenVideo">视频演示</a>
                <img @click="handleOpenVideo" style="width: 23px; height: 18px; padding-left: 5px; cursor: pointer"
                    src="https://static-1256600262.file.myqcloud.com/h5/icon/video-icon.png" />
            </div>
            <div class="flex flex-wrap items-center justify-start outline-title-desc space-x-1" v-else>
                &middot; 操作说明：
                <a-tag :bordered="false" style="margin-right: 0;">Enter</a-tag>
                可新增同一级别，
                <a-tag :bordered="false" style="margin-right: 0;">Tab</a-tag>
                /
                <a-tag :bordered="false" style="margin-right: 0;">Shift</a-tag>
                +
                <a-tag :bordered="false" style="margin-right: 0;">Tab</a-tag>
                可切换大纲级别；

                <a class="video" @click="handleOpenVideo">视频演示</a>
                <img @click="handleOpenVideo" style="width: 23px; height: 18px; padding-left: 5px; cursor: pointer"
                    src="https://static-1256600262.file.myqcloud.com/h5/icon/video-icon.png" />
            </div>
        </template>
        <!-- InputNotice作为标题和输入框中间的说明 -->
        <!-- Placeholder作为输入框的占位符 -->
        <!-- description作为标题后面的问号显示 -->
        <div class="outline-title-desc mt-[10px]" v-if="outlineInputNotice" v-for="item in outlineInputNotice"
            :key="item">
            &middot;
            {{ item }}
        </div>


    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
// import Iconfont from '@/components/Iconfont.vue'

import type { CreatorRequireFieldInfo } from '@/services/types/appMessage';

const props = defineProps({
    code: {
        type: String,
        required: true
    },
    maxLevel: {
        type: Number,
        required: true
    },
    fieldItem: {
        type: Object as PropType<CreatorRequireFieldInfo>,
        required: true
    }
})

const emit = defineEmits(['playVideo'])

//   const creatorInfo = computed(() => {
//     return props.creatorDetail
//   })

const outlineInputNotice = computed(() => {
    if (!props.fieldItem) {
        return []
    }
    if (!props.fieldItem.inputNotice || props.fieldItem.inputNotice.length == 0) {
        return []
    }
    const data = props.fieldItem.inputNotice.replace(/\n/g, '').split('；')
    return data
})

const handleOpenVideo = () => {
    emit('playVideo', props.maxLevel)
}
</script>

<style lang="scss">
.outline-body {
    width: 100%;

    .outline-title-desc {
        min-height: 32px;
        // margin-bottom: 10
        font-size: 14px;
        color: #777777;
        line-height: 21px;

        .video {
            font-size: 14px;
            line-height: 21px;
        }
    }

    .outline-title-desc div {
        &:not(.cursor-pointer) {
            width: 5px;
            height: 5px;
            display: inline-block;
            background-color: #777777;
            margin-right: 10px;
        }
    }
}
</style>