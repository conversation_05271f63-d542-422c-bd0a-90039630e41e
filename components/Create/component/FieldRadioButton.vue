<template>
    <div class="mb-8">
        <label class="flex items-center text-sm font-medium text-gray-700 py-2">
            <span class="text-red-500 mr-1" v-if="fieldItem.isRequired == 'Y'">*</span>
            {{ fieldItem.fieldName }}
            <PopoverHelp :content="fieldItem.description" />
        </label>
        <div class="flex py-2">
            <button v-for="(item, index) in getFieldItemList()" :key="item.value" @click="handleChangeRadio(item.value)"
                :class="[
                    'px-4 py-2 text-sm font-medium border transition-colors duration-200 relative',
                    'focus:outline-none focus:z-10',
                    index === 0 ? 'rounded-l-md' : '',
                    index === getFieldItemList().length - 1 ? 'rounded-r-md' : '',
                    index > 0 ? '-ml-px' : '',
                    radioValue === item.value
                        ? 'bg-blue-600 text-white border-blue-600 hover:bg-blue-700 z-10'
                        : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                ]">
                {{ getDisplayText(item) }}
            </button>
        </div>
    </div>
</template>
<script setup lang="ts">
import PopoverHelp from '~/components/Common/PopoverHelp.vue'
import { ChartType, EditorContentType } from '~/utils/constants'

const props = defineProps({
    fieldItem: {
        type: Object,
        required: true
    },
    radioValue: {
        type: String,
        required: true
    }
})

const emit = defineEmits(['update:radioValue'])

const handleChangeRadio = (value: any) => {
    emit('update:radioValue', value)
}

const getFieldItemList = () => {
    const item = props.fieldItem
    if (!item.options) {
        return []
    }
    const list = item.options.split(',')
    const data = list.map((child: any) => {
        return { name: child, value: child, fieldCode: item.fieldCode }
    })
    return data
}

const getDisplayText = (item: any) => {
    // 如果是图表类型字段，使用 ChartType 映射
    if (props.fieldItem.fieldCode == 'chartType') {
        return ChartType[item.name as keyof typeof ChartType] || item.name
    }

    // 如果是图片比例字段，直接显示原值
    if (props.fieldItem.fieldCode == 'imageRatio') {
        return item.name
    }

    // 其他情况，尝试从 EditorContentType 获取，如果没有则显示原值
    return EditorContentType[item.name as keyof typeof EditorContentType] || item.name
}

</script>
