<template>
    <div class="flex flex-row items-center justify-between space-x-2">
        <h2 class="text-xm font-medium text-gray-600">写作指南</h2>

        <div class="flex items-center space-x-4">
            <!-- 步骤 1 -->
            <div class="flex items-center">
                <div class="w-5 h-5 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 text-sm">
                    1
                </div>
                <span class="ml-2 text-sm text-gray-600">选择写作应用</span>
            </div>

            <!-- 箭头 -->
            <svg class="w-3 h-3 text-gray-400" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>

            <!-- 步骤 2 -->
            <div class="flex items-center">
                <div class="w-5 h-5 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 text-sm">
                    2
                </div>
                <span class="ml-2 text-sm text-gray-600">输入写作要求</span>
            </div>

            <!-- 箭头 -->
            <svg class="w-3 h-3 text-gray-400" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>

            <!-- 步骤 3 -->
            <div class="flex items-center">
                <div class="w-5 h-5 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 text-sm">
                    3
                </div>
                <span class="ml-2 text-sm text-gray-600">AI生成初稿</span>
            </div>

            <!-- 箭头 -->
            <svg class="w-3 h-3 text-gray-400" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>

            <!-- 步骤 4 -->
            <div class="flex items-center">
                <div class="w-5 h-5 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 text-sm">
                    4
                </div>
                <span class="ml-2 text-sm text-gray-600">AI在线编辑</span>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
// 如果需要添加交互功能可以在这里编写
</script>

<style scoped>
/* 如果需要添加自定义样式可以在这里编写 */
</style>
