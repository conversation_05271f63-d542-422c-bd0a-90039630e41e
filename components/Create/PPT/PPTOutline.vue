<template>
  <div class="outline_div">
    <div class="flex-between" v-if="!empty">
      <div>
        <template v-if="outlineTree.length > 0">
          PPT页数：约
          <text style="color: #1e99ff">{{ pptPageNumber }}</text>
          页
        </template>
      </div>
      <div class="flex items-center gap-2">
        <Popover v-slot="{ close }" class="relative">
          <!-- 返回上次按钮 -->
          <PopoverButton v-if="isShowOutlineCallbackButton"
            class="inline-flex items-center gap-2 px-6 py-2 text-base font-medium text-white bg-gradient-to-r from-blue-400 to-indigo-400 hover:from-blue-500 hover:to-indigo-500 rounded-xl">
            <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd"
                d="M7.707 3.293a1 1 0 010 1.414L5.414 7H11a7 7 0 017 7v2a1 1 0 11-2 0v-2a5 5 0 00-5-5H5.414l2.293 2.293a1 1 0 11-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                clip-rule="evenodd" />
            </svg>
            <span>返回上次</span>
          </PopoverButton>

          <PopoverPanel
            class="absolute right-0 z-10 mb-2 bg-white rounded-lg shadow-lg bottom-full w-72 ring-1 ring-black ring-opacity-5">
            <div class="p-4">
              <h3 class="mb-2 text-sm font-medium text-gray-900">确定返回上一次加载的大纲吗?</h3>
              <div class="flex justify-end gap-2 mt-3">
                <button @click="() => { close(); onPopoverCancel(); }"
                  class="px-3 py-1.5 text-sm text-gray-600 hover:text-gray-800">
                  取消
                </button>
                <button @click="() => { close(); onPopoverConfirm(); }"
                  class="px-3 py-1.5 text-sm text-white bg-blue-500 rounded hover:bg-blue-600">
                  确定
                </button>
              </div>
            </div>
          </PopoverPanel>
        </Popover>

        <button @click="handleReloadOutline" v-if="outlineCount > 0"
          class="inline-flex items-center gap-2 px-6 py-2 text-base font-medium text-white bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 rounded-xl">
          <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd"
              d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z"
              clip-rule="evenodd" />
          </svg>
          <span>换个大纲</span>
        </button>
      </div>
    </div>

    <!-- <div class="flex-1 overflow-y-auto bg-white rounded-lg outline-body"> -->
    <!-- 使用递归组件处理大纲结构 -->
    <!-- <OutlineSubItem :items="outlineTree" :parent-idx="''" :base-level="1" @update="update" @focus="onFocus"
        @blur="onBlur" @operate="operate" />
    </div> -->
    <div class="outline-body" v-if="outlineTree.length > 0">
      <div v-for="(chapter, chapterIdx) in outlineTree" :key="version + '_' + chapterIdx" style="margin: 10px">
        <template v-if="chapterIdx == 0">
          <div class="chapter_div">
            <div class="chapter_header">封面</div>
            <div class="content-point"></div>
            <div class="chapter_title" style="flex: 1;">
              <input v-model="topic" @change="handleTopicChange" style="color: #333333;" @focus="onFocus('topic')"
                @blur="onBlur" placeholder="请输入文字" class="weui-input"
                :class="{ 'weui-input-focus': currentFocus == `topic` }" />
            </div>
          </div>
          <div class="chapter_div">
            <div class="chapter_header">目录</div>
            <div class="content-point"></div>
            <div class="chapter_title">目录</div>
          </div>
        </template>
        <div class="hover-container chapter_div">
          <div class="chapter_header">章节</div>
          <div class="content-point"></div>
          <div class="chapter_title">第{{ chapterIdx + 1 }}章</div>

          <div class="flex-between chapter_input" style="margin-left: -30px;">
            <div class="chapter_name chapter-outline-input">
              <input v-model="chapter.name" @change="update" @focus="onFocus(`${chapterIdx}`)" @blur="onBlur"
                style="margin-left: 5px; " placeholder="请输入文字" class="weui-input"
                :class="{ 'weui-input-focus': currentFocus == `${chapterIdx}` }" />
            </div>
            <div class="operate_div">
              <PPTAndBookOutlineMenu :list="outlineTree" :index="chapterIdx" :level="1" :max-level="3"
                @operate="operate" />
              <Popover v-slot="{ close }" class="relative">
                <PopoverButton class="flex items-center p-1.5 rounded-lg hover:bg-gray-100">
                  <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-gray-500" viewBox="0 0 20 20"
                    fill="currentColor">
                    <path fill-rule="evenodd"
                      d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                      clip-rule="evenodd" />
                  </svg>
                </PopoverButton>

                <PopoverPanel
                  class="absolute z-10 right-0 w-[260px] bottom-full mb-2 bg-white rounded-lg shadow-lg ring-1 ring-black ring-opacity-5 overflow-hidden">
                  <div class="p-3">
                    <div class="text-sm font-medium text-left text-gray-900">是否确认删除该标题？若有下级子标题将一起删除。
                    </div>
                    <div class="flex justify-end mt-2 space-x-2">
                      <button @click="close" class="px-2 py-1 text-xs text-gray-600 hover:text-gray-800">
                        取消
                      </button>
                      <button @click="() => { close(); operate(outlineTree, chapterIdx, 3); }"
                        class="px-2 py-1 text-xs text-white bg-blue-500 rounded hover:bg-blue-600">
                        确定
                      </button>
                    </div>
                  </div>
                </PopoverPanel>
              </Popover>
            </div>
          </div>
        </div>

        <div v-for="(page, pageIdx) in chapter.children" :key="chapterIdx + '-' + pageIdx">
          <div class="hover-container page_div">
            <div class="chapter_header">内容</div>
            <div class="horizontal-line"></div>
            <div class="page-point"></div>
            <span class="page_number">{{ chapterIdx + 1 }}.{{ pageIdx + 1 }}</span>
            <div class="flex-between chapter_input">
              <div class="page_name chapter-outline-input" style="width: 100%;">
                <input v-model="page.name" @change="update" @focus="onFocus(`${chapterIdx}-${pageIdx}`)" @blur="onBlur"
                  placeholder="请输入文字" class="weui-input"
                  :class="{ 'weui-input-focus': currentFocus == `${chapterIdx}-${pageIdx}` }" />
              </div>
              <div class="operate_div">
                <PPTAndBookOutlineMenu :list="chapter.children" :index="pageIdx" :level="2" :max-level="3"
                  @operate="operate" />
                <Popover v-slot="{ close }" class="relative">
                  <PopoverButton class="flex items-center p-1.5 rounded-lg hover:bg-gray-100">
                    <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-gray-500" viewBox="0 0 20 20"
                      fill="currentColor">
                      <path fill-rule="evenodd"
                        d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                        clip-rule="evenodd" />
                    </svg>
                  </PopoverButton>

                  <PopoverPanel
                    class="absolute z-10 right-0 w-[260px] bottom-full mb-2 bg-white rounded-lg shadow-lg ring-1 ring-black ring-opacity-5 overflow-hidden">
                    <div class="p-3">
                      <div class="text-sm font-medium text-left text-gray-900">是否确认删除该标题？若有下级子标题将一起删除。
                      </div>
                      <div class="flex justify-end mt-2 space-x-2">
                        <button @click="close" class="px-2 py-1 text-xs text-gray-600 hover:text-gray-800">
                          取消
                        </button>
                        <button @click="() => { close(); operate(chapter.children, pageIdx, 3); }"
                          class="px-2 py-1 text-xs text-white bg-blue-500 rounded hover:bg-blue-600">
                          确定
                        </button>
                      </div>
                    </div>
                  </PopoverPanel>
                </Popover>
              </div>
            </div>
          </div>

          <div v-for="(title, titleIdx) in page.children" :key="chapterIdx + '-' + pageIdx + '-' + titleIdx">
            <div class="hover-container title_div">
              <div class="chapter_header" style="border: none"></div>
              <div class="horizontal-line" style="width: 65px"></div>
              <div class="title-point"></div>
              <span class="title_number">
                {{ chapterIdx + 1 }}.{{ pageIdx + 1 }}.{{ titleIdx + 1 }}
              </span>
              <div class="flex-between chapter_input">
                <div class="title_name chapter-outline-input" style="width: 100%;">
                  <input v-model="title.name" @change="update" @focus="onFocus(`${chapterIdx}-${pageIdx}-${titleIdx}`)"
                    @blur="onBlur" placeholder="请输入文字" class="weui-input" :class="{
                      'weui-input-focus': currentFocus == `${chapterIdx}-${pageIdx}-${titleIdx}`
                    }" />
                </div>
                <div class="operate_div">
                  <PPTAndBookOutlineMenu :list="page.children" :index="titleIdx" :level="3" :max-level="3"
                    @operate="operate" />
                  <Popover v-slot="{ close }" class="relative">
                    <PopoverButton class="flex items-center p-1.5 rounded-lg hover:bg-gray-100">
                      <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-gray-500" viewBox="0 0 20 20"
                        fill="currentColor">
                        <path fill-rule="evenodd"
                          d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                          clip-rule="evenodd" />
                      </svg>
                    </PopoverButton>

                    <PopoverPanel
                      class="absolute z-10 right-0 w-[170px] bottom-full mb-2 bg-white rounded-lg shadow-lg ring-1 ring-black ring-opacity-5 overflow-hidden">
                      <div class="p-3">
                        <div class="text-sm font-medium text-left text-gray-900">是否确认删除该标题？
                        </div>
                        <div class="flex justify-end mt-2 space-x-2">
                          <button @click="close" class="px-2 py-1 text-xs text-gray-600 hover:text-gray-800">
                            取消
                          </button>
                          <button @click="() => { close(); operate(page.children, titleIdx, 3); }"
                            class="px-2 py-1 text-xs text-white bg-blue-500 rounded hover:bg-blue-600">
                            确定
                          </button>
                        </div>
                      </div>
                    </PopoverPanel>

                  </Popover>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="chapter_div" v-if="chapterIdx == outlineTree.length - 1">
          <div class="chapter_header">封底</div>
          <div class="content-point"></div>
          <div class="chapter_title">感谢</div>
        </div>
      </div>
    </div>

    <div class="line-end"></div>

    <!-- 选择模版按钮区域 -->
    <div class="flex justify-center my-8">
      <button @click="handleOpenTemplate"
        class="relative px-16 py-3 overflow-hidden text-base font-medium text-white bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 rounded-xl group">
        <span class="relative z-10">选择模版</span>
      </button>
    </div>

    <PPTSelectTemplateModal v-if="pptTemplateVisible" v-model="pptTemplateVisible" :topic="topic"
      @submit="onSelectTemplateSubmit" />
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watchEffect } from 'vue';
//   import { PlusOutlined, RollbackOutlined } from '@ant-design/icons-vue'
//   import Iconfont from '@/components/Iconfont.vue'
import type { CreatorsInfo } from '@/services/types/appMessage';
import { StarloveConstants } from '@/utils/starloveConstants';
import { Popover, PopoverButton, PopoverPanel } from '@headlessui/vue';
import PPTSelectTemplateModal from '~/components/Create/PPT/PPTSelectTemplateModal.vue';

import { message } from 'ant-design-vue';

interface Props {
  outlineData: any
  creatorDetail: CreatorsInfo
  changeOutlineCount?: number
}
const props = withDefaults(defineProps<Props>(), {})
const emit = defineEmits(['loadOutline', 'submit'])
const version = ref(0)
const currentFocus = ref()

const openPopover = ref(false)


const topic = ref()
const isShowOutlineCallbackButton = ref(false)

const pptTemplateVisible = ref(false)

const outlineTree = ref<any[]>([])
const currentOutlineIndex = ref(0)


const outlineCount = computed(() => {
  return 3 - (props.changeOutlineCount || 0)
})

const fieldOutline = computed(() => {
  return props.creatorDetail?.details?.filter((item) => item.fieldCode == 'outline')
})

const predictWordsNumber = computed(() => {
  const list = fieldOutline.value
  if (list.length == 0) {
    return 0
  }
  return list[0].maxLength || 0
})

const transformCatalogs = (catalogs: any) => {
  return catalogs.map((catalog: any) => ({
    name: catalog.catalog,
    children: catalog.pages.map((page: any) => ({
      name: page.title,
      children: page.points.map((point: any) => ({
        name: point,
      })),
    })),
  }))
}

const empty = computed(() => props.outlineData?.catalogs?.length == 0)

const pptPageNumber = computed(() => {
  if (!outlineTree.value) {
    return 0
  }
  if (!Array.isArray(outlineTree.value)) {
    return 0
  }
  if (outlineTree.value.length == 0) {
    return 0
  }
  let _pptPageNumber = 0

  outlineTree.value.map((item) => {
    if (item.children && item.children.length > 0) {
      _pptPageNumber += item.children.length
    } else {
      _pptPageNumber++
    }
  })

  return predictWordsNumber.value + _pptPageNumber
})

const handleOpenTemplate = () => {
  pptTemplateVisible.value = true
}

const onSelectTemplateSubmit = (templateInfo: any) => {
  emit('submit', { outlineTree: outlineTree.value, topic: topic.value, pptPageNumber: pptPageNumber.value, templateFileName: templateInfo?.fileName, templateName: templateInfo.name, pptData: templateInfo.pptData });
}

const update = () => {
  let outlineMd = ''
  // outlineMd += '# ' + topic.value + '\n'
  outlineMd += appendMd(outlineTree.value)
  version.value++
}

function appendMd(children: any) {
  let str = ''
  for (let i = 0; i < children.length; i++) {
    let level = children[i].level
    if (level == 0) {
      str += '- '
    } else {
      for (let j = 0; j < level; j++) {
        str += '#'
      }
      str += ' '
    }
    str += children[i].name + '\n'
    if (children[i].children) {
      str += appendMd(children[i].children)
    }
  }
  return str
}

const operate = (children: any, idx: number, type: number) => {
  let current = children[idx]
  if (type == 1) {
    if (idx == children.length) {
      children.push({
        level: 1,
        name: '',
        children: [],
      })
    } else {
      children.splice(idx, 0, {
        level: current.level,
        name: '',
        children: [],
      })
    }
  } else if (type == 2) {
    ; (current.children = current.children || []).splice(0, 0, {
      level: current.level + 1,
      name: '',
      children: [],
    })
  } else if (type == 3) {
    // 删除
    children.splice(idx, 1)
  }
  update()
}

const onFocus = (value: any) => {
  currentFocus.value = value
}
const onBlur = () => {
  currentFocus.value = ''
}

const handleTopicChange = (e: any) => {
  console.log('handleTopicChange ==>', e)
}

const getStorePPTOutlineList = () => {
  const data = storage.get(StarloveConstants.keyOflocalStorage.aMaximumOfThreePPTOutlineData)
  if (!data) {
    return []
  }
  return JSON.parse(data)
}

const getNumberOfTimesThePPTOutlineHasBeenLoaded = () => {
  const count =
    storage.get(StarloveConstants.keyOflocalStorage.numberOfTimesThePPTOutlineHasBeenLoaded) || 0
  return count
}

const setPPTOutlineDataToStorage = (list: any) => {
  storage.set(
    StarloveConstants.keyOflocalStorage.aMaximumOfThreePPTOutlineData,
    JSON.stringify(list)
  )
}

const loadPPTOutlineData = async () => {
  const currentCount = getNumberOfTimesThePPTOutlineHasBeenLoaded()
  storage.set(StarloveConstants.keyOflocalStorage.numberOfTimesThePPTOutlineHasBeenLoaded, currentCount + 1)

  // 加载新大纲前，更新当前索引为最新位置
  const historyList = getStorePPTOutlineList()
  currentOutlineIndex.value = historyList.length

  emit('loadOutline')
}

// 添加标记变量
const isReturningToPrevious = ref(false)

watchEffect(() => {
  if (props.outlineData) {
    topic.value = props.outlineData?.topic

    if (props.outlineData?.catalogs?.length > 0) {
      outlineTree.value = transformCatalogs(props.outlineData.catalogs)

      // 保存新的大纲数据到历史记录
      const historyList = getStorePPTOutlineList()
      const currentCount = getNumberOfTimesThePPTOutlineHasBeenLoaded()

      // 只在加载新大纲且不是返回操作时保存
      if (currentCount > historyList.length && !isReturningToPrevious.value) {
        const newList = [...historyList, props.outlineData]
        // 最多保存3个大纲
        if (newList.length > 3) {
          newList.shift()
        }
        // console.log("watchEffect newList ==>", newList)
        setPPTOutlineDataToStorage(newList)
        currentOutlineIndex.value = newList.length - 1
      }

      // 当历史记录中有多个大纲时显示返回按钮
      const currentHistoryList = getStorePPTOutlineList()
      isShowOutlineCallbackButton.value = currentHistoryList.length > 1
    }
  }
})

const onPopoverConfirm = () => {
  const historyList = getStorePPTOutlineList()

  if (!historyList || historyList.length <= 1) {
    return
  }

  try {
    isReturningToPrevious.value = true

    // 删除最后一条数据并更新存储
    const newList = historyList.slice(0, -1)
    // console.log("newList =>", newList)
    setPPTOutlineDataToStorage(newList)

    // 返回到上一个大纲
    const previousOutline = newList[newList.length - 1]
    outlineTree.value = transformCatalogs(previousOutline.catalogs)
    topic.value = previousOutline.topic

    // 更新索引和按钮状态
    currentOutlineIndex.value = newList.length - 1
    isShowOutlineCallbackButton.value = newList.length > 1
  } finally {
    isReturningToPrevious.value = false
  }

  openPopover.value = false
}

const handleReloadOutline = () => {
  const count = getNumberOfTimesThePPTOutlineHasBeenLoaded()
  if (parseInt(count) >= 2) {
    message.info('更换大纲次数已用完')
    return
  }
  loadPPTOutlineData()
}





const onPopoverCancel = () => { openPopover.value = false }


onMounted(() => {
  const historyList = getStorePPTOutlineList()
  currentOutlineIndex.value = historyList.length - 1
  isShowOutlineCallbackButton.value = historyList.length > 1
})

defineExpose({
  topic,
  outlineTree,
  pptPageNumber,
})
</script>

<style scoped lang="scss">
.outline_div {
  position: relative;
  width: 100%;
  max-width: 1000px;
  margin: 10px auto;
  padding: 0 20px;
  display: flex;
  min-height: 500px;
  flex-direction: column;

  .outline-body {
    position: relative;
    margin-top: 20px;
    padding: 0 24px;
    background: #ffffff;
    min-height: 500px;
    border: 1px solid #d1e5f5;
    border-radius: 10px;

    &::before {
      content: '';
      position: absolute;
      left: 107px;
      top: 0px;
      bottom: 0px;
      width: 2px;
      z-index: 8;
      background: #ecf6ff;
    }

    .operate_div {
      display: none;
      flex-shrink: 0;
    }

    .subject_div {
      text-align: center;
      margin-bottom: 0.5em;
      color: #3c3c3c;
      font-weight: 600;
      font-size: 28px;
      line-height: 1.2;
    }

    .chapter_div {
      position: relative;
      display: flex;
      align-items: center;
      padding: 10px 0;
      height: 54px;
      width: 100%;
      gap: 20px;

      &:first-child {
        .chapter_title {
          flex: none;
          width: auto;
        }
      }
    }

    .chapter_header {
      flex-shrink: 0;
      min-width: 60px;
      border-radius: 5px;
      border: 1px solid #c0e3ff;
      color: #1e99ff;
      font-size: 13px;
      line-height: 22px;
      background: #ecf6ff;
      text-align: center;
      padding: 0px 10px;
    }

    .content-point {
      width: 6px;
      height: 6px;
      background-color: #1e99ff;
      border-radius: 50%;
      position: relative;
      left: 0px;
      z-index: 9;
      flex-shrink: 0;
    }

    .chapter_title {
      font-weight: 500;
      font-size: 15px;
      color: #999999;
      line-height: 23px;
      text-align: left;
      flex: none;
      width: auto;
      white-space: nowrap;
      margin-right: 8px;
    }

    .chapter_input {
      flex: 1;
      min-width: 0;
      display: flex;
      gap: 10px;
      // margin-left: -4px;

      .chapter-outline-input {
        flex: 1;
        min-width: 0;
      }

      .weui-input {
        width: 100%;
        max-width: 100%;
        background-color: #ffffff;
      }
    }

    .page-point {
      width: 6px;
      height: 6px;
      border-radius: 50%;
      border: 3px solid #1e99ff;
      margin-right: 10px;
      position: relative;
      left: 0px;
      z-index: 9;
      flex-shrink: 0;
    }

    .title-point {
      width: 6px;
      height: 6px;
      margin-right: 10px;
      background: #1e99ff;
      position: relative;
      left: 0px;
      z-index: 9;
      flex-shrink: 0;
    }

    .horizontal-line {
      width: 34px;
      height: 2px;
      margin-left: 3px;
      background: #ecf6ff;
    }

    .chapter_div:hover {
      background: #ecf6ff;
    }

    .page_div:hover {
      background: #ecf6ff;
    }

    .title_div:hover {
      background: #ecf6ff;
    }

    .hover-container:hover .operate_div {
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      margin: 0 10px;
    }

    .dropdown-overlay {
      pointer-events: auto;
    }

    .chapter_name,
    .page_name,
    .title_name {
      text-align: left;
      display: inline-block;
    }

    .chapter_name,
    .page_name {
      font-weight: bold;
    }

    .page_div {
      display: flex;
      align-items: center;
      padding: 10px 0;
    }

    .title_div {
      display: flex;
      align-items: center;
      padding: 10px 0;
    }

    .page_number {
      font-weight: 400;
      font-size: 13px;
      line-height: 19px;
      margin-right: 5px;
      color: #999999;
    }

    .title_number {
      font-weight: 400;
      font-size: 13px;
      line-height: 19px;
      margin-right: 5px;
      color: #999999;
    }

    .weui-input {
      width: 100%;
      max-width: 100%;
      border-radius: 5px;
      height: 35px;
      line-height: 35px;
      padding: 5px 12px;
      font-size: 15px;
      border-width: 1px;
      border-style: inset;
      border-color: transparent;
      box-sizing: border-box;
      background-color: #ffffff;
    }

    .weui-input-focus {
      border-color: #1e99ff;
    }

    .menu-items {
      &:hover {
        display: block;
      }
    }
  }

  .flex-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .flex-center {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

@media (max-width: 768px) {
  .outline_div {
    padding: 0 15px;
    max-width: 100%;
  }
}
</style>
