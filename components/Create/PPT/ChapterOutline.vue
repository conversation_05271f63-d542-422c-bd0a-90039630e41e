<template>
    <div class="outline_div">
        <div class="outline-body" v-if="outlineTree.length > 0">
            <!-- 封面 -->
            <div v-if="code != 'book'" style="margin: 10px">
                <div class="chapter_div">
                    <div class="chapter_header">封面</div>
                    <div class="content-point"></div>
                    <div class="chapter_title" style="flex: 1;">
                        <input v-model="topic" @change="handleTopicChange" style="color: #333333;"
                            @focus="onFocus('topic')" @blur="onBlur(null, -1)" placeholder="请输入文字" class="weui-input"
                            :class="{ 'weui-input-focus': currentFocus == `topic` }" />
                    </div>
                </div>
                <div class="chapter_div">
                    <div class="chapter_header">目录</div>
                    <div class="content-point"></div>
                    <div class="chapter_title">目录</div>
                </div>
            </div>

            <!-- 使用递归组件处理大纲结构 -->
            <OutlineSubItem :items="outlineTree" :parent-idx="''" :base-level="1" :max-level="props.maxLevel"
                :language="language" @update="update" @focus="onFocus" @blur="onBlur" @operate="operate" />

            <!-- 封底 -->
            <div style="margin: 10px" v-if="code != 'book'">
                <div class="chapter_div">
                    <div class="chapter_header">封底</div>
                    <div class="content-point"></div>
                    <div class="chapter_title">感谢</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';

import OutlineSubItem from './OutlineSubItem.vue';


interface Props {
    outlineTree: any
    code: string
    maxLevel?: number
    language?: string
}
const props = withDefaults(defineProps<Props>(), {
    maxLevel: 4 // 默认最大层级为4
})
const emit = defineEmits(['update', 'focus', 'blur', 'operate', 'delete'])
const version = ref(0)
const currentFocus = ref()
const topic = ref()

//   const outlineTree = ref<any[]>([])

const update = () => {
    // let outlineMd = ''
    // outlineMd += appendMd(props.outlineTree)
    version.value++
    emit('update');
}

// function appendMd(children: any) {
//     let str = ''
//     for (let i = 0; i < children.length; i++) {
//         let level = children[i].level
//         if (level == 0) {
//             str += '- '
//         } else {
//             for (let j = 0; j < level; j++) {
//                 str += '#'
//             }
//             str += ' '
//         }
//         str += children[i].name + '\n'
//         if (children[i].children) {
//             str += appendMd(children[i].children)
//         }
//     }
//     return str
// }

/**
 * 
 * @param children 
 * @param idx 
 * @param type 1、向上增加或向下增加；2、增加子级；3、删除
 */
const operate = (children: any, idx: number, type: number) => {
    let current = children[idx]
    if (type == 1) {
        if (idx == children.length) {
            children.push({
                level: current ? current.level : children[children.length - 1].level,
                name: '',
                key: '',
                children: [],
            })
        } else {
            children.splice(idx, 0, {
                level: current.level,
                name: '',
                key: '',
                children: [],
            })
        }
    } else if (type == 2) {
        ; (current.children = current.children || []).splice(0, 0, {
            level: current.level + 1,
            name: '',
            key: '',
            children: [],
        })
    } else if (type == 3) {
        // 删除
        children.splice(idx, 1)
        emit('delete', children.key)
    }
    update()
}

const onFocus = (value: any) => {
    currentFocus.value = value
}

const onBlur = (chapter: any, chapterIdx: number) => {
    currentFocus.value = ''
    if (chapter !== null) {
        emit('blur', chapter, chapterIdx)
    }
}

const handleTopicChange = (e: any) => {
    // console.log('handleTopicChange ==>', e)
    emit('update');
}


onMounted(() => {

})

defineExpose({
    topic,
})
</script>

<style scoped lang="scss">
.outline_div {
    position: relative;
    width: 100%;
    max-width: 1000px;
    margin: 10px auto;
    padding: 0 20px;
    display: flex;
    min-height: 500px;
    flex-direction: column;

    .outline-body {
        position: relative;
        margin-top: 20px;
        padding: 10px 24px;
        background: #ffffff;
        min-height: 500px;
        border: 1px solid #d1e5f5;
        border-radius: 10px;
        overflow-x: hidden;

        &::before {
            content: '';
            position: absolute;
            left: 107px;
            top: 0px;
            bottom: 0px;
            width: 2px;
            z-index: 8;
            background: #ecf6ff;
        }

        .operate_div {
            display: none;
            flex-shrink: 0;
        }

        .subject_div {
            text-align: center;
            margin-bottom: 0.5em;
            color: #3c3c3c;
            font-weight: 600;
            font-size: 28px;
            line-height: 1.2;
        }

        .chapter_div {
            position: relative;
            display: flex;
            align-items: center;
            padding: 10px 0;
            height: 54px;
            width: 100%;
            gap: 20px;

            &:first-child {
                .chapter_title {
                    flex: none;
                    width: auto;
                }
            }
        }

        .chapter_header {
            flex-shrink: 0;
            min-width: 60px;
            border-radius: 5px;
            border: 1px solid #c0e3ff;
            color: #1e99ff;
            font-size: 13px;
            line-height: 22px;
            background: #ecf6ff;
            text-align: center;
            padding: 0px 10px;
        }

        .content-point {
            width: 6px;
            height: 6px;
            background-color: #1e99ff;
            border-radius: 50%;
            position: relative;
            left: 0px;
            z-index: 9;
            flex-shrink: 0;
        }

        .chapter_title {
            font-weight: 500;
            font-size: 15px;
            color: #999999;
            line-height: 23px;
            text-align: left;
            flex: none;
            width: auto;
            white-space: nowrap;
            margin-right: 8px;
        }

        .weui-input {
            width: 100%;
            max-width: 100%;
            border-radius: 5px;
            height: 35px;
            line-height: 35px;
            padding: 5px 12px;
            font-size: 15px;
            border-width: 1px;
            border-style: inset;
            border-color: transparent;
            box-sizing: border-box;
            background-color: #ffffff;
        }

        .weui-input-focus {
            border-color: #1e99ff;
        }

        .hover-container:hover .operate_div {
            display: flex;
            justify-content: space-evenly;
            align-items: center;
            margin: 0 10px;
        }
    }

    .flex-between {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .flex-center {
        display: flex;
        justify-content: center;
        align-items: center;
    }
}

@media (max-width: 768px) {
    .outline_div {
        padding: 0 15px;
        max-width: 100%;
    }
}
</style>