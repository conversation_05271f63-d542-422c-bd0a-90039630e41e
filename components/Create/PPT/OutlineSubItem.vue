<template>
    <div class="w-full">
        <div v-for="(item, idx) in items" :key="`${parentIdx}-${idx}`" class="w-full">
            <!-- 根据层级显示不同样式 -->
            <div class="hover-container w-full flex-1" :class="getLevelClass()">
                <div class="chapter_header " :class="getLevelHeaderClass()">{{ getLevelName() }}</div>
                <!-- 不同层级使用不同的连接线和点样式 -->
                <template v-if="baseLevel === 1">
                    <!-- 章节样式 -->
                    <div class="content-point ml-[40px] mr-[10px]"></div>
                </template>
                <template v-else-if="baseLevel === 2">
                    <!-- 内容样式 -->
                    <div class="horizontal-line" :style="{ width: `${24 + baseLevel * 14}px` }"></div>
                    <div class="page-point "></div>
                </template>
                <template v-else-if="baseLevel === 3">
                    <!-- 标题样式 -->
                    <div class="horizontal-line" :style="{ width: `${24 + baseLevel * 14}px` }"></div>
                    <div class="title-point "></div>
                </template>
                <template v-else>
                    <!-- 子标题样式 -->
                    <div class="horizontal-line" :style="{ width: `${24 + baseLevel * 14}px` }"></div>
                    <div class="sub-point"></div>
                </template>

                <!-- 显示序号 -->
                <span :class="getLevelNumberClass()"> {{ getFullIndex(idx) }} </span>

                <div class="flex flex-between chapter_input w-full flex-1">
                    <div :class="[getLevelNameClass(), 'chapter-outline-input flex-1 w-full']">
                        <!-- 大纲内容输入框区域 -->
                        <input v-model="item.name" @change="$emit('update')"
                            @focus="$emit('focus', getFullFocusIndex(idx))" @blur="$emit('blur', item, getChapterIdx())"
                            placeholder="请输入文字" class="weui-input" :class="{
                                'weui-input-focus': false
                            }" />
                    </div>
                    <div class="operate_div">
                        <PPTAndBookOutlineMenu :list="items" :index="idx" :parentIdx="parentIdx" :level="baseLevel"
                            :maxLevel="maxLevel"
                            @operate="(list, index, type) => $emit('operate', list, index, type)" />

                        <!-- 大纲操作-删除按钮区域 -->
                        <Popover v-slot="{ close }" class="relative">
                            <PopoverButton class="flex items-center p-1.5 rounded-lg hover:bg-gray-100">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500"
                                    viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd"
                                        d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                                        clip-rule="evenodd" />
                                </svg>
                            </PopoverButton>

                            <PopoverPanel :class="[
                                'absolute z-10 right-0 w-[260px] bg-white rounded-lg shadow-lg ring-1 ring-black ring-opacity-5 overflow-hidden',
                                (idx === 0 && (baseLevel === 1 || baseLevel === 2))
                                    ? 'top-full mt-2' // 显示在下方
                                    : 'bottom-full mb-2' // 显示在上方
                            ]">
                                <div class="p-3">
                                    <div class="text-sm font-medium text-gray-900 text-left">是否确认删除该标题？若有下级子标题将一起删除。
                                    </div>
                                    <div class="flex justify-end space-x-2">
                                        <button @click="close"
                                            class="px-2 py-1 text-xs text-gray-600 hover:text-gray-800">
                                            取消
                                        </button>
                                        <button @click="() => { close(); $emit('operate', items, idx, 3); }"
                                            class="px-2 py-1 text-xs text-white bg-blue-500 rounded hover:bg-blue-600">
                                            确定
                                        </button>
                                    </div>
                                </div>
                            </PopoverPanel>
                        </Popover>
                    </div>
                </div>
            </div>

            <!-- 递归渲染子层级 -->
            <OutlineSubItem v-if="item.children && item.children.length > 0" :items="item.children"
                :parent-idx="getFullFocusIndex(idx)" :base-level="baseLevel + 1" :max-level="maxLevel"
                @update="$emit('update')" @focus="$emit('focus', $event)" @blur="$emit('blur', $event, getChapterIdx())"
                @operate="(list, index, type) => $emit('operate', list, index, type)" />
        </div>
    </div>
</template>

<script setup lang="ts">
import { Popover, PopoverButton, PopoverPanel } from '@headlessui/vue';
import PPTAndBookOutlineMenu from './PPTAndBookOutlineMenu.vue';

// 定义大纲项的接口
interface OutlineItem {
    level: number;
    name: string;
    key: string;
    children: OutlineItem[];
}

const props = defineProps({
    items: {
        type: Array as () => OutlineItem[],
        required: true
    },
    parentIdx: {
        type: String,
        required: true,
        default: ''
    },
    baseLevel: {
        type: Number,
        required: true,
        default: 1
    },
    maxLevel: {
        type: Number,
        default: 4
    },
    language: {
        type: String,
        default: '中文专著'
    },
});

defineEmits(['update', 'focus', 'blur', 'operate']);

// 获取层级对应的CSS类
const getLevelClass = () => {
    switch (props.baseLevel) {
        case 1: return 'chapter_div';
        case 2: return 'page_div';
        case 3: return 'title_div';
        default: return 'sub-item-div';
    }
};

// 获取层级标题文本
const getLevelName = () => {
    switch (props.baseLevel) {
        case 1: return '章节';
        case 2: return '内容';
        case 3: return '';
        default: return '';
    }
};

// 获取不同层级的header样式
const getLevelHeaderClass = () => {
    if (props.baseLevel > 2) {
        return { 'border': 'none' };
    }
    return {};
};



// 获取层级对应的输入名称类
const getLevelNameClass = () => {
    switch (props.baseLevel) {
        case 1: return 'chapter_name';
        case 2: return 'page_name';
        case 3: return 'title_name';
        default: return 'sub_name';
    }
};

// 获取层级对应的序号类
const getLevelNumberClass = () => {
    switch (props.baseLevel) {
        case 1: return 'chapter_number';
        case 2: return 'page_number';
        case 3: return 'title_number';
        default: return 'sub_number';
    }
};

// 获取章节索引
const getChapterIdx = () => {
    const parts = props.parentIdx.split('-');
    return parseInt(parts[0] || '0');
};

// 生成用于focus的完整索引
const getFullFocusIndex = (idx: number) => {
    if (props.parentIdx) {
        return `${props.parentIdx}-${idx}`;
    }
    return `${idx}`;
};

// 生成完整的索引层级显示
const getFullIndex = (idx: number) => {
    if (props.baseLevel === 1) {
        if (props.language == '英文专著') {
            return `Chapter ${idx + 1}`;
        }
        return `第${idx + 1}章`;
    }

    const indexParts = props.parentIdx.split('-');
    let displayParts = [];

    if (props.baseLevel === 2) {
        // 内容层级
        displayParts = [parseInt(indexParts[0]) + 1, idx + 1];
    } else {
        // 标题层级及以下
        for (let i = 0; i < indexParts.length; i++) {
            displayParts.push(parseInt(indexParts[i]) + 1);
        }
        displayParts.push(idx + 1);
    }

    return displayParts.join('.');
};

// 添加一个方法处理输入内容，移除章节前缀
// const removeChapterPrefix = (text: string) => {
//     if (!text) return '';

//     // 匹配多种章节前缀格式：
//     // 1. 第x章/节/部分 - 支持中文数字和阿拉伯数字
//     // 2. 章节x - 支持不同的分隔符
//     // 3. 章x/第x部分 - 支持各种组合形式

//     return text.replace(/^(第\s*[一二三四五六七八九十百千万0-9]+\s*[章节部分]|[章节]\s*[一二三四五六七八九十0-9]+)[：:\s]*/g, '')
//         .replace(/^[第章节]\s*[一二三四五六七八九十0-9]+\s*[\.、]/g, '');
// };
</script>

<style scoped lang="scss">
.chapter_div,
.page_div,
.title_div,
.sub-item-div {
    display: flex;
    align-items: center;
    padding: 10px 0;
}

.chapter_div {
    position: relative;
    // width: 100%;
    // gap: 20px;
    height: 54px;

}

.chapter_div:hover,
.page_div:hover,
.title_div:hover,
.sub-item-div:hover {
    background: #ecf6ff;
}

.content-point {
    width: 6px;
    height: 6px;
    background-color: #1e99ff;
    border-radius: 50%;
    position: relative;
    left: 0px;
    z-index: 9;
    flex-shrink: 0;
}

.page-point {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    border: 3px solid #1e99ff;
    margin-right: 10px;
    position: relative;
    left: 0px;
    z-index: 9;
    flex-shrink: 0;
}

.title-point {
    width: 6px;
    height: 6px;
    margin-right: 10px;
    background: #1e99ff;
    position: relative;
    left: 0px;
    z-index: 9;
    flex-shrink: 0;
}

.sub-point {
    width: 4px;
    height: 4px;
    margin-right: 10px;
    background: #1e99ff;
    position: relative;
    left: 0px;
    z-index: 9;
    flex-shrink: 0;
}

.chapter_number {
    margin-right: 5px;
    width: auto;
    // min-width: 40px;
}

.page_number,
.title_number,
.sub_number {
    font-weight: 400;
    font-size: 13px;
    line-height: 19px;
    margin-right: 5px;
    color: #999999;
}

.chapter_name,
.page_name,
.title_name,
.sub_name {
    text-align: left;
    display: inline-block;
}

.chapter_name,
.page_name {
    font-weight: bold;
}

.chapter_header {
    // flex-shrink: 0;
    min-width: 60px;
    border-radius: 5px;
    border: none;
    color: #1e99ff;
    font-size: 13px;
    line-height: 22px;
    background: #ecf6ff;
    text-align: center;
    padding: 0px 10px;
}

.horizontal-line {
    height: 2px;
    margin-left: 3px;
    background: #ecf6ff;
}

.operate_div {
    display: none;
    flex-shrink: 0;
}

.hover-container:hover .operate_div {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    margin: 0 10px;
    // padding: 10px 0;
}

.weui-input {
    width: 100%;
    max-width: 100%;
    border-radius: 5px;
    height: 35px;
    line-height: 35px;
    padding: 5px 12px;
    font-size: 15px;
    border-width: 1px;
    border-style: inset;
    border-color: transparent;
    box-sizing: border-box;
    background-color: #ffffff;
}

.weui-input-focus {
    border-color: #1e99ff;
}

.flex-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
</style>