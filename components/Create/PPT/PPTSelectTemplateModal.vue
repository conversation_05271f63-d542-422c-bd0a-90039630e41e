<template>
    <Modal :model-value="modelValue" @update:model-value="handleClose" title="选择PPT模版" :width="1000"
        :show-footer="false" :height="screenHeight > baseHeight + 80 ? baseHeight + 80 : screenHeight"
        confirmText="继续生成" @confirm="handleOk" :show-confirm-only="true">
        <!-- 弹窗内容区域，动态设置最大高度 -->
        <div class="flex justify-evenly w-[600px] m-auto  mt-1 mb-5 border border-blue-200 rounded-lg ">
            <button v-for="tab in tabs" :key="tab.key"
                class="px-4 py-2 flex-1 text-sm font-medium transition-colors -mb-[2px] rounded-lg mt-[-2px]"
                :class="currentTab === tab.key ? 'text-white bg-blue-600' : 'text-gray-500  hover:text-gray-700'"
                @click="currentTab = tab.key">
                <BookIconfont v-if="tab.key == 'default'" name="tuijianmuban"></BookIconfont>
                <BookIconfont v-else name="zhidingyi"></BookIconfont> {{ tab.name }}
            </button>
        </div>
        <div class="flex flex-col w-full gap-6 px-4 overflow-y-auto ppt-template-area md:flex-row"
            :style="{ maxHeight: maxContentHeight }" v-if="currentPPTTemplateInfo">
            <!-- 左侧预览区域 -->
            <div class="flex flex-col flex-1 template-left md:overflow-y-auto">
                <div class="flex-1 h-full template-left-content">
                    <div class="w-full">
                        <div class="preview-img-item"><img :src="getImageUrl(currentPPTTemplateInfo, 0)"
                                class="object-cover w-full h-auto border border-gray-200 rounded-xl" />
                            <div class="preview-img-item-title" v-if="currentPPTTemplateInfo?.id"
                                :style="`${currentPPTTemplateInfo?.content?.fontColor ? `color: ${currentPPTTemplateInfo?.content?.fontColor}` : ''}`">
                                {{ topic || '' }}</div>
                        </div>
                        <div v-if="currentTab == 'custom'" class="preview-img-tip">第一页用于封面</div>
                    </div>
                    <div class="grid grid-cols-2 gap-3 mt-3">
                        <div>
                            <div class="preview-img-item"><img :src="getImageUrl(currentPPTTemplateInfo, 1)"
                                    class="object-cover w-full h-auto border border-gray-200 rounded-xl" /></div>
                            <div v-if="currentTab == 'custom'" class="preview-img-tip">第二页用于内容</div>
                        </div>
                        <div>
                            <div class="preview-img-item"><img :src="getImageUrl(currentPPTTemplateInfo, 2)"
                                    class="object-cover w-full h-auto border border-gray-200 rounded-xl" /></div>
                            <div v-if="currentTab == 'custom'" class="preview-img-tip">最后一页用于封底</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧模板列表区域 -->
            <div class="flex-1 template-right">

                <!-- 列表区域，设置最大高度为弹窗高度减去底部按钮的高度 -->
                <div class="template-list overflow-y-auto bg-white rounded-lg h-full max-h-[calc(100%-80px)]">
                    <div v-if="currentTab === 'default'" class="grid grid-cols-2 gap-2">
                        <div v-for="item in pptTemplateList" :key="item.id"
                            class="template-item select_template-imgwrap" @click="handleSelectedTemplate(item)">
                            <img :src="getImageUrl(item, 0)"
                                :class="{ active: currentPPTTemplateInfo?.fileName == item?.fileName }"
                                class="object-cover w-full h-auto transition-all duration-200 rounded-lg select_template-img" />
                            <div class="preview-img-item-title1"
                                :style="typeof item?.content === 'object' && item?.content && 'fontColor' in item?.content && item?.content.fontColor ? `color: ${item?.content.fontColor}` : ''">
                                {{ topic || '' }}</div>
                        </div>
                    </div>
                    <UploadPPT :files="files" v-if="currentTab === 'custom'" @confirm="handleUplodCustom"></UploadPPT>
                </div>
            </div>
        </div>
        <!-- <div class="sticky bottom-0 z-10 p-4 text-center bg-white template-left-footer">
            <button @click="handleOk"
                class="p-2 px-20 text-white rounded-lg bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700">
                继续生成
            </button>
        </div> -->
    </Modal>
</template>

<script setup lang="ts">
import BookIconfont from '@/components/Book/BookIconfont.vue';
import { message } from "ant-design-vue";
import { onBeforeUnmount, onMounted, ref } from 'vue';
import { getPPTTemplateList } from '~/api/create';
import Modal from '~/components/Common/Modal.vue';
import UploadPPT from '~/components/Create/PPT/UploadPPT.vue';
import type { PPTTemplateInfo } from '~/services/types/submission';

const props = defineProps({
    modelValue: Boolean,
    topic: String
})

const emit = defineEmits(['update:modelValue', 'submit'])
const currentTab = ref('default'); // 当前选中的标签页

const pptTemplateList = ref<PPTTemplateInfo[]>([])
const currentCustomPPTTemplateInfo = ref<PPTTemplateInfo | null>({
    "id": "",
    "name": "自定义",
    "imageUrls": ['https://static-1256600262.file.myqcloud.com/xiaoin-pc/images/ppt/ppt-example1.png', 'https://static-1256600262.file.myqcloud.com/xiaoin-pc/images/ppt/ppt-example2.png', 'https://static-1256600262.file.myqcloud.com/xiaoin-pc/images/ppt/ppt-example3.png'].join(','),
    "fileName": "data",
    "content": {
        "fontName": "Arial",
        "fontColor": '#000000',
        "themeColors": ['#ffffff', '#ffffff', '#eeece1', '#1e497b', '#4e81bb', '#e2534d'],
        "backgroundColor": '#1e497b'
    },
    "sort": 1,
})
const currentSelectPPTTemplateInfo = ref<PPTTemplateInfo | null>(null)
const currentPPTTemplateInfo = computed(() => {
    if (currentTab.value == 'custom') {
        return currentCustomPPTTemplateInfo.value
    }
    return currentSelectPPTTemplateInfo.value
})

const screenHeight = ref(window.innerHeight); // 用于存储当前屏幕高度

const tabs = ref([
    {
        key: 'default',
        name: '推荐模版',
        roundedClass: 'rounded-tl-[20px] rounded-tr-[20px]'
    },
    {
        key: 'custom',
        name: '自定义模板',
        roundedClass: 'rounded-tl-[20px] rounded-tr-[20px]'
    },

]);
const updateScreenHeight = () => {
    screenHeight.value = window.innerHeight; // 更新屏幕高度
}

// 关闭弹窗
const handleClose = () => {
    emit('update:modelValue', false)
}

// 选择模板
const handleSelectedTemplate = (item: any) => {
    currentSelectPPTTemplateInfo.value = item
}

// 确认选择
const handleOk = () => {
    console.log('当前选择的PPT模板信息:', currentPPTTemplateInfo.value)
    if (!currentPPTTemplateInfo.value?.id) {
        message.error('请上传或选择模板')
        return;
    }
    emit('submit', currentPPTTemplateInfo.value)
    setTimeout(() => {
        handleClose()
    }, 1000)
}

// 获取图片URL
const getImageUrl = (item: { imageUrls: string; _imageUrls: [] }, index: number) => {
    if (!item.imageUrls) return
    if (item._imageUrls && Array.isArray(item._imageUrls)) {
        return item._imageUrls[index] || ''
    }
    const list = item.imageUrls.split(',')
    return list[index] || ''
}

// 加载模板数据
const loadPPTTemplateData = async () => {
    const params = {
        pageNo: 1,
        pageSize: 100
    }
    const res = await getPPTTemplateList(params)
    if (!res.ok || !res.data) return

    const list = res.data.records || []
    list.forEach((item: any) => {
        // 确保每个模板都有至少3张图片
        if (item.imageUrls) {
            item._imageUrls = item.imageUrls.split(',')
        }
    })
    pptTemplateList.value = list
    if (list.length > 0) {
        currentSelectPPTTemplateInfo.value = list[0]
    }
}
const files = ref([])

const handleUplodCustom = (item: any) => {
    currentCustomPPTTemplateInfo.value = item
}

// 计算属性来动态设置最大高度
const baseHeight = 560
const maxContentHeight = computed(() => {
    // const baseHeight = 560; // 基础高度
    const mobileAdjustment = window.innerWidth < 599 ? 120 : 0; // 手机端调整
    return `calc(${baseHeight}px - ${mobileAdjustment}px)`;
});
onMounted(() => {
    window.addEventListener('resize', updateScreenHeight);
    loadPPTTemplateData()
})

onBeforeUnmount(() => {
    window.removeEventListener('resize', updateScreenHeight); // 移除事件监听器
})
</script>

<style scoped lang="scss">
.title {
    font-size: 15px;
    font-weight: bold;
    color: #333333;
    line-height: 23px;
    text-align: center;
    margin: 10px 0;
}

.template-left-footer {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 100;
}

.template-item {
    cursor: pointer;
}

.template-item img {
    border: 2px solid #e5e5e5;
    transition: all 0.2s;

    width: 100%;
    border-radius: 5px;
    border: 1px solid #E5E5E5;
}

.template-item img:hover {
    border-color: #1e99ff;
}

.template-item img.active {
    border-color: #388bef;
}

.preview-img-tip {
    padding: 10px 0 0;
    font-weight: 500;
    color: #333;
    text-align: center;
}

.preview-img1 {
    height: 219px;
}

.preview-img2,
.preview-img3 {
    height: 106px;
}

.preview-img-item {
    position: relative;
    overflow: hidden;

    &-title {
        position: absolute;
        top: 46px;
        left: 40px;
        font-weight: 500;
        font-size: 22px;
        text-align: left;
    }

    &-title1 {
        position: absolute;
        top: 23px;
        left: 10px;
        font-size: 9px;
        scale: 0.8;
        text-align: left;
    }

}

.select_template {
    padding: 10px;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    background: #FFFFFF;
    border-radius: 10px;
    height: 360px;
    overflow: auto;

    &-imgwrap {
        position: relative;
    }

    .preview-img-item-title {
        position: absolute;
        top: 23px;
        left: 10px;
        font-size: 9px;
        scale: 0.8;
        text-align: left;
    }

    img {
        width: 100%;
        border-radius: 5px;
        border: 1px solid #E5E5E5;
    }

    &-img {
        cursor: pointer;

        &:hover {
            border: 1px solid #5B69E5;
            box-shadow: inset 0px 0px 10px rgba(0, 0, 0, 0.1);
        }
    }
}
</style>