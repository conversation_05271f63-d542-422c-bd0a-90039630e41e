<template>
    <!-- <button class="p-1.5 sm:p-2 text-gray-500 hover:text-gray-700 transition-colors relative">
        <picture-one theme="outline" size="24" class="text-gray-500" />
        <input type="file" class="absolute inset-0 opacity-0 cursor-pointer" accept="image/*">
    </button> -->
    <div class="pb-4 border border-gray-200 rounded-lg">
        <div class="flex items-center justify-center p-6 ">
            <a-upload v-model:file-list="fileList" :before-upload="beforeUpload" :customRequest="httpUpload"
                :show-upload-list="false" class="upload-list-inline" accept=".ppt,.pptx">
                <button class="p-1.5 sm:p-2 text-gray-500 hover:text-gray-700 transition-colors relative">
                    <div class="space-y-2 ma">
                        <div class="flex items-center justify-center w-12 h-12 mx-auto rounded-xl bg-blue-50">
                            <upload theme="outline" size="24" :fill="'#3B82F6'" />
                        </div>
                        <div class="text-sm" :class="'text-gray-600'">
                            点击进行文件上传
                        </div>
                        <div class="text-xs text-gray-400">支持文件格式： .ppt, .pptx</div>

                    </div>
                </button>
            </a-upload>
        </div>
        <div class="text-xs text-gray-400 text-center leading-[1.8]">
            <span class="pr-1 text-red-500">*</span>请上传至少3页的PPT模板。第一页将用于封面页,<br />
            第二页将用于内容页，最后一页将用于封底页。
            <!-- <button
                class="text-blue-700 transition-colors bg-transparent rounded-lg bg-blue-50 hover:text-blue-600 disabled:opacity-50 disabled:cursor-not-allowed whitespace-nowrap"
                @click="handleShowExample">查看示例</button> -->
        </div>
    </div>

    <div v-if="fileListCurrent" class="file-item">
        <span><img :src="getFileIcon(getFileTypeFromName(fileListCurrent.name || ''))"
                :alt="getFileTypeFromName(fileListCurrent.name || '')">
        </span> <span class="file-item-name">{{ fileListCurrent.name }}</span>
        <div @click="handleDelFile" class="file-item-del">
            <Close :size="20" />
        </div>
    </div>
    <div class="flex items-center justify-center py-3 text-xs text-gray-400" v-if="uploading">
        <LoadingFour :size="14" /> <span class="pl-2">等待中...</span>
    </div>

    <a-modal v-model:open="localModelValue" @cancel="handleHideExample" @confirm="handleConfirm" :footer="null" centered
        :width="600" :closable="false" wrap-class-name="customize-template-example-modal" :z-index="1300"
        style="z-index: 1300 !important;">
        <div class="header">为确保正确识别您的PPT结构，请按照以下顺序排列您的PPT内容</div>
        <div class="example-item">
            <div class="example-item-left">1<img
                    src="https://static-1256600262.file.myqcloud.com/xiaoin-pc/images/ppt/ppt-example1.png" alt="">
            </div>
            <div class="example-item-name">第一页 用做封面</div>
        </div>
        <div class="example-item">
            <div class="example-item-left">2<img
                    src="https://static-1256600262.file.myqcloud.com/xiaoin-pc/images/ppt/ppt-example2.png" alt="">
            </div>

            <div class="example-item-name">第二页 用做内容示例页</div>
        </div>
        <div class="example-s">
            <div class="example-d"></div>
            <div class="example-d example-d1"></div>
            <div class="example-d example-d2"></div>
        </div>
        <div class="example-item">
            <div class="example-item-left">3<img
                    src="https://static-1256600262.file.myqcloud.com/xiaoin-pc/images/ppt/ppt-example2.png" alt="">
            </div>
            <div class="example-item-name">最后一页 用做封底</div>
        </div>

        <div class="red-example">
            请勿在PPT中省略封面或封底，否则可能影响模版识别效果
        </div>
        <div class="row footer">
            <a-button type="primary" @click="handleHideExample">继续上传</a-button>
        </div>
    </a-modal>
</template>
<script lang="ts" setup>
import { extraPptTpl } from '@/api/upload';
import { Close, LoadingFour, Upload } from '@icon-park/vue-next';
import { Upload as AUpload, message } from 'ant-design-vue';
import { watch } from 'vue';
const props = defineProps({
    files: {
        type: Array,
        default: [],
    },
    pasteFileInfo: {
        type: Object as () => any,
        default: null
    }
})
const emit = defineEmits(['update:files', 'confirm']);
const fileList = computed({
    get: () => props.files,
    set: (val) => {
        emit('update:files', val)
    }
})

const fileListCurrent = ref<any>(null)
const accept = '.png,.jpg,jpeg'

const handleDelFile = () => {
    fileListCurrent.value = null
}
const triggerUpload = (file: any) => {
    const uploadOptions = {
        file: file.originFileObj,
        onSuccess: (response: any, _file) => {
            file.status = 'done'
            console.log(response, 'response', file)
            file.response = {
                ...response
            }
            _file.status = 'done'
            _file.response = {
                ...response
            }
            fileList.value = [{
                ..._file,
            }];
            // message.success('图片上传成功');
        },
        onProgress: (progressEvent: { percent: number }) => {
            file.percent = progressEvent.percent
        },
        onError: (error: any) => {
            file.status = 'error'
            // message.error('图片上传失败');
        }
    }
    httpUpload(uploadOptions)
}
// 根据文件名获取文件类型
const getFileTypeFromName = (fileName: string): string => {
    if (!fileName) return ''

    const extension = fileName.split('.').pop()?.toLowerCase() || ''


    return extension
}
const getFileIcon = (fileType: string): string => {
    switch (fileType.toLowerCase()) {
        case 'pdf':
            return KnowledgeFileIcon.pdf;
        case 'doc':
        case 'docx':
            return KnowledgeFileIcon.doc;
        case 'ppt':
        case 'pptx':
            return KnowledgeFileIcon.ppt;
        case 'img':
        case 'jpg':
        case 'jpeg':
        case 'png':
            return KnowledgeFileIcon.img;
        case 'txt':
        case 'md':
        case 'text':
            return KnowledgeFileIcon.text;
        case 'xlsx':
        case 'csv':
            return KnowledgeFileIcon.xlsx;
        default:
            return KnowledgeFileIcon.encode;
    }
}
watch(() => props.pasteFileInfo, (newVal) => {
    const _pasteFileInfo = newVal
    if (_pasteFileInfo && _pasteFileInfo.uid) {
        if (!accept.includes(_pasteFileInfo.name.split('.')[1])) {
            message.warning('不支持的图片格式')
            return
        }
        fileList.value.push(_pasteFileInfo)
        const isBeforeUpload = beforeUpload(_pasteFileInfo.originFileObj, 1)
        if (!isBeforeUpload) {
            return
        }
        triggerUpload(_pasteFileInfo)
    }

})

interface FileInfo {
    uid: string
    webkitRelativePath: string
    name: string
    size: number
    type: string
    status: string
    percent: number
    response: {
        fileUrl?: string
        fileId?: string
    },
    url: string
}
const uploading = ref(false)

const beforeUpload = (file: any, _fileList: any) => {
    let isLt10M = file.size / 1024 / 1024 < 50
    if (!isLt10M) {
        message.error(`文件:${file.name}大小不能超过50M!`)
    }
    if (uploading.value) {
        message.error(`正在上传中，请稍后再试`)
        isLt10M = false
    }
    return isLt10M || AUpload.LIST_IGNORE
}
const handleUploadFileByFile = async (file: FileInfo, onProgress: any, onSuccess: any) => {
    try {

        fileListCurrent.value = file
        // 文件需要上传
        uploading.value = true
        const res = await extraPptTpl(file as any)
        setTimeout(() => {
            uploading.value = false
        }, 100);
        if (!res.success || !res.data) return message.error(file.name + '上传失败')
        const imageUrls = [
            `${res.data.slides[0].image}`,
            `${res.data.slides[1].image}`,
            `${res.data.slides[2].image}`,
        ]
        emit('confirm',
            {
                "id": "9999999",
                "name": "自定义",
                "imageUrls": imageUrls.join(','),
                "_imageUrls": imageUrls,
                "fileName": "data",
                "content": {
                    "fontName": "Arial",
                    "fontColor": res.data.color.fontColor,
                    "themeColors": [
                        res.data.color.accent1,
                        res.data.color.accent2,
                        res.data.color.accent3,
                        res.data.color.accent4,
                        res.data.color.accent5,
                        res.data.color.accent6,
                    ],
                    "backgroundColor": res.data.color.backgroundColor
                },
                "sort": 1,
                "pptData": {
                    theme: {
                        themeColors: [
                            res.data.color.accent1,
                            res.data.color.accent2,
                            res.data.color.accent3,
                            res.data.color.accent4,
                            res.data.color.accent5,
                            res.data.color.accent6,
                        ],
                        fontColor: res.data.color.fontColor,
                        backgroundColor: res.data.color.backgroundColor
                    },
                    background: [
                        {
                            slideType: 'cover',
                            type: "image",
                            image: {
                                src: imageUrls[0],
                                size: "cover",
                            },
                        },
                        {
                            slideType: 'content',
                            type: "image",
                            image: {
                                src: imageUrls[1],
                                size: "cover",
                            },
                        },
                        {
                            slideType: 'end',
                            type: "image",
                            image: {
                                src: imageUrls[2],
                                size: "cover",
                            },
                        }
                    ]


                }
            })
    } catch (error: any) {
        console.log(error, 'error')
        message.error('文件上传失败，请重试')
    }
}
const httpUpload = async (data: { file: any; onProgress?: any; onSuccess?: any }) => {
    const { file, onProgress, onSuccess } = data

    setTimeout(() => {
        handleUploadFileByFile(file, onProgress, onSuccess)
    }, 300)
}
const localModelValue = ref(false)
const handleShowExample = () => {
    localModelValue.value = true
}
const handleHideExample = () => {
    localModelValue.value = false
}
</script>

<style lang="scss">
.customize-template-example-modal {

    .header {
        font-size: 16px;
        padding: 20px 0;
        text-align: center;
        font-weight: 500;
    }

    .cion_blance {
        margin-top: 10px;
        padding: 20px;
        background: #F5F7FF;
        border-radius: 10px;
        display: flex;
        justify-content: space-between;
        font-size: 14px;
        align-items: center;

        &-gray {
            color: #777777;
        }
    }

    .cion_blance1 {
        justify-content: flex-start;
        gap: 10px;
    }

    .row {
        width: 100%;
        display: flex;
        align-items: center;
        margin-bottom: 20px;
    }

    .footer {
        padding-top: 30px;
        text-align: center;
        display: flex;
        justify-content: center;
    }

    .red-example {
        color: #FF4242;
        font-size: 12px;
        margin-top: 40px;
        text-align: center;
    }

    .example-s {
        padding: 10px 100px;
        display: flex;
        gap: 10px;
    }

    .example-d {
        width: 5px;
        height: 5px;
        border-radius: 50%;
        background: linear-gradient(315deg, #5B69E5 0%, #7FA9FF 100%);
        opacity: 0.7;
    }

    .example-d1 {
        background: linear-gradient(315deg, #4b5ae1 0%, #6895f0 100%);
    }

    .example-d2 {
        background: linear-gradient(315deg, #3647e0 0%, #4a74c6 100%);
    }

    .example-item {
        display: flex;
        align-items: center;
        margin: 10px 0;
        font-weight: 500;
        font-size: 18px;

        &-left {
            display: flex;
            gap: 5px;
            width: 240px;
            color: #777777;
        }

        img {
            width: 224px;
            height: 126px;
            margin-right: 6px;
        }

        &-name {
            margin-left: 80px;
            font-size: 17px;
            font-weight: 500;
            white-space: nowrap;
        }
    }


}
</style>
<style lang="scss" scoped>
.file-item {
    position: relative;
    display: flex;
    align-items: center;
    margin: 10px 0;
    padding: 0 20px;
    height: 73px;
    background: #F9FAFF;
    border-radius: 10px;
    border: 1px solid #E3EAFB;

    img {
        width: 18px;
        height: 18px;
        margin-right: 6px;
    }

    &-name {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 300px;
    }

    &-del {
        padding: 5px;
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        cursor: pointer;
        color: #bbb;

        :hover {
            color: rgb(132, 134, 149)
        }
    }
}
</style>