<template>
    <Menu as="div" class="relative inline-block">
        <div class="menu-container" @mouseenter="handleMouseEnter" @mouseleave="handleMouseLeave">
            <MenuButton class="flex items-center p-1.5 rounded-lg hover:bg-gray-100">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-500" viewBox="0 0 20 20"
                    fill="currentColor">
                    <path fill-rule="evenodd"
                        d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z"
                        clip-rule="evenodd" />
                </svg>
            </MenuButton>

            <transition enter-active-class="transition duration-100 ease-out"
                enter-from-class="transform scale-95 opacity-0" enter-to-class="transform scale-100 opacity-100"
                leave-active-class="transition duration-75 ease-in" leave-from-class="transform scale-100 opacity-100"
                leave-to-class="transform scale-95 opacity-0">
                <MenuItems v-if="isMenuOpen" static
                    class="menu-items absolute right-0 mt-1 w-48 origin-top-right bg-white rounded-lg shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none z-30"
                    :class="[
                        isTopMenu ? 'bottom-full mb-2' : 'top-full mt-2',
                        { 'menu-items-top': isTopMenu }
                    ]">
                    <div class="p-1">
                        <MenuItem v-slot="{ active }">
                        <button @click="handleOperation(1)" :class="[
                            active ? 'bg-gray-100 text-gray-900' : 'text-gray-700',
                            'group flex items-center w-full px-3 py-2 text-sm rounded-md'
                        ]">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-gray-500"
                                viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd"
                                    d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z"
                                    clip-rule="evenodd" />
                            </svg>
                            向上增加
                        </button>
                        </MenuItem>

                        <MenuItem v-slot="{ active }">
                        <button @click="handleOperation(2)" :class="[
                            active ? 'bg-gray-100 text-gray-900' : 'text-gray-700',
                            'group flex items-center w-full px-3 py-2 text-sm rounded-md'
                        ]">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-gray-500"
                                viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd"
                                    d="M14.707 10.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 12.586V5a1 1 0 012 0v7.586l2.293-2.293a1 1 0 011.414 0z"
                                    clip-rule="evenodd" />
                            </svg>
                            向下增加
                        </button>
                        </MenuItem>

                        <!-- TODO 后续动态判断是否显示增加子级的功能 -->
                        <MenuItem v-slot="{ active }" v-if="maxLevel ? level < maxLevel : level < 4">
                        <button @click="handleOperation(3)" :class="[
                            active ? 'bg-gray-100 text-gray-900' : 'text-gray-700',
                            'group flex items-center w-full px-3 py-2 text-sm rounded-md'
                        ]">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-gray-500"
                                viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd"
                                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z"
                                    clip-rule="evenodd" />
                            </svg>
                            增加子级
                        </button>
                        </MenuItem>
                    </div>
                </MenuItems>
            </transition>
        </div>
    </Menu>
</template>

<script setup lang="ts">
import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/vue';
import { computed, ref } from 'vue';

const props = defineProps<{
    list: any[],
    index: number,
    level: number,
    parentIdx: String,
    maxLevel?: number
}>()

const emit = defineEmits<{
    (e: 'operate', list: any[], index: number, type: number): void
}>()

const isMenuOpen = ref(false)
const closeTimeout = ref<number | null>(null)

// 计算菜单是否应该显示在上方-----此判断不判断显示的特定位置，显示在上方和下方无所谓，只要第1、2和最后1、2的菜单不被遮挡即可
const isTopMenu = computed(() => {
    if (props.parentIdx == '0') {
        return false
    }
    // 显示在上方
    if (props.index == props.list.length - 1 || props.index == props.list.length - 2) {
        return true
    }
    return false
})

/**
 * 处理鼠标进入菜单区域
 */
const handleMouseEnter = () => {
    // 清除任何可能存在的关闭定时器
    if (closeTimeout.value) {
        clearTimeout(closeTimeout.value)
        closeTimeout.value = null
    }
    isMenuOpen.value = true
}

/**
 * 处理鼠标离开菜单区域
 */
const handleMouseLeave = (event: MouseEvent) => {
    // 检查鼠标是否移向菜单项
    const relatedTarget = event.relatedTarget as HTMLElement

    // 如果鼠标移向的元素是菜单项或其子元素，则不关闭菜单
    if (relatedTarget && (
        relatedTarget.closest('.menu-items') ||
        relatedTarget.classList.contains('menu-items')
    )) {
        return
    }

    // 添加短暂延迟，以便用户有时间移动到菜单
    closeTimeout.value = window.setTimeout(() => {
        isMenuOpen.value = false
        closeTimeout.value = null
    }, 150)
}

/**
 * 
 * @param type 1、向上增加；2、向下增加；3、增加子级
 */
const handleOperation = (type: number) => {
    if (type === 1) {
        emit('operate', props.list, props.index, 1)
    } else if (type === 2) {
        emit('operate', props.list, props.index + 1, 1)
    } else if (type === 3) {
        emit('operate', props.list, props.index, 2)
    }
    isMenuOpen.value = false
}

onMounted(() => {
    // console.log("idx =>", props.index, props.list)
})

// 清理定时器
onBeforeUnmount(() => {
    if (closeTimeout.value) {
        clearTimeout(closeTimeout.value)
    }
})
</script>

<style scoped>
.menu-container {
    position: relative;
    display: inline-block;
}

.menu-items {
    &::before {
        content: '';
        position: absolute;
        top: -8px;
        right: 0;
        left: 0;
        height: 8px;
    }
}

/* 添加特殊类用于上方菜单，创建连接区域 */
.menu-items-top {

    /* 添加一个伪元素作为连接区域 */
    &::after {
        content: '';
        position: absolute;
        bottom: -16px;
        /* 连接到触发按钮 */
        left: 0;
        right: 0;
        height: 16px;
        background: transparent;
        /* 透明背景允许鼠标事件穿透 */
        z-index: 25;
        /* 确保足够高的z-index */
    }
}
</style>