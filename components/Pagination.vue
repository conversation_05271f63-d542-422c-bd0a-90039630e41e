<template>
    <div class="flex justify-center items-center mt-6" v-if="total > 0">
        <!-- <nav class="flex items-center gap-1" aria-label="Pagination">
            <button class="p-2 rounded-lg hover:bg-gray-100 text-gray-500 disabled:text-gray-300"
                :disabled="currentPage === 1" @click="handlePageChange(currentPage - 1)">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                </svg>
            </button>

            <div class="flex items-center gap-1">
                <div v-for="page in visiblePages" :key="page">
                    <button :class="[
                        'px-3.5 py-2 rounded-lg text-sm font-medium',
                        currentPage === page
                            ? 'bg-blue-50 text-blue-600'
                            : 'text-gray-600 hover:bg-gray-100'
                    ]" @click="handlePageChange(page)">
                        {{ page }}
                    </button>
                </div>

                <span v-if="showEllipsis" class="px-2 text-gray-400">...</span>

                <button v-if="totalPages > maxVisible" :class="[
                    'px-3.5 py-2 rounded-lg text-sm font-medium ccc',
                    currentPage === totalPages
                        ? 'bg-blue-50 text-blue-600'
                        : 'text-gray-600 hover:bg-gray-100'
                ]" @click="handlePageChange(totalPages)">
                    {{ totalPages }}
                </button>
            </div>

            <button class="p-2 rounded-lg hover:bg-gray-100 text-gray-500 disabled:text-gray-300"
                :disabled="currentPage === totalPages" @click="handlePageChange(currentPage + 1)">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
            </button>
        </nav> -->
        <UPagination v-model:model-value="currentPage" :page-count="pageCount" :total="total" show-first show-last />


    </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
const props = defineProps({
    current: {
        type: Number,
        required: true,
        default: 1,
    },
    totalPages: {
        type: Number,
        default: 0
    },
    pageCount: {
        type: Number,
        required: false,
        default: 10
    },
    total: {
        type: Number,
        required: true,
        default: 50
    }
})
const emit = defineEmits(['update:current', 'change', 'update:total'])
const currentPage = computed({
    get: () => props.current,
    set: (val) => {
        // console.log(val, 'val')
        emit('update:current', val)
        emit('change', val)
    }
})
// const maxVisible = 5
// 是否显示省略号
// const showEllipsis = computed(() => {
//     return props.totalPages > 5 && !visiblePages.value.includes(props.totalPages)
// })
// // 计算要显示的页码
// const visiblePages = computed(() => {
//     const pages = []


//     if (props.totalPages <= maxVisible) {
//         for (let i = 1; i <= props.totalPages; i++) {
//             pages.push(i)
//         }
//     } else {
//         let start = Math.max(1, props.current - 2)
//         let end = Math.min(start + maxVisible - 1, props.totalPages)

//         if (end - start < maxVisible - 1) {
//             start = end - maxVisible + 1
//         }

//         for (let i = start; i <= end; i++) {
//             pages.push(i)
//         }
//     }
//     return pages
// })

// const handlePageChange = (page: number) => {
//     if (!page) {
//         return;
//     }
//     if (page > props.totalPages) {
//         return;
//     }
//     currentPage.value = page
//     emit('change', page)
// }
</script>
<style scoped>
button:disabled {
    cursor: not-allowed;
    /* 其他想要添加的样式 */
}
</style>