<!-- <template>
    <div class="image-container">
        <img :src="src" :class="class" @error="onError" :style="containerStyle" @load="onLoad" />
 
        <div v-if="!loaded" class="placeholder"></div>
    </div>
</template>

<script lang="ts" setup>

const props = defineProps({
    src: {
        type: String,
        default: '',
    },
    class: {
        type: String,
        default: '',
    },
})
const loaded = ref(false)
const error = ref(false)
const placeholderColor = '#e0e0e0'
const containerStyle = computed(() => {
    return {
        position: 'relative',
        backgroundColor: error ? placeholderColor : 'transparent',
        opcity: loaded ? 1 : 0
    }
})

const onError = () => {
    error.value = true
    loaded.value = false // 确保显示占位符
}
const onLoad = () => {
    loaded.value = true
}
</script>
<style scoped>
.image-container {
    position: relative;
    overflow: hidden;
}

img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.placeholder {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #e0e0e0;
    /* 灰色背景 */
}
</style> -->