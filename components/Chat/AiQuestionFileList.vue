<template>
    <div class="absolute w-full bottom-1 left-1">
        <UCarousel :showArrows="selectFileList.length > 1" v-slot="{ item }" loop wheel-gestures
            :arrows="!!selectFileList.length" :dots="false" :autoplay="{ delay: 2000 }" :items="selectFileList">
            <div
                class="mx-2 overflow-hidden transition-all duration-200 border bg-white/80 backdrop-blur-sm rounded-xl border-gray-200/70 group hover:border-blue-700/30">
                <div class="flex items-center justify-between w-full p-3.5 relative">
                    <div class="flex-shrink-0">
                        <img :src="getFileIcon(item)" :alt="item.type || 'file'" class="w-6  mx-4 py-[13px]">
                    </div>

                    <!-- 左侧：文件信息 -->
                    <div class="flex flex-col justify-center flex-1 min-w-0 pr-4">
                        <!-- 左上：文件名 -->
                        <h3
                            class="overflow-hidden text-ellipsis text-sm font-bold text-gray-700 break-words max-w-[160px] whitespace-nowrap">
                            {{ item.title || '--' }}
                        </h3>
                        <!-- 左下：文件信息 -->
                        <div
                            class="flex items-center gap-3 my-1 text-sm text-gray-500 truncate transition-colors whitespace-nowrap">
                            <span v-if="item.fileSize" class="flex items-center">
                                {{
                                    formatStorageSize(item.fileSize, 2) }}
                            </span>
                            <span v-if="item.wordCount" class="flex items-center">
                                {{ (item.wordCount / 10000).toFixed(2) }}万字
                            </span>
                            <span v-if="item.createTime" class="flex items-center">
                                {{ formatDate(item.createTime) }}
                            </span>
                        </div>
                    </div>
                    <div class="absolute bottom-0 left-0 w-full">
                        <AiQuestionFileItemProcess :repository-file="item"></AiQuestionFileItemProcess>
                    </div>
                </div>
            </div>
        </UCarousel>
    </div>
    <!-- 加上默认高度，以便不影响输入框的大小 -->
    <div v-if="selectFileList.length" class="h-20"></div>
</template>
<script setup lang="ts">
import type { NewRepositoryFile } from '~/services/types/repositoryFile';
import AiQuestionFileItemProcess from './AiQuestionFileItemProcess.vue';
const props = defineProps({
    selectFileList: {
        type: Array,
        default: [],
    },
})
// const items = [
//     'https://picsum.photos/468/468?random=1',
//     'https://picsum.photos/468/468?random=2',
//     'https://picsum.photos/468/468?random=3',
//     'https://picsum.photos/468/468?random=4',
//     'https://picsum.photos/468/468?random=5',
//     'https://picsum.photos/468/468?random=6'
// ]
const formatDate = (dateString: string) => {
    if (!dateString) return '';

    // 使用工具函数获取日期部分
    const formattedDateTime = formatDateTimeString(dateString);
    // 只返回日期部分，去掉时间
    return formattedDateTime.split(' ')[0];

    // const date = new Date(dateString);
    // return date.toISOString().split('T')[0];
}

const getFileNameAndExtension = (fullName: string) => {
    const match = fullName.match(/^(.+)(\.[^.]+)$/)
    return {
        name: match ? match[1].toLowerCase() : fullName.toLowerCase(),
        extension: (match ? match[2] : '').toLowerCase().replace('.', '')
    }
}
const getFileIcon = (item: NewRepositoryFile) => {
    if (!item.name) return KnowledgeFileIcon.encode;

    const { extension } = getFileNameAndExtension(item.name);


    // 根据扩展名返回对应图标
    switch (extension) {
        case 'pdf':
            return KnowledgeFileIcon.pdf;
        case 'doc':
        case 'docx':
            return KnowledgeFileIcon.doc;
        case 'ppt':
        case 'pptx':
            return KnowledgeFileIcon.ppt;
        case 'img':
        case 'jpg':
        case 'jpeg':
        case 'png':
            return KnowledgeFileIcon.img;
        case 'txt':
        case 'md':
        case 'text':
            return KnowledgeFileIcon.text;
        case 'xlsx':
        case 'csv':
            return KnowledgeFileIcon.xlsx;
        default:
            return KnowledgeFileIcon.encode;
    }
}
</script>