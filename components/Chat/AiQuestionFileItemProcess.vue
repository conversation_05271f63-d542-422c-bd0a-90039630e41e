<template>
    <UProgress v-if="value > -1 && value < 100" class="w-full" :max="100" v-model="value" />
</template>
<script setup lang="ts">
import type { PropType } from 'vue';
import { computed, ref } from 'vue';
import type { UploadDocument } from '~/services/types/knowledgeUpload';
const props = defineProps({
    repositoryFile: {
        type: Object as PropType<UploadDocument>,
        default: () => ({}),
    },
})
const value1 = ref(20);
const value = computed(() => {
    const { status, processData } = props.repositoryFile;
    if (processData?.error) {
        return -1
    }
    if (status == RepositoryFileStatus.DONE) {
        return 100
    }
    return processData?.summary_status || 0
});
</script>