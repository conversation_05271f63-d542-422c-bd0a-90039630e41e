<template>
  <div class="share-page">
    <div v-if="isFinished">
      <div class="card-container">
        <div class="chat-history">
          <div v-for="(message, index) in messageList" :key="message.id">
            <MessageView :message="message" :isAsking="false" :isKnowledge="false" :isShowClearContextHint="false"
              :is-last-message-record="index == messageList.length - 1" :current-is-clicked-clear-button="false"
              :is-share="true" />
          </div>
          <div class="share-button-area">
            <div class="footer">
              <a-button size="normal" type="primary" @click="handlePressEntrance" color="#388bef">
                <div class="share-button">
                  <div class="share-button-text" style="color: #ffffff">
                    想了解什么知识，快来问问我
                  </div>
                </div>
              </a-button>
              <div class="footer-power">
                回复的内容由AI生成，非人工编辑；其内容准确性和完整性无法保证，不代表我们的态度和观点。
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-else>
      <div class="w-full h-full flex flex-col items-center justify-center">
        <div class="w-20 h-20 mx-auto mb-4 bg-gray-50 rounded-full flex items-center justify-center">
          <loading theme="outline" size="40" class="text-gray-400 animate-spin" />
        </div>
        <h3 class="text-lg font-medium text-gray-600 mb-2 text-[13px]">加载中...</h3>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { AppCategory, AppMessage, OneMessageForShareUserInfo } from '@/services/types/appMessage';
import { computed, onMounted, ref } from 'vue';

import { fetchOneMessageForShare } from '@/api/appMessage';
import { UserService } from '@/services/user';
import { isWechatBrowser } from '@/utils/constants';
import { StarloveConstants } from '@/utils/starloveConstants';
import { Loading } from '@icon-park/vue-next';
import { message } from 'ant-design-vue';
import { useRouter } from 'vue-router';
const MessageView = defineAsyncComponent(() =>
  import('./MessageView.vue')
)

interface Props {
  questionId?: string
  robotCode: string
  mode: string
  // category: string
  categoryName?: string
}
const props = withDefaults(defineProps<Props>(), {})

const category = ref<AppCategory | undefined>()

const emit = defineEmits(['update:categoryName'])
const categoryName = computed({
  get: () => category.value?.name || '',
  set: (val) => {
    emit('update:categoryName', val)
  }
})

const router = useRouter()

const messageList = ref<AppMessage[]>([])
const askerUser = ref<OneMessageForShareUserInfo | undefined>(undefined)

const shareOverlayVisible = ref<boolean>(false)

const isFinished = ref<boolean>(false)

const questionId = computed(() => {
  return props.questionId
})


// const shareDesc = () => {
//     if (messageList.value.length == 0) {
//         return ''
//     }

//     return StarloveUtil.truncateString(messageList.value[0].content, 50)
// }

// const handlePressShareSession = () => {
//   shareOverlayVisible.value = true
// }
const handlePressEntrance = () => {
  router.push({ path: `/` })
}

const loadData = async () => {
  const params = {
    msgId: questionId.value
  }
  // console.log('loadData params ==>', params)
  const res = await fetchOneMessageForShare(params)
  // console.log('loadData res ==>', res)
  // Taro.hideLoading()
  if (!res.ok || !res.data) {
    message.error('加载失败')
    return
  }

  if (res.data.messageList && res.data.messageList.length > 0) {
    messageList.value = res.data.messageList

    console.log('messageList ==>', messageList.value)
  }
  if (res.data.category) {
    category.value = res.data.category
    categoryName.value = res.data.category.name
  }

  askerUser.value = res.data.askerUser
  // console.log('askerUser 11 ==>', askerUser.value)
  // dialogData.value = res.data.result

  isFinished.value = true
}

const configPageShareWechatMp = () => {
  // 使用URL构造函数创建URL对象
  // const urlObj = new URL(window.location.href)

  // const query = qs.stringify({
  //     questionId: questionId.value,
  //     robotCode: robotCode.value,
  //     // category: category.value,
  //     sharerUserId: UserService.getSelfUserId(),
  //     mode: StarloveConstants.sharePageMode.view
  // })

  // const pageUrl = urlObj.origin + `/pages/xiaoin/shareAnswer/index`
  // const link = `${pageUrl}?${query}`
  // // console.log('link == >', link)
  // const appendix = StarloveUtil.isInTestServer() ? ' (测试环境)' : ''
  // initWxConfig({
  //     title: `${askerUser.value?.nickname || '万能小in用户'}写作了问答${appendix}`,
  //     timelineTitle: shareDesc(),
  //     desc: shareDesc(),
  //     link: link,
  //     imgUrl: askerUser.value?.avatar || xiaoinLogo,
  //     callback: (res: any) => {
  //         console.log('initWxConfig', res)
  //     }
  // })
}
const setupData = async () => {
  try {
    await loadData()
    if (!UserService.isLogined()) {
      return
    }
    // if (isPcWeb()) {
    //     return
    // }
    if (isWechatBrowser()) {
      //必须等读取到内容后才可以配置
      setTimeout(() => {
        configPageShareWechatMp()
      }, 1000)
    }

    setTimeout(() => {
      if (props.mode == StarloveConstants.sharePageMode.share) {
        shareOverlayVisible.value = true
      }
    }, 1000)
  } catch (error) {
    isFinished.value = true
  }
}

onMounted(() => {
  setupData()
})
</script>

<style lang="scss">
.share-page {
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  height: 100vh;
  min-height: 300px;

  .header {
    font-size: 24px;
    font-weight: bold;
    text-align: center;
    margin-top: 20px;
  }

  .content {
    flex: 1;
    padding: 20px;
    padding-bottom: 80px;

    .article {
      .paragraph {
        margin-bottom: 15px;
        font-size: 16px;
        line-height: 1.5;
        text-indent: 2em;
      }
    }
  }

  .entrance {
    margin: 24px 23px;
  }

  .share-button-area {
    padding-bottom: 50px;
  }

  .footer {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    padding: 0 13px;
    margin-top: 50px;

    .share-button {
      min-width: 100px;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .share-button-text {
      font-size: 15px;

      font-weight: 500;
      // color: #333333;
      line-height: 23px;
    }

    // .share-button-icon {
    //   width: 23px;
    //   height: 23px;
    //   margin-left: 5px;
    // }
    // .share-button-icon image {
    //   width: 23px;
    //   height: 23px;
    // }

    .footer-power {
      margin-top: 10px;
      font-size: 11px;
      color: #cccccc;
      text-align: center;
      // background-color: pink;
      margin-left: 15px;
      margin-right: 15px;
      margin-bottom: 20px;
    }
  }

  .share-area {
    display: flex;
    justify-content: flex-end;
    align-items: flex-start;
  }

  .share-image {
    width: 215px;
    height: 130px;
    margin: 10px 55px 0 0;
  }
}
</style>