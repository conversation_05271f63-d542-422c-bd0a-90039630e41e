<template>
    <div @click="showSelectModal = true">
        <slot name="content">
        </slot>
    </div>
    <!-- 在 template 底部添加弹窗组件 -->
    <SelectKnowledgeDocumentModal v-if="showSelectModal" v-model:modelValue="showSelectModal" :appCode="appCode"
        :selected-ids="selectedKnowledgeFileIds" :options="options" :maxLength="maxLength" :is-chat="true"
        @select="onKnowledgeSelect" />
</template>
<script setup lang="ts">
import { ref } from 'vue';
import type { UploadDocument, UploadFileRepositoryInfo } from '~/services/types/knowledgeUpload';

const emit = defineEmits(['select'])

const showSelectModal = ref(false)
const appCode = ref('ppt')
const options = ref('docx,pdf,ppt,xlsx,txt,md')
const maxLength = ref(20)
const selectedKnowledgeFileIds = ref<(string | number | undefined)[]>([])
const selectedKnowledgeList = ref<UploadDocument[]>([])
const fileRepositoryInfoList = ref<UploadFileRepositoryInfo[]>([])


const setFileRepositoryInfoList = () => {
    const newList = selectedKnowledgeList.value
        .filter((item): item is (typeof item & { fileId: string }) => {
            return selectedKnowledgeFileIds.value.includes(item.id) && !!item.id
        })
    if (newList.length == 0) {
        fileRepositoryInfoList.value = []
        return
    }
    fileRepositoryInfoList.value = newList.map(item => ({
        fileId: item.fileId,
        repositoryFileId: item.id as string
    }))
}
const onKnowledgeSelect = (list: UploadDocument[]) => {
    selectedKnowledgeList.value = list
    selectedKnowledgeFileIds.value = list.map(item => item.id)
    setFileRepositoryInfoList()
    emit('select', list)
}

</script>