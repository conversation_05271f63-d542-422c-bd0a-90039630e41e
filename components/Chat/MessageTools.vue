<template>
    <div class="max-w-[302px] w-4/5 flex flex-row px-4" v-if="isShowActionButton">
        <div class="h-9 flex items-center justify-center mr-2.5 cursor-pointer w-[71px]" @click="handlePressCopy">
            <img class="w-3 h-3" :src="copyIcon" />
            <div class="ml-1 text-sm text-gray-500 hover:text-blue-600 transition-colors">复制</div>
        </div>

        <div v-if="!isHuawen" class="h-9 flex items-center justify-center mr-2.5 cursor-pointer w-[71px]"
            @click="handlePressDelete">
            <img class="w-3 h-3" :src="deleteIcon" />
            <a-popconfirm title="确定删除该条消息吗？" ok-text="确定" cancel-text="取消" @confirm="handlePressDelete">
                <button @click.stop class="ml-1 text-sm text-gray-500 hover:text-blue-600 transition-colors">
                    <div>删除</div>
                </button>
            </a-popconfirm>

        </div>

        <div class="h-9 flex items-center justify-center mr-2.5 cursor-pointer w-[71px]" v-if="isShowShare"
            @click="handlePressShare">
            <img class="w-3 h-3" :src="shareIcon" />
            <view class="ml-1 text-sm text-gray-500 hover:text-blue-600 transition-colors">分享</view>
        </div>

        <a-dropdown :trigger="['click']" v-if="isHuawen">
            <a-tooltip title="更多" placement="top" overlay-class-name="black-tooltip">
                <div class="h-9 flex items-center justify-center mr-2.5 cursor-pointer w-[71px]">
                    <more theme="outline" size="24" fill="#333" />
                </div>
            </a-tooltip>
            <template #overlay>
                <a-menu>
                    <a-menu-item key="report" @click="handlePressReport">
                        <div class="flex items-center">
                            <attention theme="outline" size="16" fill="#333" class="mr-1" />
                            <span class="text-sm">举报</span>
                        </div>
                    </a-menu-item>
                    <a-menu-item key="delete" v-if="message.id">
                        <a-popconfirm title="确定删除该条消息吗？" ok-text="确定" cancel-text="取消" @confirm="handlePressDelete">
                            <div class="flex items-center">
                                <delete-one theme="outline" size="16" fill="#333" class="mr-1" />
                                <span class="text-sm">删除</span>
                            </div>
                        </a-popconfirm>
                    </a-menu-item>
                </a-menu>
            </template>
        </a-dropdown>
    </div>

    <!-- 使用举报弹窗组件 -->
    <ReportModal v-model:open="reportModalOpen" />
</template>

<script setup lang="ts">
import { deleteMessage } from '@/api/appMessage';
import { type AppMessage } from '@/services/types/appMessage';
import { copyToClipboard } from '@/utils/copyToClipboard';
import { isJSON } from '@/utils/utils';
import { Attention, DeleteOne, More } from '@icon-park/vue-next';
import { computed, ref } from 'vue';
import { useRoute } from 'vue-router';
import ReportModal from '~/components/Common/ReportModal.vue';
import { ToastService } from '~/services/toast';
import { UserService } from '~/services/user';

const props = defineProps<{
    message: AppMessage,
    isShare: boolean
}>()

const { $eventBus } = useNuxtApp();

const route = useRoute()

const isHuawen = computed(() => {
    return sessionStorage.getItem('utm_source') == 'huawei'
})

// 举报相关状态
const reportModalOpen = ref(false);

const isSelf = computed(() => {
    return props.message.senderId != 'AI'
})

const isDemo = computed(() => {
    return props.message.userId == 'DEMO' || props.message.id?.startsWith('DEMO')
})

const isDone = computed(() => {
    return props.message.status == 'done'
})

const allowCopyOrShare = computed(() => {
    return !isDemo.value
})

const isShowShare = computed(() => {
    if (allowCopyOrShare.value && !isSelf.value) {
        return true
    }
    if (!props.message.questionId) {
        return false
    }
    return false
})

const isShowActionButton = computed(() => {
    if (isDemo.value) {
        return false
    }
    if (isSelf.value) {
        return true
    }
    if (isDone.value) {
        return true
    }
    if (props.message.mediaType == 'text') {
        return true
    }
    return false
})

const copyIcon = computed(() => {
    return 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/icons/black_copy.png'
})

const deleteIcon = computed(() => {
    return 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/icons/black_delete.png'
})

const shareIcon = computed(() => {
    return 'https://static-1256600262.file.myqcloud.com/mini-app/roles/share-icon.png'
})

const answer = computed(() => {
    if (!props.message || !props.message.content) {
        return
    }
    if (isJSON(props.message.content)) {
        const data = JSON.parse(props.message.content)
        const contentText = data.content
        return {
            ...data,
            content: contentText
        }
    }
    let data = props.message.content
    return { content: data }
})

const handlePressCopy = () => {
    const text = answer.value.content
    if (!text) {
        return
    }
    const textStr = String(text)
    const processedText = textStr.replace(/<think[\s\S]*?<\/think>/g, '')
    // console.log('处理后内容:', processedText)
    copyToClipboard(processedText)
    ToastService.success('复制成功')
}

const handlePressDelete = async () => {
    if (!props.message.id) {
        return
    }
    const res = await deleteMessage({ messageId: props.message.id })
    if (!res.ok) {
        ToastService.success('操作失败')
        return
    }
    ToastService.success('删除成功')
    const _message = {
        ...props.message
    }
    $eventBus.emit(StarloveConstants.keyOfEventBus.updateMessageDelete, _message)
}

const handlePressShare = () => {

    let text = `${window.location.origin}/answer?questionId=${props.message.questionId
        }&mode=${StarloveConstants.sharePageMode.share}&sharerUserId=${UserService.getSelfUserId()}`
    if (!text) {
        ToastService.error('复制内容不能为空')
        return
    }
    console.log('textToCopy ->', text)
    copyToClipboard(text)
    ToastService.success('分享链接复制成功')

}

const handlePressReport = () => {
    reportModalOpen.value = true;
}

</script>

<style>
.black-tooltip .ant-tooltip-inner {
    background-color: rgba(0, 0, 0, 0.75);
    color: white;
}

.black-tooltip .ant-tooltip-arrow-content {
    background-color: rgba(0, 0, 0, 0.75);
}
</style>
