<template>
  <div class="message-view-new">
    <div :class="{
      'message-item': true,
      'message-item-mine': message.senderId != 'AI',
      'message-item-ai': message.senderId == 'AI'
    }">
      <div class="message-content">
        <div v-for="(item, replyMessageIndex) in replyMessage"
          :key="`${item?.item_id ? item?.item_id : new Date().valueOf()}`">
          <!-- 思考中... -->
          <a-collapse :bordered="false"
            :defaultActiveKey="message.status != LastAgentMessageStatus.done ? [item.item_id] : []"
            :style="{ background: '#fff' }"
            v-if="[LastAgentMessageStatus.think_delta, LastAgentMessageStatus.think_done].includes(item.type) && message.senderId == 'AI'"
            expandIconPosition="end">
            <template #expandIcon="props">
              <caret-right-outlined :rotate="props?.isActive ? 90 : 0" />
            </template>
            <a-collapse-panel :key="item.item_id" :style="customStyle">
              <template #header>
                <div style="padding: 0 !important">
                  思考中...
                </div>
              </template>
              <div class="flex px-1 pt-[11px]">
                <div class="relative w-[2px] pr-5">
                  <div class="absolute inset-y-0 w-[2px] bg-[#E9E9E9]"></div>
                </div>
                <div>
                  <div v-if="item.type == LastAgentMessageStatus.think_delta">
                    <RenderMessageContent :content="item.delta" :is-my-self="false"></RenderMessageContent>
                  </div>
                  <div v-else-if="item.type == LastAgentMessageStatus.think_done">
                    <RenderMessageContent :content="item.content" :is-my-self="false"></RenderMessageContent>
                  </div>
                </div>

              </div>
            </a-collapse-panel>
          </a-collapse>
          <!-- 工具调用... -->
          <a-collapse :bordered="false" :style="{ background: '#fff' }"
            :defaultActiveKey="message.status != LastAgentMessageStatus.done ? [item.item_id] : []"
            v-if="[LastAgentMessageStatus.tool_call_started, LastAgentMessageStatus.tool_call_completed].includes(item.type)"
            expandIconPosition="end">
            <template #expandIcon="props">
              <caret-right-outlined :rotate="props?.isActive ? 90 : 0" />
            </template>
            <a-collapse-panel :key="item.item_id" :style="customStyle">
              <template #header>
                <div style="padding: 0 !important">
                  {{ AgentToolCName[item?.tool_name as keyof typeof AgentToolCName] || '' }}
                </div>
              </template>
              <div class="flex px-1 pt-[11px]">
                <div class="relative w-[2px] pr-5">
                  <div class="absolute inset-y-0 w-[2px] bg-[#E9E9E9]"></div>
                </div>
                <div>
                  <div v-if="item.type == LastAgentMessageStatus.tool_call_started">
                    <div class="py-2">请求:</div>
                    <div class="text-sm text-gray-600">{{ item.params }}</div>
                  </div>
                  <div v-else-if="item.type == LastAgentMessageStatus.tool_call_completed">
                    <div class="py-2">请求:</div>
                    <div class="text-sm text-gray-600">{{ item.result.params }}</div>
                    <template v-if="item.result.results.length">
                      <div class="py-2">响应:</div>
                      <div class="text-sm text-gray-600">
                        <div v-if="item.tool_name == AgentToolName.repo_search_filename">
                          <div class="cursor-pointer my-0.5 text-blue-600"
                            v-for="(resultItem, index) in item.result.results" :key="`${index}_a`"
                            @click="handleOpenKnowedge(resultItem)">
                            <span class="ref-type">文件名：</span>
                            <span class="ref-content">{{
                              resultItem?.title && resultItem?.title.length > 30 ? resultItem.title.substring(0, 31) :
                                resultItem?.title || ''
                            }}</span>
                          </div>
                        </div>
                        <div v-else-if="item.tool_name == AgentToolName.repo_file_block">
                          <div class="cursor-pointer my-0.5 text-violet-500"
                            v-for="(resultItem, index) in item.result.results" :key="index"
                            @click="handleOpenKnowedge(resultItem)">
                            <span class="ref-type">知识库资料：</span>
                            <span class="ref-content">{{
                              resultItem.desc.length > 30 ? resultItem.desc.substring(0, 31) : resultItem.desc
                            }}<span v-if="resultItem.meta?.page.length > 1">_{{ resultItem.meta.page }}</span></span>
                          </div>
                        </div>
                        <div v-else-if="item.tool_name == AgentToolName.repo_search_fileinfo">
                          <div class="cursor-pointer my-0.5 text-violet-500"
                            v-for="(resultItem, index) in item.result.results" :key="index"
                            @click="handleOpenKnowedge(resultItem)">
                            <span class="ref-type">文件信息：</span>
                            <span class="ref-content">{{
                              resultItem?.title && resultItem?.title.length > 30 ? resultItem.title.substring(0, 31) :
                                resultItem?.title || ''
                            }}</span>
                          </div>
                        </div>
                        <template v-else-if="item.tool_name == AgentToolName.web_search">
                          <div class="cursor-pointer my-0.5 text-violet-500"
                            v-for="(resultItem, index) in item.result.results" :key="index">
                            <a class="cursor-pointer my-0.5 text-violet-500" :href="resultItem?.meta?.url || ''"
                              target="_blank">
                              <span class="ref-content">{{
                                resultItem?.title || ''
                                }}</span>
                            </a>
                          </div>
                        </template>
                        <template v-else-if="item.tool_name == AgentToolName.scholar_search">
                          <div class="cursor-pointer my-0.5 text-violet-500"
                            v-for="(resultItem, index) in item.result.results" :key="index">
                            <a class="cursor-pointer my-0.5 text-violet-500"
                              :href="resultItem?.meta?.main_url || resultItem?.meta?.pdf_url || ''" target="_blank">
                              <span class="ref-content">{{
                                resultItem?.title || ''
                                }}</span>
                            </a>
                          </div>
                        </template>
                        <div v-else>
                          <div class="cursor-pointer my-0.5 text-blue-600"
                            v-for="(resultItem, index) in item.result.results" :key="`${index}_${item.tool_name}`"
                            @click="handleOpenKnowedge(resultItem)">
                            <span class="ref-content">{{
                              resultItem?.title && resultItem?.title.length > 30 ? resultItem.title.substring(0, 31) :
                                resultItem?.title || ''
                            }}</span>
                          </div>
                        </div>
                      </div>
                    </template>

                  </div>
                </div>
              </div>
            </a-collapse-panel>
          </a-collapse>
          <div class="message-item-content" v-if="item.type == LastAgentMessageStatus.text_delta">
            <RenderMessageContent :content="item.delta" :is-my-self="false"></RenderMessageContent>
          </div>
          <div class="message-item-content" v-else-if="item.type == LastAgentMessageStatus.text_done">
            <button @click="handleReferenceSource"
              v-if="message.status === LastAgentMessageStatus.done && (getLastTextDeltaIndexByMessage === replyMessageIndex)"
              class="flex items-center justify-center px-3 py-1.5 text-gray-500 bg-gray-50  rounded-lg cursor-pointer hover:text-blue-500 ">
              <span class="pr-1 text-sm ">参考来源</span>
              <right :size="14" />
            </button>
            <RenderMessageContent :content="item.content" :is-my-self="false"></RenderMessageContent>
          </div>

        </div>

        <template v-if="message.senderId !== 'AI'">
          <RenderMessageContent :content="answer.content" :is-my-self="false" class="message-item-content">
          </RenderMessageContent>
        </template>
        <div v-if="message.senderId == 'AI' &&
          props.isLastMessageRecord &&
          props.isAsking &&
          message.status != LastMessageStatus.answeing && message.status != LastMessageStatus.thinking
        " style="margin-left: 12px; padding: 0 15px 15px 15px">
          <LoadingOutlined style="color: #42e5b5; filter: drop-shadow(0 0 5px #249cff); font-size: 15px" />
        </div>

        <MessageTools :is-share="isShare" :message="message" v-if="isShowMessageTools"></MessageTools>

      </div>

      <MessageRecommendApply v-if="isShowRecommendApply" :recommendApplyList="recommendApplyList"
        :isKnowledge="isKnowledge">
      </MessageRecommendApply>
    </div>

    <!-- <div class="clear-hint-text" v-if="isShowClearContextText">——已清除上下文，请开启新话题——</div> -->
  </div>
</template>

<script setup lang="ts">
import type { AppCategory, AppMessage } from '@/services/types/appMessage'
import { useChatStore } from '@/stores/chat'
import { AgentToolCName, LastAgentMessageStatus, LastMessageStatus, PagePath } from '@/utils/constants'
import asyncLoadJs from '@/utils/loadResources'
import { isJSON, sleep } from '@/utils/utils'
import { Right } from '@icon-park/vue-next'

import { CaretRightOutlined, LoadingOutlined } from '@ant-design/icons-vue'
import markdownItKatex from '@vscode/markdown-it-katex'
import copy from 'copy-text-to-clipboard'
import hljs from 'highlight.js/lib/core'
import 'highlight.js/styles/ir-black.css'
import 'katex/dist/katex.min.css'
import { cloneDeep } from 'lodash'
import markdownit from 'markdown-it'
import markdownItContainer from 'markdown-it-container'
import markdownItHighlightjs from 'markdown-it-highlightjs'
import { computed, nextTick, onMounted, ref, watch } from 'vue'
import { ToastService } from '~/services/toast'
import MessageRecommendApply from './MessageRecommendApply.vue'
import MessageTools from './MessageTools.vue'
import RenderMessageContent from './RenderMessageContent.vue'

const { $eventBus } = useNuxtApp();
declare global {
  interface Window {
    exportToExcelMD: (tableId: string) => Promise<void>;
    copyMD: (codeId: string) => Promise<void>;
    XLSX: any;
  }
}

interface Props {
  message: AppMessage
  isAsking: boolean
  isKnowledge?: boolean
  // isShowClearContextHint?: boolean
  isLastMessageRecord: boolean
  currentIsClickedClearButton: boolean
  messageRecommendApplyData?: AppCategory[]
  isFirstAnswer?: boolean,
  isShare?: boolean
  currentReferenceSourceMessageId?: string
}

const props = withDefaults(defineProps<Props>(), {
  isShare: false,
  currentReferenceSourceMessageId: ''
})
const customStyle = 'background: #fff;border-radius: 10px;margin: 10px;'
const chat = useChatStore()
const { openReferenceSource } = storeToRefs(useChatStore())
let lastHeading = 'table-data'
const isActive = ref(false)
const activeKey = ref('final_answer_6.think')

const isShowMessageTools = computed(() => {
  if (props.isLastMessageRecord && props.isAsking) {
    return false
  }
  return true
})


const isShowRecommendApply = computed(() => {
  // if (props.isAsking) {
  //   return false
  // }
  if (props.isAsking && props.isLastMessageRecord) {
    return false
  }
  if (recommendApplyList.value.length == 0) {
    return false
  }
  return true
})

const understands = computed(() => {
  if (!props.isFirstAnswer) {
    return []
  }
  if (!props.message.understands) {
    return []
  }
  return props.message.understands.filter((item) => item)
})
const answerRefs = computed(() => {
  if (!answer.value) {
    return []
  }
  if (!answer.value.refs) {
    return []
  }
  return answer.value.refs
})

const refs = computed(() => {
  let data
  if (props.message.refs) {
    data = props.message.refs || []
  } else {
    data = answerRefs.value || []
  }
  const list = data.filter((item: any) => item.snippet || (item.meta && item.meta.page))
  return list || []
})

const thinkCollapseActiveKey = ref<string[]>(['2'])

// 监听消息状态变化
// watch(() => props.message.status, (newStatus) => {
//   if (newStatus === LastMessageStatus.thinking) {
//     thinkCollapseActiveKey.value = ['2']  // 思考中状态，设置面板的 key，使其展开
//   }
// }, { immediate: true })

// 监听思考内容变化
watch(() => props.message.think, (newVal) => {
  if (newVal) {
    nextTick(() => {
      $eventBus.emit(StarloveConstants.keyOfEventBus.chatScrollToBottom)
    })
  }
})

const refsCount = computed(() => {
  if (!props.message) {
    return 0
  }
  return refs.value.length
})

const recommendApplyList = computed(() => {
  if (!props.message) {
    return []
  }
  return props.message.recommendApplyList || []
})
const replyMessage = computed(() => {
  // props.message.replyMessage 是的类型是对象 直接返回
  if (props.message?.replyMessage && typeof props.message.replyMessage === 'object') {
    return props.message.replyMessage
  }
  if (props.message.mediaType === 'json') {
    const obj = JSON.parse(props.message?.content || '{}')
    if (obj?.events) {

      const list = obj.events
      const keyMap = new Map();
      list.forEach((item: any) => {
        const key = `${item.item_id}`; // 拼接多个字段为唯一键
        keyMap.set(key, item);
      });
      const uniqueData = Array.from(keyMap.values());
      console.log('uniqueData ==>', uniqueData)
      return uniqueData
    }
  } else {
    if (props.message.senderId == 'AI') {
      return [{
        id: '1',
        type: LastAgentMessageStatus.text_done,
        content: props.message.content
      }]
    }
  }
  return []
})
// 获取 type 是text_delta 最后一个节点的序号
const getLastTextDeltaIndexByMessage = computed(() => {
  let index = -1;
  replyMessage.value.forEach((item: any, _index: number) => {
    if (item.type == LastAgentMessageStatus.text_done) {
      index = _index;
    }
  });
  return index;
})
watch(replyMessage, (newVal) => {
  chat.setReplyMessage((newVal || []).filter((item: any) => item.type.startsWith('tool')))
}, { immediate: true })
const answer = computed(() => {
  if (!props.message || !props.message.content) {
    return
  }
  // console.log('props.message.content ==>', props.message.content)
  if (isJSON(props.message.content)) {
    const data = JSON.parse(props.message.content)
    // const regex = /\[\[(.*?)\]\]\((.*?)\)/g
    const contentText = data.content
    return {
      ...data,
      content: contentText
    }
  }
  let data = props.message.content //.replace(/\*\*/g, '').replace(/\\n/g, '<br/>')
  // console.log('darta 1234232 2343 ==>', data)
  return { content: data }
})

const hasStartThinkTag = computed(() => {
  const startTag = '<think>'
  if (props.message.think) {
    return true
  }
  if (answer.value?.content.includes(startTag)) {
    return true
  }
  return false
})

const hasEndThinkTag = computed(() => {
  const endTag = '</think>'
  if (props.message.think?.includes(endTag)) {
    return true
  }
  if (answer.value?.content.includes(endTag)) {
    return true
  }
  return false
})



const mdSelf = markdownit({
  html: true, // 启用 HTML 标签
  linkify: true, // 自动将 URL 转换为链接
  breaks: true
})
const md = markdownit({
  html: true, // 启用 HTML 标签
  linkify: true, // 自动将 URL 转换为链接
  breaks: true,
  xhtmlOut: true,
  langPrefix: 'language-',
  typographer: true,
  highlight: function (str: string, lang: string): string {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return `<pre class="hljs" data-lang="${lang}"><code>${hljs.highlight(lang, str, true).value
          }</code></pre>`
      } catch (__) { }
    }

    return `<pre class="hljs" data-lang="${lang}"><code>${md.utils.escapeHtml(str)}</code></pre>`
  }
})
  .use(markdownItKatex)
  .use(markdownItContainer, 'spoiler', {
    validate: function (params: string) {
      return params.trim().match(/^spoiler\s+(.*)$/)
    },
    render: function (tokens: any, idx: number) {
      var m = tokens[idx].info.trim().match(/^spoiler\s+(.*)$/)
      if (tokens[idx].nesting === 1) {
        return '<details><summary>' + md.utils.escapeHtml(m[1]) + '</summary>\n'
      } else {
        return '</details>\n'
      }
    }
  })
  .use(markdownItHighlightjs)
  .use((md) => {
    // 添加一个计数器来跟踪表格数量
    let tableCounter = 0;

    // 拦截标题的解析
    const defaultHeadingRender =
      md.renderer.rules.heading_open ||
      function (tokens, idx, options, env, self) {
        return self.renderToken(tokens, idx, options)
      }

    md.renderer.rules.heading_open = function (tokens, idx, options, env, self) {
      // 获取标题内容
      const nextToken = tokens[idx + 1]
      if (nextToken && nextToken.type === 'inline') {
        lastHeading = nextToken.content // 保存标题内容
      }
      return defaultHeadingRender(tokens, idx, options, env, self)
    }

    // 重置表格计数器的函数
    const resetTableCounter = () => {
      tableCounter = 0;
    };

    // 在每次渲染开始时重置计数器
    const defaultCoreRender = md.core.process;
    md.core.process = function (state) {
      resetTableCounter();
      return defaultCoreRender.call(this, state);
    };

    const defaultRender =
      md.renderer.rules.table_open ||
      function (tokens, idx, options, env, self) {
        return self.renderToken(tokens, idx, options)
      }

    md.renderer.rules.table_open = function (tokens, idx, options, env, self) {
      // 增加计数器并生成唯一的表格ID
      tableCounter++;
      const uniqueTableId = `table-${Date.now()}-${tableCounter}`;

      // 在渲染表格时，为表格前添加一个小图标
      return `
        <div class="table-wrapper1">
          <span class="table-icon" onclick="exportToExcelMD('${uniqueTableId}')">
          <img class="table-icon-img" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAASVJREFUWEftllESgyAMRMGLVQ8j4y1ab+HgYerJpI0jnUCDBqR12oEff5B9rskSKU5e8mR98dsASqkbOKi1Xp4pK9mBVfwKosaYaRzH5msAWNyKpkJEO0CJH4GIAvDE+6f7r18gpaxTfgcbwBeHwlNKGRCtqqqZ5xkAomuCBUCJgzAGGIZhSilMFkDbtvfV4h63nA+wQkFLLk5orXfP391gC6zruhq+ErcaBbC1n2pTNgD18hYANxMKQHGgOOA4AL0Okcq932PbkMoSB8BGKWS7HzpHc8Ce7V/bDkAockOhAocaYy6cYeQjANy0w3dEVgf+FyB1vttyhFUDxMgV43JwLxSqHdmEEM5M8ZaEqBOyiONDKGfJKLbtlZuAatdDd0EOwALwAPKYDzBALxiRAAAAAElFTkSuQmCC"> 导出为表格</span>
          <div class="table-wrapper" data-table-id="${uniqueTableId}" data-table-name="${lastHeading}">
          ${defaultRender(tokens, idx, options, env, self)}
      `
    }

    const defaultClose =
      md.renderer.rules.table_close ||
      function (tokens, idx, options, env, self) {
        return self.renderToken(tokens, idx, options)
      }

    md.renderer.rules.table_close = function (tokens, idx, options, env, self) {
      return `${defaultClose(tokens, idx, options, env, self)}</div>`
    }
  })
// 自定义渲染器显示语言名称
md.renderer.rules.fence = (tokens, idx, options, env, self) => {
  const token = tokens[idx]
  const language = token.info.trim() || 'plaintext' // 获取语言名称
  const code = token.content
  let highlighted = ''
  try {
    highlighted = language
      ? hljs.highlight(code, { language: language }).value
      : md.utils.escapeHtml(code)
  } catch (error) {
    highlighted = md.utils.escapeHtml(code)
  }
  const codeId = `code-${idx}`
  return `
        <div class="code-block">
          <div class="code-language">
            <div>${language}</div>
            <div class="copy-code" onclick="copyMD('${codeId}')">复制</div>
          </div>
          <pre><code data-code-id="${codeId}" class="hljs ${md.utils.escapeHtml(
    language
  )}">${highlighted}</code></pre>
        </div>
      `
}
const defaultRender =
  md.renderer.rules.link_open ||
  function (tokens, idx, options, env, self) {
    return self.renderToken(tokens, idx, options)
  }

md.renderer.rules.link_open = function (tokens: any, idx: number, options: any, env: any, self: any) {
  // 如果链接没有 target 属性，则添加 target="_blank"
  const aIndex = tokens[idx].attrIndex('target')
  if (aIndex < 0) {
    tokens[idx].attrPush(['target', '_blank']) // 添加新的属性
  } else {
    tokens[idx].attrs[aIndex][1] = '_blank' // 替换现有属性的值
  }
  // 传递 token 给默认渲染器
  return defaultRender(tokens, idx, options, env, self)
}

const thinkContent = computed(() => {
  // console.log("thinkContent  ==>", props.message.think)
  if (props.message.think) {
    return md.render(props.message.think)
  }
  try {
    if (!answer.value) {
      return ''
    }
    const content = answer.value?.content;

    const match = content.match(/<think>([\s\S]*?)(?:<\/think>|$)/);
    // console.log("match  ==>", match)
    const newData = match ? match[1] : ""
    return md.render(newData)
  } catch (error) {
    return ''
    // console.error("JSON 解析错误:", error);
  }
})

const questionContent = computed(() => {
  return mdSelf.render(answer.value.content)
})

const answerContent = computed(() => {
  if (!answer.value) {
    return
  }
  if (answer.value.files) {
    return answer.value
  }
  // 如果是自己发的消息，不做走别的markdown解析
  if (props.message.senderId !== 'AI') {
    return mdSelf.render(answer.value.content)
  }

  const newData = answer.value.content
    .replace(/<think>[\s\S]*?<\/think>/g, '')
    .replace(/\\\{/g, '${')
    .replace(/\\\}/g, '}$')
    .replace(/\\\[/g, '$[')
    .replace(/\\\]/g, ']$')
    .replace(/\\\(/g, '$(')
    .replace(/\\\)/g, ')$')
    .replace(/#{2,}/g, (match: any) => match + ' ');
  // .replace(/\\text\{([^}]*)\}/g, '$1')
  // console.log('newData ==>', newData.length)
  if ((!newData || newData.trim().length == 0) && props.message.status != LastMessageStatus.thinking) {
    return thinkContent.value
  }
  return md.render(newData)
})
const renderMdToHtml = (content: string) => {
  const newData = content
    .replace(/<think>[\s\S]*?<\/think>/g, '')
    .replace(/\\\{/g, '${')
    .replace(/\\\}/g, '}$')
    .replace(/\\\[/g, '$[')
    .replace(/\\\]/g, ']$')
    .replace(/\\\(/g, '$(')
    .replace(/\\\)/g, ')$')
    .replace(/#{2,}/g, (match: any) => match + ' ');
  return md.render(newData)
}
const renderUnder = (dd: string) => {
  return md.render(dd)
}


const handleOpenKnowedge = (_item: any) => {
  const item = cloneDeep(_item)
  if (!item || !item.file_id) {
    // toast.show('该文件获取失败')
    return
  }

  if (item.meta && item.meta.bboxs) {
    const jsonString = JSON.stringify(item.meta)
    // 编码JSON字符串以确保它可以通过URL传输
    const meta = encodeURIComponent(jsonString)
    return window.open(`${PagePath.MyKnowledgeDetail}/${item.file_id}?meta=${meta}`, '_blank')
  }

  window.open(
    `${PagePath.MyKnowledgeDetail}/${item.file_id}?pageNum=${item.meta?.page || 1}`,
    '_blank'
  )
}

const handleReferenceSource = () => {
  const list = replyMessage.value.filter((item: any) => item.type.startsWith('tool'))
  chat.setReplyMessage(list)
  openReferenceSource.value = true
}

onMounted(() => {
  // console.log('message 123 ==>', props.message)
  // console.log('props.isAsking ==>', props.isAsking && props.isLastMessageRecord)
  window['exportToExcelMD'] = async (tableId) => {
    let XLSX = window['XLSX']
    // Taro.showLoading({ title: '导出下载表格中...' })
    if (!XLSX) {
      await asyncLoadJs('https://static-1256600262.file.myqcloud.com/lib/excel/xlsx.full.min.js')
      await sleep(1000)
      XLSX = window['XLSX']
    }
    try {
      const tableWrapper = document.querySelector(`[data-table-id="${tableId}"]`)
      if (!tableWrapper) {
        // Taro.hideLoading()
        return
      }
      const table = tableWrapper.querySelector('table')
      if (!table) {
        // Taro.hideLoading()
        return
      }
      // 获取表格数据
      const rows = Array.from(table.rows).map((row) =>
        Array.from(row.cells).map((cell) => cell.textContent)
      )

      // 创建 Excel 工作表
      const worksheet = XLSX.utils.aoa_to_sheet(rows)
      const workbook = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')
      const _tableName = tableWrapper.getAttribute('data-table-name') || 'table-data'
      // 导出 Excel 文件
      XLSX.writeFile(workbook, `${_tableName}.xlsx`)
      // Taro.hideLoading()
    } catch (error) {
      console.error('导出Excel失败:', error)
      // Taro.hideLoading()
    }
  }

  window['copyMD'] = async (codeId) => {
    const codeWrapper = document.querySelector(`[data-code-id="${codeId}"]`)
    if (!codeWrapper) {
      return
    }
    copy((codeWrapper as HTMLElement).innerText)
    ToastService.success('复制成功')
  }
})
</script>

<style lang="scss">
@import 'katex/dist/katex.min.css';

.katex {
  line-height: 2.5em;
}

.katex .vlist {
  // vertical-align: super;
  line-height: 1.8em;
}

.message-view-new {
  // background-color: aqua;
  // max-width: 850px;
  // overflow-x: hidden;

  .ant-collapse-header {
    border-radius: 10px !important;
    padding: 10px 15px !important;
    background: #E7EDFE !important;
    width: max-content;
    margin-left: 15px;
  }


  .message-item {
    width: 100%;
    display: flex;
    align-items: center;

    padding-left: 10px;
    padding-right: 10px;
    margin-top: 10px;
    margin-bottom: 10px;

    .message-content {
      border-radius: 5px;

      .message-text {
        // margin: 15px;
        padding: 7px 11px;
        font-size: 15px;
        line-height: 180%;
      }
    }

    .action-button {
      border: none !important;
      background: white !important;
      color: #999 !important; // 可以根据需要更改文本颜色
      box-shadow: none !important;

      &:hover {
        cursor: pointer;
        opacity: 0.8;
        box-shadow: none !important;
      }
    }
  }

  .message-item-mine {
    max-width: 100%;
    justify-content: flex-end;


    .message-content {
      color: #333333;
      background-color: #dae7ff;
      border-radius: 15px 15px 0px 15px;


      .message-item-content {
        padding: 10px 15px;
        line-height: 1.8;
        font-size: 15px;

        .ai-result {
          white-space: pre-wrap;
        }

        // p {
        //     margin: 0;
        // }

        // h2 {
        //     font-size: 16px;
        //     font-weight: bold;
        //     background-color: #f2f6ff;
        //     padding: 5px 10px;
        //     border-radius: 5px;
        //     margin: 10px 0;
        // }
      }
    }
  }

  .message-item-ai {
    // background: forestgreen;
    justify-content: flex-start;
    align-items: flex-start;
    flex-direction: column;


    .message-content {
      color: #333333;

      background-color: #fff;
      border-radius: 15px 15px 15px 0px;

      .message-item-content {
        padding: 10px 15px;
        line-height: 1.8;
        font-size: 15px;
        margin-left: 15px;

        // p {
        //     padding-bottom: 0px;
        // }


      }
    }

    .message-content-knowledge {
      color: #333333;
      max-width: 700px;
      // background-color: #f7f7f7;
      // border-radius: 15px 15px 15px 0px;
    }
  }

  .clear-hint-text {
    font-weight: 400;
    font-size: 14px;
    color: #bbbbbb;
    line-height: 21px;
    text-align: center;
    margin: 20px 0;
  }

  .overflow {
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    // -webkit-line-clamp: 1;
    overflow: hidden;
    word-break: break-all;
  }

  .flex-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .flex-start {
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }

  .understand-question p {
    margin: 0;
  }

  .understand-question-title {
    padding-bottom: 6px;
    font-weight: bolder;
  }

  .tink-content {
    // font-weight: 500;
    font-size: 15px;
    color: #777777;
    line-height: 27px;
  }

  .resource-link {
    display: block;
    max-width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 5px;
    line-height: 1.5;
  }

  .refs-container {
    width: 100%;
    max-width: 100%;


    .ref-item {
      width: 100%;
      margin-bottom: 5px;

    }

    .ref-link {
      display: flex;
      width: 100%;

      align-items: center;

      .ref-type {
        color: #777777;
        flex-shrink: 0;
      }

      .ref-content {
        max-width: 700px; //待优化
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}

.message-item-content {
  .ai-result {
    white-space: pre-wrap;
  }

  table {
    width: 100%;
    margin: 10px 0;
    border-collapse: collapse;
  }

  table,
  th,
  td {
    border: 1px solid black;
    /* 设置表格、表头和单元格的边框 */
  }

  th,
  td {
    padding: 8px;
    /* 设置单元格内边距 */
    text-align: center;
    /* 文本左对齐 */
  }

  ul,
  ol {
    li {
      margin-left: 15px;
    }
  }

  .table-wrapper1 {
    position: relative;
  }

  .table-wrapper {
    overflow: auto;

    table {
      overflow-x: auto;
    }
  }

  .table-icon {
    position: absolute;
    font-size: 20px;
    top: -30px;
    right: 0px;
    padding: 0 6px;
    cursor: pointer;
    font-size: 12px;
    color: #666;

    &:hover {
      color: #1e99ff;
    }
  }

  .table-icon-img {
    width: 15px;
    height: 15px;
  }

  .code-block {
    margin: 16px 0;
    border: 1px solid #e1e1e1;
    border-radius: 4px;
    overflow: hidden;

    .code-language {
      height: 40px;
      background: #f5f5f5;
      color: #333;
      font-size: 12px;
      font-family: monospace;
      padding: 4px 8px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    pre {
      margin: 0;
      padding: 0;
      background: #282c34;
      color: #f8f8f2;
    }

    .copy-code {
      padding: 4px 8px;
      cursor: pointer;

      &:hover {
        color: #1e99ff;
      }
    }
  }


  ol li {
    // background-color: #ff0000;
    list-style-type: decimal;
    line-height: 180%;
  }

  p {
    // line-height: 180%;
    line-height: 1.8;
    // background-color: #df2d00;
    margin-bottom: 0.5em;
    margin-top: 0.5em;
  }

  h1 {
    // line-height: 1.8;
    font-size: 2em;
    font-weight: bold;
    margin-top: 0.5em;
    margin-bottom: 0.5em;
  }

  h2 {
    // line-height: 1.8;
    font-size: 1.5em;
    font-weight: bold;
    line-height: 1.8;
    margin-top: 0.5em;
    margin-bottom: 0.5em;
  }

  h3 {
    // line-height: 1.8;
    font-size: 1.2em;
    font-weight: bold;
    line-height: 1.8;
    margin-top: 0.5em;
    margin-bottom: 0.5em;
  }

  .table-icon {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 10px;
  }

}
</style>
