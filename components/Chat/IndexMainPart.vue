<template>
  <div class="relative flex flex-col flex-1 h-full">
    <!-- 添加顶部按钮组 -->
    <div v-if="isVisibleTop" class="p-4 border-b border-gray-200 bg-white/80">
      <div class="flex items-center justify-between mx-auto">
        <!-- 左侧会话主题 -->
        <div class="flex items-center space-x-3">
          <div
            class="flex items-center justify-center flex-shrink-0 w-8 h-8 rounded-lg bg-gradient-to-r from-blue-500 to-purple-500">
            <MessageOne theme="outline" size="24" fill="#FFFFFF" />
          </div>
          <div>
            <h1 class="w-64 text-lg font-medium text-blue-700 truncate md:w-60">
              {{ currentChatTitle.title }}
            </h1>
            <p class="text-xs text-gray-400">
              {{ dateFormatS(new Date(currentChatTitle?.createTime || ''), 'MM月dd日 hh:mm') }}
            </p>
          </div>
        </div>

        <!-- 右侧按钮组 -->
        <!-- <UserInfoArea /> -->
      </div>
    </div>

    <!-- 聊天历史区域 -->
    <div class="flex-1 p-4 overflow-auto" :class="[
      visibleMsgList ? 'opacity-1' : 'opacity-0'
    ]" id="messageContainer" ref="messageContainer" @scroll="onContainerScroll">
      <div class="max-w-4xl mx-auto space-y-6">
        <div
          class="m-0 mx-4 p-2.5 px-5 bg-white rounded-tl-lg rounded-tr-lg rounded-bl-none rounded-br-lg font-normal text-base text-gray-800 leading-7"
          v-if="messages.length == 0 && !isKnowledge && !isLoading">
          Hi，我是你的小in知识助手，想了解什么知识，都可以来问我~联网提问+个人知识库，准确找到你想要的答案！
        </div>
        <div class=" text-center p-[10px] text-gray-900 cursor-pointer " @click="handlePressLoadMore"
          v-if="(data?.pages || 0) > (data?.current || 1)">
          <a-spin :spinning="isLoading">点击加载更多历史记录</a-spin>
        </div>
        <a-spin :spinning="!((data?.pages || 0) > (data?.current || 1)) && isLoading">
          <ExampleQuestion :robot-code="robotCode" :exampleList="exampleList"
            @sendExampleMessage="handleMessageExampleInput"></ExampleQuestion>
          <div v-for="(message, index) in messages" :key="message.id">
            <MessageView :message="message" :isAsking="isAsking" :isKnowledge="isKnowledge"
              :messageRecommendApplyData="messageRecommendApplyData"
              :is-last-message-record="index == messages.length - 1"
              :is-first-answer="messages.length > 0 && message.senderId == 'AI' && index == 1"
              :current-is-clicked-clear-button="currentIsClickedClearButton" :is-share="false" />
          </div>
        </a-spin>
      </div>
    </div>

    <div v-if="!visibleMsgList" class="absolute top-0 bottom-0 left-0 right-0 flex items-center justify-center">
      <a-spin></a-spin>
    </div>
    <!-- 输入区域 -->
    <AiQuestionTextarea v-model:question="question" v-model:questionMode="questionModeValue" :is-asking="isAsking"
      :is-home="false" :is-knowledge="isKnowledge || false" ref="aiQuestionTextareaRef" @submit="onSubmit" />
  </div>
</template>
<script lang="ts" setup>
import { useChatStore } from '@/stores/chat';
import { useUserStore } from '@/stores/user';
import { BindPhoneModal } from '@/utils/constants';
import { StarloveConstants } from '@/utils/starloveConstants';
import { getAiQuestionModeList } from '@/utils/utils';
import { MessageOne } from '@icon-park/vue-next';
import { fetchEventSource } from '@microsoft/fetch-event-source';
import { useThrottleFn } from '@vueuse/core';
import { message } from 'ant-design-vue';
import { defineAsyncComponent, onBeforeUnmount, onMounted, ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { getSuggestCreator, pageAppMessages } from '~/api/appMessage';
import { getPlatformNew } from '~/composables/useApp';
import { useTracking } from '~/composables/useTracking';
import type { AppCategory, AppMessage } from '~/services/types/appMessage';
import { UserService } from '~/services/user';
import { useRechargeStore } from '~/stores/recharge';
import { dateFormatS } from '~/utils/utils';
import AiQuestionTextarea from './AiQuestionTextarea.vue';
import ExampleQuestion from './ExampleQuestion.vue';

const { track } = useTracking();

const MessageView = defineAsyncComponent(() =>
  import('./MessageView.vue')
)

const { $eventBus } = useNuxtApp();
// 定义一个props来接收 里面有个isVisibleTop，bool类型，
const props = defineProps({
  isVisibleTop: {
    type: Boolean,
    default: false,
  },
  sessionId: {
    type: String,
    default: '',
  },
  isKnowledge: {
    type: Boolean,
    default: false,
  },
  pageNo: {
    type: Number,
    default: 1,
  },
  repositoryFileId: {
    type: String,
    default: '',
  },
  exampleList: {
    type: Array as PropType<string[]>,
    default: () => [],
  },
  isNewSessionId: {
    type: Boolean,
    default: false,
  }
})
let iDate = new Date().toUTCString()
const sessionId = ref<string>(props.sessionId || '')
const isNewSessionId = ref<boolean>(props.isNewSessionId)
const robotCode = ref('knowledge')
const webUserBindPhoneVisible = ref(false)
const aiQuestionTextareaRef = ref()
const chat = useChatStore()
const visibleMsgList = ref(false)
const currentChatTitle = computed(() => {
  const _list = chat.messageRecordList
  const obj = _list.find(d => d.sessionId == props.sessionId)
  return obj || {
    title: '新提问',
    createTime: dateFormatS(new Date(), 'yyyy-MM-dd hh:mm:ss')
  }
})

const loadRecommendApply = async (trimmedMessage: string) => {
  const res = await getSuggestCreator({ question: trimmedMessage })
  if (!res.ok || !res.data) {
    return
  }
  const list = res.data || []
  messageRecommendApplyData.value = list

  // storage.set(
  //   StarloveConstants.keyOflocalStorage.aiQuestionMessageRecommendApplyData,
  //   JSON.stringify(list)
  // )
}
const clearQuestionFile = () => {
  if (!aiQuestionTextareaRef.value) {
    return
  }
  aiQuestionTextareaRef.value.clearQuestionFileInfo()
}
const updateQuestionMessageRecordsAndLocation = (params: any) => {
  if (!params.title) {
    isNewSessionId.value = false
  }
  console.log('updateAiQuestionLeftMessageRecords updateAiQuestionLeftMessageRecords')
  $eventBus.emit(StarloveConstants.keyOfEventBus.updateAiQuestionLeftMessageRecords, params)
}
const reqParams = {
  pageNo: 1,
  pageSize: 25,
  sessionId: props.isKnowledge ? '' : sessionId.value,
  category: robotCode.value,
  repositoryFileId: props.repositoryFileId,

}
const { data, isLoading, isFinished, query } = pageAppMessages(reqParams) as any
const isAsking = ref<boolean>(false)
const messageRecommendApplyData = ref<AppCategory[]>([])
const currentIsClickedClearButton = ref(false)
const messages = ref<AppMessage[]>([])

const questionModeValue = ref<string[]>([])
let timerId: NodeJS.Timeout | undefined = undefined
const reloadMessagesData = () => {
  query({ params: reqParams })
}
const execQuery = () => {
  // console.log('props.sessionId', props.sessionId, isNewSessionId.value, 'isNewSessionId')
  if (!isNewSessionId.value) {
    sessionId.value = props.sessionId
    if (reqParams.pageNo == 1) {
      visibleMsgList.value = false
    }
    query({
      params: {
        ...reqParams,
        sessionId: props.sessionId
      }
    })
  } else {
    visibleMsgList.value = true
  }
}
// watch(
//   () => props.sessionId,
//   (newValue, oldValue) => {
//     console.log('props.sessionId', newValue, oldValue, isNewSessionId.value, 'isNewSessionId')
//     if (!isNewSessionId.value) {
//       sessionId.value = newValue
//       query({
//         params: {
//           ...reqParams,
//           sessionId: props.sessionId
//         }
//       })
//     }
//   }
// )
const user = useUserStore()
const handleBindPhoneSuccess = () => {
  // temporaryQuestioContent.value = messages.value[messages.value.length - 2].content
  $eventBus.emit(StarloveConstants.keyOfEventBus.updateLeftMessageRecords)
  // query({ params: reqParams }) //lsg 未绑定手机的情况，不应该去查询
  const _content = messages.value[messages.value.length - 2].content || ''
  if (_content) {
    try {
      const json = JSON.parse(_content)
      if (json.content) {
        question.value = json.content
        if (Array.isArray(json.files) && json.files.length > 0) {
          questionFileId.value = json.files[0].fileId
          questionFileUrl.value = json.files[0].fileUrl
        }
      }
    } catch (error) {
      question.value = _content
      console.log(error, '未绑定手机的情况')
    }
  }
  // question.value = messages.value[messages.value.length - 2].content || ''
  setTimeout(() => {
    messages.value = []
    handlePressSubmit()
  }, 500)
}

watch(() => user.showPhoneBoundModal.status, (newValue) => {
  console.log(newValue, 'newValue', '手机号绑定成功的回调')

  if (newValue == BindPhoneModal.FINISH_BINDING) {
    handleBindPhoneSuccess()
  }
})

watch(isFinished, (newValue, oldValue) => {
  // console.log('isFinished发生变化 新值是' + newValue, '旧值是' + oldValue)

  // console.log('isLoading -> ', isLoading.value)
  // console.log('isFinished -> ', isFinished.value)
  // console.log('response -> ', data.value)
  if (isFinished.value == false) {
    visibleMsgList.value = true
    return
  }

  if (!data.value) {
    visibleMsgList.value = true
    // message.showToast({ title: '请检查网络', icon: 'error' })
    return
  }

  if (data.value.current) {
    reqParams.pageNo = data.value.current
  }

  if (data.value.current == 1) {
    messages.value = []
  }

  if (isFinished.value && data.value) {
    const _list = data.value.records || []
    if (_list) {
      messages.value = [...data.value.records.reverse(), ...messages.value]
      // console.log('data.value.current', data.value.current)
      if (data.value.current == 1) {
        isStopScroll = false
        setTimeout(() => {
          scrollToBottom()
          visibleMsgList.value = true
        }, 500)
      }

      if (messages.value.length > 0) {
        const data = messages.value[messages.value.length - 1]
        // iDate = new Date()
        if (data && data.status == 'ing') {
          timerId = setTimeout(() => {
            reloadMessagesData()
          }, 5000)
        } else {
          clearInterval(timerId)
          // removeMessageRecommendApplyAndUnderstands()
        }
      }
    } else {
      visibleMsgList.value = true
    }

  }
})
const questionFileId = ref()
const questionFileUrl = ref()
let ctrl: any
const networkLookupOrKnowledgeInfo = ref()
const question = ref<string>('')
const temporaryQuestioContent = ref() //用于未绑定手机时，存储问题内容
let isStopScroll = false
let offsetHeight = 0
// 添加变量记录消息容器的高度信息
let lastScrollHeight = 0
let lastScrollTop = 0
const SCROLL_THRESHOLD = 50 // 滚动判定阈值，单位像素

const insideThinkTag = ref(false) // 用于标记是否在<think>标签内
let bufferedData = '' // 临时存储接收到的数据片段

const messageContainer = ref<HTMLElement | null>(null)
const isAskedAfterOpenPage = ref<boolean>(false)

const handleMessageExampleInput = (appMessage: any) => {
  isAskedAfterOpenPage.value = true

  question.value = appMessage
  handlePressSubmit()
}

const handlePressLoadMore = () => {
  // console.log('handlePressLoadMore = ')
  query({
    params: {
      ...reqParams,
      pageNo: reqParams.pageNo + 1
    }
  })
}

// 优化滚动检测逻辑
const isAtBottom = () => {
  if (!messageContainer.value) return true

  const { scrollHeight, scrollTop, clientHeight } = messageContainer.value
  // 当滚动位置接近底部时视为在底部
  return scrollHeight - scrollTop - clientHeight <= SCROLL_THRESHOLD
}

// 替换原有的 onBottom 函数
const onContainerScroll = useThrottleFn((event: Event) => {
  const target = event.target as HTMLElement
  const { scrollHeight, scrollTop, clientHeight } = target

  // 保存最后的滚动位置数据
  lastScrollHeight = scrollHeight
  lastScrollTop = scrollTop

  // 判断是否在底部
  const atBottom = scrollHeight - scrollTop - clientHeight <= SCROLL_THRESHOLD

  // 只有当用户手动滚动时才改变状态
  if (scrollHeight > offsetHeight) {
    isStopScroll = !atBottom
  }

  // 更新偏移高度，用于后续比较
  offsetHeight = clientHeight || 0
}, 200)

// 修改滚动到底部的逻辑
const _scrollToBottom = () => {
  // 使用nextTick确保DOM已更新
  if (isStopScroll) {
    return
  }

  nextTick(() => {
    if (messageContainer.value) {
      // 使用CSS过渡实现滚动动画
      messageContainer.value.style.transition = 'scrollTop 0.3s ease-out'
      messageContainer.value.scrollTop = messageContainer.value.scrollHeight
    }
  })
}

const scrollToBottom = useThrottleFn(_scrollToBottom, 650)
const _handlePressSubmit = useThrottleFn(
  async () => {
    networkLookupOrKnowledgeInfo.value = null
    // console.log(question.value, 'question.value', questionModeValue.value)
    let trimmedMessage = question.value.trim().replace(/^\n+|\n+$/g, '') // 去除首尾空白和换行
    if (temporaryQuestioContent.value) {
      trimmedMessage = temporaryQuestioContent.value
      temporaryQuestioContent.value = ''
    }
    if ((!trimmedMessage || trimmedMessage.trim() == '') && !questionFileId.value) {
      message.info('请输入问题或上传图片')
      return
    }

    let msg = {
      id: '',
      senderId: 'self',
      content: question.value,
      isRead: 'Y',
      mediaType: 'text'
    }
    if (questionFileId.value) {
      msg.content = JSON.stringify({
        content: question.value || '请理解这张图的含义',
        files: [
          {
            fileId: questionFileId.value,
            fileUrl: questionFileUrl.value,
            content: question.value || '请理解这张图的含义'
          }
        ]
      })
    }

    messages.value.push(msg)
    if (!props.isKnowledge) {
      $eventBus.emit(
        StarloveConstants.keyOfEventBus.aiQuestionIsANewQuestionAndTheSessionIdThatIsBeingAnswered,
        false
      )
    }

    scrollToBottom()
    isAskedAfterOpenPage.value = true
    isAsking.value = true
    question.value = ''
    isStopScroll = false

    let params: any = {
      question: trimmedMessage,
      category: robotCode.value,
      sessionId: '',
      useSearch: false,
      useRepository: false,
      useR1: false
    }
    if (props.isKnowledge) {
      params.sessionId = ''
    } else {
      params.sessionId = sessionId.value
      //问的时候没有，生成一个新的sessionId
      if (!params.sessionId) {
        params.sessionId = generateCurrentTimestampString()
        sessionId.value = params.sessionId
      }
    }
    if (questionModeValue.value && questionModeValue.value.length > 0) {
      params.useR1 =
        questionModeValue.value.filter((item) => item == QuestionTypeEnum.useR1).length > 0
      params.useSearch =
        questionModeValue.value.filter((item) => item == QuestionTypeEnum.useSearch).length > 0
      params.useRepository =
        questionModeValue.value.filter((item) => item == QuestionTypeEnum.useRepository).length > 0
    }

    storage.set(
      StarloveConstants.keyOflocalStorage.aiQuestionModeList,
      JSON.stringify(questionModeValue.value)
    )

    if (props.isKnowledge && props.repositoryFileId) {
      params['repositoryFileId'] = props.repositoryFileId
      params.useSearch = false
      params.useRepository = true
    }
    if (questionFileId.value) {
      params['fileIds'] = [questionFileId.value]
      if (!trimmedMessage) {
        params.question = '请理解这张图的含义'
      }
    }
    // console.log('params == >', params)
    // 需要写在params后面，不然会清除questionModeValue的值
    clearQuestionFile()

    try {
      if (chat.homeChatQestionData) {
        chat.setHomeChatQestionData(null)
      }
      const responseMessage = {
        content: '',
        id: '',
        isRead: 'N',
        mediaType: 'text',
        category: 'knowledge',
        isDeleted: 'N',
        senderId: 'AI',
        status: 'waiting',
        sessionId: sessionId.value
      }
      messages.value.push(responseMessage)

      const url = `${StarloveUtil.getBaseUrl()}/ask/streamQuestion?platform=${getPlatformNew()}`
      ctrl = new AbortController() // 创建AbortController实例，以便中止请求

      fetchEventSource(url, {
        method: 'POST',
        body: JSON.stringify(params),
        headers: {
          'Content-Type': 'application/json;charset=utf-8',
          // Token: UserService.getToken() || '',
          Authorization: `Bearer ${UserService.getToken()}`,
          'i-date': iDate
        },
        openWhenHidden: true,
        signal: ctrl.signal, // AbortSignal

        onopen(response) {
          // console.log('开始', response)
          return new Promise((resolve) => {
            if (isNewSessionId.value) {
              setTimeout(() => {
                updateQuestionMessageRecordsAndLocation({ title: '新提问', sessionId: sessionId.value })
              }, 500)
            }

            if (trimmedMessage) {
              loadRecommendApply(trimmedMessage)
            }
            resolve()
          })
        },
        onmessage(event) {
          // console.log('正在回答中。。。 ==>', event.data)

          if (isJSON(event.data)) {
            const data = JSON.parse(event.data)
            const lastMessageData = messages.value[messages.value.length - 1]
            if (data.event == 'end') {
              lastMessageData.status = LastMessageStatus.end
              submitSuccess(data.event)
              return
            }
            if (data.event == 'message') {
              const index = messages.value.findIndex((item) => item.id == data?.id)
              if (index == -1) {
                return
              }
              const messageData = messages.value[index]
              if (messageData) {
                messageData.id = data.content?.answer ? data.content?.answer : messageData.id
                messageData.questionId = data.content?.question
                  ? data.content?.question
                  : messageData.questionId
              }
              const questionData = messages.value[index - 1]
              questionData['id'] = data.content?.question
              return
            }

            if (data.event == 'error') {
              // console.log('lastMessageData ==>', lastMessageData)
              lastMessageData['id'] = data?.id
              lastMessageData['status'] = LastMessageStatus.error
              lastMessageData['content'] = data.content
              // console.log("data.event == 'error' ==>", data.content.code)
              submitSuccess(data.event)
              if (data.code === HTTP_STATUS.MOBILE_NOT_BOUND) {
                //存储发送消息的session，
                if (!reqParams.sessionId) {
                  if (params.sessionId) {
                    reqParams.sessionId = params.sessionId
                  }
                }
                webUserBindPhoneVisible.value = true
                user.setShowPhoneBoundModal({
                  status: BindPhoneModal.SHOW_BINDING
                })
                return
              }

              message.error('回答失败:' + data.content)
              return
            }

            if (!data || !data.id) {
              isAsking.value = false
              return
            }

            lastMessageData['id'] = data?.id

            if (data.event == 'action' && data.content == 'understand') {
              lastMessageData.content += '理解问题' + '\n\n'
            }

            if (
              data.event == 'action' &&
              (data.content == 'search_repository' || data.content == 'search_internet')
            ) {
              lastMessageData.content = '正在搜索资料' + '\n\n'
            }

            if (data.event == 'action_list') {
              if (lastMessageData['understands']) {
                lastMessageData['understands'].push(...data.content)
              } else {
                lastMessageData['understands'] = data.content
              }
            }

            if (data.event == 'ref') {
              if (networkLookupOrKnowledgeInfo.value) {
                networkLookupOrKnowledgeInfo.value.content.push(...data.content)
              } else {
                networkLookupOrKnowledgeInfo.value = data
              }
            }

            if (data.event == 'chat') {
              let newContent = data.content
              // 检查是否包含<think>标签
              const startThinkIndex = data.content.indexOf('<think>');
              const endThinkIndex = data.content.indexOf('</think>');

              // 如果包含 <think> 标签，检查它是否是开始标签
              if (startThinkIndex !== -1) {

                lastMessageData.status = LastMessageStatus.thinking
                lastMessageData.refs = networkLookupOrKnowledgeInfo.value?.content || ''
                lastMessageData.think = newContent.substring(0, startThinkIndex);
                lastMessageData.content = lastMessageData.content
                  ?.replace('理解问题', '')
                  .replace('正在搜索资料', '')
                // lastMessageData.content = '思考中'

                insideThinkTag.value = true;
                // 如果是开始标签，先保存当前数据并跳过
                bufferedData += newContent//.substring(0, startThinkIndex); // 将<think>标签前的内容保存
                return;
              }
              // console.log("newContent == >", newContent)
              // console.log("lastMessageData.think == >", lastMessageData.think)
              // 如果已经在<think>标签内，忽略这个部分直到</think>结束
              if (insideThinkTag.value) {
                lastMessageData.think += newContent
                // 处理结束标签
                if (endThinkIndex !== -1) {
                  insideThinkTag.value = false;
                  bufferedData += data.content.substring(0, endThinkIndex + 8); // 包含</think>结束标签

                  // 更新内容并清空缓冲区
                  newContent += bufferedData;
                  bufferedData = '';

                  lastMessageData.think += newContent.substring(0, endThinkIndex + 8)
                  lastMessageData.status = LastMessageStatus.answeing
                  return;
                } else {
                  // 如果没有结束标签，跳过这段数据
                  return;
                }
              }

              if (lastMessageData.status == LastMessageStatus.thinking && newContent == '') {
                return
              }

              lastMessageData.status = LastMessageStatus.answeing
              lastMessageData.refs = networkLookupOrKnowledgeInfo.value?.content || ''
              lastMessageData.content = lastMessageData.content
                ?.replace('理解问题', '')
                .replace('正在搜索资料', '')
              // .replace('思考中', '')

              lastMessageData.content += newContent
            }

            // 优化滚动处理逻辑：如果用户没有手动滚动或者用户滚动到底部，则自动滚动
            if (messageContainer.value && (!isStopScroll || isAtBottom())) {
              scrollToBottom()
            }
          }
        },
        onerror(error) {
          console.log('错误 ==>', error)
          ctrl?.abort()
          isAsking.value = false
          $eventBus.emit(
            StarloveConstants.keyOfEventBus
              .aiQuestionIsANewQuestionAndTheSessionIdThatIsBeingAnswered,
            true
          )
          throw error // 直接抛出错误，避免反复调用
        },
        onclose() {
          // console.log('结束')
          isAsking.value = false
          // console.log(messages.value)
          $eventBus.emit(
            StarloveConstants.keyOfEventBus
              .aiQuestionIsANewQuestionAndTheSessionIdThatIsBeingAnswered,
            true
          )
          scrollToBottom()
          track('chat_end', sessionId.value.toString(), '搜索完成')
        }
      })
    } catch (error) {
      console.log(error)
    }
  },
  5000,
  false
)
const submitSuccess = (status: any) => {
  // isFirstAnswerQuestion = false

  let messageData = messages.value.find(
    (item) => item.id == messages.value[messages.value.length - 1].id
  )
  if (networkLookupOrKnowledgeInfo.value) {
    messageData = messages.value.find((item) => item.id == networkLookupOrKnowledgeInfo.value.id)
  }

  if (messageData) {
    messageData.content = JSON.stringify({
      content: messageData.content?.replace('理解问题', '').replace('正在搜索资料', ''),
      refs: networkLookupOrKnowledgeInfo.value?.content || ''
    })

    if (messageRecommendApplyData.value.length > 0) {
      messageData.recommendApplyList = messageRecommendApplyData.value
    }
  }

  questionFileId.value = ''
  questionFileUrl.value = ''
  clearQuestionFile()
  if (status != 'error' && isNewSessionId.value) {
    updateQuestionMessageRecordsAndLocation({})
  }

  $eventBus.emit(StarloveConstants.keyOfEventBus.updateLeftManualOperationMessageRecords)
  // isFirstAnswerQuestion.value = true

  // if (messages.value[messages.value.length - 1]['understands']) {
  //   storage.set(
  //     StarloveConstants.keyOflocalStorage.aiQuestionMessageUnderstandsData,
  //     JSON.stringify(messages.value[messages.value.length - 1]['understands'])
  //   )
  // }

  currentIsClickedClearButton.value = false

  isAsking.value = false

  setTimeout(() => {
    UserService.loadKnowledgeAssistantMemberInfo()
  }, 400)

  // deleteChatContentDraft(props.keyOflocalStorage)
  // 只有当用户未主动滚动或已滚动到底部时，才执行自动滚动
  if (!isStopScroll || isAtBottom()) {
    scrollToBottom()
  }
}
const handlePressSubmit = async () => {
  // if (!UserService.isLogined()) {
  //   if (isPcWeb()) {
  //     loginVisible.value = true
  //     return
  //   }
  //   RouteService.pageToLogin()
  //   return
  // }
  // console.log(isAsking.value, 'isAsking.value')
  if (isAsking.value == true) {
    return
  }

  if (!UserService.isCanAskQuestion()) {
    //提示去充值
    console.log('提示去充值')
    const rechargeStore = useRechargeStore()
    rechargeStore.$state.rechargeModalVisible = true
    return
  }
  _handlePressSubmit()
}
const route = useRoute()
const router = useRouter()
const onSubmit = (questionFileData: any) => {
  // 如果是新搜索
  if (isNewSessionId.value) {
    let params: any = {
      question: question.value,
      mode: questionModeValue.value,
      isHome: true
    }
    if (questionFileData) {
      params['fileId'] = questionFileData.id
      params['fileUrl'] = questionFileData.fileUrl
    } else {
      if (!params.question) {
        message.error('请输入问题')
        return
      }
    }
    params['sessionId'] = sessionId.value
    chat.setIsNewSessionId(true)
    console.log('setHomeChatQestionData', params)
    chat.setHomeChatQestionData(params)
    if (route.path.startsWith(`/chat/${params['sessionId']}`)) {
      const deepCopy = JSON.parse(JSON.stringify(chat.homeChatQestionData));
      handleSendQuestionEvent(deepCopy)
      return
    }
    router.push({
      path: `/chat/${params['sessionId']}`,
    })
    return;
  }

  if (questionFileData) {
    questionFileId.value = questionFileData.id
    questionFileUrl.value = questionFileData.fileUrl
  }
  // console.log(questionFileData, 'questionFileData')
  setTimeout(() => {
    handlePressSubmit()
  }, 100)
}
const handleSendQuestionEvent = (data: any) => {
  // console.log('askAiQuestionInTheFrontPageOfTheKnolwedgeBase ==>', data.mode)
  question.value = data.question
  questionModeValue.value = data.mode
  questionFileId.value = data.fileId || ''
  questionFileUrl.value = data.fileUrl || ''
  sessionId.value = data.sessionId || ''
  setTimeout(() => {
    handlePressSubmit()
  }, 200)
}
const handleDeleteMessageEvent = (data: any) => {
  messages.value = messages.value.filter((item) => item.id != data.id)
}
onMounted(() => {
  const homeChatQestionData = chat.getHomeChatQestionData()
  if (homeChatQestionData) {
    const deepCopy = JSON.parse(JSON.stringify(homeChatQestionData));
    handleSendQuestionEvent(deepCopy)
  } else {
    questionModeValue.value = getAiQuestionModeList()
  }

  // 初始化滚动容器的高度信息
  nextTick(() => {
    if (messageContainer.value) {
      offsetHeight = messageContainer.value.clientHeight || 0
      // 初始状态下自动滚动是启用的
      isStopScroll = false
    }
  })

  $eventBus.on(
    StarloveConstants.keyOfEventBus.askAiQuestionInTheFrontPageOfTheKnolwedgeBase,
    handleSendQuestionEvent
  )
  $eventBus.on(StarloveConstants.keyOfEventBus.updateMessageDelete, handleDeleteMessageEvent)
  $eventBus.on(StarloveConstants.keyOfEventBus.chatScrollToBottom, () => {
    if (messageContainer.value && (!isStopScroll || isAtBottom())) {
      scrollToBottom()
    }
  })
  if (chat.homeChatQestionData) {
    visibleMsgList.value = true
  } else {
    execQuery()
  }
})
onBeforeUnmount(() => {
  if (!chat.homeChatQestionData) {
    chat.setHomeChatQestionData(null)
  }
  $eventBus.off(StarloveConstants.keyOfEventBus.askAiQuestionInTheFrontPageOfTheKnolwedgeBase, handleSendQuestionEvent);
  $eventBus.off(StarloveConstants.keyOfEventBus.updateMessageDelete, handleDeleteMessageEvent)
  $eventBus.off(StarloveConstants.keyOfEventBus.chatScrollToBottom)
  // chat.setIsNewSessionId(false)
});
</script>
