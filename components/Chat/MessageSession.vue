<template>
    <a-spin :spinning="loading">
        <div class="space-y-1 p-2 min-h-20">
            <div v-for="item in messageRecordList" :key="item.id" :class="[
                'p-2 rounded-lg cursor-pointer transition-colors',
                { 'bg-blue-100': currentSessionId == item?.sessionId },
            ]" @click="handleChatChange(item)">
                <div class="flex items-center space-x-3">

                    <div class="flex-1 min-w-0">
                        <div class="text-sm text-gray-900 truncate">{{ item.title }}
                        </div>
                        <!-- <div class="text-xs text-gray-500 truncate">{{ item.lastContent }}</div> -->
                    </div>
                </div>
            </div>
        </div>
    </a-spin>
</template>

<script lang="ts" setup>
import { useChatStore } from '@/stores/chat';
import { message } from 'ant-design-vue';
import { onBeforeUnmount, onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import { getMessageSessionList } from '~/api/appMessage';
import type { AppMessageRecord } from '~/services/types/appMessage';

const messageRecordList = ref<AppMessageRecord[]>([])
const loading = ref(true)
const { $eventBus } = useNuxtApp();

const props = defineProps({
    currSessionId: {
        type: String,
        default: ''
    }
})
const emit = defineEmits(['update:currSessionId', 'chatChange'])
const currentSessionId = computed({
    get: () => props.currSessionId || '',
    set: (val) => {
        emit('update:currSessionId', val)
    }
})
const handleChatChange = (item: any) => {
    currentSessionId.value = item.sessionId
    emit('chatChange', item)
}
const chat = useChatStore()
const router = useRouter()
const loadMessageRecordData = async () => {
    try {
        loading.value = true
        const params: any = {
            pageNo: 1,
            pageSize: 12,
        }

        const res = await getMessageSessionList(params)

        loading.value = false
        if (!res.ok) {
            message.error(res.message || '获取消息失败')
            return []
        }
        const list = res.data?.records || []

        messageRecordList.value = list
        // if (list.length) {
        //     currentSessionId.value = `${routeId}`
        // }
        chat.setMessageRecordList(list)
        return list
    } catch (error) {
        loading.value = false
        return []
    }
}
const handleEvent = async (value: any) => {
    // console.log('接收到事件：', value);
    const _list = await loadMessageRecordData()
    if (_list.length == 0) {
        return
    }

    currentSessionId.value = `${_list[0].sessionId}`
}
const newSessionEvent = async (params: any) => {
    loading.value = true
    setTimeout(async () => {
        // 创建一个假标题
        if (params && params.title) {
            const obj: AppMessageRecord = {
                id: '',
                createTime: '',
                updateTime: '',
                userId: '',
                sessionId: params.sessionId,
                title: params.title,
                lastContent: '',
                lastUseTime: '',
                isDeleted: 'N'
            }
            messageRecordList.value = [obj, ...messageRecordList.value]
            currentSessionId.value = params.sessionId
            loading.value = false
        } else {
            const _list = await loadMessageRecordData()
            if (_list.length == 0) {
                return
            }
            // router.replace({
            //     path: `/chat/${_list[0].sessionId}`
            // })

            currentSessionId.value = `${_list[0].sessionId}`
        }
    }, 500)
}
onMounted(() => {
    loadMessageRecordData()
    $eventBus.on(StarloveConstants.keyOfEventBus.aiQuestionIsANewQuestionAndTheSessionIdThatIsBeingAnswered, handleEvent);
    $eventBus.on(StarloveConstants.keyOfEventBus.updateAiQuestionLeftMessageRecords, newSessionEvent)
})
onBeforeUnmount(() => {
    $eventBus.off(StarloveConstants.keyOfEventBus.aiQuestionIsANewQuestionAndTheSessionIdThatIsBeingAnswered, handleEvent);
    $eventBus.off(StarloveConstants.keyOfEventBus.updateAiQuestionLeftMessageRecords, newSessionEvent);
});
</script>