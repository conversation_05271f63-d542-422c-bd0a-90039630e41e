<template>
    <div class="knowledge-textarea-action-view">
        <div class="knowledge-action-line">
            <div class="chat-footer-checkbox">
                <div class="flex flex-wrap gap-1 sm:space-x-2">
                    <a-tooltip v-for="item in questionOptionsGroupB" :key="item.value">
                        <template #title>{{ questionModeValue.includes(item.value) ? item.tooltip : item.tooltipDisabled
                        }}</template>
                        <template v-if="questionModeValue.includes(item.value)">
                            <label
                                class="relative group/check flex items-center px-3 sm:px-3 py-1.5 sm:py-1 bg-gradient-to-r from-blue-50/50 to-blue-100/50 rounded-lg cursor-pointer hover:from-blue-100/50 hover:to-blue-200/50 transition-colors justify-center"
                                :class="[item.mClass]">
                                <input type="checkbox" :value="item.value" v-model="questionModeValue"
                                    :disabled="item.disabled"
                                    class="w-0 h-0 hidden mr-1.5 sm:mr-2 appearance-none bg-transparent border-0 checked:bg-transparent checked:border-transparent">
                                <span
                                    class="text-xs text-gray-600 transition-colors sm:text-sm group-hover/check:text-gray-800"
                                    :class="[item.disabled ? 'text-gray-400 group-hover/check:text-gray-400' : '']">{{
                                        item.label }}</span><span class="w-1.5 h-1.5 ml-1.5 rounded-full bg-green-400"
                                    :class="[item.ignoreChecked ? 'bg-green-400' : 'bg-gray-400']"></span>
                            </label>
                        </template>
                        <template v-else>
                            <label
                                class="relative group/check flex items-center px-3 sm:px-3 py-1.5 sm:py-1 bg-gradient-to-r from-blue-50/50 to-blue-100/50 rounded-lg  hover:from-blue-100/50 hover:to-blue-200/50 transition-colors justify-center"
                                :class="[item.disabled ? 'bg-gradient-to-r from-gray-50/50 to-blue-50/50  hover:from-gray-50/50 hover:to-gray-50/50 cursor-not-allowed ' : 'text-gray-600 cursor-pointer', item.mClass]">
                                <input type="checkbox" :value="item.value" v-model="questionModeValue"
                                    :disabled="item.disabled"
                                    class="w-0 h-0 hidden mr-1.5 sm:mr-2 appearance-none bg-transparent border-0 checked:bg-transparent checked:border-transparent">
                                <span class="text-xs transition-colors sm:text-sm"
                                    :class="[item.disabled ? 'text-gray-400 group-hover/check:text-gray-400 ' : 'group-hover/check:text-gray-800 text-gray-600']">{{
                                        item.labelDisabled }}</span><span
                                    class="w-1.5 h-1.5 ml-1.5 rounded-full bg-gray-400"
                                    :class="[item.ignoreChecked ? 'bg-green-400' : 'bg-gray-400']"></span>
                            </label>
                        </template>

                    </a-tooltip>
                    <UPopover v-if="!isKnowledge" v-model:open="openPopover" mode="hover"
                        class="flex items-center h-full ">
                        <div class="hidden md:block">
                            <div
                                class="text-gray-500 relative group/check flex flex-wrap gap-4 items-center px-2 sm:px-3 py-1.5 sm:py-2 bg-gradient-to-r from-blue-50/50 to-blue-100/50 rounded-lg  hover:from-blue-100/50 hover:to-blue-200/50 transition-colors justify-center ">
                                <span v-for="item in questionOptionsGroupA" :key="item.icon" :class="{
                                    'text-blue-600': item.checked
                                }">
                                    <BookIconfont :name="item.icon" :size="20">
                                    </BookIconfont>
                                </span>
                                <!-- <Up :size="20" key="up" /> -->
                                <Down :size="20" key="down" />
                            </div>
                        </div>
                        <template #panel>
                            <div class="w-40 p-2 text-sm text-gray-500 bg-white shadow-sm">
                                <div v-for="item in questionOptionsGroupA" :key="item.icon" :class="{
                                    'text-indigo-700': item.checked,
                                    'px-4 py-3 cursor-pointer flex items-center hover:text-indigo-400 relative': true
                                }" @click="handleQuestionOptionsChange(item)">
                                    <BookIconfont :name="item.icon" :size="17">
                                    </BookIconfont>
                                    <span class="pl-2 text-sm">{{ item.label }}</span>
                                    <span class="absolute -translate-y-1/2 right-2 top-1/2">
                                        <Check v-if="item.checked" :size="16"></Check>
                                    </span>
                                </div>
                            </div>
                        </template>
                    </UPopover>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import BookIconfont from '@/components/Book/BookIconfont.vue';
import { Check, Down } from '@icon-park/vue-next';

import type { QuestionFile } from '@/services/types/repositoryFile';
import { QuestionLabelUnCheckedEnum, QuestionTypeEnum } from '@/utils/constants';
import { message } from 'ant-design-vue';
import { computed, onMounted, onUnmounted, ref, watch } from 'vue';

interface Props {
    isAskQuestionInTheDocument?: boolean
    isKnowledgeHomePage?: boolean
    questionModeValue: string[]
    questionFileInfo?: QuestionFile
    isKnowledge?: boolean
    isHome?: boolean
    repositoryFileIdList: string[]
}
const props = withDefaults(defineProps<Props>(), {
    isKnowledge: false,
    isHome: false,
    repositoryFileIdList: () => []
})

const emit = defineEmits(['update:questionModeValue', 'ok', 'uploadImage'])

const questionModeValue = computed({
    get: () => props.questionModeValue,
    set: (val) => {
        emit('update:questionModeValue', val)
    }
})
const openPopover = ref(false)
const isVisibleRecharge = ref(false)
const questionOptions = ref([
    { label: QuestionLabelEnum.useR1, value: QuestionTypeEnum.useR1, originalValue: QuestionTypeEnum.useR1, disabled: false, tooltip: '采用Deepseek R1满血版推理模型，解决复杂推理问题', labelDisabled: QuestionLabelUnCheckedEnum.normal, tooltipDisabled: `采用小in通用大模型，适用于普通问题回答`, ignoreChecked: true, mClass: 'min-w-28', icon: '', checked: false, sort: 1 },
    { label: QuestionLabelEnum.useSearch, value: QuestionTypeEnum.useSearch, originalValue: QuestionTypeEnum.useSearch, disabled: false, tooltip: '接入互联网，按需搜索网页资料', labelDisabled: QuestionLabelUnCheckedEnum.noUseSearch, tooltipDisabled: `未接入互联网`, ignoreChecked: false, mClass: 'min-w-24', icon: 'lianwang', checked: false, sort: 2 },
    { label: QuestionLabelEnum.useRepository, value: QuestionTypeEnum.useRepository, originalValue: QuestionTypeEnum.useRepository, disabled: false, tooltip: '接入知识库，按需搜索知识库内资料', labelDisabled: QuestionLabelUnCheckedEnum.noUseRepository, tooltipDisabled: `未接入知识库`, ignoreChecked: false, mClass: 'min-w-28', icon: 'zhishiku', checked: false, sort: 4 },
    { label: QuestionLabelEnum.scholar, value: QuestionTypeEnum.useScholar, originalValue: QuestionTypeEnum.useScholar, disabled: false, tooltip: '接入学术搜索，按需搜索学术资料', labelDisabled: QuestionLabelUnCheckedEnum.noScholar, tooltipDisabled: `未接入学术搜索`, ignoreChecked: false, mClass: 'min-w-28', icon: 'xueshubiaoti', checked: false, sort: 3 }
])
const questionOptionsGroupA = computed(() => {
    const list = questionOptions.value.filter(item => item.label !== QuestionLabelEnum.useR1)
    list.forEach(item => {
        item.checked = questionModeValue.value.includes(item.value)
        // if (item.label === QuestionLabelEnum.useSearch) {
        //     item.checked = questionModeValue.value[1] == item.value
        // } else if (item.label === QuestionLabelEnum.useRepository) {
        //     item.checked = questionModeValue.value[2] == item.value
        // } else if (item.label === QuestionLabelEnum.scholar) {
        //     item.checked = questionModeValue.value[3] == item.value
        // }
    })
    return list.sort((a, b) => a.sort - b.sort)
})
const questionOptionsGroupB = computed(() => {
    return questionOptions.value.filter(item => item.label === QuestionLabelEnum.useR1)
})
const handleQuestionOptionsChange = (itemA: any) => {
    questionOptions.value.forEach(item => {
        if (item.label == itemA.label) {
            item.checked = !item.checked
            if (item.checked) {
                questionModeValue.value.push(item.originalValue)
            } else {
                questionModeValue.value = questionModeValue.value.filter((d: any) => d != item.originalValue)
            }
        }
    })
    setTimeout(() => {
        openPopover.value = false
    }, 100)
}
const handleCheckboxChange = (checkedValue: any) => {
    // console.log('handleCheckboxChange checkedValue ==>', checkedValue)
    // emit('updateQuestionModeValue', checkedValue)
    storage.set(
        StarloveConstants.keyOflocalStorage.aiQuestionModeList,
        JSON.stringify(checkedValue)
    )
}

const handleQuestionUploadImage = () => {
    if (props.questionFileInfo) {
        message.warning('只能上传一张图片')
        return
    }
    emit('uploadImage')
}

watch(
    () => {
        return props.questionFileInfo
    },
    (_newValue, _oldValue) => {
        // console.log('props.questionFileInfo ===>', _newValue)
        if (!_newValue) {
            questionOptions.value[0].disabled = false
            questionOptions.value[1].disabled = false
            questionOptions.value[2].disabled = false
            return
        }
        questionOptions.value[0].disabled = true
        questionOptions.value[0].value = QuestionTypeEnum.useR1
        questionOptions.value[1].disabled = true
        questionOptions.value[2].disabled = true
        // questionModeValue.value = []
        // emit('updateQuestionModeValue', [])
    }
)

// 选文件是
watch(
    () => {
        return props.repositoryFileIdList
    },
    (_newValue, _oldValue) => {
        // console.log('props.questionFileInfo ===>', _newValue)
        if (!_newValue || !_newValue.length) {
            questionOptions.value[0].disabled = false
            questionOptions.value[1].disabled = false
            questionOptions.value[2].disabled = false
            return
        }
        questionOptions.value[0].disabled = true
        questionOptions.value[0].value = QuestionTypeEnum.useR1
        questionOptions.value[1].disabled = true
        questionOptions.value[2].disabled = true
        // questionModeValue.value = []
        // emit('updateQuestionModeValue', [])
    }
)


onMounted(() => {
    questionModeValue.value = props.questionModeValue

})
onUnmounted(() => { })

</script>

<style lang="scss" scoped>
.knowledge-textarea-action-view {

    .knowledge-action-line {
        display: flex;
        justify-content: space-between;
        align-items: center;


        .chat-footer-checkbox {
            display: flex;
            justify-content: flex-start;
            padding: 10px;
        }

        .question-image {
            display: flex;
            align-items: center;
            padding: 0 6px;
            margin-left: 20px;
            cursor: pointer;
        }

        .residue {
            padding: 4px 15px;
            background: #fffaec;
            border-radius: 10px;
            border: 1px solid #ffe6ba;
            color: #f09209;
            font-size: 14px;
            white-space: nowrap;
        }

        @media screen and (max-width: 768px) {
            .residue {
                display: none;
            }
        }

        .vip-upgrade {
            margin-left: 25px;
            font-weight: 400;
            font-size: 15px;
            color: #388bef;
            line-height: 23px;
            cursor: pointer;
        }
    }

    .flex-end {
        display: flex;
        justify-content: flex-end;
        align-items: center;
    }

    .ant-checkbox {

        // .ant-checkbox-disabled .ant-checkbox-inner
        ::v-deep .custom-checkbox-style .ant-checkbox-disabled .ant-checkbox-inner {
            background-color: #1677ff !important;
            border-color: #1677ff !important;
        }

        ::v-deep .ant-checkbox-disabled .ant-checkbox-inner:after {
            border-color: #ffffff !important;
        }

        ::v-deep .ant-checkbox-disabled+span {
            color: rgba(0, 0, 0, 0.88);
        }
    }

}
</style>