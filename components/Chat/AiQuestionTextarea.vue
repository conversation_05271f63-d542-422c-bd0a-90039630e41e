<template>
    <div class="border-gray-200" :class="[isHome ? 'p-0' : ' p-4']">
        <div :class="[isHome ? '' : 'max-w-4xl mx-auto']">
            <div v-if="!isHome && isLogined" class="flex justify-end gap-3 pb-3 ">
                <!-- <button @click="handleOpenNewQueation"
                    class="flex items-center justify-center px-6 py-2 text-indigo-700 border border-indigo-700 rounded-lg cursor-pointer xt-indigo-700 bg-blue-50 hover:bg-blue-100 hover:border-indigo-500 ">
                    <plus :size="14" /> <span class="pl-2 text-sm ">新提问</span>
                </button> -->
                <button @click="handleOpenNewQueation"
                    class="flex items-center justify-center px-6 py-2 text-gray-800 bg-white border border-blue-100 rounded-lg cursor-pointer hover:border-blue-200 hover:text-indigo-500 ">
                    <span class="pl-2 text-sm">新提问</span>
                </button>
                <button @click="handleOpenQueationRecord"
                    class="flex items-center justify-center px-6 py-2 text-gray-800 bg-white border border-blue-100 rounded-lg cursor-pointer hover:border-blue-200 hover:text-indigo-500 ">
                    <history :size="14" /> <span class="pl-2 text-sm ">提问记录</span>
                </button>
            </div>
            <div class="bg-white rounded-xl p-1 sm:p-2 pt-0 border transition-all group  mb-1 focus-within:shadow-[0_0_15px_rgba(59,130,246,0.3)] focus-within:border-blue-400 relative before:absolute before:inset-0 before:rounded-xl before:transition-all before:opacity-0 focus-within:before:opacity-100 before:bg-gradient-to-r before:from-blue-500/5 before:to-blue-400/5 before:-z-10"
                :class="[isHome ? 'border-blue-300/50' : 'border-gray-200']">
                <div class="relative">
                    <!-- <div class="flex justify-start w-full" v-if="isHome">
                        <div class="flex justify-center items-center px-3 lg:px-8 xl:px-8  py-2 lg:py-2 xl:py-2 rounded-[10px]  cursor-pointer text-xs lg:text-base xl:text-base"
                            :class="{ 'bg-[#EEF3FF] text-[#2551B5]': currentMode === HomeQuestionInputModal.AI_CREATE }"
                            @click="handleChangeMode(HomeQuestionInputModal.AI_CREATE)">
                            AI写作
                        </div>
                        <div class="flex justify-center items-center px-3 lg:px-8 xl:px-8  py-2 lg:py-2 xl:py-2 rounded-[10px]  cursor-pointer text-xs lg:text-base xl:text-base"
                            :class="{ 'bg-[#EEF3FF] text-[#2551B5]': currentMode === HomeQuestionInputModal.AI_QUESTION }"
                            @click="handleChangeMode(HomeQuestionInputModal.AI_QUESTION)">
                            AI提问
                        </div>
                        <div class="flex justify-center items-center px-3 lg:px-8 xl:px-8  py-2 lg:py-2 xl:py-2 rounded-[10px]  cursor-pointer text-xs lg:text-base xl:text-base"
                            :class="{ 'bg-[#EEF3FF] text-[#2551B5]': currentMode === HomeQuestionInputModal.ACADEMIC_SEARCH }"
                            @click="handleChangeMode(HomeQuestionInputModal.ACADEMIC_SEARCH)">
                            学术搜索
                        </div>
                    </div> -->

                    <textarea v-model="questionValue" :auto-size="{ minRows: 3, maxRows: 2 }"
                        :placeholder="textAreaPlaceholder"
                        class="w-full p-2 text-sm bg-transparent rounded-lg outline-none resize-none sm:p-3 focus:ring-0 focus:border-transparent sm:text-base"
                        :class="[isHome ? 'min-h-[168px]' : 'min-h-[80px]']" :maxlength="maxLength"
                        @keydown="handleKeyDown" @paste="handlePaste" />
                    <div class="bg-blue-50 w-[176px] h-[99px] flex justify-center items-center relative"
                        v-for="item in files">
                        <img class="max-w-[176px] max-h-[99px] object-contain transition-opacity duration-300"
                            :src="item?.url || ''" />
                        <div class="absolute top-[7px] right-[7px] px-[4px] cursor-pointer" @click="handleDelImg"><span
                                data-v-d5d524ce="" role="img" aria-label="close-circle"
                                class="anticon anticon-close-circle"
                                style="font-size: 16px; color: rgb(56, 139, 239);"><svg focusable="false" class=""
                                    data-icon="close-circle" width="1em" height="1em" fill="currentColor"
                                    aria-hidden="true" fill-rule="evenodd" viewBox="64 64 896 896">
                                    <path
                                        d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.04.04.06.05.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z">
                                    </path>
                                </svg></span>
                        </div>
                        <UProgress v-if="item.percent < 100" :value="item.percent" color="blue"
                            class="absolute top-1/2 -translate-y-1/2 px-[10px]" />
                    </div>
                    <!--文件列表-->

                    <AiQuestionFileList :select-file-list="selectFileList" />

                </div>

                <div class="flex items-start justify-between space-y-3 sm:flex-row sm:items-center sm:space-y-0 ">
                    <div class="flex flex-wrap gap-2 mt-3 sm:space-x-3 sm:mt-0">
                        <AiSelect :questionFileInfo="questionFileInfo" v-model:question-mode-value="questionModeValue"
                            :isKnowledge="isKnowledge" :is-home="isHome"
                            :repository-file-id-list="repositoryFileIdList" />
                    </div>

                    <!-- 右侧按钮组：新增上传图片按钮 -->
                    <div class="flex items-center justify-end w-full mr-2 space-x-2 sm:w-auto">
                        <ClientOnly>
                            <div class="mt-1 text-sm text-red-500 text-nowrap" v-if="remainingCharacters < 1">
                                已超出字数
                            </div>
                            <div class="px-[15px] py-[4px] bg-[#fffaec] rounded-[10px] border border-[#ffe6ba] text-[#f09209] text-[14px] whitespace-nowrap"
                                v-if="isShowResidue">
                                剩余提问:
                                {{ isSSSVip ? '无限' : canAskQuestionCount }}次
                                <span
                                    class="ml-[25px] font-normal text-[15px] text-[#388bef] leading-[23px] cursor-pointer"
                                    v-if="!isSSSVip && canAskQuestionCount <= 0" @click="handleUpgrade">
                                    升级
                                </span>
                            </div>
                        </ClientOnly>
                        <!-- 上传图片 -->
                        <XUploadImg v-if="!isKnowledge" v-model:files="files" :paste-file-info="pasteFileInfo"
                            :repository-file-id-list="repositoryFileIdList" />
                        <!-- 上传知识库文件 -->
                        <XUploadKnowledge v-if="!isKnowledge" v-model:files="files" @select="onKnowledgeSelect">
                            <template #content>
                                <button
                                    class="p-1.5 sm:p-2 text-gray-500 hover:text-gray-700 transition-colors relative"
                                    :disabled="!!files.length">
                                    <span
                                        :class="[!!files.length ? 'text-gray-400 group-hover/check:text-gray-400 cursor-not-allowed' : 'text-gray-500 cursor-pointer']">
                                        <BookIconfont name="fileupload" :size="24">
                                        </BookIconfont>
                                    </span>
                                </button>

                            </template>
                        </XUploadKnowledge>
                        <!-- 发送按钮 -->

                        <button class="relative group" @click="handlePressSubmit">
                            <div
                                class="absolute -inset-0.5 bg-gradient-to-r from-blue-500/30 to-indigo-600/30 rounded-lg blur-sm group-hover:blur-md transition-all duration-300">
                            </div>
                            <div class="relative bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 p-2 sm:p-2.5 rounded-lg leading-none flex items-center justify-center transition-all duration-300"
                                :class="isSubmitEnabled ? 'text-white' : 'disabled-chat-input text-gray-500 cursor-not-allowed'">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"
                                    class="w-3.5 h-3.5 sm:w-4 sm:h-4 group-hover:scale-110 transition-transform duration-300">
                                    <path
                                        d="M3.478 2.404a.75.75 0 0 0-.926.941l2.432 7.905H13.5a.75.75 0 0 1 0 1.5H4.984l-2.432 7.905a.75.75 0 0 0 .926.94 60.519 60.519 0 0 0 18.445-8.986.75.75 0 0 0 0-1.218A60.517 60.517 0 0 0 3.478 2.404Z" />
                                </svg>
                            </div>
                        </button>
                    </div>
                </div>
            </div>
            <!-- 部功能提示 -->
            <div v-if="!isHome" class="flex items-center justify-center mt-2 text-xs text-center text-gray-500">
                回复的内容由AI生成，非人工编辑；其内容准确性和完整性无法保证，不代表我们的态度和观点。
            </div>

        </div>
    </div>
</template>
<script lang="ts" setup>
import { useChatStore } from '@/stores/chat';
import { getAiQuestionModeList } from '@/utils/utils';
import { History } from '@icon-park/vue-next';
import { useRouter } from 'vue-router';
import { getFilesByIds } from '~/api/repositoryFile';
import XUploadImg from '~/components/XUploadImg.vue';
import type { UploadDocument } from '~/services/types/knowledgeUpload';
import type { QuestionFile, RepositoryFile } from '~/services/types/repositoryFile';
import { UserService } from '~/services/user';
import { useRechargeStore } from '~/stores/recharge';
import { useUserStore } from '~/stores/user';

import { HomeQuestionInputModal } from '~/utils/constants';
import AiQuestionFileList from './AiQuestionFileList.vue';
import AiSelect from './AiSelect.vue';
import XUploadKnowledge from './XUploadKnowledge.vue';

const props = defineProps({
    question: {
        type: String,
        default: '',
    },
    isKnowledge: {
        type: Boolean,
        default: false,
    },
    isAsking: {
        type: Boolean,
        default: false,
    },
    isWaitingAnswer: {
        type: Boolean,
        default: false,
    },
    isHome: {
        type: Boolean,
        default: false,
    },
    questionMode: {
        type: Array as PropType<string[]>,
        default: () => [],
    },
    repositoryFileIdList: {
        type: Array as PropType<string[]>,
        default: () => [],
    },
    // 知识库文件夹id列表
    repositoryFolderIdList: {
        type: Array as PropType<string[]>,
        default: () => [],
    },
    // 会话来源
    sessionFrom: {
        type: String,
        default: SessionFrom.Normal,
    },
})


const currentMode = ref(HomeQuestionInputModal.AI_CREATE)

const emit = defineEmits(['update:question', 'update:questionMode', 'update:repositoryFileIdList', 'submit', 'openNewQuestion'])
const questionValue = computed({
    get: () => props.question,
    set: (val) => {
        // console.log(val, 'val')
        emit('update:question', val)
    }
})
const questionModeValue = computed({
    get: () => props.questionMode,
    set: (val) => {
        emit('update:questionMode', val)
    }
})
const userStore = useUserStore()
const { isLogined } = storeToRefs(userStore)

const textAreaPlaceholder = computed(() => {
    if (props.isKnowledge) {
        if (props.repositoryFolderIdList.length > 0) {
            const folderId = props.repositoryFolderIdList[0]
            if (folderId === '0') {
                return '对我的知识库进行提问'
            } else {
                return '对本文件夹进行提问'
            }

        }
        return '对本文进行提问'
    }
    return '想了解什么知识，快来问问我！Shfit+Enter/Ctrl+Enter换行'
})
const questionFileInfo = ref<QuestionFile>()
const selectFileList = ref<UploadDocument[]>([])
const handleKeyDown = (event: KeyboardEvent) => {
    if ((event.ctrlKey || event.shiftKey) && event.key === 'Enter') {
        event.preventDefault()

        const textarea = event.target as HTMLTextAreaElement
        const start = textarea.selectionStart
        const end = textarea.selectionEnd

        if (!questionValue.value) {
            return
        }
        questionValue.value = `${questionValue.value.substring(
            0,
            start
        )}\n${questionValue.value.substring(end)}`

        textarea.setSelectionRange(start + 1, start + 1)
        return
    }
    if (event.key == 'Enter') {
        event.preventDefault()
        handlePressSubmit()
    }
}
const pasteFileInfo = ref()
const handlePaste = (event: ClipboardEvent) => {
    if (props.isKnowledge) {
        return
    }
    const clipboardData = event.clipboardData
    if (clipboardData) {
        // 获取粘贴的内容
        const items = clipboardData.items
        // 遍历粘贴板中的内容
        for (const item of items) {
            // 检查是否是图片类型
            if (item.type.startsWith('image/')) {
                // 获取图片文件
                const file = item.getAsFile()
                if (file) {
                    // 这里可以处理图片文件，比如上传或显示
                    // message.success('图片粘贴成功！')
                    console.log('粘贴的图片文件:', file)
                    pasteFileInfo.value = {
                        uid: 1,
                        name: file.name,
                        status: 'ready',
                        originFileObj: file
                    }
                }
                // 阻止默认粘贴行为（防止图片以 URL 粘贴）
                event.preventDefault()
                break
            }
        }
    }
}
const files = ref<any>([])
watch(
    () => {
        return files.value
    },
    (_newValue) => {
        if (_newValue.length) {
            const file = _newValue[0];
            if (file && file.response) {
                const { fileId, fileUrl } = file.response;
                questionFileInfo.value = {
                    id: fileId,
                    fileUrl: fileUrl,
                    fileName: '',
                    status: 'done',
                    userId: '',
                };
                questionModeValue.value = [];
            }
        } else {
            // questionFileInfo.value = undefined
            // questionModeValue.value = [QuestionTypeEnum.useR1, QuestionTypeEnum.useSearch]
        }
    }
)
// 按钮是否可用
const isSubmitEnabled = computed(() => {
    if (props.isAsking) {
        return false
    }
    if (!questionFileInfo.value && !questionFileInfo.value?.id) {
        if (!props.question) {
            return false
        }
    }
    if (remainingCharacters.value < 1) {
        return false
    }
    return true
})
const handlePressSubmit = () => {
    if (!UserService.isLogined()) {
        const userStore = useUserStore()
        userStore.openLoginModal()
        return
    }
    // if (files.value.length) {
    //     const file = files.value[0];
    //     if (file && file.response) {
    //         const { fileId, fileUrl } = file.response;
    //         questionFileInfo.value = {
    //             id: fileId,
    //             fileUrl: fileUrl,
    //             fileName: '',
    //             status: 'done',
    //             userId: '',
    //         };
    //         questionModeValue.value = []
    //     }
    // }
    // console.log('questionFileInfo', questionFileInfo.value, files.value)
    if (!isSubmitEnabled.value) {
        return
    }
    emit('submit', questionFileInfo.value)
}

const handleDelImg = () => {
    files.value = []
    questionFileInfo.value = undefined
    questionModeValue.value = [QuestionTypeEnum.useR1, QuestionTypeEnum.useSearch]
}
const handleDeleteImage = () => {
    files.value = []
    questionFileInfo.value = undefined
    questionModeValue.value = getAiQuestionModeList()
}
const clearQuestionFileInfo = () => {
    handleDeleteImage()
}
const isShowResidue = computed(() => {
    if (!UserService.isLogined()) {
        return false
    }
    if (UserService.canAskQuestionCount() > 10) {
        return false
    }

    return true

})
const canAskQuestionCount = computed(() => {
    return UserService.canAskQuestionCount()
})
const isSSSVip = computed(() => {
    return UserService.isSSSVip()
})

const handleChangeMode = (mode: HomeQuestionInputModal) => {
    currentMode.value = mode
}

const handleUpgrade = () => {
    const rechargeStore = useRechargeStore()
    rechargeStore.$state.rechargeModalVisible = true
}
const maxLength = 2000;

const remainingCharacters = computed(() => {
    return maxLength - questionValue.value.length;
});
let timerId: NodeJS.Timeout | undefined = undefined

const refreshSelectFileList = async () => {
    const list = unref(selectFileList)
    if (list.length) {
        const unlearnedFile = list.find(item => item.status !== RepositoryFileStatus.DONE)
        if (unlearnedFile) {
            const ids = list.map(item => `${item.id}`).filter(item => item)
            const res = await getFilesByIds({
                fileIds: ids,
                spaceId: UserService.getSelfUserId()
            })
            if (!res.success) {
                return
            }
            const teamFiles = res.data
            teamFiles.forEach((d: RepositoryFile) => {
                const dd = selectFileList.value.find(item => `${item.id}` == d.id)
                if (dd) {
                    dd.status = d.status
                    dd.wordCount = d.wordCount
                    dd.status = d.status
                    dd.processData = d.processData
                }
            });
            const processingList = teamFiles.filter((d: RepositoryFile) => d.status !== RepositoryFileStatus.ERROR && d.status !== RepositoryFileStatus.DONE) || []
            if (timerId) {
                clearTimeout(timerId)
            }
            // 检查是否有处理中的文件
            if (processingList.length > 0) {
                timerId = setTimeout(() => {
                    refreshSelectFileList()
                }, 3000)
            }

        }
    }
}
const onKnowledgeSelect = (list: UploadDocument[]) => {
    let repositoryFileIdList: string[] = []
    list.forEach(item => {
        if (item.id) {
            repositoryFileIdList.push(`${item.id}`)
        }
    })
    console.log('onKnowledgeSelect', repositoryFileIdList, list)
    selectFileList.value = list
    emit('update:repositoryFileIdList', repositoryFileIdList)
    //  已添加文件后，联网搜索、学术搜索、知识库搜索关闭，不可点击打开，图片上传关闭
    questionModeValue.value = [];
    // 如果 selectFileList 文件有为完成学习的，需要调用刷新接口，更新状态
    refreshSelectFileList()
}
const router = useRouter()

const handleOpenNewQueation = () => {
    const chat = useChatStore()
    chat.setIsNewSessionId(true)
    // 如果是知识库提问，就不要跳转
    if ([SessionFrom.KnowledgeFolder, SessionFrom.KnowledgeSingleFile].includes(props.sessionFrom as any)) {
        emit('openNewQuestion')
        return
    }
    setTimeout(() => {
        router.replace(`/chat`)
    }, 300)
}
const handleOpenQueationRecord = () => {
    router.replace(`/profile/questions`)
}
defineExpose({
    clearQuestionFileInfo
})
</script>
<style lang="scss" scoped>
.disabled-chat-input {
    background: #f1f1f1;
}
</style>