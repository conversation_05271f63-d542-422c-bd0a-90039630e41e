<template>
    <div v-html="messageContent"></div>
</template>
<script setup lang="ts">
import markdownItKatex from '@vscode/markdown-it-katex'
import hljs from 'highlight.js/lib/core'
import 'highlight.js/styles/ir-black.css'
import 'katex/dist/katex.min.css'
import markdownit from 'markdown-it'
import markdownItContainer from 'markdown-it-container'
import markdownItHighlightjs from 'markdown-it-highlightjs'
const props = defineProps({
    content: {
        type: String,
        default: '',
    },
    isMySelf: {
        type: Boolean,
        default: false,
    }
})
let lastHeading = 'table-data'
const mdSelf = markdownit({
    html: true, // 启用 HTML 标签
    linkify: true, // 自动将 URL 转换为链接
    breaks: true
})
const md = markdownit({
    html: true, // 启用 HTML 标签
    linkify: true, // 自动将 URL 转换为链接
    breaks: true,
    xhtmlOut: true,
    langPrefix: 'language-',
    typographer: true,
    highlight: function (str: string, lang: string): string {
        if (lang && hljs.getLanguage(lang)) {
            try {
                return `<pre class="hljs" data-lang="${lang}"><code>${hljs.highlight(lang, str, true).value
                    }</code></pre>`
            } catch (__) { }
        }

        return `<pre class="hljs" data-lang="${lang}"><code>${md.utils.escapeHtml(str)}</code></pre>`
    }
})
    .use(markdownItKatex)
    .use(markdownItContainer, 'spoiler', {
        validate: function (params: string) {
            return params.trim().match(/^spoiler\s+(.*)$/)
        },
        render: function (tokens: any, idx: number) {
            var m = tokens[idx].info.trim().match(/^spoiler\s+(.*)$/)
            if (tokens[idx].nesting === 1) {
                return '<details><summary>' + md.utils.escapeHtml(m[1]) + '</summary>\n'
            } else {
                return '</details>\n'
            }
        }
    })
    .use(markdownItHighlightjs)
    .use((md) => {
        // 添加一个计数器来跟踪表格数量
        let tableCounter = 0;

        // 拦截标题的解析
        const defaultHeadingRender =
            md.renderer.rules.heading_open ||
            function (tokens, idx, options, env, self) {
                return self.renderToken(tokens, idx, options)
            }

        md.renderer.rules.heading_open = function (tokens, idx, options, env, self) {
            // 获取标题内容
            const nextToken = tokens[idx + 1]
            if (nextToken && nextToken.type === 'inline') {
                lastHeading = nextToken.content // 保存标题内容
            }
            return defaultHeadingRender(tokens, idx, options, env, self)
        }

        // 重置表格计数器的函数
        const resetTableCounter = () => {
            tableCounter = 0;
        };

        // 在每次渲染开始时重置计数器
        const defaultCoreRender = md.core.process;
        md.core.process = function (state) {
            resetTableCounter();
            return defaultCoreRender.call(this, state);
        };

        const defaultRender =
            md.renderer.rules.table_open ||
            function (tokens, idx, options, env, self) {
                return self.renderToken(tokens, idx, options)
            }

        md.renderer.rules.table_open = function (tokens, idx, options, env, self) {
            // 增加计数器并生成唯一的表格ID
            tableCounter++;
            const uniqueTableId = `table-${Date.now()}-${tableCounter}`;

            // 在渲染表格时，为表格前添加一个小图标
            return `
        <div class="table-wrapper1">
          <span class="table-icon" onclick="exportToExcelMD('${uniqueTableId}')">
          <img class="table-icon-img" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAASVJREFUWEftllESgyAMRMGLVQ8j4y1ab+HgYerJpI0jnUCDBqR12oEff5B9rskSKU5e8mR98dsASqkbOKi1Xp4pK9mBVfwKosaYaRzH5msAWNyKpkJEO0CJH4GIAvDE+6f7r18gpaxTfgcbwBeHwlNKGRCtqqqZ5xkAomuCBUCJgzAGGIZhSilMFkDbtvfV4h63nA+wQkFLLk5orXfP391gC6zruhq+ErcaBbC1n2pTNgD18hYANxMKQHGgOOA4AL0Okcq932PbkMoSB8BGKWS7HzpHc8Ce7V/bDkAockOhAocaYy6cYeQjANy0w3dEVgf+FyB1vttyhFUDxMgV43JwLxSqHdmEEM5M8ZaEqBOyiONDKGfJKLbtlZuAatdDd0EOwALwAPKYDzBALxiRAAAAAElFTkSuQmCC"> 导出为表格</span>
          <div class="table-wrapper" data-table-id="${uniqueTableId}" data-table-name="${lastHeading}">
          ${defaultRender(tokens, idx, options, env, self)}
      `
        }

        const defaultClose =
            md.renderer.rules.table_close ||
            function (tokens, idx, options, env, self) {
                return self.renderToken(tokens, idx, options)
            }

        md.renderer.rules.table_close = function (tokens, idx, options, env, self) {
            return `${defaultClose(tokens, idx, options, env, self)}</div>`
        }
    })
// 自定义渲染器显示语言名称
md.renderer.rules.fence = (tokens, idx, options, env, self) => {
    const token = tokens[idx]
    const language = token.info.trim() || 'plaintext' // 获取语言名称
    const code = token.content
    let highlighted = ''
    try {
        highlighted = language
            ? hljs.highlight(code, { language: language }).value
            : md.utils.escapeHtml(code)
    } catch (error) {
        highlighted = md.utils.escapeHtml(code)
    }
    const codeId = `code-${idx}`
    return `
        <div class="code-block">
          <div class="code-language">
            <div>${language}</div>
            <div class="copy-code" onclick="copyMD('${codeId}')">复制</div>
          </div>
          <pre><code data-code-id="${codeId}" class="hljs ${md.utils.escapeHtml(
        language
    )}">${highlighted}</code></pre>
        </div>
      `
}
const defaultRender =
    md.renderer.rules.link_open ||
    function (tokens, idx, options, env, self) {
        return self.renderToken(tokens, idx, options)
    }

md.renderer.rules.link_open = function (tokens: any, idx: number, options: any, env: any, self: any) {
    // 如果链接没有 target 属性，则添加 target="_blank"
    const aIndex = tokens[idx].attrIndex('target')
    if (aIndex < 0) {
        tokens[idx].attrPush(['target', '_blank']) // 添加新的属性
    } else {
        tokens[idx].attrs[aIndex][1] = '_blank' // 替换现有属性的值
    }
    // 传递 token 给默认渲染器
    return defaultRender(tokens, idx, options, env, self)
}

const messageContent = computed(() => {
    if (props.isMySelf) {
        return mdSelf.render(props.content)
    }
    const newData = props.content
        .replace(/<think>[\s\S]*?<\/think>/g, '')
        .replace(/\\\{/g, '${')
        .replace(/\\\}/g, '}$')
        .replace(/\\\[/g, '$[')
        .replace(/\\\]/g, ']$')
        .replace(/\\\(/g, '$(')
        .replace(/\\\)/g, ')$')
        .replace(/#{2,}/g, (match: any) => match + ' ');
    return md.render(newData)
})

</script>