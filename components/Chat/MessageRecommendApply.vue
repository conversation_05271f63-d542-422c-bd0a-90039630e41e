<template>
    <div class="p-5 bg-blue-50 rounded-[15px] rounded-tl-[15px] max-w-[1000px] my-5">
        <div class="flex justify-between items-center">
            <div class="font-bold text-[15px] text-gray-800" :class="{ 'mr-[50px]': isKnowledge }">
                使用小in写作功能，一键生成各类长文。几分钟数万字，专业、规范、准确
            </div>
            <div class="min-w-[130px] text-[14px] text-blue-500">
                <NuxtLink :to="`/create`" :target="app?.isMouse ? '_self' : '_blank'" class="hover:text-blue-600">
                    查看更多写作应用
                </NuxtLink>
            </div>
        </div>
        <div class="text-[14px] text-gray-500 mt-2 mb-3">已根据你的请求，为你推荐以下写作应用</div>

        <!-- v-if="!isKnowledge" -->
        <div class="flex flex-wrap gap-3">
            <AppCard v-for="item in recommendApplyList" :key="item.code" class="flex-1" :title="item.name"
                :desc="item.description" :to="`/create/${item.code}`" :icon="item.avatar" :code="item.code"
                layout="vertical" />
        </div>

        <!-- <div v-else>
            <div v-for="item in recommendApplyList" :key="item.name"
                class="bg-white rounded-lg p-6 mb-3 flex justify-between items-center">
                <div class="flex items-start">
                    <img :src="item.avatar" class="w-[50px] h-[50px] rounded-lg mr-3" />
                    <div>
                        <div class="font-bold text-[15px] text-gray-800">{{ item.name }}</div>
                        <div class="text-[13px] text-gray-400 mt-2 truncate">{{ item.description }}</div>
                    </div>
                </div>
                <div>
                    <NuxtLink :to="`/create/${item.code}`" class="block" :target="app?.isMouse ? '_self' : '_blank'">
                        <button
                            class="px-6 py-2 rounded-lg text-white text-[14px] bg-gradient-to-r from-[#42e5b5] to-[#249cff] hover:opacity-90 transition-opacity">
                            开始写作
                        </button>
                    </NuxtLink>
                </div>
            </div>
        </div> -->
    </div>
</template>

<script setup lang="ts">
import AppCard from '@/components/AppCard.vue';
import type { AppCategory } from '@/services/types/appMessage';
const app = useApp()
interface Props {
    isKnowledge?: boolean
    recommendApplyList: AppCategory[]
}

defineProps<Props>()
</script>