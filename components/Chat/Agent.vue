<template>
    <div class="relative flex flex-col flex-1 h-full">
        <!-- 添加顶部按钮组 -->
        <div v-if="isVisibleTop" class="p-4 border-b border-gray-200 bg-white/80">
            <div class="flex items-center justify-between mx-auto">
                <!-- 左侧会话主题 -->
                <div class="flex items-center space-x-3">
                    <div
                        class="flex items-center justify-center flex-shrink-0 w-8 h-8 rounded-lg bg-gradient-to-r from-blue-500 to-purple-500">
                        <MessageOne theme="outline" size="24" fill="#FFFFFF" />
                    </div>
                    <div>
                        <h1 class="w-64 text-lg font-medium text-blue-700 truncate md:w-60">
                            {{ currentChatTitle.title }}
                        </h1>
                        <p class="text-xs text-gray-400">
                            {{ dateFormatS(new Date(currentChatTitle?.createTime || ''), 'MM月dd日 hh:mm') }}
                        </p>
                    </div>
                </div>

                <!-- 右侧按钮组 -->
                <!-- <UserInfoArea /> -->
            </div>
        </div>

        <!-- 聊天历史区域 -->
        <div class="flex-1 p-4 overflow-auto" :class="[
            visibleMsgList ? 'opacity-1' : 'opacity-0'
        ]" id="messageContainer" ref="messageContainer" @scroll="onContainerScroll">
            <div class="max-w-4xl mx-auto space-y-6">
                <div class="m-0 mx-4 p-2.5 px-5 bg-white rounded-tl-lg rounded-tr-lg rounded-bl-none rounded-br-lg font-normal text-base text-gray-800 leading-7"
                    v-if="messages.length == 0 && !isKnowledge && !isLoading">
                    Hi，我是你的小in知识助手，想了解什么知识，都可以来问我~联网提问+个人知识库，准确找到你想要的答案！
                </div>
                <div class=" text-center p-[10px] text-gray-900 cursor-pointer " @click="handlePressLoadMore"
                    v-if="(data?.pages || 0) > (data?.current || 1)">
                    <a-spin :spinning="isLoading">点击加载更多历史记录</a-spin>
                </div>
                <a-spin :spinning="!((data?.pages || 0) > (data?.current || 1)) && isLoading">
                    <ExampleQuestion :editorData="editorData" :robot-code="robotCode" :exampleList="exampleList"
                        @sendExampleMessage="handleMessageExampleInput"></ExampleQuestion>
                    <div v-for="(message, index) in messages" :key="message.id">
                        <AgentMessageView v-if="currentChatTitle.version == 2" :message="message" :isAsking="isAsking"
                            :isKnowledge="isKnowledge" :messageRecommendApplyData="messageRecommendApplyData"
                            :is-last-message-record="index == messages.length - 1"
                            :is-first-answer="messages.length > 0 && message.senderId == 'AI' && index == 1"
                            :current-is-clicked-clear-button="currentIsClickedClearButton" :is-share="false"
                            :current-reference-source-message-id="currentReferenceSourceMessageId" />
                        <MessageView v-else :message="message" :isAsking="isAsking" :isKnowledge="isKnowledge"
                            :messageRecommendApplyData="messageRecommendApplyData"
                            :is-last-message-record="index == messages.length - 1"
                            :is-first-answer="messages.length > 0 && message.senderId == 'AI' && index == 1"
                            :current-is-clicked-clear-button="currentIsClickedClearButton" :is-share="false" />
                    </div>
                </a-spin>
            </div>
        </div>

        <div v-if="!visibleMsgList" class="absolute top-0 bottom-0 left-0 right-0 flex items-center justify-center">
            <a-spin></a-spin>
        </div>
        <!-- 输入区域 -->
        <AiQuestionTextarea v-model:question="question" v-model:questionMode="questionModeValue" :is-asking="isAsking"
            :is-home="false" :is-knowledge="isKnowledge || false" ref="aiQuestionTextareaRef"
            v-model:repository-file-id-list="repositoryFileIdList" :repository-folder-id-list="repositoryFolderIdList"
            :session-from="sessionFrom" @submit="onSubmit" @open-new-question="handleOpenNewQueation" />
    </div>
</template>
<script lang="ts" setup>
import { useChatStore } from '@/stores/chat';
import { useUserStore } from '@/stores/user';
import { BindPhoneModal, LastAgentMessageStatus } from '@/utils/constants';
import { StarloveConstants } from '@/utils/starloveConstants';
import { getAiQuestionModeList } from '@/utils/utils';
import { MessageOne } from '@icon-park/vue-next';
import { fetchEventSource } from '@microsoft/fetch-event-source';
import { useThrottleFn } from '@vueuse/core';
import { message } from 'ant-design-vue';
import { defineAsyncComponent, onBeforeUnmount, onMounted, ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { getBySessionId, getSuggestCreator, pageAppMessages } from '~/api/appMessage';
import { getPlatformNew } from '~/composables/useApp';
import { useTracking } from '~/composables/useTracking';
import type { AgentStreamData } from '~/services/types/agent';
import type { AppCategory, AppMessage, AppMessageRecord } from '~/services/types/appMessage';
import type { EditorData } from '~/services/types/repositoryFile';
import { UserService } from '~/services/user';
import { useRechargeStore } from '~/stores/recharge';
import { dateFormatS } from '~/utils/utils';
import AiQuestionTextarea from './AiQuestionTextarea.vue';
import ExampleQuestion from './ExampleQuestion.vue';
const { track } = useTracking();

const AgentMessageView = defineAsyncComponent(() =>
    import('./AgentMessageView.vue')
)
const MessageView = defineAsyncComponent(() =>
    import('./MessageView.vue')
)

const { $eventBus } = useNuxtApp();
// 定义一个props来接收 里面有个isVisibleTop，bool类型，
const props = defineProps({
    isVisibleTop: {
        type: Boolean,
        default: false,
    },
    sessionId: {
        type: String,
        default: '',
    },
    isKnowledge: {
        type: Boolean,
        default: false,
    },
    pageNo: {
        type: Number,
        default: 1,
    },
    repositoryFileId: {
        type: String,
        default: '',
    },
    exampleList: {
        type: Array as PropType<string[]>,
        default: () => [],
    },
    isNewSessionId: {
        type: Boolean,
        default: false,
    },
    // 知识库文件夹id列表
    repositoryFolderIdList: {
        type: Array as PropType<string[]>,
        default: () => [],
    },
    // 会话来源
    sessionFrom: {
        type: String,
        default: SessionFrom.Normal,
    },
    isEmptyDocument: {
        type: Boolean,
        default: false,
    },
    editorData: {
        type: Array as PropType<EditorData[]>,
        default: () => [],
    },
})
let iDate = new Date().toUTCString()
const sessionId = ref<string>(props.sessionId || '')
const isNewSessionId = ref<boolean>(props.isNewSessionId)
const robotCode = ref('knowledge')
const webUserBindPhoneVisible = ref(false)
const aiQuestionTextareaRef = ref()
const currentReferenceSourceMessageId = ref('')
const chat = useChatStore()
const visibleMsgList = ref(false)
const currentSessionInfo = ref<AppMessageRecord | null>(null)
const loadSessionDetail = async () => {
    if (!sessionId.value) {
        return
    }
    const res = await getBySessionId({ sessionId: sessionId.value })
    if (!res.success) {
        return;
    }
    currentSessionInfo.value = res.data || null

}
const currentChatTitle = computed(() => {
    // const _list = chat.messageRecordList
    // const obj = _list.find(d => d.sessionId == props.sessionId) as any
    return currentSessionInfo.value || {
        title: '新提问',
        createTime: dateFormatS(new Date(), 'yyyy-MM-dd hh:mm:ss'),
        version: 2
    }
})
const loadRecommendApply = async (trimmedMessage: string) => {
    const res = await getSuggestCreator({ question: trimmedMessage })
    if (!res.ok || !res.data) {
        return
    }
    const list = res.data || []
    messageRecommendApplyData.value = list

    // storage.set(
    //   StarloveConstants.keyOflocalStorage.aiQuestionMessageRecommendApplyData,
    //   JSON.stringify(list)
    // )
}
const clearQuestionFile = () => {
    if (!aiQuestionTextareaRef.value) {
        return
    }
    aiQuestionTextareaRef.value.clearQuestionFileInfo()
}
const updateQuestionMessageRecordsAndLocation = (params: any) => {
    if (!params.title) {
        isNewSessionId.value = false
    }
    console.log('updateAiQuestionLeftMessageRecords updateAiQuestionLeftMessageRecords')
    $eventBus.emit(StarloveConstants.keyOfEventBus.updateAiQuestionLeftMessageRecords, params)
}
const reqParams = {
    pageNo: 1,
    pageSize: 25,
    // sessionId: props.isKnowledge ? '' : sessionId.value, //都用sessionId 了
    sessionId: sessionId.value,
    category: robotCode.value,
    // repositoryFileId: props.repositoryFileId, //这个参数没有用了，统一用sessionId来控制了
}
const { data, isLoading, isFinished, query } = pageAppMessages(reqParams) as any
const isAsking = ref<boolean>(false)
const messageRecommendApplyData = ref<AppCategory[]>([])
const currentIsClickedClearButton = ref(false)
const messages = ref<AppMessage[]>([])

const questionModeValue = ref<string[]>([])
const repositoryFileIdList = ref<string[]>([])
let timerId: NodeJS.Timeout | undefined = undefined
const reloadMessagesData = () => {
    query({ params: reqParams })
    loadSessionDetail()
}
const execQuery = () => {
    console.log('props.sessionId', props.sessionId, isNewSessionId.value, 'isNewSessionId')
    if (!isNewSessionId.value) {
        sessionId.value = props.sessionId
        if (reqParams.pageNo == 1) {
            visibleMsgList.value = false
        }
        query({
            params: {
                ...reqParams,
                sessionId: props.sessionId
            }
        })
        loadSessionDetail()
    } else {
        visibleMsgList.value = true
    }
}
// watch(
//   () => props.sessionId,
//   (newValue, oldValue) => {
//     console.log('props.sessionId', newValue, oldValue, isNewSessionId.value, 'isNewSessionId')
//     if (!isNewSessionId.value) {
//       sessionId.value = newValue
//       query({
//         params: {
//           ...reqParams,
//           sessionId: props.sessionId
//         }
//       })
//     }
//   }
// )
const user = useUserStore()
const handleBindPhoneSuccess = () => {
    // temporaryQuestioContent.value = messages.value[messages.value.length - 2].content
    $eventBus.emit(StarloveConstants.keyOfEventBus.updateLeftMessageRecords)
    // query({ params: reqParams }) //lsg 未绑定手机的情况，不应该去查询
    const _content = messages.value[messages.value.length - 2].content || ''
    if (_content) {
        try {
            const json = JSON.parse(_content)
            if (json.content) {
                question.value = json.content
                if (Array.isArray(json.files) && json.files.length > 0) {
                    questionFileId.value = json.files[0].fileId
                    questionFileUrl.value = json.files[0].fileUrl
                }
            }
        } catch (error) {
            question.value = _content
            console.log(error, '未绑定手机的情况')
        }
    }
    // question.value = messages.value[messages.value.length - 2].content || ''
    setTimeout(() => {
        messages.value = []
        handlePressSubmit()
    }, 500)
}

watch(() => user.showPhoneBoundModal.status, (newValue) => {
    console.log(newValue, 'newValue', '手机号绑定成功的回调')

    if (newValue == BindPhoneModal.FINISH_BINDING) {
        handleBindPhoneSuccess()
    }
})

watch(isFinished, (newValue, oldValue) => {
    // console.log('isFinished发生变化 新值是' + newValue, '旧值是' + oldValue)

    // console.log('isLoading -> ', isLoading.value)
    // console.log('isFinished -> ', isFinished.value)
    // console.log('response -> ', data.value)
    if (isFinished.value == false) {
        visibleMsgList.value = true
        return
    }

    if (!data.value) {
        visibleMsgList.value = true
        // message.showToast({ title: '请检查网络', icon: 'error' })
        return
    }

    if (data.value.current) {
        reqParams.pageNo = data.value.current
    }

    if (data.value.current == 1) {
        messages.value = []
    }

    if (isFinished.value && data.value) {
        const _list = data.value.records || []
        if (_list) {
            messages.value = [...data.value.records.reverse(), ...messages.value]
            // console.log('data.value.current', data.value.current)
            if (data.value.current == 1) {
                isStopScroll = false
                if (messages.value.length > 0) {
                    currentReferenceSourceMessageId.value = messages.value[messages.value.length - 1].id
                }
                setTimeout(() => {
                    scrollToBottom()
                    visibleMsgList.value = true
                }, 500)
            }

            if (messages.value.length > 0) {
                const data = messages.value[messages.value.length - 1]
                // iDate = new Date()
                if (data && data.status == 'ing') {
                    timerId = setTimeout(() => {
                        reloadMessagesData()
                    }, 5000)
                } else {
                    clearInterval(timerId)
                    // removeMessageRecommendApplyAndUnderstands()
                }
            }
        } else {
            visibleMsgList.value = true
        }

    }
})
const questionFileId = ref()
const questionFileUrl = ref()
let ctrl: any
const networkLookupOrKnowledgeInfo = ref()
const question = ref<string>('')
const temporaryQuestioContent = ref() //用于未绑定手机时，存储问题内容
let isStopScroll = false
let offsetHeight = 0
// 添加变量记录消息容器的高度信息
let lastScrollHeight = 0
let lastScrollTop = 0
const SCROLL_THRESHOLD = 50 // 滚动判定阈值，单位像素

const insideThinkTag = ref(false) // 用于标记是否在<think>标签内
let bufferedData = '' // 临时存储接收到的数据片段

const messageContainer = ref<HTMLElement | null>(null)
const isAskedAfterOpenPage = ref<boolean>(false)

const handleMessageExampleInput = (appMessage: any) => {
    isAskedAfterOpenPage.value = true

    question.value = appMessage
    handlePressSubmit()
}

const handlePressLoadMore = () => {
    // console.log('handlePressLoadMore = ')
    query({
        params: {
            ...reqParams,
            pageNo: reqParams.pageNo + 1
        }
    })
}

// 优化滚动检测逻辑
const isAtBottom = () => {
    if (!messageContainer.value) return true

    const { scrollHeight, scrollTop, clientHeight } = messageContainer.value
    // 当滚动位置接近底部时视为在底部
    return scrollHeight - scrollTop - clientHeight <= SCROLL_THRESHOLD
}

// 替换原有的 onBottom 函数
const onContainerScroll = useThrottleFn((event: Event) => {
    const target = event.target as HTMLElement
    const { scrollHeight, scrollTop, clientHeight } = target

    // 保存最后的滚动位置数据
    lastScrollHeight = scrollHeight
    lastScrollTop = scrollTop

    // 判断是否在底部
    const atBottom = scrollHeight - scrollTop - clientHeight <= SCROLL_THRESHOLD

    // 只有当用户手动滚动时才改变状态
    if (scrollHeight > offsetHeight) {
        isStopScroll = !atBottom
    }

    // 更新偏移高度，用于后续比较
    offsetHeight = clientHeight || 0
}, 200)

// 修改滚动到底部的逻辑
const _scrollToBottom = () => {
    // 使用nextTick确保DOM已更新
    if (isStopScroll) {
        return
    }

    nextTick(() => {
        if (messageContainer.value) {
            // 使用CSS过渡实现滚动动画
            messageContainer.value.style.transition = 'scrollTop 0.3s ease-out'
            messageContainer.value.scrollTop = messageContainer.value.scrollHeight
        }
    })
}

const scrollToBottom = useThrottleFn(_scrollToBottom, 650)
const _handlePressSubmit = useThrottleFn(
    async () => {
        networkLookupOrKnowledgeInfo.value = null
        // console.log(question.value, 'question.value', questionModeValue.value)
        let trimmedMessage = question.value.trim().replace(/^\n+|\n+$/g, '') // 去除首尾空白和换行
        if (temporaryQuestioContent.value) {
            trimmedMessage = temporaryQuestioContent.value
            temporaryQuestioContent.value = ''
        }
        if ((!trimmedMessage || trimmedMessage.trim() == '') && !questionFileId.value) {
            message.info('请输入问题或上传图片')
            return
        }

        let msg = {
            id: '',
            senderId: 'self',
            content: question.value,
            isRead: 'Y',
            mediaType: 'text'
        }
        if (questionFileId.value) {
            msg.content = JSON.stringify({
                content: question.value || '请理解这张图的含义',
                files: [
                    {
                        fileId: questionFileId.value,
                        fileUrl: questionFileUrl.value,
                        content: question.value || '请理解这张图的含义'
                    }
                ]
            })
        }


        messages.value.push(msg)
        if (!props.isKnowledge) {
            $eventBus.emit(
                StarloveConstants.keyOfEventBus.aiQuestionIsANewQuestionAndTheSessionIdThatIsBeingAnswered,
                false
            )
        }

        scrollToBottom()
        isAskedAfterOpenPage.value = true
        isAsking.value = true
        question.value = ''
        isStopScroll = false

        let params: any = {
            question: trimmedMessage,
            category: robotCode.value,
            sessionId: '',
            useSearch: false,
            useRepository: false,
            useR1: false,
            useGoogleScholarSearch: false,
            sessionFrom: SessionFrom.Normal
        }
        if (props.isKnowledge) {
            // params.sessionId = ''
            params.sessionId = sessionId.value || ''
        } else {
            params.sessionId = sessionId.value
            //问的时候没有，生成一个新的sessionId
            if (!params.sessionId) {
                // 不要生成sessionId了，后端会生成
                // params.sessionId = generateCurrentTimestampString()
                sessionId.value = params.sessionId
            }
        }
        if (questionModeValue.value && questionModeValue.value.length > 0) {
            params.useR1 =
                questionModeValue.value.filter((item) => item == QuestionTypeEnum.useR1).length > 0
            params.useSearch =
                questionModeValue.value.filter((item) => item == QuestionTypeEnum.useSearch).length > 0
            params.useRepository =
                questionModeValue.value.filter((item) => item == QuestionTypeEnum.useRepository).length > 0
            params.useGoogleScholarSearch =
                questionModeValue.value.filter((item) => item == QuestionTypeEnum.useScholar).length > 0
        }
        if (repositoryFileIdList.value && repositoryFileIdList.value.length > 0) {
            params.repositoryFileIdList = repositoryFileIdList.value;
        }
        if (params.useRepository) {
            params.collection = "user_knowledge"
        }
        if (props.repositoryFolderIdList.length > 0) {
            params.repositoryFolderIdList = props.repositoryFolderIdList
            params.sessionFrom = SessionFrom.KnowledgeFolder
            // 可能文件夹是下没有文件或者问夹，则提示
            if (props.isEmptyDocument) {
                message.error('知识库中暂无文档, 请选择重新选择')
                return;
            }
        }
        storage.set(
            StarloveConstants.keyOflocalStorage.aiQuestionModeList,
            JSON.stringify(questionModeValue.value)
        )

        if (props.isKnowledge) {
            // 不要用这个参数了，统一用 repositoryFileIdList
            // params['repositoryFileId'] = props.repositoryFileId
            params['repositoryFileIdList'] = [props.repositoryFileId]
            params.useSearch = false
            params.useRepository = true
            params.useGoogleScholarSearch = false
            params.sessionFrom = SessionFrom.KnowledgeSingleFile
        }
        if (questionFileId.value) {
            params['fileIds'] = [questionFileId.value]
            if (!trimmedMessage) {
                params.question = '请理解这张图的含义'
            }
        }
        // console.log('params == >', params)
        // 需要写在params后面，不然会清除questionModeValue的值
        clearQuestionFile()
        try {
            if (chat.homeChatQestionData) {
                chat.setHomeChatQestionData(null)
            }
            const responseMessage = {
                content: '',
                id: '',
                isRead: 'N',
                mediaType: 'text',
                category: 'knowledge',
                isDeleted: 'N',
                senderId: 'AI',
                status: 'waiting',
                sessionId: sessionId.value
            }
            messages.value.push(responseMessage)

            const url = `${StarloveUtil.getBaseUrl()}/agent/start?platform=${getPlatformNew()}`
            ctrl = new AbortController() // 创建AbortController实例，以便中止请求

            fetchEventSource(url, {
                method: 'POST',
                body: JSON.stringify(params),
                headers: {
                    'Content-Type': 'application/json;charset=utf-8',
                    // Token: UserService.getToken() || '',
                    Authorization: `Bearer ${UserService.getToken()}`,
                    'i-date': iDate
                },
                openWhenHidden: true,
                signal: ctrl.signal, // AbortSignal

                onopen(response) {

                    console.log('开始', response)
                    // 检查响应状态
                    if (response.status === 404) {
                        message.error('接口不存在 (404)')
                        throw new Error('接口不存在 (404)')
                    }
                    if (!response.ok) {
                        message.error(`请求失败: ${response.status} ${response.statusText}`)
                        throw new Error(`请求失败: ${response.status}`)
                    }

                    return new Promise((resolve) => {
                        if (isNewSessionId.value) {
                            setTimeout(() => {
                                updateQuestionMessageRecordsAndLocation({ title: '新提问', sessionId: sessionId.value })
                            }, 500)
                        }

                        if (trimmedMessage) {
                            loadRecommendApply(trimmedMessage)
                        }
                        resolve()
                    })
                },
                onmessage(event) {
                    // console.log('正在回答中。。。 ==>', event.data)

                    if (isJSON(event.data)) {
                        const data: AgentStreamData = JSON.parse(event.data)
                        const lastMessageData = messages.value[messages.value.length - 1]
                        const replyMessageData = lastMessageData?.replyMessage || []
                        lastMessageData.replyMessage = replyMessageData
                        // 查找 item_id 是否存在 replyMessageData 中，不存在就创建
                        let currentReply = replyMessageData.find((item: any) => item.item_id == data.item_id || '') as AgentStreamData
                        if (!currentReply) {
                            currentReply = data
                            currentReply.item_id = data.item_id || ''
                            replyMessageData.push(data)
                        } else {
                            currentReply.type = data.type
                            currentReply.sequence_number = data.sequence_number
                        }

                        console.log('正在回答中。。。 ==>', data)
                        if (data.type == LastAgentMessageStatus.conversation_started) {
                            lastMessageData.status = LastAgentMessageStatus.answeing
                            sessionId.value = data.conversation_id
                        } else if (data.type == LastAgentMessageStatus.conversation_completed) {
                            lastMessageData.status = LastAgentMessageStatus.done
                            submitSuccess(data, lastMessageData)
                            return
                        } else if (data.type == LastAgentMessageStatus.conversation_error || data.type == LastAgentMessageStatus.error) {
                            // data:{"conversation_id":"1950506111614455809","type":"error","content":"错误"}
                            lastMessageData['id'] = data?.id || ''
                            lastMessageData['status'] = LastMessageStatus.error
                            lastMessageData['content'] = data.content
                            replyMessageData.length = 0
                            replyMessageData.push({
                                item_id: '',
                                status: LastMessageStatus.error,
                                content: data.content,
                                type: LastAgentMessageStatus.text_done
                            })
                            // console.log("data.event == 'error' ==>", data.content.code)
                            submitSuccess(data)
                            if (data.code === HTTP_STATUS.MOBILE_NOT_BOUND) {
                                //存储发送消息的session，
                                if (!reqParams.sessionId) {
                                    if (data.conversation_id) {
                                        reqParams.sessionId = data.conversation_id
                                    }
                                }
                                webUserBindPhoneVisible.value = true
                                user.setShowPhoneBoundModal({
                                    status: BindPhoneModal.SHOW_BINDING
                                })
                                return
                            }
                            console.error('data ==>', data)
                            message.error('回答失败:' + data?.content || '')
                            throw new Error('回答失败:' + data?.content || '')
                        } else if (data.type == LastAgentMessageStatus.text_delta) {
                            if (!currentReply?.delta) {
                                currentReply.delta = ''
                            }
                            currentReply.delta += data.delta
                        } else if (data.type == LastAgentMessageStatus.text_delta) {
                            if (!currentReply?.delta) {
                                currentReply.delta = ''
                            }
                            currentReply.delta += data.delta
                        } else if (data.type == LastAgentMessageStatus.text_done) {
                            currentReply.content = data.content
                        } else if (data.type == LastAgentMessageStatus.tool_call_started) {
                            currentReply.params = data.params
                        } else if (data.type == LastAgentMessageStatus.tool_call_completed) {
                            currentReply.result = data.result
                        } else if (data.type == LastAgentMessageStatus.think_delta) {
                            if (!currentReply?.delta) {
                                currentReply.delta = ''
                            }
                            currentReply.delta += data.delta
                        } else if (data.type == LastAgentMessageStatus.think_done) {
                            currentReply.content = data.content
                        } else if (data.type == LastAgentMessageStatus.message) {
                            console.log(data.content, 'data.content')
                            // {"conversation_id":"1949767324353564674","type":"message","content":{"question":"1950090101392543745","answer":"1950090101442875393"}}
                            if (typeof data.content == 'object') {
                                lastMessageData.id = data.content?.answer ? data.content?.answer : lastMessageData.id
                                lastMessageData.questionId = data.content?.question
                                    ? data.content?.question
                                    : lastMessageData.questionId
                            }

                        }
                        console.log('data', lastMessageData.replyMessage)
                        // 优化滚动处理逻辑：如果用户没有手动滚动或者用户滚动到底部，则自动滚动
                        if (messageContainer.value && (!isStopScroll || isAtBottom())) {
                            scrollToBottom()
                        }
                    }
                },
                onerror(error) {
                    console.log('错误 ==>', error)
                    ctrl?.abort()
                    isAsking.value = false
                    $eventBus.emit(
                        StarloveConstants.keyOfEventBus
                            .aiQuestionIsANewQuestionAndTheSessionIdThatIsBeingAnswered,
                        true
                    )
                    throw error // 直接抛出错误，避免反复调用
                },
                onclose() {
                    // console.log('结束')
                    isAsking.value = false
                    // console.log(messages.value)
                    $eventBus.emit(
                        StarloveConstants.keyOfEventBus
                            .aiQuestionIsANewQuestionAndTheSessionIdThatIsBeingAnswered,
                        true
                    )
                    scrollToBottom()
                    track('chat_end', sessionId.value.toString(), '搜索完成')
                }
            })
        } catch (error) {
            console.log(error)
        }
    },
    5000,
    false
)
const submitSuccess = (status: any, lastMessageData: any = null) => {
    // isFirstAnswerQuestion = false
    console.log('submitSuccess', status, lastMessageData)
    // 更新左侧的参考来源
    if (lastMessageData) {
        const replyMessageList = lastMessageData?.replyMessage || []
        chat.setReplyMessage(replyMessageList)
        // 发现有工具，自动弹出来
        const _replyMessageList = replyMessageList.filter((item: any) => item.type.startsWith('tool'))
        if (_replyMessageList.length > 0) {
            chat.openReferenceSource = true
        }

    }
    let messageData = messages.value.find(
        (item) => item.id == messages.value[messages.value.length - 1].id
    )
    if (networkLookupOrKnowledgeInfo.value) {
        messageData = messages.value.find((item) => item.id == networkLookupOrKnowledgeInfo.value.id)
    }

    // if (messageData) {
    //     messageData.content = JSON.stringify({
    //         content: messageData.content?.replace('理解问题', '').replace('正在搜索资料', ''),
    //         refs: networkLookupOrKnowledgeInfo.value?.content || ''
    //     })

    //     if (messageRecommendApplyData.value.length > 0) {
    //         messageData.recommendApplyList = messageRecommendApplyData.value
    //     }
    // }

    questionFileId.value = ''
    questionFileUrl.value = ''
    clearQuestionFile()
    if (status != 'error' && isNewSessionId.value) {
        // updateQuestionMessageRecordsAndLocation({})
        loadSessionDetail()
    }

    // $eventBus.emit(StarloveConstants.keyOfEventBus.updateLeftManualOperationMessageRecords)
    // isFirstAnswerQuestion.value = true

    // if (messages.value[messages.value.length - 1]['understands']) {
    //   storage.set(
    //     StarloveConstants.keyOflocalStorage.aiQuestionMessageUnderstandsData,
    //     JSON.stringify(messages.value[messages.value.length - 1]['understands'])
    //   )
    // }

    currentIsClickedClearButton.value = false

    isAsking.value = false

    setTimeout(() => {
        UserService.loadKnowledgeAssistantMemberInfo()
    }, 400)

    // deleteChatContentDraft(props.keyOflocalStorage)
    // 只有当用户未主动滚动或已滚动到底部时，才执行自动滚动
    if (!isStopScroll || isAtBottom()) {
        scrollToBottom()
    }
}
const handlePressSubmit = async () => {
    // if (!UserService.isLogined()) {
    //   if (isPcWeb()) {
    //     loginVisible.value = true
    //     return
    //   }
    //   RouteService.pageToLogin()
    //   return
    // }
    // console.log(isAsking.value, 'isAsking.value')
    if (isAsking.value == true) {
        return
    }

    if (!UserService.isCanAskQuestion()) {
        //提示去充值
        console.log('提示去充值')
        const rechargeStore = useRechargeStore()
        rechargeStore.$state.rechargeModalVisible = true
        return
    }
    _handlePressSubmit()
}
const route = useRoute()
const router = useRouter()
const onSubmit = (questionFileData: any) => {
    try {
        // 如果是新搜索
        if (isNewSessionId.value) {
            let params: any = {
                question: question.value,
                mode: questionModeValue.value,
                isHome: true
            }
            if (questionFileData) {
                params['fileId'] = questionFileData.id
                params['fileUrl'] = questionFileData.fileUrl
            } else {
                if (!params.question) {
                    message.error('请输入问题')
                    return
                }
            }
            params['sessionId'] = sessionId.value
            chat.setIsNewSessionId(true)
            console.log('setHomeChatQestionData', params)
            chat.setHomeChatQestionData(params)
            // if (route.path.startsWith(`/chat/${params['sessionId']}`)) {
            //     const deepCopy = JSON.parse(JSON.stringify(chat.homeChatQestionData));
            //     handleSendQuestionEvent(deepCopy)
            //     return
            // }
            // 如果是知识库提问，不要跳转到聊天页面
            if (props.isKnowledge) {
                handlePressSubmit()
                return
            } else {
                // 如果是AI提问
                if (route.path.startsWith(`/chat/`)) {
                    const deepCopy = JSON.parse(JSON.stringify(chat.homeChatQestionData));
                    handleSendQuestionEvent(deepCopy)
                } else {
                    router.push({
                        path: `/chat/${params['sessionId']}`,
                    })
                }
            }

            return;
        }

        if (questionFileData) {
            questionFileId.value = questionFileData.id
            questionFileUrl.value = questionFileData.fileUrl
        }
        // console.log(questionFileData, 'questionFileData')
        setTimeout(() => {
            handlePressSubmit()
        }, 100)
    } catch (error) {
        console.log(error)
    }

}
const handleSendQuestionEvent = (data: any) => {
    // console.log('askAiQuestionInTheFrontPageOfTheKnolwedgeBase ==>', data.mode)
    question.value = data.question
    questionModeValue.value = data.mode
    questionFileId.value = data.fileId || ''
    questionFileUrl.value = data.fileUrl || ''
    sessionId.value = data.sessionId || ''
    setTimeout(() => {
        handlePressSubmit()
    }, 200)
}
const handleDeleteMessageEvent = (data: any) => {
    messages.value = messages.value.filter((item) => item.id != data.id)
}
// 新提问事件
const handleOpenNewQueation = (data: any) => {
    // console.log('handleNewQuestionEvent', data)
    chat.setIsNewSessionId(true)
    sessionId.value = ''
    messages.value = []
}
onMounted(() => {
    const homeChatQestionData = chat.getHomeChatQestionData()
    if (homeChatQestionData) {
        const deepCopy = JSON.parse(JSON.stringify(homeChatQestionData));
        handleSendQuestionEvent(deepCopy)
    } else {
        questionModeValue.value = getAiQuestionModeList()
    }

    // 初始化滚动容器的高度信息
    nextTick(() => {
        if (messageContainer.value) {
            offsetHeight = messageContainer.value.clientHeight || 0
            // 初始状态下自动滚动是启用的
            isStopScroll = false
        }
    })

    $eventBus.on(
        StarloveConstants.keyOfEventBus.askAiQuestionInTheFrontPageOfTheKnolwedgeBase,
        handleSendQuestionEvent
    )
    $eventBus.on(StarloveConstants.keyOfEventBus.updateMessageDelete, handleDeleteMessageEvent)
    $eventBus.on(StarloveConstants.keyOfEventBus.chatScrollToBottom, () => {
        if (messageContainer.value && (!isStopScroll || isAtBottom())) {
            scrollToBottom()
        }
    })
    console.log('agent onMounted', chat.homeChatQestionData)

    if (chat.homeChatQestionData) {
        visibleMsgList.value = true
    } else {
        execQuery()
    }
})
onBeforeUnmount(() => {
    if (!chat.homeChatQestionData) {
        chat.setHomeChatQestionData(null)
    }
    $eventBus.off(StarloveConstants.keyOfEventBus.askAiQuestionInTheFrontPageOfTheKnolwedgeBase, handleSendQuestionEvent);
    $eventBus.off(StarloveConstants.keyOfEventBus.updateMessageDelete, handleDeleteMessageEvent)
    $eventBus.off(StarloveConstants.keyOfEventBus.chatScrollToBottom)
    // chat.setIsNewSessionId(false)
    chat.openReferenceSource = false
});
</script>
