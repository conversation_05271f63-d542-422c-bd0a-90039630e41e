<template>
    <view>
        <GuideInfo :editorData="editorData || []"></GuideInfo>
        <div class="example-question-view" v-if="messageExampleList.length > 0">
            <view class="example-title-area">
                <view class="example-title-text">你可以这样提问</view>
                <view class="example-title-image">
                    <image src="https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/bulb-icon.png" />
                </view>
            </view>
            <view v-for="item in messageExampleList" @click="handleMessageExampleInput(item)">
                <view v-if="item" class="message-example">
                    <view class="example-text">“{{ item }}”</view>
                    <view class="example-image">
                        <img src="https://static-1256600262.file.myqcloud.com/mini-app/hand-icon.png" />
                    </view>
                </view>
            </view>
        </div>
    </view>
</template>

<script setup lang="ts">
import { type EditorData } from '@/services/types/repositoryFile';


const props = defineProps({
    robotCode: {
        type: String,
        default: '',
    },
    exampleList: {
        type: Array as PropType<string[]>,
        default: () => [],
    },
    editorData: {
        type: Array as PropType<EditorData[]>,
        default: () => [],
    },
})

const emit = defineEmits(['sendExampleMessage'])

const messageExampleList = computed(() => {
    return props.exampleList || []
})

const handleMessageExampleInput = (item: any) => {
    emit('sendExampleMessage', item)
}

</script>

<style lang="scss">
.example-question-view {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    margin: 0 13px;

    .example-title-area {
        display: flex;
        justify-content: center;
        align-items: center;

        margin: 30px 0 20px 0;
    }

    .example-title-text {
        font-size: 14px;
        color: #333333;
        line-height: 21px;
    }

    .example-title-image {
        width: 21px;
        height: 21px;
        display: flex;
        align-items: center;
    }

    .example-title-image image {
        width: 21px;
        height: 21px;
    }

    .message-example {
        margin-top: 10px;
        background-color: #ffffff;
        max-width: 850px;
        border-radius: 5px;
        display: flex;
        justify-content: center;
        align-items: flex-start;

        padding: 7px 15px;
        box-shadow: 0px 0px 10px 0px #eeeeee;
        cursor: pointer;
    }

    .example-text {
        // background-color: #6d1414;
        max-width: 850px;
        padding-right: 15px;
        font-size: 14px;

        font-weight: 500;
        color: #333333;
        line-height: 23px;
    }

    .example-image {
        width: 18px;
        height: 18px;
        // text-align: right;
    }

    .example-image img {
        width: 18px;
        height: 18px;
    }
}
</style>