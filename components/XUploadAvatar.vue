<template>
    <!-- <button class="p-1.5 sm:p-2 text-gray-500 hover:text-gray-700 transition-colors relative">
        <picture-one theme="outline" size="24" class="text-gray-500" />
        <input type="file" class="absolute inset-0 opacity-0 cursor-pointer" accept="image/*">
    </button> -->
    <a-upload v-model:file-list="fileList" :before-upload="beforeUpload" :customRequest="httpUpload" list-type="picture"
        :showUploadList="false" class="upload-list-inline" accept="image/*">
        <slot></slot>
    </a-upload>
</template>
<script lang="ts" setup>
import { checkFileHash } from '@/api/repositoryFile.js';
import { generatePutUrl, uploadByUrl } from '@/api/upload';
import { ToastService } from '@/services/toast';
import { getFileSha256 } from '@/utils/utils';
import { Upload as AUpload } from 'ant-design-vue';
import axios from 'axios';
import { removeQuestionMarkText } from '~/utils/utils';
const props = defineProps({
    avatar: {
        type: String,
        default: ''
    }
})
const emit = defineEmits(['update:avatar'])
const fileList = ref([])

interface FileInfo {
    uid: string
    webkitRelativePath: string
    name: string
    size: number
    type: string
    status: string
    percent: number
    response: {
        fileUrl?: string
        fileId?: string
    },
    url: string
}
const beforeUpload = (file: any, _fileList: any) => {
    const reader = new FileReader();
    reader.onload = (e) => {
        // 获取 Base64 格式的结果并保存
        if (fileList.value.length) {
            fileList.value = [{
                ...fileList.value[0],
                url: e.target.result
            }];
        }
    };
    // 读取文件为 DataURL(Base64)
    reader.readAsDataURL(file);
    const isLt10M = file.size / 1024 / 1024 < 10
    if (!isLt10M) {
        ToastService.error(`文件:${file.name}大小不能超过10M!`)
    }
    return isLt10M || AUpload.LIST_IGNORE
}
// const removeQuestionMarkText = (str: string) => {
//     return str.replace(/\?.*$/, '')
// }
const saveUploadByUrl = async (params: { fileName: string, fileUrl: string, fileSha256: string }) => {
    const res = await uploadByUrl({
        fileName: params.fileName,
        fileUrl: params.fileUrl,
        fileSha256: params.fileSha256
    })
    if (!res.success) {
        ToastService.error(params.fileName + '上传失败')
        return null
    }
    return res.data
}
const handleUploadFileByFile = async (file: FileInfo, onProgress: any, onSuccess: any) => {
    try {
        // 文件需要上传
        const item = file
        // console.log(fileList.value, 'fileList')
        ToastService.loading()

        const sha256 = await getFileSha256(file as any)
        const checkFileShaResult = await checkFileHash({
            sha256: `${sha256}`,
        });

        if (!checkFileShaResult.ok) {
            ToastService.error(checkFileShaResult.message || '图片上传失败，请重试')
            return
        }

        if (checkFileShaResult.data != null) {
            const params = {
                fileName: checkFileShaResult.data.fileName,
                fileUrl: checkFileShaResult.data.fileUrl,
                fileSha256: checkFileShaResult.data.fileSha256
            }

            const fileData = await saveUploadByUrl(params)
            const _response = { fileUrl: params.fileUrl, fileId: fileData.id, name: file.name, status: 'done' }
            file.url = params.fileUrl
            onSuccess(_response, file)
            emit('update:avatar', params.fileUrl)
            ToastService.success('上传成功')
            return
        } else {
            // console.log(fileList.value, 'fileList')
            const cosClientAndParams = await generatePutUrl({
                filename: item.name

            })
            // console.log('cosClientAndParams ==>', cosClientAndParams)
            if (!cosClientAndParams.ok || !cosClientAndParams.data) {
                throw new Error(cosClientAndParams?.message)
            }

            const response = await axios.put(cosClientAndParams.data.url, file, {
                headers: {
                    'Content-Type': cosClientAndParams.data.contentType
                }
            });

            if (response.status !== 200) {
                throw new Error(`上传失败: ${response.status} ${response.statusText}`);
            }
            const params = {
                fileName: item.name,
                fileUrl: removeQuestionMarkText(cosClientAndParams.data.url),
                fileSha256: sha256
            }

            const fileData = await saveUploadByUrl(params)

            const _response = { fileUrl: params.fileUrl, fileId: fileData.id, name: item.name, status: 'done' }
            item.url = params.fileUrl
            onSuccess(_response, item)
            emit('update:avatar', params.fileUrl)
            ToastService.success('上传成功')
        }
    } catch (error: any) {
        console.log(error, 'error')
        ToastService.error('文件上传失败，请重试')
    }
}
const httpUpload = async (data: { file: any; onProgress?: any; onSuccess?: any }) => {
    const { file, onProgress, onSuccess } = data

    setTimeout(() => {
        handleUploadFileByFile(file, onProgress, onSuccess)
    }, 300)
}
</script>