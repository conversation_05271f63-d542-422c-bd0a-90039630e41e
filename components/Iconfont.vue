<template>
  <svg :class="iconClassName" :style="iconStyle" aria-hidden="true">
    <use :xlink:href="iconName"></use>
  </svg>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  iconName: {
    type: String,
    required: false
  },
  name: {
    type: String,
    required: true
  },
  size: {
    type: Number,
    required: false
  }
})
const iconClassName = computed(() => {
  return `icon ${props.iconName || ''}`
})
const iconName = computed(() => `#icon-${props.name}`)
const iconStyle = computed(() => ({
  fontSize: props.size ? `${props.size}px` : '1em'
}))
</script>

<style scoped>
.icon {
  width: 1em;
  height: 1em;
  fill: currentColor;
  overflow: hidden;
}
</style>
