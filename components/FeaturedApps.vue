<template>
  <div id="featured" class="mb-8">
    <div class="flex flex-row items-center justify-between mb-4">
      <div class="flex items-center">
        <div class="flex items-center justify-center w-8 h-8 mr-3 rounded-lg bg-blue-50">
          <star theme="outline" size="20" fill="#10b981" />
        </div>
        <h2 class="text-lg font-bold text-blue-800">推荐写作</h2>
      </div>

      <Manual />
    </div>

    <!-- 左侧区域 -->
    <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3"
      v-if="homeData.length > 0 && homeData[0].list.length > 0">

      <!-- 第一个卡片 -->
      <AppCard class="h-full" :title="homeData[0].list[0].name" :desc="homeData[0].list[0].description"
        :to="`/create/${homeData[0].list[0].code}`" :icon="homeData[0].list[0].avatar" :code="homeData[0].list[0].code"
        layout="vertical" showFeatures />

      <!-- 中间区域 -->
      <div class="flex flex-col h-full">
        <AppCard class="flex-1 mb-4" :title="homeData[0].list[1].name" :desc="homeData[0].list[1].description"
          :to="`/create/${homeData[0].list[1].code}`" :icon="homeData[0].list[1].avatar"
          :code="homeData[0].list[1].code" />
        <AppCard class="flex-1" :title="homeData[0].list[2].name" :desc="homeData[0].list[2].description"
          :to="`/create/${homeData[0].list[2].code}`" :icon="homeData[0].list[2].avatar"
          :code="homeData[0].list[2].code" />
      </div>

      <!-- 右侧区域 -->
      <div
        class="relative flex flex-col p-4 overflow-hidden text-white rounded-lg bg-gradient-to-br from-blue-400/90 via-blue-500/90 to-indigo-500/90">
        <!-- 右侧显示内容区域 -->
        <div class="flex flex-col items-center justify-center flex-1 py-6 space-y-6">
          <div class="text-center">
            <p class="text-xl font-bold sm:text-xl">已成功写作</p>
          </div>
          <div class="w-full text-center">
            <div class="relative flex items-center justify-center text-3xl font-bold sm:text-3xl">
              <span>{{ formattedCreateCount }}</span>
              <span class="ml-2 text-base sm:text-base">次</span>
              <div
                class="absolute w-full h-10 transform -translate-x-1/2 -translate-y-1/2 rounded-full opacity-75 bg-white/20 top-1/2 left-1/2 blur-md">
              </div>
            </div>
          </div>
          <div class="w-full min-w-0">
            <p class="px-2 overflow-hidden text-base max-h-20" v-if="latestSubmissionList.length > 0">
              <ClientOnly>
                <VerticalMarquee class="text-center break-words whitespace-normal text-white/80 text-ellipsis"
                  :texts="latestSubmissionList" color="text-gray-800" />
              </ClientOnly>
            </p>
          </div>
        </div>
        <div
          class="absolute transform rotate-45 rounded-full -bottom-20 -right-20 w-52 sm:w-72 h-52 sm:h-72 bg-white/10">
        </div>
        <div class="absolute w-32 h-32 transform -rotate-45 rounded-full -top-10 -left-10 sm:w-52 sm:h-52 bg-white/10">
        </div>
        <div class="absolute bottom-0 left-0 w-full h-1/3 bg-gradient-to-t from-blue-400/90"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import VerticalMarquee from '@/components/Common/VerticalMarquee.vue';
import { Star } from '@icon-park/vue-next';
import AppCard from './AppCard.vue';

import Manual from '~/components/Create/Index/Manual.vue';

import { apiHomePath } from '~/api/home';
import { useHomeStore } from '~/stores/home';

interface AsyncDataResponse {
  success: boolean;
  code: number | null;
  result: { list: any[] }[];
  message?: string;
}


const homeStore = useHomeStore()

const { homeData, createCount, latestSubmissionList } = storeToRefs(useHomeStore())

const marqueeTexts = ref<any[]>([])

const route = useRoute()
const topCode = route.query.topCode ? route.query.topCode.toString() : '';

const config = useRuntimeConfig()
const { data: asyncData } = useAsyncData<AsyncDataResponse>('fetchData', async () => {
  // 检查并加载 homeStore 数据
  if (homeStore.homeData.length === 0) {
    const homeParams: any = {}
    if (topCode) {
      homeParams.topCode = topCode
    }
    await homeStore.loadHomeData(homeParams, `${config.public.apiBase}${apiHomePath}`)
  }

  return {
    success: true,
    code: null,
    result: homeStore.homeData,
    message: ''
  }
}, {
  server: true,
  lazy: false,
  immediate: true,
  default: () => ({ success: false, code: null, result: [], message: '' })
})

onMounted(async () => {
  await homeStore.loadListHotCreator()
})

// defineProps({
//   apps: {
//     type: Array,
//     required: true
//   }
// })

// 添加计算属性来格式化 createCount
const formattedCreateCount = computed(() => {
  return createCount.value.toLocaleString('zh-CN')
})
</script>