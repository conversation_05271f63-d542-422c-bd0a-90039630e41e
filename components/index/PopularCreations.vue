<template>
  <!-- 热门写作组件 -->
  <div class="">
    <div class="flex flex-wrap items-center mb-4 gap-2">
      <div class="flex items-center">
        <div class="w-7 h-7 rounded-lg flex items-center justify-center mr-2">
          <edit theme="outline" size="18" class="text-blue-800" />
        </div>

        <h2 class="text-lg font-medium text-transparent bg-gradient-to-r from-blue-700 to-indigo-700 bg-clip-text">
          热门写作</h2>
      </div>

      <TextMarquee class="hidden md:block mx-4" :texts="sloganTexts" />

      <nuxt-link to="/create" class="sm:ml-auto flex items-center group relative">
        <div
          class="relative flex items-center px-3 py-1.5 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200/50 rounded-full hover:from-blue-100 hover:to-indigo-100 hover:border-blue-300/50 transition-all duration-200 hover:shadow-sm">
          <span class="text-sm font-medium text-blue-800 group-hover:text-blue-900">
            150+ 写作应用</span>
          <svg
            class="w-3.5 h-3.5 ml-1.5 text-gray-400 group-hover:translate-x-0.5 group-hover:text-gray-600 transition-all duration-200"
            viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd"
              d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z"
              clip-rule="evenodd" />
          </svg>
        </div>
      </nuxt-link>
    </div>

    <!-- 热门写作卡片 -->

    <div class="grid grid-cols-12 gap-3" v-if="groupList.length">
      <!-- 第一行 -->
      <div class="col-span-12 lg:col-span-4">
        <AppCard :title="groupList[0].name" :desc="groupList[0].description" :to="`/create/${groupList[0].code}`"
          :icon="groupList[0].avatar" :code="groupList[0].code" class="h-full" layout="vertical" />
      </div>

      <div class="col-span-12 lg:col-span-8 grid grid-cols-2 gap-3">
        <AppCard v-for="item in (data?.necessaryList || []).slice(1, 5)" :key="item.name" :title="item.name"
          :desc="item.description" :to="item.code == 'book' ? '/book' : `/create/${item.code}`" :icon="item.avatar"
          :code="item.code" class="h-full" />
      </div>

      <!-- 第二行 -->
      <div class="col-span-12 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
        <AppCard v-for="item in groupList.slice(5, 99)" :key="item.name" :title="item.name" :desc="item.description"
          :to="`/create/${item.code}`" :icon="item.avatar" :code="item.code" class="h-full" />
      </div>
    </div>
  </div>
</template>

<script setup>
import {
  Edit
} from '@icon-park/vue-next';
import { computed } from 'vue';

import AppCard from '../AppCard.vue';
import TextMarquee from '../Common/TextMarquee.vue';

const sloganTexts = [
  // 'AI长文写作引领者',
  // '文笔质量高，无AI味儿',
  '联网+知识库检索参考信息，专业全面',
  '直出Word，格式规范、美观',
  '可灵活定义篇幅、大纲、背景文档、语言',
  '海量应用模板，想写什么就写什么',
  'AI原创，无版权风险',
  '支持AI在线编辑，快速改稿'
]
const props = defineProps({
  data: {
    type: Object,
    default: null,
  },
})
// const sloganTexts = computed(() => {
//   if (!props.data) return []; // 确保 data.value 已加载
//   return props.data.groupList.map(d => d.title) || [];
// });
const groupList = computed(() => {
  if (!props.data) return []; // 确保 data.value 已加载
  if (!props.data.groupList) {
    return []
  }
  return props.data.groupList[0].list || [];
});
// const creationItems = [
//   {
//     title: '专业论文',
//     desc: '格式规范，内容专业，支持多种论文类型和格式要求',
//     to: '/create',
//     icon: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/logo/paper.png'
//   },
//   {
//     title: 'PPT',
//     desc: '一键快速生成，轻松定制PPT',
//     to: '/create',
//     icon: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/logo/PPT.png'
//   },
//   {
//     title: '资料搜索管理',
//     desc: '帮你快速集成各类问题（适用于文案写作）',
//     to: '/create',
//     icon: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/logo/paper.png'
//   },
//   {
//     title: '思维导图',
//     desc: 'AI思维导图，让想法清晰起来',
//     to: '/create',
//     icon: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/logo/zhichang.png'
//   },
//   {
//     title: '自定义写作',
//     desc: '使用自定义写作前，建议先做一遍合适应用',
//     to: '/create',
//     icon: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/logo/paper.png'
//   },
//   {
//     title: '演讲稿',
//     desc: '各类用语、风格演讲稿快速生成！',
//     to: '/create',
//     icon: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/logo/yanjiang.png'
//   },
//   {
//     title: '资料搜索管理',
//     desc: '帮你快速集成各类问题（适用于文案写作）',
//     to: '/create',
//     icon: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/logo/paper.png'
//   },
//   {
//     title: '思维导图',
//     desc: 'AI思维导图，让想法清晰起来',
//     to: '/create',
//     icon: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/logo/zhichang.png'
//   },
// ]
</script>

<style scoped>
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  position: absolute;
  will-change: transform, opacity;
}

.slide-fade-enter-from {
  transform: translateY(20px);
  opacity: 0;
}

.slide-fade-leave-to {
  transform: translateY(-20px);
  opacity: 0;
}

.slide-fade-enter-to,
.slide-fade-leave-from {
  transform: translateY(0);
  opacity: 1;
}

@media (prefers-color-scheme: dark) {
  :deep(.app-card) {
    @apply bg-gray-800 border-gray-700;
  }
}
</style>
