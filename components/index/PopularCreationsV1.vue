<template>
    <!-- 热门写作组件 -->
    <div class="">

        <div class="grid grid-cols-2 gap-4 sm:grid-cols-4 lg:grid-cols-6 xl:grid-cols-7" v-if="groupList.length">
            <div class="col-span-1" v-for="group in groupList" :key="group.code">
                <PopularCreationItem :title="group.name" :desc="group.description"
                    :to="`/create${group.code ? `/${group.code}` : ''}`" :icon="group.avatar" :code="group.code"
                    class="h-full" layout="vertical" />
            </div>
        </div>
    </div>
</template>

<script setup>
import { computed } from 'vue';

import PopularCreationItem from './PopularCreationItem.vue';

const props = defineProps({
    data: {
        type: Object,
        default: null,
    },
})
// const sloganTexts = computed(() => {
//   if (!props.data) return []; // 确保 data.value 已加载
//   return props.data.groupList.map(d => d.title) || [];
// });
const groupList = computed(() => {
    if (!props.data) return []; // 确保 data.value 已加载
    if (!props.data.groupList) {
        return []
    }
    const list = [{
        "type": "",
        "code": "",
        "name": "150+",
        "avatar": "https://static-1256600262.file.myqcloud.com/xiaoin-h5/logo/app150%2B.png",
        "description": "150+",
        "searchKeyword": "150+",
        "isUseLargeWorker": true
    }]
    return (props.data.groupList[0].list || []).slice(0, 6).concat(list);
});
// const creationItems = [
//   {
//     title: '专业论文',
//     desc: '格式规范，内容专业，支持多种论文类型和格式要求',
//     to: '/create',
//     icon: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/logo/paper.png'
//   },
//   {
//     title: 'PPT',
//     desc: '一键快速生成，轻松定制PPT',
//     to: '/create',
//     icon: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/logo/PPT.png'
//   },
//   {
//     title: '资料搜索管理',
//     desc: '帮你快速集成各类问题（适用于文案写作）',
//     to: '/create',
//     icon: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/logo/paper.png'
//   },
//   {
//     title: '思维导图',
//     desc: 'AI思维导图，让想法清晰起来',
//     to: '/create',
//     icon: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/logo/zhichang.png'
//   },
//   {
//     title: '自定义写作',
//     desc: '使用自定义写作前，建议先做一遍合适应用',
//     to: '/create',
//     icon: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/logo/paper.png'
//   },
//   {
//     title: '演讲稿',
//     desc: '各类用语、风格演讲稿快速生成！',
//     to: '/create',
//     icon: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/logo/yanjiang.png'
//   },
//   {
//     title: '资料搜索管理',
//     desc: '帮你快速集成各类问题（适用于文案写作）',
//     to: '/create',
//     icon: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/logo/paper.png'
//   },
//   {
//     title: '思维导图',
//     desc: 'AI思维导图，让想法清晰起来',
//     to: '/create',
//     icon: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/logo/zhichang.png'
//   },
// ]
</script>

<style scoped>
.slide-fade-enter-active,
.slide-fade-leave-active {
    transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    position: absolute;
    will-change: transform, opacity;
}

.slide-fade-enter-from {
    transform: translateY(20px);
    opacity: 0;
}

.slide-fade-leave-to {
    transform: translateY(-20px);
    opacity: 0;
}

.slide-fade-enter-to,
.slide-fade-leave-from {
    transform: translateY(0);
    opacity: 1;
}

@media (prefers-color-scheme: dark) {
    :deep(.app-card) {
        @apply bg-gray-800 border-gray-700;
    }
}
</style>
