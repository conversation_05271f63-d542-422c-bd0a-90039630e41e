<template>
    <div class="flex items-center justify-center ">
        <!-- AI生成PPT能力升级，真正支持模版自定义! -->
        <HomeTextMarquee :texts="newsList" />
    </div>
</template>
<script setup lang="ts">
import type { ResponsePagination } from '~/services/types/reponse'
import type { NewsInfo } from '~/services/types/repositoryFile'

// const items = [
//     'AI生成PPT能力升级，真正支持模版自定义!',
//     'AI生成PPT能力升级，真正支持模版自定义!1',
//     'AI生成PPT能力升级，真正支持模版自定义!12',
// ]
const { public: { apiBase } } = useRuntimeConfig()
const { data: asyncData } = useAsyncData<ResponsePagination<NewsInfo>>('home_top_bar', async () => {
    try {
        const controller = new AbortController()
        const timeout = setTimeout(() => controller.abort(), 8000) // 8秒超时

        const res = await $fetch<ResponsePagination<NewsInfo>>(
            `${apiBase}/news/list?platform=${getPlatformNew()}`,
            {
                signal: controller.signal,
                params: {
                    publishDate: '2025-07-01',
                }
            }
        )
        clearTimeout(timeout)
        return res
    } catch (error) {
        console.error('获取首页数据失败:', error)
        // 返回结构需满足 ResponsePagination<NewsInfo>
        return {
            success: false,
            code: null,
            message: (error as Error).message || '请求失败',
            result: {
                records: [],
                current: 1,
                size: 0,
                total: 0,
                pages: 0
            }
        }
    }
}, {
    server: true,
    lazy: false,
    immediate: true,
    default: () => ({
        success: false,
        code: null,
        message: '',
        result: {
            records: [],
            current: 1,
            size: 0,
            total: 0,
            pages: 0
        }
    })
})

const newsList = computed(() => {
    const data = asyncData.value as any
    if (data && data?.result && data?.result?.records) {
        // 假设 result 是一个包含 list 的分页结构，根据实际类型调整
        return data?.result.records || []
    }
    return []
})
</script>