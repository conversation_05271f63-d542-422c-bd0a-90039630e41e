<template>
  <!-- 添加外层边框 -->
  <div class="my-2 h-full pb-10 flex flex-col">
    <div class="flex items-center py-2">
      <div class="w-7 h-7 rounded-lg flex items-center justify-center mr-2">
        <brain theme="outline" size="18" class="text-blue-800" />
      </div>
      <h2 class="text-lg font-medium text-transparent bg-gradient-to-r from-blue-700 to-indigo-700 bg-clip-text">
        我的知识库</h2>
    </div>
    <div class="rounded-xl p-4 border border-blue-100 flex-1 bg-white">
      <!-- 主标题区域 -->
      <div class="group mb-6">
        <div
          class="bg-gradient-to-br to-[#5b69e5] from-[#7fa9ff] rounded-xl py-4 text-center space-y-3 flex flex-col items-center">
          <div class="flex justify-center">
            <div
              class="w-16 h-16 bg-white/10 backdrop-blur rounded-full flex items-center justify-center transform group-hover:scale-105 transition-all">
              <brain theme="outline" size="36" fill="#ffffff" />
            </div>
          </div>

          <h2 class="text-lg font-bold text-white tracking-wide">
            搭建你的AI外脑
          </h2>
          <p class="text-blue-100 text-sm">文档越多，提问写作越准确</p>

          <button class="bg-white/95 text-blue-600 px-8 py-1 rounded-full font-medium 
            hover:bg-blue-50 hover:shadow-lg hover:scale-105 
            transition-all duration-300 shadow-md text-sm" @click="handleAddFile">
            添加文件
          </button>
        </div>
      </div>

      <!-- 待完成任务列表 -->
      <div class="space-y-3">
        <TaskItem v-for="task in tasks" :key="task.step" :task="task" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useLibraryUploadStore } from '@/stores/libraryUpload';
import { useUserStore } from '@/stores/user';
import { Brain } from '@icon-park/vue-next';
import { useRouter } from 'vue-router';
import { UserService } from '~/services/user';

const libraryUploadStore = useLibraryUploadStore()


const router = useRouter()
const handleAddFile = () => {
  libraryUploadStore.setShowUploadModal(true)
  router.push('library')
}
const isLogined = ref(false)
const tasks = ref([
  {
    title: '上传文档',
    description: '深度文档学习，阅读整理翻译更轻松',
    step: 1,
    done: '',
    save: '',
  },
  {
    title: '向文档提问',
    description: '知识库信息检索，回答更可靠',
    step: 2,
    done: '',
    save: ''
  },
  {
    title: '基于文档写作',
    description: '知识库相关内容素材，写作时自动引用',
    step: 3,
    done: '',
    save: ''
  }
])

const setupData = () => {
  const usedChat = UserService.getKnowledgeAssistantMemberInfo()?.usedChat || 0
  const repoFileCount = UserService.getCurrentLoginInfo()?.repoFileCount || 0
  const submissionCount = UserService.getCurrentLoginInfo()?.submissionCount || 0
  tasks.value = [
    {
      title: '上传文档',
      description: '深度文档学习，阅读整理翻译更轻松',
      step: 1,
      done: repoFileCount ? `已学习文档${repoFileCount}篇` : '',
      save: repoFileCount ? `帮你节约${repoFileCount}小时` : '',
    },
    {
      title: '向文档提问',
      description: '知识库信息检索，回答更可靠',
      step: 2,
      done: usedChat ? `已提问${getLearningWordCount(usedChat)}次` : '',
      save: usedChat ? `帮你节约${usedChat * 0.5}小时` : ''
    },
    {
      title: '基于文档写作',
      description: '知识库相关内容素材，写作时自动引用',
      step: 3,
      done: submissionCount ? `已写作${submissionCount}次` : '',
      save: submissionCount ? `帮你节约${submissionCount * 5}小时` : ''
    }
  ]
}
const user = useUserStore()
watch(() => user.isLogined, async (_isLogined) => {
  if (_isLogined) {
    console.log('用户登录成功，重新加载数据')
    isLogined.value = _isLogined
    setupData()
  }
})

onMounted(() => {
  setTimeout(() => {
    setupData()
  }, 300)
})

</script>