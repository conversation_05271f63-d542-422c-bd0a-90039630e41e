<template>
  <div class="bg-blue-50/50 border border-gray-100 rounded-xl p-3 sm:p-5 hover:shadow-sm transition-shadow">
    <div class="flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-4">
      <div
        class="w-6 h-6 sm:w-7 sm:h-7 rounded-full bg-blue-50 flex items-center justify-center text-sm sm:text-base font-medium text-blue-600">
        {{ task.step }}
      </div>

      <div class="flex-1">
        <div class="flex items-center gap-2 mb-1">
          <h3 class="text-sm font-medium text-gray-600">{{ task.title }}</h3>
        </div>
        <p class="text-sm text-gray-500">{{ task.description }}</p>

        <div class="mt-2 sm:mt-3 flex flex-wrap items-center gap-2 sm:gap-4">
          <div class="flex items-center gap-1" v-if="task.done">
            <Icon name="material-symbols:description-outline" class="text-gray-400 text-base sm:text-lg" />
            <span class="text-xs  text-gray-500">{{ task.done }}</span>
          </div>
          <div class="flex items-center gap-1" v-if="task.save">
            <Icon name="material-symbols:timer-outline" class="text-gray-400 text-base sm:text-lg" />
            <span class="text-xs  text-gray-500">{{ task.save }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
defineProps<{
  task: {
    title: string
    description: string
    step: number
    done: string
    save: string

  }
}>()
</script>