<template>
  <NuxtLink :to="to" :title="altFn(code, title)"
    class="block p-3 transition-all duration-200 border cursor-pointer bg-white/80 backdrop-blur-sm rounded-xl sm:p-4 hover:border-blue-300/50 hover:shadow-lg group border-blue-200/50">
    <!-- 垂直模式-->
    <div v-if="layout === 'vertical'" class="flex flex-col items-center justify-center h-full gap-3">
      <div class="w-[55px]  aspect-square rounded-lg 
                  flex items-center justify-center shrink-0 overflow-hidden mt-1
                 ">
        <img :src="icon" :alt="altFn(code, title)" class="object-cover w-full h-full" />
      </div>

      <h3 class="text-sm text-gray-800 truncate transition-colors group-hover:text-blue-600">
        {{ title }}
      </h3>
    </div>

    <!-- 水平模式 -->
    <div v-else class="block py-4 sm:flex sm:items-start sm:gap-3">
      <div class="w-[50px] mx-auto aspect-square rounded-lg 
                  flex items-center justify-center shrink-0 overflow-hidden 
                  transition-transform group-hover:scale-105">
        <img :src="icon" :alt="altFn(code, title)" class="object-cover w-full h-full" />
      </div>

      <div class="flex-1 min-w-0 mt-2 text-center sm:text-left sm:mt-0">
        <h3 class="mb-1 text-base text-gray-800 truncate transition-colors group-hover:text-blue-600">
          {{ title }}
        </h3>
      </div>
    </div>

  </NuxtLink>
</template>

<script setup lang="ts">


const props = defineProps({
  icon: {
    type: String,
    default: 'https://static-1256600262.file.myqcloud.com/xzkc/icon/course-paper.png'
  },
  title: {
    type: String,
    required: true
  },
  desc: {
    type: String,
    default: ''
  },
  to: {
    type: String,
    required: true
  },
  // tag: {
  //   type: String,
  //   required: false
  // },
  layout: {
    type: String,
    required: false,
    default: 'default'
  },
  showFeatures: {
    type: Boolean,
    default: false
  },
  code: {
    type: String,
    default: ''
  }
})

const altFn = (code: string, title: string): string => {
  if (code === 'rewrite-document') {
    return '文档改写AI降重'
  }

  if (code === 'literature_review') {
    return `${title}AI`
  }

  const altCodes = [
    'paper', // 论文助手
    'thesis_report', // 论文开题报告
    'thesis_correction', //论文批改
    'proposal', // 论文大纲
    'book', // 万能小in AI智能写书软件
    'zhixie', // 论文致谢
    'task', // 论文任务书
    'paper_title', // 论文题目
    'abstract', // 论文摘要
    'paper_middle', // 论文中期报告
    'yiju', // 论文选题依据
  ]

  return altCodes.includes(code) ? `AI${title}` : title
}



</script>
