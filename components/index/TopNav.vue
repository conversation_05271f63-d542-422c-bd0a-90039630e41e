<template>
  <!-- 顶部栏 -->
  <div class="w-full bg-gradient-to-r from-blue-100 to-indigo-50 border-b border-gray-200">
    <div class="px-4 py-2">
      <!-- 桌面端和平板端布局 -->
      <div class="flex flex-wrap items-center justify-between gap-y-3">
        <!-- 第一行：会员信息 -->
        <div class="flex items-center space-x-3">
        </div>

        <!-- 右侧按钮组替换为组件 -->
        <!-- <UserInfoArea /> -->
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
</script>