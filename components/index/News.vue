<template>
  <div class="mb-5">
    <!-- 标题栏响应式调整 -->
    <div class="flex flex-wrap items-center mb-2 p-2 gap-2">
      <div class="flex items-center">
        <div
          class="w-7 h-7 backdrop-blur-sm rounded-lg flex items-center justify-center mr-2 group-hover:scale-110 transition-transform duration-300">
          <newspaper-folding theme="outline" size="18" class="text-blue-600" />
        </div>
        <h2 class="text-lg font-medium text-transparent bg-gradient-to-r from-blue-500 to-blue-800 bg-clip-text">
          资讯导读
        </h2>
      </div>

      <div class="flex items-center px-3 py-1 bg-blue-50 rounded-full">
        <calendar theme="outline" size="14" class="text-blue-600 mr-1" />
        <span class="text-sm text-blue-600">2024-12-14</span>
      </div>

      <!-- 响应式调整按钮组 -->
      <div class="flex items-center gap-3 ml-auto">
        <a href="#" class="text-sm text-blue-600 hover:text-blue-700 transition-colors hidden sm:inline-block">更多资讯导读</a>
        <button @click="handlePressCustomize"
          class="flex font-medium items-center px-3 py-1 text-sm text-blue-800 hover:text-blue-700 border border-blue-200 hover:border-blue-300 rounded-full transition-colors bg-gradient-to-r from-blue-50 to-indigo-50">
          <International theme="outline" size="14" class="mr-1" />
          <span class="hidden sm:inline">个性化订阅</span>
          <span class="sm:hidden">订阅</span>
        </button>
      </div>
    </div>

    <!-- 新闻卡片网格响应式布局 -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div v-for="(news, index) in newsArray" :key="index"
        class="bg-white rounded-lg overflow-hidden border border-blue-100 hover:shadow-md transition-shadow flex flex-col h-full">
        <div class="p-4 flex flex-col flex-grow">
          <!-- 标题区域响应式调整 -->
          <div class="px-3 flex flex-col sm:flex-row sm:items-center sm:justify-between mb-3 gap-2">
            <h3 class="text-lg font-medium text-gray-800 line-clamp-2 hover:text-blue-600 transition-colors">
              {{ news.title }}
            </h3>
            <span class="text-sm text-gray-500 sm:ml-4 whitespace-nowrap">{{ news.time }}</span>
          </div>

          <!-- 内容区域响应式调整 -->
          <div class="flex flex-col lg:flex-row gap-4 flex-grow">
            <!-- 导读列 -->
            <div class="flex-1">
              <p class="text-sm text-gray-600 leading-relaxed first:mt-0 first:ml-2 first:text-indent-2 line-clamp-3">
                <span
                  class="inline-block bg-blue-50 px-1 py-0.5 text-xs font-medium text-blue-600 mr-1 rounded-full">导读</span>
                {{ news.intro }}
              </p>
            </div>
            <!-- 要点列 -->
            <div class="flex-1">
              <div class="space-y-1">
                <div v-for="(point, pointIndex) in news.points.slice(0, 3)" :key="pointIndex"
                  class="flex items-center text-sm text-gray-600">
                  <div class="w-1.5 h-1.5 rounded-full bg-blue-400 mr-2"></div>
                  <span class="line-clamp-1">{{ point }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 操作按钮区域响应式调整 -->
          <div
            class="flex flex-col sm:flex-row sm:items-center sm:justify-between mt-3 pt-3 border-t border-gray-100 gap-2">
            <div class="flex items-center space-x-2">
              <button v-if="news.tags && news.tags.length"
                class="inline-flex items-center text-xs px-2 py-1 bg-blue-50 text-blue-600 rounded-full">
                <tag theme="outline" size="12" class="mr-1" />
                {{ news.tags[0] }}
              </button>
            </div>
            <div class="flex items-center space-x-1">
              <button
                class="text-sm text-gray-600 hover:text-blue-600 px-2 py-1 hover:bg-gray-50 rounded transition-colors">
                查看
              </button>
              <button class="p-1.5 hover:bg-gray-50 rounded-full transition-colors hidden sm:block" title="复制链接">
                <copy theme="outline" size="16" class="text-gray-500" />
              </button>
              <button class="p-1.5 hover:bg-gray-50 rounded-full transition-colors hidden sm:block" title="分享">
                <share theme="outline" size="16" class="text-gray-500" />
              </button>
              <button class="p-1.5 hover:bg-gray-50 rounded-full transition-colors" title="添加到知识库">
                <folder-plus theme="outline" size="16" class="text-gray-500" />
              </button>
              <a :href="news.url" target="_blank" class="p-1.5 hover:bg-gray-50 rounded-full transition-colors"
                title="查看原文">
                <link-one theme="outline" size="16" class="text-gray-500" />
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>

    <subscribe-modal v-model="showSubscribeModal" @save="handleSubscribe" />
  </div>
</template>

<script setup lang="ts">
import { useUserStore } from '@/stores/user';
import {
  Calendar,
  Copy,
  FolderPlus,
  International,
  LinkOne,
  NewspaperFolding,
  Share,
  Tag
} from '@icon-park/vue-next';
import { ref } from 'vue';
import SubscribeModal from '../News/SubscribeModal.vue';

// 控制订阅弹窗显示
const showSubscribeModal = ref(false)

// 处理订阅保存
const handleSubscribe = (subscriptionData) => {
  console.log('订阅设置：', subscriptionData)
  // TODO: 处理订阅数据，可能需要调用API保存到后端
  // subscriptionData 结构:
  // {
  //   type: 'knowledge' | 'tags',
  //   tags: string[] // 当 type 为 'tags' 时包含选中的标签
  // }
}

const newsArray = [
  {
    title: "AI在医疗影像诊断中取得重大突破",
    time: "2024-12-14 10:30",
    image: "https://picsum.photos/800/400?random=1",
    intro: "人工智能技术在医疗影像识别领域实现新突破，准确率达到前所未有的水平。这项突破性进展不仅大大提高了诊断效率，还显著降低了误诊率。研究团队表示，该技术已在多家三甲医院进行了临床试验，效果显著。",
    points: ["诊断准确率提升至98%", "处理速度提高10倍", "可识别早期病变", "支持多模态图像分析", "实时诊断反馈", "自动生成诊断报告"],
    tags: ["AI医疗"],
    url: "#"
  },
  {
    title: "量子计算研究获得重要进展",
    time: "2024-12-14 09:45",
    intro: "科学家成功实现了新的量子比特稳定性突破。",
    image: "https://picsum.photos/800/400?random=2",
    points: ["量子比特稳定性显著提升", "错误修正能力增强", "运算能力大幅提高", "成本降低40%"],
    tags: ["量子计算"],
    url: "#"
  }
]

const handlePressCustomize = () => {
  const userStore = useUserStore()
  if (!userStore.isLogined) {
    userStore.openLoginModal()
    return
  }
  showSubscribeModal.value = true
}
</script>
