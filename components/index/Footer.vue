<template>
  <div class="mt-auto">
    <div class="px-6 py-3">
      <div class="text-center text-gray-400 ">
        <div class="flex flex-wrap items-center justify-center text-xs gap-x-4 gap-y-2">

          <div class="space-x-4">
            <router-link to="/manual" class="transition-colors hover:text-blue-700">写作宝典</router-link>
            <template v-if="isLoggedIn">
              <router-link to="/profile/about">关于我们</router-link>
            </template>
            <a v-else :href="aboutUrl" target="_blank" rel="nofollow"
              class="transition-colors hover:text-blue-700">关于我们</a>
            <a :href="userPrivacy" target="_blank" rel="nofollow" class="transition-colors hover:text-blue-700">隐私政策</a>
            <a :href="userAgreement" target="_blank" rel="nofollow"
              class="transition-colors hover:text-blue-700">用户协议</a>
          </div>
          <span class="text-gray-200">|</span>
          <template v-if="isXiaoin">
            <a href="https://beian.miit.gov.cn/" target="_blank" rel="nofollow">沪ICP备2024049133号-1</a>
            <a href="https://www.beian.gov.cn/portal/registerSystemInfo?recordcode=31010402333814" target="_blank"
              rel="nofollow">
              沪公网安备：31010402333814号
            </a>
          </template>
          <template v-else>
            <a href="https://beian.miit.gov.cn/" target="_blank" rel="nofollow">沪ICP备20022513号-6</a>
            <a href="https://www.beian.gov.cn/portal/registerSystemInfo?recordcode=31010402333815" target="_blank"
              rel="nofollow">
              沪公网安备：31010402333815号
            </a>
            <div class="space-x-4">
              <span>网信算备：310115124334401240013号</span>
              <span>上线编号：Shanghai-WanNengXiaoin-20240829S0025</span>
              <span>备案号：Shanghai-Xiaoin-202502050038</span>
            </div>
          </template>
          <!-- <span>B</span> -->
          <div class="hidden ">{{ url }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { userAgreement, userPrivacy } from '@/utils/constants';
import { isXiaoinNew } from '@/utils/utils';
import { UserService } from '~/services/user';
const isLoggedIn = ref(false)
const url = useRequestURL().href;
const isXiaoin = isXiaoinNew(url)
// const aboutUrl = isXiaoin ? 'https://xiaoin.cn/about.html' : 'https://www.xiaoin.com.cn/about'
const aboutUrl = '/about'
onMounted(() => {
  isLoggedIn.value = UserService.isLogined()
})
</script>