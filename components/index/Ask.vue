<template>
    <!-- 提问组件 -->
    <div class="mb-5">
        <div class="flex items-center justify-end mb-2" v-if="isLogined">
            <div
                class=" h-11 px-3 bg-gradient-to-r from-[#E7E6FF] to-[#ECF6FC rounded-lg border border-[#CBDBFF] shadow-sm flex items-center justify-center">
                <Iconfont name="wodezhishiku1" :size="15"></Iconfont>
                <span class="px-2 text-base text-gray-800">知识库</span>
                <h2 class="text-sm text-gray-500">
                    {{ getCurrentUserSpaceUsedSize }}/{{ getCurrentUserSpaceTotalSize }}
                </h2>
            </div>

        </div>
        <!-- 提问框卡片 -->
        <AiQuestionTextarea v-model:question="question" v-model:questionMode="questionModeValue" :is-asking="false"
            :is-home="true" :is-knowledge="false" ref="aiQuestionTextareaRef" @submit="onSubmit"></AiQuestionTextarea>
    </div>

</template>
<script setup lang="ts">
import Iconfont from '@/components/Iconfont.vue';
import { useChatStore } from '@/stores/chat';
import { QuestionTypeEnum } from '@/utils/constants';
import { formatStorageSize, getCurrentTimestampString } from '@/utils/utils';
import { message } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import { newSessionId } from '~/api/appMessage';
import AiQuestionTextarea from '~/components/Chat/AiQuestionTextarea.vue';
import { useSpaceStore } from '~/stores/space';

// const { $eventBus } = useNuxtApp();
const router = useRouter()

const question = ref('')
const questionModeValue = ref([QuestionTypeEnum.useR1, QuestionTypeEnum.useSearch])
const chat = useChatStore()

const spaceStore = useSpaceStore()
const { spaceUsedBytes, spaceQuotaBytes } = storeToRefs(spaceStore)

const getCurrentUserSpaceUsedSize = computed(() => {
    return formatStorageSize(spaceUsedBytes.value)
})

const getCurrentUserSpaceTotalSize = computed(() => {
    return formatStorageSize(spaceQuotaBytes.value, 0)
})

const onSubmit = async (questionFileData: any) => {
    let params: any = {
        question: question.value,
        mode: questionModeValue.value,
        isHome: true
    }
    if (questionFileData) {
        params['fileId'] = questionFileData.id
        params['fileUrl'] = questionFileData.fileUrl
    } else {
        if (!params.question) {
            message.error('请输入问题')
            return
        }
    }
    const res = await newSessionId()
    if (!res.success) {
        return
    }
    const sessionId = res?.data || getCurrentTimestampString()
    params['sessionId'] = sessionId
    chat.setIsNewSessionId(true)
    chat.setHomeChatQestionData(params)
    setTimeout(() => {
        router.push({
            path: `/chat/${sessionId}`,
        })
    }, 200);
}
onMounted(() => {
    spaceStore.loadSpaceInfo()
})
const user = useUserStore()
const { isLogined } = storeToRefs(user)

watch(() => user.isLogined, async (_isLogined) => {
    spaceStore.loadSpaceInfo()
})
</script>