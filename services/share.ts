import { getChannelId } from '@/api/user'
import { useChannelStore } from '@/stores/channelId'
import { useUserStore } from '@/stores/user'
import { channelType, mobileSessionKey, wechatAppAppIdCJ } from '@/utils/constants'
import { storage } from '@/utils/local-storage'
import { sessionStore } from '@/utils/session-storage'
import { StarloveConstants } from '@/utils/starloveConstants'
import { StarloveUtil } from '@/utils/util'
import { useRoute } from 'vue-router'
import { UserService } from './user'

const ShareService = {
  removeAnythingOlderThanOneDaySharer: () => {
    const channelStore = useChannelStore()
    const dataTime = storage.get(StarloveConstants.keyOflocalStorage.sharerUserIdUpdateTime)
    if (!dataTime) {
      return
    }
    const sharerUserId = storage.get(StarloveConstants.keyOflocalStorage.sharerUserId)
    if (!sharerUserId) {
      return
    }
    if (StarloveUtil.isEmptyString(sharerUserId)) {
      return
    }
    const now = new Date().getTime()
    // 根据环境区分超时时间: 测试环境使用1分钟(60000毫秒)，生产环境使用1天(86400000毫秒)
    // const timeoutDuration = process.env.DEPLOY_ENV !== 'prod' ? 60000 : 86400000
    if (now - dataTime > 86400000) {
      // if (now - dataTime > 1000 * 2 * 60) {
      channelStore.remove() // 删除 判断的渠道
      storage.remove(StarloveConstants.keyOflocalStorage.sharerUserId)
      storage.remove(StarloveConstants.keyOflocalStorage.sharerUserIdUpdateTime)
    }
  },

  saveSharer: () => {
    const sharerUserId = StarloveUtil.getSharerUserIdFromUrl()
    ShareService.saveSharerWithSharerUserId(sharerUserId)
  },

  saveSharerWithSharerUserId: (sharerUserId: string | undefined) => {
    if (!sharerUserId) {
      return
    }
    if (StarloveUtil.isEmptyString(sharerUserId)) {
      return
    }
    if (UserService.getSharerUserId() == sharerUserId) {
      return
    }
    storage.set(StarloveConstants.keyOflocalStorage.sharerUserIdUpdateTime, new Date().getTime())
    storage.set(StarloveConstants.keyOflocalStorage.sharerUserId, sharerUserId)
  },

  saveChannel: () => {
    const store = useUserStore()
    // const href = window.location.href
    // lsg 保存时，检查是否包含/pdf-preview， 如果是，则截取50个， 后端太长了报错了
    // if (href.indexOf('/pdf-preview') > 0 || href.indexOf('/file-preview') > 0) {
    //   store.openRecordUrl = href.slice(0, 50)
    // } else {
    //   store.openRecordUrl = href
    // }
    const channelInfo = StarloveUtil.getChannelInfoFromUrl()
    if (!channelInfo) {
      return
    }
    if (StarloveUtil.isEmptyString(channelInfo)) {
      return
    }
    store.channel = channelInfo || ''
    storage.set(StarloveConstants.keyOflocalStorage.channelInfo, channelInfo)
    // console.log('saveChannel ==>', channelInfo)
  },

  saveClickId: () => {
    const store = useUserStore()
    // 如果链接中含有bd_vid就保存并传给后台
    const clickId = StarloveUtil.getClickIdFromUrl()
    // console.log('saveClickId ==>', clickId)
    if (!clickId) {
      return
    }
    if (StarloveUtil.isEmptyString(clickId)) {
      return
    }
    store.clickId = clickId || ''
    storage.set(StarloveConstants.keyOflocalStorage.clickId, clickId)
  },

  // 用于 bilibili 渠道
  saveTrackId: () => {
    const store = useUserStore()
    // 如果链接中含有bd_vid就保存并传给后台
    const trackId = StarloveUtil.getTrackIdFromUrl()
    // console.log('saveClickId ==>', clickId)
    if (!trackId) {
      return
    }
    if (StarloveUtil.isEmptyString(trackId)) {
      return
    }
    store.track_id = trackId || ''
    storage.set(StarloveConstants.keyOflocalStorage.trackId, trackId)
  },

  save360QhclickId: () => {
    const store = useUserStore()
    const qhclickId = StarloveUtil.getQhclickIdFromUrl()
    if (!qhclickId) {
      return
    }
    if (StarloveUtil.isEmptyString(qhclickId)) {
      return
    }
    store.qhclickId = qhclickId || ''
    storage.set(StarloveConstants.keyOflocalStorage.qhclickId, qhclickId)
  },

  save360SourceId: () => {
    const store = useUserStore()
    const sourceId = StarloveUtil.getSourceIdFromUrl()
    if (!sourceId) {
      return
    }
    if (StarloveUtil.isEmptyString(sourceId)) {
      return
    }
    store.sourceId = sourceId || ''
    storage.set(StarloveConstants.keyOflocalStorage.sourceId, sourceId)
  },

  getShareTimelineInRedirectUrl: () => {
    // const redirectUrl = StarloveUtil.getSharerTimelineFromUrl()
    // // console.log('getShareTimelineInRedirectUrl redirectUrl', redirectUrl)
    // if (!redirectUrl) {
    //   return
    // }
    // const newUrl = decodeURIComponent(redirectUrl)
    // // console.log('getShareTimelineInRedirectUrl newUrl', newUrl)
    // if (newUrl) {
    //   // const linkParams = newUrl.replace('/pages/xiaoin/index/index?', '')
    //   const linkParams = newUrl.replace('/home/<USER>', '')
    //   const params = qs.parse(linkParams)
    //   // console.log('getShareTimelineInRedirectUrl params', params)
    //   if (!params.sharerUserId || !params.sharerUserId.toString()) {
    //     return
    //   }
    //   if (StarloveUtil.isEmptyString(params.sharerUserId.toString())) {
    //     ShareService.saveSharerWithSharerUserId(params.sharerUserId.toString())
    //   }
    // }
  },

  buildQrCodeUrl: (params: any): string => {
    // params['envVersion'] = NativeUtil.getEnvVersion()
    const qrcodeQuery = StarloveUtil.convertObjectToParams(params)
    // ${process.env.VUE_TRANSFER_API_BASE_URL}
    const qrcodeImgUrl = `https://stellar.starringshop.com/wechat/ma/${wechatAppAppIdCJ}/createWxaCodeUnlimit?${qrcodeQuery}`
    // console.log('qrcodeImgUrl ==>', qrcodeImgUrl)
    return qrcodeImgUrl
  },
  saveUtmSourceLenovo: () => {
    const utm_source = StarloveUtil.getUtmSourceFromUrl()
    if (!utm_source) {
      return ''
    }
    if (StarloveUtil.isEmptyString(utm_source)) {
      return ''
    }
    sessionStorage.setItem('utm_source', utm_source)
    return utm_source
  },
  saveUtmPhoneThunderobot: () => {
    const phone = StarloveUtil.getPhoneFromUrl()
    if (!phone) {
      return ''
    }
    if (StarloveUtil.isEmptyString(phone)) {
      return ''
    }
    sessionStorage.setItem(mobileSessionKey, phone)
    return phone
  },

  // 计算 channelId
  getChannelIdByUserInfo: async () => {
    const store = useUserStore()
    const channelStore = useChannelStore()
    const route = useRoute();
    const currentPath = route.path;

    // 处理 bilibili 渠道
    if (route.query.channel == 'bilibili' && route.query.track_id) {
      // 获取 channelId
      const res = await getChannelId({
        channel: route.query.channel || 'bilibili',
      })
      if (!res || !res.ok || !res.data) {
        return
      }

      const channelData = {
        channelId: res.data.toString(), // 未知
        channel: route.query.channel || 'bilibili',
        referrer: document.referrer || "",
      };
      sessionStore.set(StarloveConstants.keyOfsessionStorage.sessionChannelData, JSON.stringify(channelData))
      storage.set(StarloveConstants.keyOflocalStorage.localChannelData, JSON.stringify(channelData))

      // 只更新 channelId
      channelStore.channelId = res.data
      return
    }


    // 获取 sharerUserId
    const sharerUserId = storage.get(StarloveConstants.keyOflocalStorage.sharerUserId)

    // 如果没有 sharerUserId 或者 sharerUserId 为 "0"，使用前端计算
    if (!sharerUserId || sharerUserId == '0' || StarloveUtil.isEmptyString(sharerUserId)) {
      // 先检查会话存储中是否有 channelData
      let sessionChannelData = sessionStore.get(StarloveConstants.keyOfsessionStorage.sessionChannelData);
      let channelData = sessionChannelData ? JSON.parse(sessionChannelData) : null;

      // 如果会话存储中没有 channelData，说明是新开页面，需要计算新的 channelData
      if (!channelData) {
        // 获取本地存储中的 channelData
        const localChannelData = storage.get(StarloveConstants.keyOflocalStorage.localChannelData);
        const storedChannelData = localChannelData ? JSON.parse(localChannelData) : null;

        // 如果是来自我们自己网站的跳转，不生成新的 channelData
        const isInternalJump = document.referrer.includes('xiaoin');

        if (isInternalJump && storedChannelData) {
          // 尝试复用本地存储中的 channelData
          channelData = storedChannelData;
          if (channelData.channelId && channelData.channel) {
            channelStore.channelId = Number(channelData.channelId)
            store.setChannel(channelData.channel)
            return
          }
        }

        // 计算新的 channelId 和 channel 和 referrer
        const newChannelId = channelStore.calculateChannelId(currentPath);
        const newChannel = currentPath == '/' ? 'comcn-home' : `comcn${currentPath.replace(/\//g, '-')}`;
        const newReferrer = document.referrer || "";

        // 特殊处理：如果是从收藏夹打开（document.referrer 为空）且本地存储有值
        if (!document.referrer && storedChannelData) {
          // 如果新计算的值为自然流量（3），则使用本地存储的值
          if (newChannelId == channelType.ORGANIC) {
            channelData = storedChannelData;
          } else {
            // 否则使用新计算的值
            channelData = {
              channelId: newChannelId.toString(),
              channel: newChannel,
              referrer: newReferrer
            };
          }
        } else {
          // 其他情况使用新计算的值
          channelData = {
            channelId: newChannelId.toString(),
            channel: newChannel,
            referrer: newReferrer
          };
        }

        // 存储到会话存储中，用于判断是否是刷新
        sessionStore.set(StarloveConstants.keyOfsessionStorage.sessionChannelData, JSON.stringify(channelData))
        // 同时存储到本地存储中，用于持久化
        storage.set(StarloveConstants.keyOflocalStorage.localChannelData, JSON.stringify(channelData))
      } else {
        // 如果会话存储中有 channelData，说明是刷新页面，不需要重新计算
        // 但是需要确保本地存储中也有这个值
        storage.set(StarloveConstants.keyOflocalStorage.localChannelData, JSON.stringify(channelData))
      }

      // 更新应用状态
      channelStore.channelId = Number(channelData.channelId)
      store.setChannel(channelData.channel)
      return
    }

    // 如果有 sharerUserId，使用后端计算
    const res = await getChannelId({
      channel: store.channel,
      inviteUserId: UserService.getSharerUserId(),
    })
    if (!res || !res.ok) {
      return
    }
    if (!res.data) {
      return
    }

    // 创建 channelData 对象，但不计算新的 channel，保留 store 中的现有 channel
    const channelData = {
      channelId: res.data.toString(),
      channel: store.channel || "wangye",
      referrer: document.referrer || ""
    };

    // 将后端返回的 channelData 存储到会话存储和本地存储中
    sessionStore.set(StarloveConstants.keyOfsessionStorage.sessionChannelData, JSON.stringify(channelData))
    storage.set(StarloveConstants.keyOflocalStorage.localChannelData, JSON.stringify(channelData))

    // 只更新 channelId
    channelStore.channelId = res.data
  }
}

export { ShareService }

