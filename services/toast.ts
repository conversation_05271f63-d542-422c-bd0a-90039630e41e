import { message } from 'ant-design-vue'

const ToastService = {
  hide: null as any,
  loading(text: string = '') {
    this.hide = message.loading(text || '加载...', 0)
  },
  hideLoading() {
    if (this.hide) {
      this.hide()
    }
  },
  success(text: string) {
    if (this.hide) {
      this.hide()
    }
    this.hide = message.success(text)
  },
  error(text: string) {
    if (this.hide) {
      this.hide()
    }
    this.hide = message.error(text)
  },
  warn(text: string) {
    if (this.hide) {
      this.hide()
    }
    this.hide = message.warn(text)
  },
  info(text: string) {
    if (this.hide) {
      this.hide()
    }
    this.hide = message.info(text)
  },
}
export { ToastService }
