export interface TeamInfo {
    id: string;
    createTime: Date;
    name: string;
    logoUrl: string;
    status: string;
    description: string;
    teamLevel: string;
    maxMembers: number;
    expiryDate: Date;
    space: Space;
    member: null;
}

export interface Space {
    id: string;
    spaceType: string;
    spaceQuotaBytes: number;
    spaceUsedBytes: number;
}

export interface TeamInviteInfo {
    teamId: string;
    nickName: string;
    userId: string;
    mobile: string;
    teamName: string;
    timestamp: number;
}


export interface TeamMemerInfo {
    id: string;
    teamId: string;
    userId: string;
    name: null;
    avatar: null;
    joinTime: Date;
    status: string;
    role: string;
    inviteTime: Date;
    inviteeMobile: string;
}

export interface TeamMemerInfo {
    id: string;
    teamId: string;
    userId: string;
    name: null;
    avatar: null;
    joinTime: Date;
    status: string;
    role: string;
    inviteTime: Date;
    inviteeMobile: string;
}
export interface InviteCodeInfo {
    teamId: string;
    teamName: string;
    teamLogo: string;
    inviteUserName: string;
}

