 export interface WPPost {
    id: number;
    title: {
        rendered: string;
    };
    content: {
        rendered: string;
    };
    date: string;
    categories: number[];
    meta: {
        views: any;
        custom_title?: string;
        custom_keywords?: string;
        custom_description?: string;
    };
    acf?: {
        custom_title?: string;
        custom_keywords?: string;
        custom_description?: string;
    };
    _links: Record<string, any>;
    _embedded: Record<string, any>;
}


export interface WPCategory {
    id: number;
    name: string;
    count: number;
    description: string;
    parent: number;
    meta: Record<string, any>;
    slug: string;
}

export interface WPTerm {
    id: number;
    name: string;
    parent: number;
}

export interface ViewData {
    [key: string]: string | number | string[] | undefined;
    _superfast_singular_meta?: string[];
}