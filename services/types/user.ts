
export interface TeamSpaceInfo {
    id: string
    spaceQuotaBytes: number
    spaceType: string
    spaceUsedBytes: number
}

export interface TeamMemberInfo {
    id: string
    teamId: string
    userId: string
    name: string
    inviteTime: string
    joinTime: string
    mobile: string
    role: string
    status: string
}
export interface TeamInfo {
    id: string
    name: string
    logoUrl: string
    status: string
    description: string
    teamLevel: string
    maxMembers: number
    createTime?: string
    expiryDate?: string
    isCurrent?: boolean
    space: TeamSpaceInfo
    member: TeamMemberInfo
}


export interface TeamCoinInfo {
    normal: number
    balance: number
    list: TeamCoinListInfo[]
}

export interface TeamCoinListInfo {
    type: number
    balance: number
    total: number
    createTime: string
    expireTime: string
}

export interface LoginThunderobotParams {
    mobile: string //加密手机号
    verifyCode: string //123456
    inviteUserId: string
    channelId: number // 17
}