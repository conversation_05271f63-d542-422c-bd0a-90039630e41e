export interface AttachmentsInfo {
  coinNum: number
  fileName: string
  fileUrl: string
  fileId: string
  url: string
  id: string
  status: string
  userId: string
  wordCount: number
  repositoryId?: string
  repositoryFileId?: string
}

export interface FormDataAttachmentsInfo {
  fileId: string
  url: string
  repositoryFileId?: string
}

export interface UploadFileInfo {
  uid: string
  webkitRelativePath: string
  name: string
  size: number
  type: string
  status: string
  percent: number
  response: {
    fileUrl?: string
    fileId?: string
  }
  url: string
}
