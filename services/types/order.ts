import { type AttachmentsInfo } from './file'

export interface SubmissionOrderInfo {
  id: string
  answer?: string
  createTime: string
  creatorCode: string
  creatorName: string
  creatorCoin: number
  extra: string
  failReason: string
  formData: SubmissionFormData
  isDeleted: string
  remark: string
  status: string
  tokens: number
  userId: string
  attachments?: AttachmentsInfo[]
  helpStatus?: number
  helpGotCount?: number
  helpNeedCount?: number
}

export interface SubmissionFormData {
  isPro: boolean
  keywords: string
  size: string
  topic: string
  attachments?: AttachmentsInfo[]
  params?: any
}

export interface RechargeOrderInfo {
  body: string
  createAte: string
  createTime: string
  id: string
  payment: number
  remark: string
  status: number
  status_dictText: string
  totalAmount: number
  tradeId: string
  userId: string
}

export interface ReceiverInfo {
  addressDetail: string
  cityCode: string
  cityName: string
  districtCode: string
  districtName: string
  extra: string
  id: string
  isDefault: string
  provinceCode: string
  provinceName: string
  receiverMobile: string
  receiverName: string
}

export interface ExpressInfo {
  expressCode: string
  expressCompanyLogo: string
  expressCompanyName: string
  logisticsStatus: string
  logisticsStatusDesc: string
  logisticsTraceDetails: LogisticsTraceDetails[]
  number: string
  theLastMessage: string
  theLastTime: string
}

export interface LogisticsTraceDetails {
  areaName: string
  desc: string
  logisticsStatus: string
  subLogisticsStatus: string
  time: string
}
