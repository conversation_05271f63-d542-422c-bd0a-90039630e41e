import type { ComponentRef, FileUploadRef, OutlineTextareaRef } from './create'

export type FileUploadComponent = ComponentRef<FileUploadRef>
export type OutlineTextareaComponent = ComponentRef<OutlineTextareaRef>

export interface FieldOutlineProps {
    fieldItem: {
        fieldCode: string
        fieldName: string
        fieldType: string
        isRequired: 'Y' | 'N'
        fieldValue: string
        defaultValue: string
        options: string
        description: string
        placeholder: string
        maxLength: number
        id: string
        isShowField: boolean
        againEditOutlineList: any[]
    }
    creatorData: any
    outlineType: string
}

export interface FieldUploadProps {
    fieldItem: {
        fieldCode: string
        fieldName: string
        fieldType: string
        isRequired: 'Y' | 'N'
        fieldValue: string
        defaultValue: string
        options: string
        description: string
        placeholder: string
        maxLength: number
        id: string
        isShowField: boolean
    }
    referencesList: any[]
    knowledgeUploadList: any[]
}

export interface FieldInputProps {
    fieldItem: {
        fieldCode: string
        fieldName: string
        fieldType: string
        isRequired: 'Y' | 'N'
        fieldValue: string
        defaultValue: string
        options: string
        description: string
        placeholder: string
        maxLength: number
        id: string
        isShowField: boolean
    }
    inputValue: string
}

export interface FieldSelectProps {
    fieldItem: {
        fieldCode: string
        fieldName: string
        fieldType: string
        isRequired: 'Y' | 'N'
        fieldValue: string
        defaultValue: string
        options: string
        description: string
        placeholder: string
        maxLength: number
        id: string
        isShowField: boolean
    }
    modelValue: string
} 