export interface LoginMobileRes {
  expiresTime: number
  isNeedMobileVerify: boolean
  isNewUser: boolean
  token: string
  userId: string
}
export interface LocalCacheUserInfo {
  token: string
}

export interface AppLoginResultInfo {
  token: string
  userId: string // 返回有值
  nickname?: string // 返回为null
  avatar: string
  account?: string // 返回为null
  coinBalance?: number
  vipLevel?: number
  isSubscribe?: boolean
}

export interface AppUserInfo {
  nickname: string
  avatar: string
  id?: string
  userId: string
  account?: string
  coinBalance?: number
  vipLevel?: number
  isSubscribe?: boolean
  isAsked?: boolean
  authenticationStatus?: string
  submissionCount?: number
  repoFileCount?: number
  channelId: number
  createTime: string
}

export interface ResponseUpdateUser {
  nickname: string
  avatar: string
}

export interface BindPhoneUserInfo {
  bindingUserInfo: AppUserInfo
  selfUserInfo: AppUserInfo
  bindingUserUseDetail: {
    coinBalance: number
    questionCount: number
    submissionCount: number
  }
  selfUseDetail: {
    coinBalance: number
    questionCount: number
    submissionCount: number
  }
}
export interface KnowledgeAssistantMemberInfo {
  userId: string
  vipLevel: number
  vipExpireTime: string
  usedWord: number
  maxWord: number
  maxChat: number
  usedChat: number
  freeAIOnlineEditing: number
  coinNum: number
  vipCode: string
  spaceQuotaBytes: number
  isVip: boolean
  maxScholarSearch: number
  usedScholarSearch: number
}

export interface TranslateListData {
  index: number
  translateStatus: number
  list: TranslateInfo[]
  status: number
}

export interface TranslateInfo {
  sourceIndex: string
  source: TranslateSourceInfo
  status: string
  content: string
}

export interface TranslateSourceInfo {
  id: string
  meta: TranslateSourceMateInfo
  content: string
}

export interface TranslateSourceMateInfo {
  page: number
  bbox: number[]
  ocr: boolean
  total_pages: number
  layout_type: string
}



export interface SendVerifyCodeResultInfo {
  captchaStatus: boolean,
  sendStatus: boolean,
  message: string
}

export interface BindMousseDeviceInfo {
  title: string
  content: string
  status: string
}


export interface AppContent {
  code: string
  title: string
  content: string
  htmlContent: string
}
