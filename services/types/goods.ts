export interface GoodsInfo {
  id: string
  isListing?: string
  picUrl?: string
  sellPoint?: string
  description?: string
  title?: string
  extraParams: GoodsInfoForExtraParams
  goodsGroupId?: string
  originPrice?: number
  price?: number
  discountDesc?: string
  type?: number
}

export interface GoodsInfoForExtraParams {
  coinNum: number
  validityDays: number
}

export interface CoinInfo {
  balance?: number
  normal?: number
  list?: CoinStaleInfo[]
}

export interface CoinStaleInfo {
  balance?: number
  expireTime?: string
  total?: number
  type?: number
}

interface GoodsInfoForKnowledgeExtraParams {
  vipLevel: number
  validityDays: number
}
export interface GoodsInfoForKnowledge {
  id: string
  isListing?: string
  picUrl?: string
  sellPoint?: string
  description?: any
  title?: string
  extraParams?: any
  goodsGroupId?: string
  originPrice: number
  price: number
  discountDesc?: string
  type?: number
  levelImg: string
}
