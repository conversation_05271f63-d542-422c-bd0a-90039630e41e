import type { AttachmentsInfo } from './file'

export enum SubmissionStatus {
  init = 'init',
  needPay = 'need_pay',
  payed = 'payed',
  ing = 'ing',
  done = 'done',
  error = 'error',
  wait = 'wait',
}

export interface SubmissionFormDataInfo {
  topic: string
  keywords?: string
  size: string
  language: string
  isPro: boolean
  image?: SubmissionFormDataImageInfo
  params?: SubmissionFormDataParamsInfo
  userOutline?: string
  attachments: AttachmentsInfo[]
  uploadAttachments: AttachmentsInfo[]
  creatorLearnCoin: number
  customSizeLength: number
}

export interface SubmissionFormDataParamsInfo {
  [key: string]: any
  ask?: string
  language?: string
  size?: string
  subject?: string
  grade?: string
  keywords?: string
  content?: string
  period?: string
  target?: string
  style?: string
  templateName?: string
}

export interface SubmissionFormDataImageInfo {
  imgUrl: string
  styleId: string
  type?: string
}

export interface SubmissionPptAnswerInfo {
  topic?: string
  title?: string
  pptxPath?: string
  pdfPath?: any
  pageCount?: number
  refs?: AnswerInfoRef[]
}

export interface SubmissionPaperAnswerInfo {
  topic: string
  outline?: string
  finalDocxPath?: string
  finalPdfPath?: string
  checkDocxPath?: string
  pptxPath?: string
  content?: string
  error?: string
  author_type?: string
  authorType?: string
  outlineTitleEn?: string
  outlineTitle?: string
  refs?: AnswerInfoRef[]
  version?: string
}

export interface SubmissionExtraInfo {
  stage: string
  lastMessage: string
}

export interface SubmissionInfo {
  id: string
  formData: SubmissionFormDataInfo
  attachments: AttachmentsInfo[]
  uploadAttachments: AttachmentsInfo[]
  tokens: number | undefined
  status: string
  stage: string
  answer: string
  extra: string
  creatorCode: string
  creatorCoin: number
  helpGotCount: number | undefined
  helpNeedCount: number | undefined
  helpStatus: number | undefined
  isEditable: boolean | undefined
  userId: string
}

export interface MagicAvatarInfo {
  id: string
  title: string
  imgUrl: string
  description: string
  sort: number
  model: string
  prompt: string
  negativePrompt: string
  params: string
  isListing: string
  isDeleted: string
}

export interface PPTTemplateInfo {
  fileName: string
  id: string
  imageUrls: string
  name: string
  sort: number
}

export interface AnswerInfoRef {
  name: string
  snippet: string
  url: string
}

export interface preparePayInfo {
  combos: combos[]
  submission: any
}

export interface combos {
  creatorCode: string
  creatorName: string
  tag: string
  prices: prices[]
  icon: string
  isSelected: boolean
  selectedSize: string
}

export interface prices {
  size: string
  tokens: number
  originTokens: number
  name: string
}