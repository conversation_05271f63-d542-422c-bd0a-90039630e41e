// 创建者字段基础信息
// export interface CreatorRequireFieldInfo {
//     fieldCode: string;
//     fieldName: string;
//     fieldType: string;
//     isRequired: 'Y' | 'N';
//     fieldValue?: string;
//     defaultValue?: string;
//     options?: string;
//     description?: string;
//     placeholder?: string;
//     maxLength?: number;
//     id?: string;
//     isShowField?: boolean;
//     againEditOutlineList?: any[];
// }

// 表单字段定义
// export interface FieldItem extends CreatorRequireFieldInfo { }

// 大纲字段类型
// export interface FieldOutlineItem extends FieldItem { }

// 选择项信息
// export interface SelectRequireInfo extends CreatorRequireFieldInfo { }

// 创建者信息

// 表单值


// 提交参数
export interface SubmitParams {
    creatorCode: string;
    formData: {
        topic: string;
        size: string;
        isUseLargeWorker: boolean;
        isPayFirst: boolean;
        params: {
            [key: string]: string;
            size: string;
        };
        userOutline?: string;
        customSizeLength?: number;
        attachments?: Array<{
            fileId: string;
            repositoryFileId?: string;
        }>;
    };
}

// 文件上传引用
export interface FileUploadRef {
    fileList: Array<{
        uid: string;
        name: string;
        fileName?: string;
        file?: File;
        fileUrl?: string;
    }>;
    selectedIds: string[];
}

// 大纲文本区域引用
export interface OutlineTextareaRef {
    value: string;
}

// Vue组件实例类型
export interface ComponentPublicInstance {
    $: any;
    $data: any;
    $props: any;
    $attrs: any;
    $refs: any;
    $slots: any;
    $root: any;
    $parent: any;
    $emit: any;
    $el: any;
    $options: any;
    $forceUpdate: () => void;
    $nextTick: (fn: () => void) => void;
    $watch: <T extends string | ((...args: any) => any) >(
        source: T,
        cb: T extends (...args: any) => infer R
            ? (args_0: R, args_1: R) => any
            : (args_0: any, args_1: any) => any,
        options?: any
    ) => () => void;
}

// 组件引用类型
export interface ComponentRef<T> extends ComponentPublicInstance {
    $props: T;
}

// 组件实例类型
export type ComponentInstance<T> = ComponentRef<T> | null;

// 组件引用函数类型
export type RefCallback<T> = (el: Element | ComponentPublicInstance | null, refs?: Record<string, any>) => void;

// 应用项目
export interface AppItem {
    name: string;
    avatar: string;
    code: string;
    description?: string;
    lastTime: string;
    content: string;
    mediaType: string;
    isPinned: string;
    isRead: string;
    type: string;
    buttonText: string;
}

// 首页数据项
export interface HomeDataItem {
    list: AppItem[];
    groupId: string;
}

// 搜索结果
export interface SearchResults {
    list: AppItem[];
    recommendList: AppItem[];
}

// API响应
export interface ApiResponse {
    success: boolean;
    code: number | null;
    result: Array<{
        id: string;
        title: string;
        items: AppItem[];
    }>;
    message: string;
}