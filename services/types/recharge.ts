export interface WebPayQrcode {
    linkUrl: string
    isQrCode: boolean
    isError: boolean
}


export interface CachedPayInfo {
    createTime: number
    payInfo: WebPayQrcode
    orderId: string
    amount: number
}


export interface RemainingTime {
    hours: string
    minutes: string
    seconds: string
}

export interface ExtraParams {
    platform: string
    openUrl?: string
    inviteUserId?: string
    inviteUserIdUpdateTime?: string
    channel?: string
    clickId?: string
    sourceId?: string
    referrer?: string
    [key: string]: any
}

export interface XiaoinGoodInfo {
    id: string
    price: number
    description: {
        coinNum: number
        [key: string]: any
    }
    extraParams: {
        vipLevel: number
        [key: string]: any
    }
    levelImg?: string,
    type: number,
    discountDesc?: string,
    sellPoint?: string,
    title: string,
    originPrice?: number,

}

