interface Params {
  pageIndex: number
  pageSize: number
}
export interface Result<T = unknown> {
  records: T[]
  current: number
  size: number
  total: number
  pages: number
  [x: string]: any
}

export interface Response<T = unknown> {

  code?: number
  ok?: boolean
  success?: boolean
  result?: T
  status: number
  statusText: string
  data?: T
  message?: string
  timestamp: number
  newToken?: string
}

export interface WxPageResponse<T = unknown> {
  code: number
  msg: string
  page: WxPage<T>
}
export interface WxPage<T = unknown> {
  totalCount: number
  pageSize: string
  totalPage: number
  currPage: number
  list: T[]
}

export interface ComposerMediaInfo {
  images: any[]
  video?: WxVideo
}

export interface WxVideo {
  id: number
  appid: string
  detail: WxVideoDetail
  createTime: string
}

export interface WxVideoDetail {
  thumbMediaId: string
  mediaId: string
}

export interface MomentsDailyHotRes<T = unknown> {
  code?: number
  ok: boolean
  result: Result
}

export interface ResponsePagination<T = unknown> {
  code?: number
  ok?: boolean
  // result?: Result<T>
  data?: Result<T>
  records?: T[]
  current: string
  size: number
  total: number
  pages: string
}

export interface ResponseList<T = unknown> {
  code?: number
  ok?: boolean
  result?: T[]
  data?: T[]
}
