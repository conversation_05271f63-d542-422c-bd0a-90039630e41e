import type { BasicPageParams } from './baseModel.js';

export type NewsListParams = BasicPageParams & {
    publishDate?: string
    [key: string]: any
}
export type NewsQueryByIdParams = {
    id?: string
}
export interface RepositoryFile {
    id: string
    createBy: string
    createTime: Date
    updateBy: null
    updateTime: null
    userId: string
    repositoryId: string
    fileType: string
    fileId: string
    fileName: string
    fileUrl: string
    parsedUrl: string
    wordCount: number
    processData: ProcessData
    editorData: string
    status: string
    progress: number
    name: string
}

export interface NewRepositoryFile {
    createTime: string;
    id: string;
    name: string;
    processData: ProcessData
    status: string;
    type: string;
    wordCount: number | null;
    progress: number;
    folderId: string;
    userId: string;
    createMember: string;
    fileSize: number;
}


export interface RepositoryFolder {
    name: string
    folderName: string
    folderPath: string
    folderType: string
    id: string
    parentId: string
    parentIds: string
    userId: string
    folderId: string
}

export interface Error {
    code: string
    message: string
}
export interface EditorData {
    title: string
    content: string
}

export interface ProcessData {
    title: string
    file_id: string
    inspiration_status: number
    inspiration_data: string
    parse_status: number
    summary_data: EditorData[]
    summary_status: number
    word_count: number
    green_status: number
    translate_status: number // 2完成3失败
    error: Error
    questions?: string[]
}
export interface NewsInfo {
    id: string
    createBy: string
    createTime: string
    updateBy: string
    updateTime: string
    title: string
    content: string
    publishDate: string
    type: string
    category: string
    sourceUrl: string
    isOpen: string
    isDeleted: string
}

export interface NewsTagInfo {
    id: string
    name: string
}

export interface QuestionFile {
    fileName: string
    fileUrl: string
    id: string
    status: string
    userId: string
}

export interface NewsItem {
    id: number
    title: string
    content: string
    createTime: string
    // 根据实际API返回数据添加其他必要字段
}
