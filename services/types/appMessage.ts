export interface AppMessageRecord {
  id: string
  createTime?: string
  isDeleted?: string
  lastContent: string
  lastUseTime: string
  sessionId?: string
  title?: string
  updateTime?: string
  userId: string
  isAsking?: boolean
  version: number
  folderIds?: string
}

export interface AppMessage {
  id: string
  userId?: string
  senderId: string
  mediaType: string
  content?: string
  questionId?: string
  status?: string
  isRead: string
  createTime?: string
  rating?: string
  isPro?: string
  remark?: string
  sessionId?: string
  type?: string
  extendInfo?: ExtendInfo
  refs?: MessageRefInfo[]
  think?: string
  understands?: string[]
  recommendApplyList?: AppCategory[]
  // avatar: string
  replyMessage?: any
}

export interface MessageRefInfo {
  event: string
  id: string
  content: []
  snippet: string
}
export interface ExtendInfo {
  itemList: string[]
  type: string
}

export interface AppExampleMessage {
  id: string
  answer: string
  category: string
  isListing: string
  question: string
  remark: string
  sort: number
  createTime?: string
  sysOrgCode?: string
  createBy?: string
  updateBy?: string
  updateTime?: string
}

export interface AppFavorite {
  id: string
  messageId: string
  messageContent: string
}

export interface AppContent {
  code: string
  title: string
  content: string
  htmlContent: string
}

export interface AppCategory {
  code: string
  name: string
  avatar: string
  description: string
  buttonText: string
  remark: string
  type: string
  isPro: string
  descriptionHtml?: string
}

export interface ConversationItemInfo {
  name: string
  avatar: string
  code: string
  lastTime: string
  content: string
  mediaType: string
  isPinned: string
  isRead: string
  type: string
  buttonText: string
  description?: string
}

export interface AppDiscoverCategory {
  code: string
  name: string
  avatar: string
  description: string
  buttonText: string
  remark: string
  type: string
  isPro: string
  createTime: string
  displayOrder: number
  groupId: string
  historyLen: number
  id: string
  isDefault: string
  isDeleted: string
  isMarket: string
  isNeedHistory: string
  isOpen: string
  isPinned: string
  systemPrompt: string
}

export interface CreatorsInfo {
  creator: CreatorsCategoryInfo
  details: CreatorRequireFieldInfo[]
  recommends: CreatorsInfoRecommends[]
}
export interface CreatorsSeoInfoConfig {
  title: string
  description: string
  keywords: string
}
export interface CreatorsCategoryInfo {
  code: string
  contentFieldLines: string
  contentFieldName: string
  contentFieldPlaceholder: string
  config?: CreatorsCategoryInfoConfig
  createBy: string
  createTime: string
  creatorType: string
  defaultAnswer: string
  defaultContent: string
  description: string
  isUseLargeWorker?: boolean
  descriptionHtml?: string
  icon: string
  id: string
  isOpen: string
  isDeleted: string
  name: string
  needSelectLength: string
  sysOrgCode: string
  systemPrompt: string
  updateBy: string
  updateTime: string
  displayOrder: number
  contentFieldDesc: string
  seoConfig?: CreatorsSeoInfoConfig
}
export interface CreatorsInfoRecommends {
  type: string
  lastTime: null
  code: string
  name: string
  avatar: string
  image: null
  description: string
  descriptionHtml: string
  searchKeyword: string
  isUseLargeWorker: boolean
}

export interface CreatorsCategoryInfoConfig {
  isPayFirst: boolean
  isUseLargeWorker: boolean
  hasPro: boolean
  priceNormal: CreatorsConfigPrice
  pricePro: CreatorsConfigPrice
}

export interface CreatorsConfigPrice {
  large: number
  middle: number
  small: number
}

export interface CreatorRequireFieldInfo {
  code: string
  createBy: string
  createTime: string
  defaultValue: string
  fieldCode: string
  fieldValue: string
  fieldName: string
  fieldType: string
  id: string
  options: string
  maxLength: number
  placeholder: string
  inputNotice: string
  isRequired: string
  sysOrgCode: string
  updateBy: string
  updateTime: string
  description: string
  contentFieldDesc: string
  isShowField: boolean
  nodeWordCount?: string
  againEditOutlineList?: any[]
}

export interface IndexDataInfo {
  currentQueryTime: string
  conversationList: ConversationItemInfo[]
}

export interface CosCredentialsInfo {
  tmpSecretId: string
  tmpSecretKey: string
  sessionToken: string
}
export interface CosSignOriginResponseInfo {
  credentials: CosCredentialsInfo
  expiration: string
  expiredTime: number
  requestId: string
  startTime: number
}
export interface CosSignResultInfo {
  bucket: string
  host: string
  key: string
  region: string
  response: CosSignOriginResponseInfo
}

export interface CosUploadParams {
  bucket: string
  region: string
  protocol: string
  key: string
}
export interface PicList {
  filename: string
  height: number
  id: string
  lcId: number
  url: string
  userId: number
  width: number
}

export interface OneMessageForShareUserInfo {
  avatar: string
  nickname: string
  id?: string
}
export interface OneMessageForShareInfo {
  askerUser: OneMessageForShareUserInfo
  category: AppCategory
  messageList: AppMessage[]
}

export interface DiscoverTabsInfo {
  createTime: string
  id: string
  isDeleted: string
  isOpen: string
  sort: number
  title: string
  createBy?: string
  updateBy?: string
  updateTime?: string
  type?: string
  categoryList?: DiscoverItemInfo[]
  creatorsList?: DiscoverItemInfo[]
}

export interface SelectRequireInfo {
  id?: string
  defaultValue?: string
  fieldName: string
  fieldType: string
  fieldCode: string
  fieldValue: string
  isRequired: string
  description: string
  placeholder: string
  maxLength: number
  isShowField?: boolean
  options: string
}

export interface SearchCategoryInfo {
  list: ConversationItemInfo[]
  recommendList: ConversationItemInfo[]
}

export interface LiteFormData { }

export interface DiscoverItemInfo {
  avatar: string
  code: string
  description: string
  isUseLargeWorker: boolean
  lastTime: string //值的形式 "2023-09-12 10:45:21",
  name: string
  type: string
}

export interface LastUseAppInfo {
  type: string
  name: string
  code: string
  avatar: string
  description: string
  isUseLargeWorker?: boolean
  lastTime: string //值的形式 "2023-09-12 10:45:21",
}

export interface Nodes {
  node_title: string
}

export interface Section {
  section_title: string
  nodes: Nodes[]
}

export interface Chapter {
  chapter_title: string
  sections: Section[]
}

export interface DescriptionInfo {
  description: string
  descriptionHtml: string
  icon: string
  name: string
}

export interface OssSignResultInfo {
  contentType: string
  url: string
}

export interface FormValues {
  [key: string]: any;
  outline?: string;
  size?: string;
  language?: string;
}