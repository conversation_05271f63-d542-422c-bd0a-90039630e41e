import { reportUseApp as reportUseAppToServer } from '@/api/appCategory'
import { bindMouse, openRecordAdd } from '@/api/user'
import { useTracking } from '@/composables/useTracking'
import { useVisitorIdStore } from '@/stores/visitorId'
import { storage } from '@/utils/local-storage'
import { StarloveConstants } from '@/utils/starloveConstants'
import { StarloveUtil } from '@/utils/util'
import { getPlatform } from '@/utils/utils'
import { getStoredDate, getTodayDate } from '@/utils/uvTracker'
import { Modal, message } from 'ant-design-vue'
import { h } from 'vue'
import { repositoryGetInfo } from '~/api/repositoryFile'
import type { AppLoginResultInfo } from '~/services/types/loginMobileRes'
import { useUserStore } from '~/stores/user'
import type { LastUseAppInfo } from './types/appMessage'


declare global {
  interface Window {
    aplus?: {
      uploadUserProfile: (data: string, callback: (res: any) => void) => void;
    }
  }
}

const UserService = {
  isLogined: (): boolean => {
    // 在客户端水合期间，优先使用 cookies 状态以避免闪烁
    if (import.meta.client) {
      try {
        const { CookieAuthManager } = require('~/utils/cookie-auth')
        const cookieLoginStatus = CookieAuthManager.getLoginStatus()

        // 如果 cookies 显示已登录，直接返回 true
        if (cookieLoginStatus) {
          return true
        }

        // 如果 cookies 显示未登录，再检查 store 状态（可能是新登录）
        const user = useUserStore()
        return user?.isLogined || false
      } catch (error) {
        // 如果 cookie 模块加载失败，回退到 store 检查
        const user = useUserStore()
        return user?.isLogined || false
      }
    }

    // 服务端渲染时，优先检查 Pinia store 状态（已通过 SSR 插件预填充）
    if (import.meta.server) {
      const user = useUserStore()
      if (user?.isLogined) {
        return true
      }

      // 如果 store 中没有状态，检查 cookies
      try {
        const { CookieAuthManager } = require('~/utils/cookie-auth')
        return CookieAuthManager.getLoginStatus()
      } catch (error) {
        return false
      }
    }

    return false
  },


  getCurrentLoginInfo: () => {
    const store = useUserStore()
    return store.currentLoginInfo
  },
  isAuthentication: () => {
    const store = useUserStore()
    // console.log('store.currentLoginInfo', store.currentLoginInfo)
    if (!store.currentLoginInfo) {
      return false
    }

    if (localStorage.getItem('authenticationStatus') == 'done') {
      return true
    }

    if (store.currentLoginInfo?.authenticationStatus == 'done') {
      return true
    }
    return false
  },

  getToken: (): string | undefined => {
    return storage.get(TOKENNAME)
  },

  getSelfUserId: (): string => {
    const store = useUserStore()
    //请先登录/注册
    if (store.currentLoginInfo) {
      return store.currentLoginInfo.id || ''
    }
    return ''
  },

  getKnowledgeAssistantMemberInfo: () => {
    const store = useUserStore()
    return store.knowledgeAssistantMemberInfo
  },

  loadKnowledgeAssistantMemberInfo: async () => {
    const user = useUserStore()
    const result = await repositoryGetInfo()
    if (!result || !result.ok) {
      return
    }
    if (!result.data) {
      return
    }
    user.setKnowledgeAssistantMemberInfo(result.data)

    // 同步知识助手信息到 cookies
    if (import.meta.client) {
      const { CookieAuthManager } = await import('~/utils/cookie-auth')
      CookieAuthManager.setKnowledgeAssistantInfo(result.data)
    }

    return result.data
  },
  onLoginRes: async (data: AppLoginResultInfo, isLoadOpenRecord: boolean) => {
    storage.set(TOKENNAME, data?.token)

    // 同时将认证信息存储到 cookies 中以支持 SSR
    if (import.meta.client) {
      const { CookieAuthManager } = await import('~/utils/cookie-auth')
      CookieAuthManager.setToken(data?.token)
      CookieAuthManager.setLoginStatus(true)
    }

    UserService.loadOpenRecordAdd()

  },
  loadUserInfo: async () => {
    const userStore = useUserStore()
    await userStore.loadUserInfo()

  },
  loadUserInfoAndAssistantMemberInfo: async () => {
    await UserService.loadUserInfo()
    await UserService.loadKnowledgeAssistantMemberInfo()
  },
  reportUseApp: async (app: LastUseAppInfo) => {
    if (!app.code) {
      return
    }

    const store = useUserStore()
    // store.addRecentApp(app)

    if (!UserService.isLogined()) {
      return
    }
    reportUseAppToServer({ code: app.code, type: app.type })
  },
  isCanAskQuestion: () => {
    const store = useUserStore()
    if (!store?.knowledgeAssistantMemberInfo) {
      return false
    }
    if (store.knowledgeAssistantMemberInfo?.vipLevel >= 3) {
      return true
    }
    if (
      store.knowledgeAssistantMemberInfo?.maxChat - store.knowledgeAssistantMemberInfo?.usedChat >
      0
    ) {
      return true
    }
    return false
  },

  isLastDigitOfTheUserIDAnOddNumber: () => {
    const store = useUserStore()
    if (!store.currentLoginInfo) {
      return false
    }
    if (!store.currentLoginInfo.id) {
      return false
    }
    if (parseInt(store.currentLoginInfo.id.slice(-1)) % 2 != 0) {
      return true
    }
    return false
  },

  getSharerUserId: () => {
    const sharerUserId = storage.get(StarloveConstants.keyOflocalStorage.sharerUserId)
    if (StarloveUtil.isEmptyString(sharerUserId)) {
      return ''
    }
    return sharerUserId
  },
  getSharerUserIdUpdateTime: () => {
    const sharerUserIdUpdateTime = storage.get(
      StarloveConstants.keyOflocalStorage.sharerUserIdUpdateTime
    )
    if (StarloveUtil.isEmptyString(`${sharerUserIdUpdateTime}`)) {
      return `${new Date().getTime()}`
    }
    return sharerUserIdUpdateTime
  },

  getTrackId: () => {
    const trackId = storage.get(StarloveConstants.keyOflocalStorage.trackId)
    if (StarloveUtil.isEmptyString(trackId)) {
      return ''
    }
    return trackId
  },

  // 改成用 localChannelData 里面的 channel
  getChannel: () => {
    // 如果channelInfo有值，则用channelInfo的值，否则用localChannelData的值
    const channelInfo = storage.get(StarloveConstants.keyOflocalStorage.channelInfo)
    if (StarloveUtil.isEmptyString(channelInfo)) {
      const localChannelData = storage.get(StarloveConstants.keyOflocalStorage.localChannelData)
      if (StarloveUtil.isEmptyString(localChannelData)) {
        return ''
      }
      return JSON.parse(localChannelData).channel
    }
    return channelInfo
  },

  getClickId: () => {
    const clickId = storage.get(StarloveConstants.keyOflocalStorage.clickId)
    if (StarloveUtil.isEmptyString(clickId)) {
      return ''
    }
    storage.remove(StarloveConstants.keyOflocalStorage.clickId)
    return clickId
  },

  // 已和运营确认，目前暂时没有用了
  get360SourceId: () => {
    const sourceId = storage.get(StarloveConstants.keyOflocalStorage.sourceId)
    if (StarloveUtil.isEmptyString(sourceId)) {
      return ''
    }
    storage.remove(StarloveConstants.keyOflocalStorage.sourceId)
    return sourceId
  },

  // 需要和订单、创作等绑定，如果有值一直保存即可，除非用户主动删除
  get360QhclickId: () => {
    const qhclickId = storage.get(StarloveConstants.keyOflocalStorage.qhclickId)
    if (StarloveUtil.isEmptyString(qhclickId)) {
      return ''
    }
    return qhclickId
  },

  beforeInstallPromptListener: () => {
    // if ('serviceWorker' in navigator) {
    //   navigator.serviceWorker
    //     .register('/service-worker.js')
    //     .then((registration) => {
    //       console.log('Service Worker registered with scope:', registration.scope)
    //     })
    //     .catch((error) => {
    //       console.error('Service Worker registration failed:', error)
    //     })
    // }
    // const store = useUserStore()
    // console.log('beforeInstallPromptListener ==>')
    // window.addEventListener('beforeinstallprompt', (event: Event) => {
    //   console.log('event beforeinstallprompt 111', event)
    //   event.preventDefault()
    //   store.setInstallPromptEvent(event)
    // })
  },
  isSSSVip: () => {
    const store = useUserStore()
    if (!store.knowledgeAssistantMemberInfo) {
      return false
    }
    if (store.knowledgeAssistantMemberInfo?.vipLevel >= 3) {
      return true
    }
    return false
  },
  canAskQuestionCount: () => {
    const store = useUserStore()
    if (!store?.knowledgeAssistantMemberInfo) {
      return 0
    }
    return store?.knowledgeAssistantMemberInfo?.maxChat - store?.knowledgeAssistantMemberInfo?.usedChat
  },
  isBindPhone: () => {
    const store = useUserStore()
    if (!store?.currentLoginInfo) {
      return false
    }
    if (!store?.currentLoginInfo?.account || (store?.currentLoginInfo?.account || '') == '') {
      return false
    }
    return true
  },
  loadOpenRecordAdd: async () => {
    if (!UserService.isLogined()) {
      return
    }
    // console.log('已经登录过了 loadOpenRecordAdd')
    const alreadyLoggedIn = storage.get(StarloveConstants.keyOflocalStorage.alreadyLoggedIn) || ''
    // 有值证明已经调用，删除后下次调用
    if (alreadyLoggedIn) {
      storage.remove(StarloveConstants.keyOflocalStorage.alreadyLoggedIn)
      return
    }
    const store = useUserStore()
    const { track } = useTracking();
    // const channelStore = useChannelStore()
    if (!store.channel) {
      store.channel = UserService.getChannel()
    }
    // 用于 bilibili 渠道
    if (!store.track_id) {
      store.track_id = UserService.getTrackId()
    }
    if (!store.clickId) {
      store.clickId = UserService.getClickId()
    }
    if (!store.sourceId) {
      store.sourceId = UserService.get360SourceId()
    }
    if (!store.qhclickId) {
      store.qhclickId = UserService.get360QhclickId()
    }
    const params: any = {
      platform: getPlatform(),
      channel: store.clickId ? `baidu:${store.channel}` : store.channel,
      clickId: store.clickId || '',
      sourceId: store.sourceId || '',
      openUrl: store.openRecordUrl,
      extra: {
        referer: JSON.parse(storage.get(StarloveConstants.keyOflocalStorage.localChannelData) || '{"referrer":""}').referrer || ''
      },
      qhclickId: store.qhclickId || 0
    }
    // 360 ocpc 渠道
    if (store.qhclickId) {
      params.channel = `360:${store.channel}`
      params.clickId = store.qhclickId
    }
    if (store.clickId) {
      params.channel = `baidu:${store.channel}`
      params.clickId = store.clickId
    }
    // 用于 bilibili 渠道
    if (store.channel == 'bilibili' && store.track_id) {
      params.clickId = store.track_id || ''
    }

    if (UserService.getSharerUserId()) {
      params['inviteUserId'] = UserService.getSharerUserId()
      params['inviteUserIdUpdateTime'] = UserService.getSharerUserIdUpdateTime()
    }
    openRecordAdd(params)

    const storedDate = await getStoredDate();
    if (storedDate === getTodayDate()) {
      // console.log('UV 已经上报过，无需重复上报');
      return;
    }
    const visitorIdStore = useVisitorIdStore()
    visitorIdStore.setVisitorId();
    if (!visitorIdStore.getVisitorId) return;
    track('unique_visitor', visitorIdStore.getVisitorId, 'uv');

  },
  // 绑定鼠标设备
  bindMouseDevice: async (params: any) => {
    const res = await bindMouse(params)
    if (!res || !res.ok) {
      message.error(res?.message || '绑定鼠标设备失败')
      return
    }
    if (!res.data || !res.data?.title) {
      return
    }
    setTimeout((data) => {
      if (data?.status == 'error') {
        Modal.error({
          title: data.title,
          content: h('div', {}, [
            h('p', data.content),
          ]),
          okText: '我知道了'
        });
      } else {
        Modal.success({
          title: data.title,
          content: h('div', {}, [
            h('p', data.content),
          ]),
          okText: '立即体验'
        });
      }
    }, 1500, res.data);
  },


}
export { UserService }

