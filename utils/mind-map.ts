interface Nodes {
  node_title: string
}

interface Section {
  section_title: string
  nodes: Nodes[]
}
interface Chapter {
  chapter_title: string
  sections: Section[]
}
export const convertPListByMarkdownList = (list: any) => {
  if (!list || !list.children || list.children.length == 0) {
    return []
  }
  return list.children.map((item: any) => {
    const chat: Chapter = {
      chapter_title: item.data.text,
      sections:
        item.children?.map((child: any) => ({
          section_title: child.data.text,
          nodes:
            child.children?.map((node: any) => ({
              node_title: node.data.text,
            })) || [],
        })) || [],
    }
    return chat
  })
}
