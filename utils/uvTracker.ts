import { useTracking } from '@/composables/useTracking';
import { useVisitorIdStore } from '@/stores/visitorId';

// 删除idb导入
// import { openDB } from 'idb';

// 定义数据库名称和存储表
const DB_NAME = 'analyticsDB';
const STORE_NAME = 'visitorData';
const KEY = 'uv_date';

// 获取当前日期（YYYY-MM-DD 格式）
export const getTodayDate = () => {
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从 0 开始，需要加 1
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
};

// 存储日期到 IndexedDB
export const saveDateToDB = async (date: string) => {
    return new Promise<void>((resolve, reject) => {
        const request = indexedDB.open(DB_NAME, 1);

        request.onerror = () => {
            reject(request.error);
        };

        request.onupgradeneeded = (event) => {
            const db = (event.target as IDBOpenDBRequest).result;
            if (!db.objectStoreNames.contains(STORE_NAME)) {
                db.createObjectStore(STORE_NAME);
            }
        };

        request.onsuccess = (event) => {
            const db = (event.target as IDBOpenDBRequest).result;
            const transaction = db.transaction([STORE_NAME], 'readwrite');
            const store = transaction.objectStore(STORE_NAME);

            const putRequest = store.put(date, KEY);

            putRequest.onsuccess = () => {
                resolve();
            };

            putRequest.onerror = () => {
                reject(putRequest.error);
            };
        };
    });
};

// 获取存储的日期
export const getStoredDate = async (): Promise<string | undefined> => {
    return new Promise((resolve, reject) => {
        const request = indexedDB.open(DB_NAME, 1);

        request.onerror = () => {
            reject(request.error);
        };

        request.onupgradeneeded = (event) => {
            const db = (event.target as IDBOpenDBRequest).result;
            if (!db.objectStoreNames.contains(STORE_NAME)) {
                db.createObjectStore(STORE_NAME);
            }
        };

        request.onsuccess = (event) => {
            const db = (event.target as IDBOpenDBRequest).result;
            const transaction = db.transaction([STORE_NAME], 'readonly');
            const store = transaction.objectStore(STORE_NAME);

            const getRequest = store.get(KEY);

            getRequest.onsuccess = () => {
                resolve(getRequest.result);
            };

            getRequest.onerror = () => {
                reject(getRequest.error);
            };
        };
    });
};

// 删除存储的日期
export const deleteStoredDate = async (): Promise<void> => {
    try {
        return new Promise<void>((resolve, reject) => {
            const request = indexedDB.open(DB_NAME, 1);

            request.onerror = () => {
                reject(request.error);
            };

            request.onsuccess = (event) => {
                const db = (event.target as IDBOpenDBRequest).result;
                const transaction = db.transaction([STORE_NAME], 'readwrite');
                const store = transaction.objectStore(STORE_NAME);

                const deleteRequest = store.delete(KEY);

                deleteRequest.onsuccess = () => {
                    console.log('UV 日期记录已删除');
                    resolve();
                };

                deleteRequest.onerror = () => {
                    reject(deleteRequest.error);
                };
            };
        });
    } catch (error) {
        console.error('删除UV日期记录失败:', error);
    }
};

// 检查并发送 UV 事件
export const trackUniqueVisitor = async () => {
    const { track } = useTracking();
    const visitorIdStore = useVisitorIdStore()
    const visitorId = visitorIdStore.getVisitorId;
    if (!visitorId) return;

    const storedDate = await getStoredDate();
    const today = getTodayDate();

    if (storedDate === today) {
        // console.log('UV 已经上报过，无需重复上报');
        return;
    }

    // 记录 UV 事件
    track('unique_visitor', visitorId, 'uv');

    // 存储今天的日期
    await saveDateToDB(today);
};


