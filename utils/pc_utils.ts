import type { CreatorsInfo } from '@/services/types/appMessage'
import type { SubmissionInfo } from '~/services/types/submission'
import { storage } from './local-storage'

export const formatCoinNumber = (minCoinsNeedRecharge: number | undefined) => {
  if (minCoinsNeedRecharge === undefined || minCoinsNeedRecharge === 0) {
    return ''
  }

  const coinsInW = minCoinsNeedRecharge / 10000
  const coinsInWCeil = Math.ceil(coinsInW * 10) / 10

  return `${coinsInWCeil}万`
}

export const getSubmissionTitle = (submission: SubmissionInfo, creatorData: CreatorsInfo) => {
  if (
    submission.formData.topic &&
    submission.formData.topic != '0' &&
    submission.formData.topic != '-'
  ) {
    // return decodeURIComponent(submission.formData.topic)
    return submission.formData.topic
  }
  return creatorData.creator.name
}

export const getStrorageKeyOfSubmissionDraft = (submissionData: any) => {
  return getStrorageKeyOfCreatorOrChatCode(submissionData['creatorCode'])
}

export const getStrorageKeyOfCreatorOrChatCode = (code: any) => {
  return `draft_${code}`
}

export const saveSubmissionDraft = (submissionData: any) => {
  const key = getStrorageKeyOfSubmissionDraft(submissionData)
  const value = JSON.stringify(submissionData)
  storage.set(key, value)
}

export const saveChatContentDraft = (code: string, keyword: string) => {
  const key = getStrorageKeyOfCreatorOrChatCode(code)
  storage.set(key, keyword)
}

export const deleteSubmissionDraft = (code: string) => {
  const key = getStrorageKeyOfCreatorOrChatCode(code)
  storage.remove(key)
}

export const deleteChatContentDraft = (code: string) => {
  const key = getStrorageKeyOfCreatorOrChatCode(code)
  storage.remove(key)
}

export const readSubmissionDraft = (code: string) => {
  const key = getStrorageKeyOfCreatorOrChatCode(code)
  const valueAsString = storage.get(key)
  if (!valueAsString) {
    return undefined
  }

  const value = JSON.parse(valueAsString)
  return value
}

export const readChatDraft = (code: string) => {
  const key = getStrorageKeyOfCreatorOrChatCode(code)
  const valueAsString = storage.get(key)
  if (!valueAsString) {
    return undefined
  }

  return valueAsString
}
