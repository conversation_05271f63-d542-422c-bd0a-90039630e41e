import { useUserStore } from '@/stores/user';
import { mobileSessionKey } from '@/utils/constants';
import { StarloveUtil } from '@/utils/util';
import { ToastService } from '~/services/toast';
import type { LoginThunderobotParams } from "~/services/types/user";
import { UserService } from "~/services/user";

export const autoLoginThunderobot = async () => {
    try {
        const userStore = useUserStore()
        if (!userStore.isLogined) {
            ToastService.loading('自动登录中...')
            const params: LoginThunderobotParams = {
                mobile: StarloveUtil.getPhoneFromUrl() || '',
                inviteUserId: UserService.getSharerUserId(),
                verifyCode: '123456',
                channelId: 17 // 这个亚光给的
            }
            if (params.mobile) {
                sessionStorage.setItem(mobileSessionKey, params.mobile)
            } else {
                params.mobile = sessionStorage.getItem(mobileSessionKey) || ''
            }

            await userStore.loginThunde(params)
            await UserService.loadKnowledgeAssistantMemberInfo()
            UserService.loadOpenRecordAdd()
            ToastService.success('自动登录成功')
        }
    } catch (error) {
        console.log(error)
        ToastService.error('登录失败')
    }

}