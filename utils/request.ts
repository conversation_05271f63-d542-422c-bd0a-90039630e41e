
import type { Response } from '@/services/types/reponse'
import { useUserStore } from '@/stores/user'
import { HTTP_STATUS, TOKENNAME } from '@/utils/constants'
import { storage } from '@/utils/local-storage'
import { StarloveConstants } from '@/utils/starloveConstants'
import { StarloveUtil } from '@/utils/util'
import { clearObjectNullParam, getPlatform } from '@/utils/utils'
import { useThrottleFn } from '@vueuse/core'
import { message as ElMessage } from 'ant-design-vue'
import type { AxiosError, AxiosResponse, InternalAxiosRequestConfig } from 'axios'
import axios from 'axios'
import { requestSignHandler } from './request-interceptor-sign'

// 这里是用于设定请求后端时，所用的 Token KEY
// 可以根据自己的需要修改，常见的如 Access-Token，Authorization
// 需要注意的是，请尽量保证使用中横线`-` 来作为分隔符，
// 避免被 nginx 等负载均衡器丢弃了自定义的请求头
export const REQUEST_TOKEN_KEY = TOKENNAME

// 使用 getEventBus 函数来获取 $eventBus
// import qs from 'qs'
// import emitter from "@/utils/mitt";

const toLogin = useThrottleFn(
  () => {
    console.log('toLogin', import.meta.client)
    if (import.meta.client) {
      deleteAuthentication()
      const userStore = useUserStore()
      userStore.openLoginModal()
    }
    // if (isLocalHost()) {
    // $eventBus.emit('visibleLoginModal', true)
    // } else {
    //   ElMessage.error('请先登录')
    //   const href = encodeURIComponent(window.location.href)
    //   window.location.href = `/home/<USER>
    // }
  },
  1000,
  false
)

// 创建 axios 实例
const request = axios.create({
  // API 请求的默认前缀
  baseURL: StarloveUtil.getBaseUrl(),
  timeout: 600000, // 请求超时时间
})

export type RequestError = AxiosError<{
  message?: string
  result?: any
  code?: number
  errorMessage?: string
}>

const showErrorMessage = (message: string) => {
  if (import.meta.client) {
    ElMessage.error(message)
  }
}

// 异常拦截处理器
const errorHandler = (error: RequestError): Promise<any> => {

  // console.log('errorHandler error', error)
  const { response } = error

  // console.log('errorHandler response', response)
  let code = 500

  let bizResult = null

  // 处理特定状态码
  if (response) {
    const { status, data } = response

    // console.log('errorHandler responseData', data)
    // console.log('errorHandler status', status)
    bizResult = data.result
    code = data.code || 500

    switch (status) {
      case HTTP_STATUS.AUTHENTICATE:
        toLogin()
        break
      case HTTP_STATUS.FORBIDDEN:
        showErrorMessage('无权限访问')
        break
      case HTTP_STATUS.SERVER_ERROR:
        showErrorMessage(data?.message || '服务器错误')
        break
    }
  } else {
    // 处理网络错误
    if (error.message === 'Network Error') {
      showErrorMessage('网络连接失败')
    } else {
      showErrorMessage('请求失败，请稍后重试')
    }
  }

  // 返回一个带错误标记的对象，而不是reject
  return Promise.resolve({
    ok: false,
    success: false,
    // code: response?.status || -1,
    code: code,
    message: response?.data?.message || error.message,
    data: bizResult
  })
}

export const deleteAuthentication = async () => {
  storage.remove(TOKENNAME)
  storage.remove('userInfo')
  storage.remove('openid')
  storage.remove('userId')
  storage.remove('loginStatus')
  storage.remove('user')
  storage.remove('auto_login_token')
  storage.remove('currentAutoLoginInfo')
  storage.remove(StarloveConstants.keyOflocalStorage.aiQuestionModeList)

  // 清理认证相关的 cookies
  if (import.meta.client) {
    const { CookieAuthManager } = await import('~/utils/cookie-auth')
    CookieAuthManager.clearAll()
  }

  const user = useUserStore()
  user.setLoginInfo(null)
  user.setKnowledgeAssistantMemberInfo(null)
  Object.keys(localStorage).forEach(key => {
    if (key.startsWith('draft_')) {
      storage.remove(key)
    }
  })
}

// 请求拦截器
const requestHandler = (
  config: InternalAxiosRequestConfig
): InternalAxiosRequestConfig | Promise<InternalAxiosRequestConfig> => {
  const savedToken = storage.get(TOKENNAME)
  const platform = getPlatform()
  if (config.headers) {
    // config.headers['bspId'] = getBspId()
    if (savedToken) {
      config.headers['Authorization'] = `Bearer ${savedToken}`
      // config.headers['token'] = `${savedToken}`
    }
  }

  if (config.method === 'post') {
    if (config.params && config.params.platform) {
      if (config.data && typeof config.data === 'object') {
        config.params = clearObjectNullParam({
          ...(config.params ? config.params : {}),
        })
      }
    } else {
      if (config.data && typeof config.data === 'object') {
        config.params = clearObjectNullParam({
          ...(config.params ? config.params : {}),
          platform: platform,
        })
      }
    }
  }
  if (config.method === 'get') {
    if (config.params && config.params.platform) {
      config.params = clearObjectNullParam({
        ...(config.params ? config.params : {}),
      })
    } else {
      config.params = clearObjectNullParam({
        ...(config.params ? config.params : {}),
        platform: platform,
      })
    }
  }
  return config
}

// Add a request interceptor
// request.interceptors.request.use(
//   (config: InternalAxiosRequestConfig) => {
//     // 实现签名逻辑
//     return config
//   },
//   errorHandler
// )
request.interceptors.request.use(requestSignHandler as any, errorHandler)
request.interceptors.request.use(requestHandler, errorHandler)

// 响应拦截器
const responseHandler = (
  response: AxiosResponse
): Response<any> | AxiosResponse<any> | Promise<any> | any => {

  // console.log('responseHandler response', response)
  if (response.status == HTTP_STATUS.AUTHENTICATE) {
    deleteAuthentication()
    return {
      ok: false,
      code: '401',
      message: '',
    }
  }

  let success: boolean | undefined = true
  let code: number | undefined = 0
  if (response.status == 200) {
    const res = response.data as Response
    response.data = res.result
    success = res.success
    code = res.code

    const message = res.message || '未知错误'
    if (res.code !== HTTP_STATUS.SUCCESS) {
      if (!success) {
        showErrorMessage(res.message || '未知错误')
      }
      if (res.code === HTTP_STATUS.AUTHENTICATE) {
        toLogin()
      }
      return {
        ok: false,
        success,
        code,
        message: message,
        data: res.result
      }
    }
    // 成功时，如果判断带有更新的token， 则写入
    if (res.newToken) {
      storage.set(TOKENNAME, res.newToken)
    }
  } else if (response.status == HTTP_STATUS.SERVER_ERROR) {
    // console.log('responseHandler response', response)
    const res = response.data as Response
    success = res.success
    code = res.code
    if (!success) {
      showErrorMessage(res.message || '未知错误')
    }
    return {
      ...response,
      success,
      code,
      message: res.message || '未知错误',
      ok: false,
      // data: res
    }
  }
  return {
    ...response,
    ok: true,
    success,
    code,
  }
}

// Add a response interceptor
request.interceptors.response.use(responseHandler, errorHandler)

export type { AxiosResponse }

export default request
