// 脑图SimpleMindMap开发文档地址：https://wanglin2.github.io/mind-map/#/doc/zh/start/%E4%BD%BF%E7%94%A8
import { cloneDeep } from 'lodash'
import MindMap from 'simple-mind-map'

import Themes from 'simple-mind-map-plugin-themes'
import Drag from 'simple-mind-map/src/plugins/Drag.js'
import Export from 'simple-mind-map/src/plugins/Export.js'
import KeyboardNavigation from 'simple-mind-map/src/plugins/KeyboardNavigation.js'
import TouchEvent from 'simple-mind-map/src/plugins/TouchEvent'

import { fullScreen } from '~/utils/utils'
import iconList from './icon-list'
import themeConfig from './theme-config'

MindMap.usePlugin(Export)
MindMap.usePlugin(Drag)
MindMap.usePlugin(TouchEvent)
MindMap.usePlugin(KeyboardNavigation)

// 注册主题
Themes.init(MindMap)

let mindMap: any

// transformMarkdownTo
const wnMindMapUtil = {
  // 初始化脑图
  /**
   * @param id 脑图容器
   * @param data 数据结构【树状结构】
   */
  initMind: function (id: any, data: any, fit = false, readonly = true, themeName = 'default') {
    // console.log("id data == >", document.getElementById(id))
    mindMap = new MindMap({
      el: document.getElementById(id),
      data: data,

      layout: 'logicalStructure', // 布局类型--logicalStructure（逻辑结构图-默认）、mindMap（思维导图）、catalogOrganization（目录组织图）、organizationStructure（组织结构图）、timeline（v0.5.4+，时间轴）、timeline2（v0.5.4+，上下交替型时间轴）、fishbone（v0.5.4+，鱼骨图）
      fit: fit,
      readonly: readonly, // 只读模式
      iconList: iconList, // 注册图标

      theme: themeName, // 主题
      themeConfig: themeConfig, // 主题配置，会和所选择的主题进行合并
      initRootNodePosition: ['1%', '50%'], // 初始根节点的位置，可传一个数组，默认为['center', 'center']
      textAutoWrapWidth: 600, // 节点内每行文本达到该宽度后自动换行
      // enableShortcutOnlyWhenMouseInSvg: true, //有当鼠标在画布内才响应快捷键事件

    })
    // console.log("mindMap ==>", mindMap)
    this.initRightClickMenu(mindMap, id)

    return mindMap
  },

  initRightClickMenu: function (mindMap: any, id: any) {
    // 获取画布元素
    mindMap.on('rendered', function () {
      const container = document.getElementById(id);
      const svg = container?.querySelector('svg');

      // 监听右键点击事件
      svg?.addEventListener('contextmenu', function (event) {
        event.preventDefault(); // 阻止默认的上下文菜单

        // 获取点击位置的节点信息
        const point = mindMap.get('renderer').getPointerPosition(event);
        const node = mindMap.get('renderer').getHoverNode(point);

        if (node) {
          // 显示自定义上下文菜单
          showContextMenu(event, node, svg);
        }
      });
    });

    // 自定义上下文菜单函数
    function showContextMenu(event: any, node: any, svg: any) {
      // 创建菜单元素
      const menu = document.createElement('div');
      menu.style.position = 'absolute';
      menu.style.top = event.clientY + 'px';
      menu.style.left = event.clientX + 'px';
      menu.style.backgroundColor = 'white';
      menu.style.border = '1px solid #ccc';
      menu.style.padding = '5px';

      // 添加菜单项
      const menuItem = document.createElement('div');
      menuItem.textContent = '选项1';
      menuItem.style.cursor = 'pointer';
      menuItem.addEventListener('click', function () {
        // 处理选项1的逻辑
        console.log('选项1被点击，当前节点数据：', node.getModel());
        // 关闭菜单
        menu.remove();
      });
      menu.appendChild(menuItem);

      const menuItem2 = document.createElement('div');
      menuItem2.textContent = '选项2';
      menuItem2.style.cursor = 'pointer';
      menuItem2.addEventListener('click', function () {
        // 处理选项2的逻辑
        console.log('选项2被点击，当前节点数据：', node.getModel());
        // 关闭菜单
        menu.remove();
      });
      menu.appendChild(menuItem2);

      // 将菜单添加到画布上
      svg?.node.appendChild(menu);

      // 点击画布其他地方关闭菜单
      svg?.node.addEventListener('click', function () {
        menu.remove();
      }, { once: true });
    }
  },

  // 插入子节点
  addInsertChildNode: function () {
    mindMap.execCommand('INSERT_CHILD_NODE')
  },

  mindMapSetData: function (data: any) {
    mindMap.setData(data)
  },

  updateData: function (data: any) {
    mindMap.updateData(data)
  },

  render: function () { },

  // 插入多个子节点
  /**
   * 
   * @param childList 要插入的子节点数据的数组，必传。
   *childList: [
      {
        data: {
          text: '自定义节点1'
        }
      }
    ]
  */
  addMulitChildNode: function (childList: any) {
    mindMap.execCommand('INSERT_MULTI_CHILD_NODE', [], childList)
  },

  // 添加兄弟节点
  addPeerNode: function () {
    console.log("addPeerNode ==>")
    mindMap.execCommand('INSERT_NODE')
  },



  /**
   * 插入多个同级节点
   * @param nodeList 插入的同级节点数据的数组，必传。
  *nodeList: [
      {
        data: {
          text: '自定义节点1'
        }
      }
    ]
  */
  addMorePeerNode: function (nodeList: any) {
    mindMap.execCommand('INSERT_NODE', [], nodeList)
  },

  // 插入父节点
  addParentNode: function () {
    mindMap.execCommand('INSERT_PARENT_NODE')
  },

  // 删除节点---会删除当前激活的所有节点
  removeNode: function () {
    mindMap.execCommand('REMOVE_NODE')
  },

  // 仅删除当前节点---仅删除激活的节点，子节点不会被删除
  removeCurrentNode: function () {
    mindMap.execCommand('REMOVE_CURRENT_NODE')
  },

  // 前进一次
  forwardNode: function () {
    mindMap.execCommand('FORWARD')
  },

  // 后退一次
  backNode: function () {
    mindMap.execCommand('BACK')
  },

  /**
   * 添加图标
   * @param {*传入数据} data 
   * data: {
   * 	activeNodes: '', // 当前选中的节点数组
      type: '', // 图标类型
      name: '', // 图标名称
      NodeIconList: [], // 图标列表
      currentIconList: [] // 当前节点icon列表
    }
   */
  addIconNode: function (data: any) {
    // 获取当前选中节点的图标
    // console.log(data, '--------------------')
    const key = data.type + '_' + data.name
    let NodeIconListNew = []
    const currentIconList = cloneDeep(data.currentIconList) || []
    // 检查当前节点是否存在该图标
    const index = currentIconList.findIndex((item: any) => {
      return item === key
    })
    // 存在则删除icon
    if (index !== -1) {
      currentIconList.splice(index, 1)
    } else {
      // 否则判断当前图标是否和要插入的图标是一个组的
      const typeIndex = currentIconList.findIndex((item: any) => {
        return item.split('_')[0] === data.type
      })
      // 是一个组的则进行替换
      if (typeIndex !== -1) {
        currentIconList.splice(typeIndex, 1, key)
      } else {
        // 否则添加icon
        currentIconList.push(key)
      }
    }
    data.activeNodes.forEach((node: any) => {
      node.setIcon([...currentIconList])
    })
    NodeIconListNew = this.isCheckIcon(
      [...currentIconList],
      this.removeCheckIcon(data.NodeIconList),
      data.type,
      data.name
    )

    return {
      NodeIconListNew: NodeIconListNew,
      currentIconList: [...currentIconList]
    }
  },

  /**
   * 图标列表某个图标是否选中
   * @param {*当前节点icon列表} currentIconList
   * @param {*图标列表} NodeIconList
   * @param {*图标类型} type
   * @param {*图标名称} name
   */
  isCheckIcon: function (currentIconList: any, NodeIconList: any, type: any, name: any) {
    let NodeIconListNew = cloneDeep(NodeIconList)
    if (currentIconList.length > 0) {
      currentIconList.forEach((item: any) => {
        NodeIconListNew.forEach((cItem: any) => {
          if (item.split('_')[0] === cItem.type) {
            cItem.list.forEach((fItem: any) => {
              if (item.split('_')[1] === fItem.name) {
                fItem.isChecked = true
              } else {
                fItem.isChecked = false
              }
            })
          }
        })
      })
    } else {
      NodeIconListNew = cloneDeep(this.removeCheckIcon(NodeIconListNew))
    }
    return NodeIconListNew
  },

  /**
   * 消除全部图标的选中状态
   * @param {*图标列表} NodeIconList
   * @returns
   */
  removeCheckIcon: function (NodeIconList: any) {
    const NodeIconListNew = cloneDeep(NodeIconList)
    NodeIconListNew.forEach((item: any) => {
      item.list.forEach((cItem: any) => {
        cItem.isChecked = false
      })
    })
    return NodeIconListNew
  },

  /**
   * 获取节点当前位置信息【获取节点的尺寸和位置信息，宽高是应用了缩放效果后的实际宽高，位置信息相对于画布。】
   * @param {*当前节点} node
   */
  getNodePositionXY: function (node: any) {
    return node.getRectInSvg()
  },

  /**
   * 设置画布缩放
   * @param {*缩放比例，必传} scale
   * @param {*以画布指定位置进行缩放，默认为画布中心点} cx
   * @param {*以画布指定位置进行缩放，默认为画布中心点} cy
   */
  setScale: function (scale: any, cx: any, cy: any) {
    mindMap.view.setScale(scale, cx, cy)
  },

  /**
   * 插入超链接
   * @param {*当前节点} node
   * @param {*链接地址} url
   * @param {*链接名称} name
   */
  setHyperlink: function (node: any, url: any, name: any) {
    node.setHyperlink(url, name)
  },

  /**
   * 插入概要
   * @param {*概要数据} data
   *默认： { text: '概要' }
   */
  addGeneralization: function (data: any) {
    mindMap.execCommand('ADD_GENERALIZATION', data)
  },

  // 容器尺寸变化后，需要调用该方法进行适应【容器内容没有自适应】
  mindMapResize: function () {
    mindMap.resize()
  },

  // 缩放思维导图至适应画布
  mindMapFit: function () {
    mindMap.view.fit()
  },

  /**
   * 插入备注
   * @param {*当前节点} node
   * @param {*备注信息} data
   */
  setNote: function (node: any, data: any) {
    node.setNote(data)
  },

  /**
   * 获取节点的尺寸和位置信息，宽高是应用了缩放效果后的实际宽高，位置是相对于浏览器窗口左上角的位置。
   * @param {*当前节点} node
   * @returns
   */
  getRect: function (node: any) {
    return node.getRect()
  },

  /**
   * 导出为png图片
   * @param {* 导出文件名称} fileName
   */
  mindMapExportPng: function (fileName: any) {
    // console.log("mindMapExportPng ==>", mindMap.getData())
    mindMap.export('png', true, fileName)
    // mindMap.doExport.png().then((data) => {
    // 	console.log("data ==>", data)
    // 	// downloadFile(data, fileName + '.png')
    // 	let a = document.createElement('a')
    // 	a.href = data // .png、.svg、.pdf、.md、.json、.smm
    // 	a.download = fileName + '.png'
    // 	a.click()
    // })
  },

  /**
   * 在小程序上直接打开为png图片
   */
  getMindMapMinappOpenPng: function (isH5 = false) {
    console.log('mindMapExportPng ==>', mindMap)
    return mindMap.export('png', !isH5, '思维导图')
    // return mindMap.doExport.png().then((data) => {
    // 	console.log("data ==>", data)
    // 	return data
    // })
  },

  // 导出svg
  mindMapExportSvg: function (fileName: any) {
    mindMap.doExport
      .svg('', false, `* { margin: 0; padding: 0; box-sizing: border-box; }`)
      .then((data: any) => {
        const a = document.createElement('a')
        a.href = data // .png、.svg、.pdf、.md、.json、.smm
        a.download = fileName + '.svg'
        a.click()
      })
  },

  // 导出pdf
  mindMapExportPDF: function (fileName: any) {
    mindMap.doExport.pdf().then((data: any) => {
      // console.log("mindExport data ==>", data)
      const a = document.createElement('a')
      a.href = data // .png、.svg、.pdf、.md、.json、.smm
      a.download = fileName + '.pdf'
      a.click()
    })
  },

  // 导出xmind
  mindMapExportXmind: function (fileName: any) {
    mindMap.doExport.xmind().then((data: any) => {
      const a = document.createElement('a')
      a.href = data // .png、.svg、.pdf、.md、.json、.smm
      a.download = fileName + '.xmind'
      a.click()
    })
  },

  /**
   * 添加标签
   * @param {* 当前节点} node
   * @param {* 标签列表} tagArry
   */
  setTag: function (node: any, tagArry: any) {
    node.setTag(tagArry)
  },

  // 销毁导图
  mindMapDestroy: function () {
    mindMap.destroy()
  },

  /**
   * 获取导图数据
   * @param {* Boolean, 默认为false, 即获取的数据只包括节点树, 如果传true则会包含主题、布局、视图等数据 } withConfig
   */
  mindMapGetData: function (withConfig = false) {
    const mindData = mindMap.getData(withConfig)
    // console.log('导图数据==>', JSON.parse(JSON.stringify(mindData)))
    return mindData
  },

  /**
   * 修改节点内容
   * @param {* 当前节点} node
   * @param {* 修改的内容} text
   */
  setText: function (node: any, text: any) {
    console.log("setText ==>", node, text)
    node.setText(text)
  },

  /**
   * 设置脑图主题
   * @param {* 主题} theme
   */
  setTheme: function (theme: any) {
    if (!mindMap) {
      return
    }
    mindMap.setThemeConfig({}, true)
    mindMap.setTheme(theme)

    console.log("mindMap.setTheme 11==>", mindMap)
  },
  // 缩小
  narrow() {
    mindMap.view.narrow()
  },

  // 放大
  enlarge() {
    mindMap.view.enlarge()
  },
  // 全屏查看
  toFullscreenShow() {
    fullScreen(mindMap.el)
  },

  /**
 * 将思维导图数据转换为markdown格式
 * @param data 思维导图数据
 * @param title 标题
 */
  convertToMarkdown: function (data: any) {
    let markdown = '```markdown\n# ' + data.data.text + '\n'
    data.children.forEach((chapter: any) => {
      markdown += `## ${chapter.data.text}\n\n`
      chapter.children.forEach((section: any) => {
        markdown += `### ${section.data.text}\n`
        section.children.forEach((node: any) => {
          markdown += `- ${node.data.text}\n`
        })
        markdown += '\n'
      })
    })
    markdown += '\n```'
    return markdown
  },

  /**
   * 初始化自动保存功能
   * @param {Function} saveCallback 保存数据的回调函数
   * @param {number} debounceTime 防抖时间(ms)，默认1000ms
   * @param {string} title 思维导图标题
   */
  initAutoSave: function (saveCallback: (data: any) => void, debounceTime = 1000) {
    if (!mindMap) {
      console.warn('Mind map not initialized')
      return
    }

    // 使用防抖来避免频繁保存
    let timer: NodeJS.Timeout | null = null
    const debounceSave = () => {
      if (timer) {
        clearTimeout(timer)
      }
      timer = setTimeout(() => {
        // 获取完整数据(包含配置)
        const data = mindMap.getData(false)
        // 转换为markdown格式
        const markdownContent = this.convertToMarkdown(data)
        // 调用保存回调
        saveCallback(markdownContent)
      }, debounceTime)
    }

    // 监听节点数据变化
    mindMap.on('data_change', () => {
      debounceSave()
    })

    // 监听视图数据变化(如位置、缩放等)
    mindMap.on('view_data_change', () => {
      debounceSave()
    })

    // 监听主题变化
    mindMap.on('theme_change', () => {
      debounceSave()
    })

    // 监听布局变化  
    mindMap.on('layout_change', () => {
      debounceSave()
    })
  }
}

export default wnMindMapUtil
