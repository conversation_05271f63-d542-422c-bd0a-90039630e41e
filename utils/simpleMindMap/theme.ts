//  布局结构图片映射
// export const layoutImgMap = {
//     logicalStructure: require('@/assets/image/structures/logicalStructure.png'),
//     mindMap: require('@/assets/image/structures/mindMap.png'),
//     organizationStructure: require('@/assets/image/structures/organizationStructure.png'),
//     catalogOrganization: require('@/assets/image/structures/catalogOrganization.png'),
//     timeline: require('@/assets/image/structures/timeline.png'),
//     timeline2: require('@/assets/image/structures/timeline2.png'),
//     fishbone: require('@/assets/image/structures/fishbone.png'),
//     verticalTimeline: require('@/assets/image/structures/verticalTimeline.png')
//   }
  
  //  主题图片映射
export const themeMap = {
  default: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/default.jpg',
  classic: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/classic.jpg',
  cactus: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/cactus.jpg',
  classic6: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/classic6.jpg',
  classic7: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/classic7.jpg',
  dark2: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/dark2.jpg',
  classic2: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/classic2.jpg',
  classic3: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/classic3.jpg',
  classic4: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/classic4.jpg',
  classicGreen: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/classicGreen.jpg',
  classicBlue: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/classicBlue.jpg',
  blueSky: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/blueSky.jpg',
  brainImpairedPink: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/brainImpairedPink.jpg',
  dark: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/dark.jpg',
  courseGreen: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/courseGreen.jpg',
  coffee: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/coffee.jpg',
  blackHumour: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/blackHumour.jpg',
  blackGold: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/blackGold.jpg',
  autumn: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/autumn.jpg',
  avocado: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/avocado.jpg',
  classic5: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/classic5.jpg',
  minions: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/minions.jpg',
  pinkGrape: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/pinkGrape.jpg',
  mint: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/mint.jpg',
  gold: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/gold.jpg',
  vitalityOrange: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/vitalityOrange.jpg',                   
  greenLeaf: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/greenLeaf.jpg',
  skyGreen: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/skyGreen.jpg',
  earthYellow: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/earthYellow.jpg',
  freshGreen: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/freshGreen.jpg',
  freshRed: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/freshRed.jpg',
  romanticPurple: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/romanticPurple.jpg',
  simpleBlack: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/simpleBlack.jpg',
  redSpirit: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/redSpirit.jpg',
  lateNightOffice: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/lateNightOffice.jpg',
  orangeJuice: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/orangeJuice.jpg',
  oreo: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/oreo.jpg',
  shallowSea: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/shallowSea.jpg',
  lemonBubbles: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/lemonBubbles.jpg',
  rose: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/rose.jpg',
  seaBlueLine: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/seaBlueLine.jpg',
  neonLamp: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/neonLamp.jpg',
  darkNightLceBlade: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/darkNightLceBlade.jpg',
  morandi: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/morandi.jpg',
  dark3: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/dark3.jpg',
  dark4: 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/dark4.jpg',
}

  // 公式列表
  export const formulaList = [
    'a^2',
    'a_2',
    'a^{2+2}',
    'a_{i,j}',
    'x_2^3',
    '\\overbrace{1+2+\\cdots+100}',
    '\\sum_{k=1}^N k^2',
    '\\lim_{n \\to \\infty}x_n',
    '\\int_{-N}^{N} e^x\\, dx',
    '\\sqrt{3}',
    '\\sqrt[n]{3}',
    '\\sin\\theta',
    '\\log X',
    '\\log_{10}',
    '\\log_\\alpha X',
    '\\lim_{t\\to n}T',
    '\\frac{1}{2}=0.5',
    '\\binom{n}{k}',
    '\\begin{matrix}x & y \\\\z & v\\end{matrix}',
    '\\begin{cases}3x + 5y +  z \\\\7x - 2y + 4z \\\\-6x + 3y + 2z\\end{cases}'
  ]
  
  export const supportLineStyleLayoutsMap = {
    curve: ['logicalStructure', 'mindMap', 'verticalTimeline'],
    direct: [
      'logicalStructure',
      'mindMap',
      'organizationStructure',
      'verticalTimeline'
    ]
  }
  
  export const supportLineRadiusLayouts = [
    'logicalStructure',
    'mindMap',
    'verticalTimeline'
  ]
  
  export const supportNodeUseLineStyleLayouts = [
    'logicalStructure',
    'mindMap',
    'catalogOrganization',
    'organizationStructure'
  ]
  
  export const supportRootLineKeepSameInCurveLayouts = [
    'logicalStructure',
    'mindMap'
  ]
  
  // 彩虹线条配置
  export const rainbowLinesOptions = [
    {
      value: 'close'
    },
    {
      value: 'colors1',
      list: [
        'rgb(255, 213, 73)',
        'rgb(255, 136, 126)',
        'rgb(107, 225, 141)',
        'rgb(151, 171, 255)',
        'rgb(129, 220, 242)',
        'rgb(255, 163, 125)',
        'rgb(152, 132, 234)'
      ]
    },
    {
      value: 'colors2',
      list: [
        'rgb(248, 93, 93)',
        'rgb(255, 151, 84)',
        'rgb(255, 214, 69)',
        'rgb(73, 205, 140)',
        'rgb(64, 192, 255)',
        'rgb(84, 110, 214)',
        'rgb(164, 93, 220)'
      ]
    },
    {
      value: 'colors3',
      list: [
        'rgb(140, 240, 231)',
        'rgb(74, 210, 255)',
        'rgb(65, 168, 243)',
        'rgb(49, 128, 205)',
        'rgb(188, 226, 132)',
        'rgb(113, 215, 123)',
        'rgb(120, 191, 109)'
      ]
    },
    {
      value: 'colors4',
      list: [
        'rgb(169, 98, 99)',
        'rgb(245, 125, 123)',
        'rgb(254, 183, 168)',
        'rgb(251, 218, 171)',
        'rgb(138, 163, 181)',
        'rgb(131, 127, 161)',
        'rgb(84, 83, 140)'
      ]
    },
    {
      value: 'colors5',
      list: [
        'rgb(255, 229, 142)',
        'rgb(254, 158, 41)',
        'rgb(248, 119, 44)',
        'rgb(232, 82, 80)',
        'rgb(182, 66, 98)',
        'rgb(99, 54, 99)',
        'rgb(65, 40, 82)'
      ]
    },
    {
      value: 'colors6',
      list: [
        'rgb(171, 227, 209)',
        'rgb(107, 201, 196)',
        'rgb(55, 170, 169)',
        'rgb(18, 135, 131)',
        'rgb(74, 139, 166)',
        'rgb(75, 105, 150)',
        'rgb(57, 75, 133)'
      ]
    }
  ]
  