export const storage = {
  get(key: string, optionalDefaultValue?: any): any {
    if (import.meta.client) {
      const ret = localStorage.getItem(key);
      if (!ret && optionalDefaultValue) {
        return optionalDefaultValue;
      }
      if (ret) {
        try {
          const obj = JSON.parse(ret);
          return obj?.data ?? "";
        } catch (error) {
          return "";
        }
      }
      return "";
    }
    return optionalDefaultValue ?? "";
  },

  set(key: string, value: any): any {
    if (import.meta.client) {
      const _val = JSON.stringify({ data: value });
      localStorage.setItem(key, _val);
    }
  },

  remove(key: string): void {
    if (import.meta.client) {
      localStorage.removeItem(key);
    }
  },
};
