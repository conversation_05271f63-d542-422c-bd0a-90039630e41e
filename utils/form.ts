import { message } from "ant-design-vue";
import type { CreatorRequireFieldInfo, CreatorsCategoryInfo, FormValues } from '~/services/types/appMessage';
import type { SubmitParams } from '~/services/types/create';
import { CreateSubmissionAssistChineseType, CreateSubmissionAssistType, CreateSubmissionOutlineModalType } from '~/utils/constants';
import { getFieldItemSizeValue } from '~/utils/utils';

/**
 * 检查大纲层级和其他条件
 */
export const checkHierarchyAndOtherCondition = (pList: OutlineElement[]): boolean => {
    let level1 = 0
    let content = ''
    let maxLevel = 0

    for (const item of pList) {
        const level = parseInt(item.getAttribute('data-lv') || '0')
        if (level === 1) {
            level1++
        }
        // 记录最大层级
        if (level > maxLevel) {
            maxLevel = level
        }
        content += item.textContent
        if (!item.textContent?.trim().length) {
            message.warning('请补充大纲的"章/节"内容')
            return false
        }
        if (item.textContent.trim().length > 500) {
            message.warning('单个章/节/条字数超过限制（500字），请修改后提交')
            return false
        }
    }

    // 检查大纲层级不超过3层
    if (maxLevel > 3) {
        message.warning('大纲最多支持3层结构')
        return false
    }

    if (!content?.trim()) {
        message.warning('请完善大纲内容，请勿出现空白内容')
        return false
    }
    if (level1 < 2) {
        message.warning('自定义大纲最少需要2章，请增加章节')
        return false
    }
    if (level1 > 15) {
        message.warning('自定义大纲最多支持15章，请删减适当章节')
        return false
    }
    const resultList = formatOutlineTextContent(pList)
    if (!resultList?.length) {
        message.warning('请补充大纲的"章/节"内容')
        return false
    }
    return true
}

/**
 * 格式化大小选项
 */
export const sizeFormat = (size: string, fieldSizeList: CreatorRequireFieldInfo[]): string => {
    const options = fieldSizeList.length > 0 ? fieldSizeList[0].options : ''
    if (!options) return size

    const items = options.split(',')
    const matchedItem = items.find((child: string) => size === getFieldItemSizeValue(child))
    return matchedItem || size
}

/**
 * 获取提交参数
 */
export const getSubmitParams = (
    formValues: FormValues,
    topicValue: string,
    creatorInfo: any,
    outlineValue: OutlineElement[],
    customSizeLength: number,
    fieldSizeList: CreatorRequireFieldInfo[],
    attachments?: Array<{ fileId: string; repositoryFileId?: string }>
): SubmitParams => {
    let size = creatorInfo.code == 'book' ? `${(formValues.size || 4)}` : transformSizeToEn(formValues.size || 'small')
    const params: SubmitParams = {
        creatorCode: creatorInfo.code,
        formData: {
            topic: topicValue,
            size,
            isUseLargeWorker: creatorInfo.config?.isUseLargeWorker || false,
            isPayFirst: creatorInfo.config?.isPayFirst || false,
            params: {
                ...formValues,
                size: creatorInfo.code == 'book' ? `${(formValues.size || 4)}` : sizeFormat(formValues.size || '', fieldSizeList)
            }
        }
    }

    if (creatorInfo.code == 'insight_chart') {
        params.formData.params.topic = topicValue


        if (params.formData.params.title == EditorContent.Schematic || params.formData.params.title == EditorContent.Table) {
            params.formData.params.chartType = ''
            params.formData.params.imageRatio = ''
        }

        if (params.formData.params.title == EditorContent.Chart) {
            params.formData.params.chartType = ''
        }

        if (params.formData.params.title == EditorContent.Image) {
            params.formData.params.imageRatio = ''
        }
    }

    if (params.formData.params.title && creatorInfo.code == 'insight_chart') {
        params.formData.params.code = params.formData.params.title.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase()
        params.formData.params.title = EditorContentType[params.formData.params.title as keyof typeof EditorContentType] || ''
    }

    if (!creatorInfo.contentFieldName) {
        params.formData.params.topic = ''
    }


    if (formValues.outline === CreateSubmissionAssistType.custom ||
        formValues.outline === CreateSubmissionAssistType.document) {
        const list = formatOutlineTextContent(outlineValue)
        let newCustomSizeLength = customSizeLength || 0
        if (list?.length) {
            params.formData.userOutline = JSON.stringify(list)

            let lv1Number = 0
            let lv2Number = 0
            let lv3Number = 0
            lv1Number = list.length
            list.map((item) => {
                if (item.sections && item.sections.length > 0) {
                    lv2Number += item.sections.length - 1
                    item.sections.map((child) => {
                        if (child.nodes && child.nodes.length > 0) {
                            lv3Number += child.nodes.length - 1
                        }
                    })
                }
            })
            newCustomSizeLength = lv1Number + lv2Number + lv3Number
        }
        params.formData.customSizeLength = newCustomSizeLength
        size = 'custom'
    } else {
        params.formData.userOutline = ''
    }

    params.formData.size = size
    if (attachments?.length) {
        params.formData.attachments = attachments
    }

    return params
}

/**
 * 验证表单
 */
export const validateForm = (
    formValues: FormValues,
    topicValue: string,
    creatorInfo: CreatorsCategoryInfo,
    requiredFields: CreatorRequireFieldInfo[],
    isShowOutline: boolean,
    outlineValue: OutlineElement[],
    isUploadOrSelectedKnowledgeFile: boolean
): boolean => {

    try {
        // console.log("formValues  ==> ", formValues)
        // console.log("requiredFields  ==> ", requiredFields)

        if (creatorInfo.contentFieldName && (!topicValue || !topicValue.trim())) {
            message.warning('请填写标题')
            return false
        }

        const uploadField = requiredFields.find(item => item.fieldCode === 'upload');
        if (uploadField) {
            if (formValues['upload'] == CreateSubmissionAssistChineseType.custom || formValues['upload'] == CreateSubmissionAssistType.custom || formValues['upload'] == CreateSubmissionAssistType.document) {
                // uploadField.isRequired = 'Y';
                if (!isUploadOrSelectedKnowledgeFile) {
                    message.warning('请上传文件')
                    return false
                }
            } else {
                // uploadField.isRequired = 'N';
            }
        }

        for (const field of requiredFields) {

            if (field.isRequired !== 'Y') continue

            // console.log("requiredFields field ==> ", field)

            if (field.fieldCode == 'outline_ask') {
                if (formValues['outline_model'] == CreateSubmissionOutlineModalType.yes && !formValues['outline_ask']) {
                    message.warning('请填写大纲')
                    return false
                }
                continue
            }

            if (field.fieldCode === 'outline') {
                if (isShowOutline && !formValues[field.fieldCode]) {
                    message.warning('请填写大纲')
                    return false
                }
                continue
            }

            if (field.fieldCode == 'upload' && !isUploadOrSelectedKnowledgeFile) {
                message.warning('请上传文件')
                return false
            }


            // 针对size字段的特殊处理
            if (field.fieldCode == 'size') {
                const fieldValue = formValues[field.fieldCode];
                // 判断是否为空值 (空字符串、null、undefined或NaN)
                const isEmpty =
                    fieldValue === undefined ||
                    fieldValue === null ||
                    fieldValue === '' ||
                    (typeof fieldValue === 'string' && fieldValue.trim() === '') ||
                    (typeof fieldValue === 'number' && isNaN(fieldValue));

                if (isEmpty) {
                    if (formValues.outline == CreateSubmissionAssistType.custom || formValues.outline == CreateSubmissionAssistType.document) {
                        continue
                    } else {
                        message.warning('请选择篇幅')
                        return false
                    }
                }
                continue
            }
            // console.log("field.fieldCode ==>", field.fieldCode)
            // console.log("isUploadOrSelectedKnowledgeFile ==>", isUploadOrSelectedKnowledgeFile)


            // 通用字段判空逻辑
            const fieldValue = formValues[field.fieldCode];
            // 判断字段值是否为空
            const isEmpty =
                fieldValue === undefined ||
                fieldValue === null ||
                fieldValue === '' ||
                (typeof fieldValue === 'string' && fieldValue.trim() === '') ||
                (typeof fieldValue === 'number' && isNaN(fieldValue));

            if (isEmpty) {
                message.warning('请填写完整信息')
                return false
            }
        }

        if (creatorInfo.code == 'ppt') {
            return true
        }

        if (creatorInfo.code == 'book') {
            return true
        }
        if (isShowOutline) {

            if (formValues.outline === CreateSubmissionAssistType.ai ||
                formValues.outline === CreateSubmissionAssistChineseType.ai) {
                return true
            }


            if (outlineValue.length == 0) {
                message.warning('请填写有效大纲')
                return false
            }

            if (outlineValue[0].getAttribute('data-lv') !== '1') {
                message.warning('请填写有效大纲')
                return false
            }
            return checkHierarchyAndOtherCondition(outlineValue)
        }

        return true
    } catch (error) {

        return false
    }
}

/**
 * 验证大纲树结构
 */
export const validateOutlineTree = (outlineTree: any[]): { valid: boolean; message: string } => {
    if (!outlineTree || outlineTree.length === 0) {
        return { valid: false, message: '请填写大纲内容' };
    }
    // console.log("outlineTree  ==> ", outlineTree)
    // 检查每一层的name是否为空
    for (const chapter of outlineTree) {
        if (!chapter.chapter_title || !chapter.chapter_title.trim()) {
            return { valid: false, message: '章标题不能为空' };
        }

        if (chapter.children && chapter.children.length > 0) {
            for (const section of chapter.children) {
                if (!section.section_title || !section.section_title.trim()) {
                    return { valid: false, message: '节标题不能为空' };
                }

                if (section.children && section.children.length > 0) {
                    for (const node of section.children) {
                        if (!node.node_title || !node.node_title.trim()) {
                            return { valid: false, message: '条标题不能为空' };
                        }
                    }
                }
            }
        }
    }

    // 检查章节数量
    if (outlineTree.length < 2) {
        return { valid: false, message: '自定义大纲最少需要2章，请增加章节' };
    }

    if (outlineTree.length > 20) {
        return { valid: false, message: '自定义大纲最多支持20章，请删减适当章节' };
    }

    return { valid: true, message: '' };
} 