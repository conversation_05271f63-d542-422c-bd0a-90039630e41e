import copy from 'copy-text-to-clipboard';
export function copyToClipboard(text: string): boolean {
    // First, we'll check to make sure the clipboard API is available. If it is, copy that text.
    if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard.writeText(text);
        return true
    } else {
        // Just in case navigator.clipboard isn't supported...which is very unlikely.
        console.log('This is a very old version of a browser.');
        return copy(text)
    }
}

// 复制文本到剪贴板
export const copyTextToClipboard = (text: string) => {
    // 使用textarea可以保留换行
    const input = document.createElement('textarea')
    // input.setAttribute('value', text)
    input.innerHTML = text
    document.body.appendChild(input)
    input.select()
    document.execCommand('copy')
    document.body.removeChild(input)
}