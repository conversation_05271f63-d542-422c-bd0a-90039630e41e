// import { StarloveConstants } from '@/utils/starloveConstants';
import { callNativeMethodWithCb } from '@/utils/device'
// 鼠标相关的业务逻辑
export const mouseCheckVersion = async (version: string) => {
    try {
        if (version) {
            const _version = +(version.replace(/\./g, ''))
            callNativeMethodWithCb(`app/checkVersion?version=${_version}`, (result: any) => {
                console.log('checkVersion result ==>', result)
            })
        }

    } catch (error) {
        console.log(error)
    }
}
// export const mouseSendDeviceInfo = (version: string) => {
//     window['sendDeviceInfo'] = function (data: string, cc: string) {
//         console.log("Sending device info:", data, cc);
//         // 这里可以加入实际的逻辑，例如调用 API 发送设备信息
//         if (data && cc) {
//             if (window && window.sessionStorage) {
//                 sessionStorage.setItem(StarloveConstants.keyOflocalStorage.mouseDeviceInfo, JSON.stringify({ data, cc }))
//             }
//         }
//     };
// }