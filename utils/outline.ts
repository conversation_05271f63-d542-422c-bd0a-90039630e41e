import type { Chapter, Nodes, Section } from "~/services/types/appMessage";

/**
 * 移除标题中的序号和标点符号
 * 处理如：第一章、第二章、一、二、1.、2. 等开头的标题
 * @param title 原始标题
 * @returns 处理后的标题
 */
const removeNumberPrefix = (title: string): string => {
    if (!title) return '';

    // 匹配"第x章"、"第x节"等模式（中文数字）
    let result = title.replace(/^第[\d一二三四五六七八九十]+[章节部分篇、. :： ]+/g, '');

    // 匹配单独的中文数字序号（如：一、二、三）
    result = result.replace(/^[\d一二三四五六七八九十]+[、\.\s:：]/g, '');

    // 匹配阿拉伯数字序号（如：1、2、3，1.、2.）
    result = result.replace(/^\d+[、\.\s:：]/g, '');

    // 移除开头可能剩余的标点符号
    result = result.replace(/^[\s、\.\:：]/g, '');

    // console.log("title ==>", result)

    return result.trim();
};

export const convertChapterData = (list: any, maxLevel: number): Chapter[] => {
    const result: Chapter[] = [];
    // 防御性编程检查
    if (!list || !Array.isArray(list)) {
        return result;
    }
    for (const chapter of list) {
        // Level 1 节点，移除序号前缀
        const level1Node: Chapter = {
            chapter_title: removeNumberPrefix(chapter.chapter_title || ''),
            sections: []
        };

        // 处理 pages
        if (chapter.pages && Array.isArray(chapter.pages) && maxLevel >= 2) {
            for (const page of chapter.pages) {
                // Level 2 节点，移除序号前缀
                const level2Node: Section = {
                    section_title: removeNumberPrefix(page.section_title || ''),
                    nodes: []
                };
                // 处理 node_title
                if (page.node_title && Array.isArray(page.node_title) && maxLevel >= 3) {
                    for (const nodeName of page.node_title) {
                        // Level 3 节点，移除序号前缀
                        const level3Node: Nodes = {
                            node_title: removeNumberPrefix(nodeName || '')
                        };
                        level2Node.nodes!.push(level3Node);
                    }
                }
                level1Node.sections!.push(level2Node);
            }
        }
        result.push(level1Node);
    }
    return result;
}