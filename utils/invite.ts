import { UserService } from '@/services/user'
import axios from 'axios'
import { reactive } from 'vue'
import { stroageKeyOfMiniappQrcode, wechatMiniAppAppId } from './constants'
import { storage } from './local-storage'
import { StarloveUtil } from './util'

const bgUrl = 'https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/miniapp-poster.png'

const InviteUtil = {
  getQRCode: async (apiBase: any) => {
    const queryParams = reactive({
      page: `pages/xiaoin/invite/index`,
      scene: StarloveUtil.convertObjectToParams({
        sharerUserId: UserService.getSelfUserId(),
      }),
      isBase64: false,
      is_hyaline: true,
      env_version: StarloveUtil.isInTestServer() ? 'trial' : 'release',
    })

    const baseUrl = `${apiBase}/wechat/ma/${wechatMiniAppAppId}/createWxaCodeUnlimited?page=${queryParams.page}&scene=${queryParams.scene}&isBase64=${queryParams.isBase64}&isHyaline=${queryParams.is_hyaline}&envVersion=${queryParams.env_version}`

    try {
      // 发送网络请求获取二维码图片
      const response = await axios.get(baseUrl, {
        responseType: 'blob', // 设置响应类型为 Blob
      })
      // 将 二维码的 Blob 数据转换为 Base64
      const qrCodeBase64 = await InviteUtil.blobToBase64(response.data)
      return qrCodeBase64
    } catch (error) {
      console.error('Error fetching QR code:', error)
    }
  },

  blobToBase64: async (blob: any) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => resolve(reader.result)
      reader.onerror = (error) => reject(error)
      reader.readAsDataURL(blob)
    })
  },

  draw: (canvas: any, qrCodeBase64: any, callback: any) => {
    // console.log('canvas ==>', canvas)
    const context = canvas.getContext('2d')
    if (!context) {
      return
    }
    // console.log('context ==>', context)
    // 绘制背景图片
    const background = new Image()
    // canvas跨域
    background.setAttribute('crossOrigin', 'anonymous')
    background.src = bgUrl

    background.onload = () => {
      requestAnimationFrame(() => {
        const devicePixelRatio = window.devicePixelRatio || 1
        const canvasWidth = canvas.offsetWidth || 320
        const canvasHeight = canvas.offsetHeight || 440

        // 根据设备像素比例调整 Canvas 大小
        canvas.width = canvasWidth * devicePixelRatio
        canvas.height = canvasHeight * devicePixelRatio

        const qrCodeSize = Math.min(canvas.width / 2, canvas.height / 2) * 0.7 // 二维码大小为 Canvas 宽高的 70%
        const qrCodeX = (canvas.width - qrCodeSize) / 2 // 二维码左上角 x 坐标
        const qrCodeY = (canvas.height - qrCodeSize) / 1.36 // 二维码左上角 y 坐标

        context.drawImage(background, 0, 0, canvas.width, canvas.height)
        // 绘制二维码图片
        const qrCode = new Image()
        qrCode.src = qrCodeBase64 // 使用获取到的二维码 Base64 数据
        qrCode.onload = () => {
          context.drawImage(qrCode, qrCodeX, qrCodeY, qrCodeSize, qrCodeSize)

          setTimeout(() => {
            InviteUtil.readBlobAsBase64Data(canvas, callback)
          }, 10)
        }
      })
    }
  },

  readBlobAsBase64Data: (canvas: any, callback: any) => {
    canvas.toBlob((blob: any) => {
      if (!blob) {
        return
      }
      const reader = new FileReader()
      reader.readAsDataURL(blob)
      reader.onloadend = function () {
        const base64Data = reader.result
        // qrcodeData.value = base64Data
        // 将 base64 数据保存在本地存储中
        storage.set(stroageKeyOfMiniappQrcode, base64Data)
        callback && callback()
      }
    }, 'image/png')
  },

  saveBas64Img: (url: any, name: any) => {
    const base64 = url.toString()
    const byteCharacters = atob(base64.replace(/^data:image\/(png|jpeg|jpg);base64,/, ''))
    const byteNumbers = new Array(byteCharacters.length)
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i)
    }
    const byteArray = new Uint8Array(byteNumbers)
    const blob = new Blob([byteArray], {
      type: undefined,
    })
    const aLink = document.createElement('a')
    aLink.download = name //这里写保存时的图片名称
    aLink.href = URL.createObjectURL(blob)
    aLink.click()
  },
}

export { InviteUtil }

