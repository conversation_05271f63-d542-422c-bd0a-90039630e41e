// import useEventBus from '@/eventBus';
import { addDesktop } from '@/api/user'
import { useUserStore } from '@/stores/user'
import { inRunningInPWA } from './utils'
// import { StarloveConstants } from './starloveConstants';

export function installPrompt() {
  const store = useUserStore()
  // const eventBus = useEventBus()
  if (store.installPromptEvent) {
    const e: any = store.installPromptEvent
    e.prompt()

    e.userChoice.then((choiceResult: any) => {
      if (choiceResult.outcome === 'accepted') {
        // 通知安装成功
        // addDeskappResultAplusQueue('success')
        inRunningInPWA()
        addDesktop()
        store.isAcceptInstallationApp = true
        // eventBus.emit(StarloveConstants.keyOfEventBus.acceptInstallationAppCompleted)
        store.clearInstallPromptEvent()
      } else {
        // addDeskappResultAplusQueue('fail')
      }
    })
  }
  // if (installPromptEvent) {
  //   installPromptEvent.prompt().then((choiceResult) => {
  //     console.log(choiceResult.outcome);

  //   });
  // }
}
