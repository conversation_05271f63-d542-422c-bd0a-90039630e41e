import OSS from 'ali-oss'

export async function getUploadAliossSign(data: any) {
  const client = new OSS({
    accessKeyId: data.accessKeyId,
    accessKeySecret: data.accessKeySecret,
    stsToken: data.securityToken,
    bucket: data.bucket,
    region: data.region,
    refreshSTSToken: {
      accessKeyId: data.accessKeyId,
      accessKeySecret: data.accessKeySecret,
      stsToken: data.securityToken,
    },
    refreshSTSTokenInterval: 300000,
    secure: true,
  })
  return client
}
