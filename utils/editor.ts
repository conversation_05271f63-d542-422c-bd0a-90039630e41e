import type { Editor } from '@tiptap/vue-3';

/**
 * 检查编辑器是否有有效的光标位置或选中内容
 * @param editor Tiptap编辑器实例
 * @returns boolean 是否有有效的光标位置
 */
export const checkEditorFocus = (editor: Editor | null | undefined): boolean => {
    if (!editor) {
        return false
    }
    const { from, to } = editor.state.selection;
    const docSize = editor.state.doc.content.size - 2;
    // console.log("docSize ==>", docSize)
    if (docSize === 0) {
        return true;
    }
    return from >= 0 && from <= docSize && to >= 0 && to <= docSize;
} 