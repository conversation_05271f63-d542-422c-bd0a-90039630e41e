import { getURLParameters } from './utils';

const StarloveUtil = {
  getBaseUrl: (): string => {
    const _defaultBaseUrl = 'https://lbm-helper-test.starringshop.com/ai'
    if (typeof window !== 'undefined') {
      const config = window.__RUNTIME_CONFIG__;
      if (!config) {
        return _defaultBaseUrl;
      }
      const url = config.apiBase || '';
      return url;
    }
    return _defaultBaseUrl;
  },
  isInTestServer: (): boolean => {
    const url = StarloveUtil.getBaseUrl()
    return url.indexOf('lbm-helper-test') > -1
  },
  getAppNameForShare: () => {
    if (StarloveUtil.isInTestServer()) {
      return '万能小in（测试环境）'
    }
    return `万能小in`
  },

  formatTime: () => {
    const date = new Date()
    const year = date.getFullYear()
    const month = date.getMonth() + 1
    const day = date.getDate()
    const hour = date.getHours()
    const minute = date.getMinutes()
    const second = date.getSeconds()
    return `${year}/${month}/${day} ${hour}:${minute}:${second}`
  },
  isEmptyString: (text: string | undefined): boolean => {
    if (!text) {
      return true
    }

    if (text.length == 0) {
      return true
    }

    if (text.trim().length == 0) {
      return true
    }

    return false
  },
  convertObjectToParams: (obj: any) => {
    const arr: string[] = []
    for (const p in obj)
      if (obj?.hasOwnProperty(p) && obj[p]) {
        arr.push(encodeURIComponent(p) + '=' + encodeURIComponent(obj[p]))
      }
    return arr.join('&')
  },
  getSharerUserIdFromUrl: (): string | undefined => {
    const data: any = getURLParameters(window.location.href)
    // console.log('getSharerUserIdFromUrl router ==>', Taro.getCurrentInstance().router)
    const sharerUserId: string | undefined = data?.sharerUserId || ''
    return sharerUserId
  },
  getChannelInfoFromUrl: (): string | undefined => {
    const data: any = getURLParameters(window.location.href)
    const channelInfo: string | undefined = data?.channel || ''
    return channelInfo
  },

  getClickIdFromUrl: (): string | undefined => {
    const data: any = getURLParameters(window.location.href)
    const clickId: string | undefined = data?.bd_vid || ''
    return clickId
  },

  getQhclickIdFromUrl: (): string | undefined => {
    const data: any = getURLParameters(window.location.href)
    const qhclickId: string | undefined = data?.qhclickid || ''
    return qhclickId
  },

  getSourceIdFromUrl: (): string | undefined => {
    //360 转化的sourceid
    const data: any = getURLParameters(window.location.href)
    const sourceId: string | undefined = data?.sourceId || ''
    return sourceId
  },

  getIsDebugFromUrl: (): boolean => {
    const data: any = getURLParameters(window.location.href)
    const isDebug: string | undefined = data?.isDebug || ''
    return isDebug == '1'
  },

  getIsEnableProFromUrl: (): boolean => {
    const data: any = getURLParameters(window.location.href)
    const isEnablePro: string | undefined = data?.isEnablePro || ''
    return isEnablePro == '1'
  },

  getSharerTimelineFromUrl: (): string | undefined => {
    const data: any = getURLParameters(window.location.href)
    const redirectUrl: string | undefined = data?.redirectUrl || ''
    return redirectUrl
  },

  getUtmSourceFromUrl: (): string | undefined => {
    //console.log(Taro.getCurrentInstance().router?.path)
    const data: any = getURLParameters(window.location.href)
    const utm_source: string | undefined = data?.utm_source || ''
    return utm_source
  },
  getAIEditorBaseUrl: () => {
    // if (StarloveUtil.isInTestServer()) {
    //   return 'https://xiaoin-dev.inschool.top/ai-editor/#'
    // }
    return '/ai-editor/#'
  },
  getInSlidesBaseUrl: () => {
    return '/in-slides'
  },

  getPhoneFromUrl: (): string | undefined => {
    //console.log(Taro.getCurrentInstance().router?.path)
    const data: any = getURLParameters(window.location.href)
    const phone: string | undefined = data?.phone || ''
    return phone
  },
  getTrackIdFromUrl: (): string | undefined => {
    const data: any = getURLParameters(window.location.href)
    const track_id: string | undefined = data?.track_id || ''
    return track_id
  },
  // switchProTheme: (enable: boolean) => {
  //   if (enable) {
  //     document.getElementById('app')?.setAttribute('data-theme', 'pro')
  //   } else {
  //     document.getElementById('app')?.removeAttribute('data-theme')
  //   }
  // }
  // getProThemeEnable: (): boolean => {
  //   return storage.get(StarloveConstants.keyOflocalStorage.proSwitch)
  // }
}

export { StarloveUtil };

