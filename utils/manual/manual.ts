import { generateSchemeLink } from '@/api/user';
import { UserService } from '~/services/user';
import { useChannelStore } from '~/stores/channelId';

/**
 * 检测当前设备是否为移动端
 * @returns {boolean} 是否为移动端设备
 */
export const isMobileDevice = () => {
    if (process.client) {
        // 检查 browser 函数是否存在且为函数类型
        if (typeof browser === 'function') {
            const info = browser();
            if (info.device === 'Mobile' || info.platform === 'Android' || info.browser === 'Quark') {
                return true;
            }
            return false;
        }
        // 如果 browser 函数不存在，使用备用检测方法
        if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
            return true;
        }
        return false;
    }
    return false;
};

/**
 * 生成小程序跳转链接
 * @param code 分类代码
 */
export const loadGenerateSchemeLinkData = async (code: string, item: string) => {
    const sharerUserId = UserService.getSharerUserId() || '1859121823127334913'
    const params = {
        jumpWxa: {
            path: 'pages/pc/home/<USER>/index',
            query: `sharerUserId=${sharerUserId}&code=${code}&${item}`,
            envVersion: 'release'
        },
        isExpire: true,
        expireTime: new Date().getTime() + 24 * 60 * 1000
    }
    const res = await generateSchemeLink(params)
    if (!res.ok || !res.data) {
        return
    }
    window.location.replace(res.data)
}



/**
 * 生成小程序跳转链接   SEO 专用
 * @param code 分类代码
 */
export const loadSeoFriendlySchemeLink = async (code: string, item: string) => {
    const sharerUserId = UserService.getSharerUserId()
    const params = {
        jumpWxa: {
            path: 'pages/pc/home/<USER>/index',
            query: sharerUserId ? `sharerUserId=${sharerUserId}&code=${code}&${item}` : `code=${code}&${item}`,
            envVersion: 'release'
        },
        isExpire: true,
        expireTime: new Date().getTime() + 24 * 60 * 1000
    }
    const res = await generateSchemeLink(params)
    if (!res.ok || !res.data) {
        return
    }
    window.location.replace(res.data)
}


function formatChannel(currentChannel: any) {
    // 先将连续的斜杠替换为单个连字符
    let formatted = currentChannel.replace(/\/+/g, '-');
    // 移除开头和结尾多余的连字符
    formatted = formatted.replace(/^-+|-+$/g, '');
    // 如果原字符串以斜杠开头，在结果前添加连字符
    if (currentChannel.startsWith('/')) {
        formatted = '-' + formatted;
    }
    return formatted;
}

export const trackInitialReferer = () => {
    const route = useRoute();
    const store = useUserStore()
    const channelStore = useChannelStore()
    // 获取当前页面的 Referer 和 URL
    const referrer = document.referrer;
    const currentChannel = route.path;

    sessionStorage.setItem('initial_referrer', referrer);
    // if (!UserService.getSharerUserId()) {
    //     // 检查会话中是否已存在 channel
    //     const existingChannel = sessionStorage.getItem('channel')
    //     const formattedChannel = existingChannel || (currentChannel == '/' ? '-home' : formatChannel(currentChannel))

    //     if (!existingChannel) {
    //         sessionStorage.setItem('channel', formattedChannel)
    //     }

    //     // 使用会话中存储的值更新 store
    //     const sessionChannel = existingChannel || formattedChannel
    //     store.setChannel(`comcn${sessionChannel}`)
    //     channelStore.setChannel(sessionChannel)
    // }



    if (!UserService.getSharerUserId()) {
        // 检查会话中是否已存在 channel
        const formattedChannel = (currentChannel == '/' ? '-home' : formatChannel(currentChannel))
        // 使用会话中存储的值更新 store
        const sessionChannel = formattedChannel
        store.setChannel(`comcn${sessionChannel}`)
    }
}

