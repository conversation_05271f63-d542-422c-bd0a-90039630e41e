// import request from '@/utils/request'
// import { ref } from 'vue'

// export const useApi = (url: string, options = {}) => {
//   const data = ref(null)
//   const error = ref()

//   const { fetch } = useFetch(async () => {
//     try {
//       const response = await request.get(url, options)
//       data.value = response.data
//     } catch (err) {
//       error.value = err
//     }
//   })

//   return { data, error, fetch }
// }
