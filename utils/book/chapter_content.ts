import type { Book, Chapter } from '~/types/book'
import { getChapterFullTitle } from '@/utils/book_utils'

/**
 * 将章节的 JSON 内容转换为纯文本
 */
export function extractTextFromContent(content: any): string {
  if (!content || !content.content) return ''

  return content.content.reduce((text: string, node: any) => {
    if (node.type === 'text') {
      return text + (node.text || '')
    } else if (node.content) {
      return text + extractTextFromContent(node)
    }
    return text
  }, '')
}

/**
 * 获取指定章节之前的所有章节
 */
export function getPreviousChapters(allChapters: Chapter[], currentChapterKey: string): Chapter[] {
  const flattenChapters = (chapters: Chapter[]): Chapter[] => {
    return chapters.reduce((acc: Chapter[], chapter) => {
      acc.push(chapter)
      if (chapter.children?.length) {
        acc.push(...flattenChapters(chapter.children))
      }
      return acc
    }, [])
  }

  const flattened = flattenChapters(allChapters)
  const currentIndex = flattened.findIndex(c => c.key === currentChapterKey)
  
  return currentIndex > 0 ? flattened.slice(0, currentIndex) : []
}

/**
 * 获取指定章节之前的所有章节内容
 */
export function getPreviousChaptersContent(allChapters: Chapter[], currentChapterKey: string, book: Book): string {
  const previousChapters = getPreviousChapters(allChapters, currentChapterKey)
  
  return previousChapters
    .map(chapter => {
      if (chapter.children.length > 0){
        return getChapterFullTitle(chapter, book)
      }

      const content = chapter.content ? extractTextFromContent(chapter.content) : ''
      return getChapterFullTitle(chapter, book) + '\n\n' + content.trim()
    })
    .filter(text => text.length > 0)
    .join('\n\n')
} 