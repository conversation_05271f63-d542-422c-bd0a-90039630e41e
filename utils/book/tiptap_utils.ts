import { generateJSON } from '@tiptap/core'

import Table from '@tiptap/extension-table'
import TableRow from '@tiptap/extension-table-row'
import TableCell from '@tiptap/extension-table-cell'
import TableHeader from '@tiptap/extension-table-header'
import Bold from '@tiptap/extension-bold'
import Document from '@tiptap/extension-document'
import Paragraph from '@tiptap/extension-paragraph'
import Text from '@tiptap/extension-text'


export function convertHtmlToJSON(content: string) {
  return generateJSON(
    content,
    [Document, Paragraph, Text],
  )
}
