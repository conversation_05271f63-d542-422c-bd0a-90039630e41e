import { $fetch } from 'ofetch'

export class DifyClient {
  private baseUrl = 'https://dify.xingxiai.cn/v1'
  // private apiKey = 'app-1eIav9JCyr3nHmK2CHZuCNLw'
  

  async getAnswer(question: string, apiKey: string): Promise<[boolean, string, string, any]> {

    const headers = {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json'
    }

    try {
      const payload = {
        inputs: {},
        query: question,
        response_mode: 'blocking',
        conversation_id: '',
        user: 'book-ai'
      }

      const response = await $fetch(`${this.baseUrl}/chat-messages`, {
        method: 'POST',
        headers: headers,
        body: payload,
        timeout: 300000 // 300秒超时
      })

      if (!response) {
        console.error('Dify API 返回数据为空')
        return [false, 'AI服务返回数据为空', '', null]
      }

      const answer = response.answer || ''
      const newConversationId = response.conversation_id || ''

      if (!answer) {
        console.error('Dify API 返回数据异常:', response)
        return [false, 'AI回答内容为空', '', null]
      }

      return [true, answer, newConversationId, response]

    } catch (error: any) {
      console.error('Dify API 请求异常:', error)
      return [false, 'AI服务连接异常', '', null]
    }
  }
}

// 导出单例实例
export const difyClient = new DifyClient() 