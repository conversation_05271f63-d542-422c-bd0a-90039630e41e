import mitt from 'mitt'

type EditorEvents = {
  'editor:setContent': { content: string | Object, emitUpdate: boolean }
  'editor:getContent': void
  'editor:contentUpdated': any
  'editor:focus': void
  'editor:blur': void
  'editor:clear': void
  'editor:undo': void
  'editor:redo': void
  'editor:insertContent': any
  'editor:insertContentAt': any
  'editor:citationClick': { paperNo: string, opinionId: string },
  'editor:insertContentAtEnd': { content: any[] },
  'editor:appendGeneratedContent': { content: any[] }
  // 'editor:showLoadingIndicator': void,
  // 'editor:hideLoadingIndicator': void,
}

export const editorBus = mitt<EditorEvents>() 