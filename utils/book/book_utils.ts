import type { Book, Chapter } from "~/types/book"

export const getChapterTitlePrefix = (chapter: Chapter, book: Book) => {
  // 用于存储从顶级到当前章节的路径
  const chapterPath: Chapter[] = []
  
  // 递归查找父章节
  const findParentChapters = (currentKey: string, chapters: Chapter[] = []) => {
    for (const ch of chapters) {
      if (ch.key === currentKey) {
        return true
      }
      if (ch.children?.length) {
        if (findParentChapters(currentKey, ch.children)) {
          chapterPath.unshift(ch)
          return true
        }
      }
    }
    return false
  }

  // 从当前章节开始查找
  if (book.chapters?.length) {
    findParentChapters(chapter.key, book.chapters)
  }
  
  // 添加当前章节到路径末尾
  chapterPath.push(chapter)
  
  // 构建前缀
  return chapterPath
    .map(ch => ch.order + 1)
    .join('.')
}

export const getChapterFullTitle = (chapter: Chapter, book: Book) => {
    return `${getChapterTitlePrefix(chapter, book)} ${chapter.title}`
  }


export const getAncestorChaptersFullTitles = (chapter: Chapter, book: Book) => {
  // 用于存储从顶级到当前章节的路径
  const chapterPath: Chapter[] = []
  
  // 递归查找父章节
  const findParentChapters = (currentKey: string, chapters: Chapter[] = []) => {
    for (const ch of chapters) {
      if (ch.key === currentKey) {
        return true
      }
      if (ch.children?.length) {
        if (findParentChapters(currentKey, ch.children)) {
          chapterPath.unshift(ch)
          return true
        }
      }
    }
    return false
  }

  // 从当前章节开始查找
  if (book.chapters?.length) {
    findParentChapters(chapter.key, book.chapters)
  }

  if (chapterPath.length === 0) {
    return '(没有父章节)'
  }
  
  // 返回所有祖先章节的完整标题，用空格连接
  return chapterPath
    .map(ch => getChapterFullTitle(ch, book))
    .join(' ')
}