import { Document, Packer, Paragraph, TextRun, ImageRun } from 'docx'
import { Chart as ChartJS } from 'chart.js'

interface ChartNode {
  type: string
  attrs: {
    type: 'bar' | 'line' | 'pie'
    data: any
  }
  content?: any[]
}

interface ParagraphNode {
  type: string
  content?: Array<{ text: string }>
}

interface BulletListNode {
  type: string
  content?: Array<{ content: Array<{ content: Array<{ text: string }> }> }>
}

type EditorNode = ChartNode | ParagraphNode | BulletListNode

async function processNode(node: EditorNode): Promise<Paragraph[]> {
  const paragraphs: Paragraph[] = []
  
  // 递归处理所有内容
  const processContent = async (content: any[]): Promise<Paragraph[]> => {
    const result: Paragraph[] = []
    if (!content) return result
    
    for (const item of content) {
      result.push(...(await processNode(item)))
    }
    return result
  }

  switch (node.type) {
    case 'paragraph':
      const paragraphNode = node as ParagraphNode
      if (paragraphNode.content) {
        paragraphs.push(new Paragraph({
          children: [new TextRun({ text: paragraphNode.content[0]?.text || '' })]
        }))
      }
      break
      
    case 'bulletList':
      const bulletListNode = node as BulletListNode
      if (bulletListNode.content) {
        for (const item of bulletListNode.content) {
          if (item.content) {
            // 递归处理列表项内容
            const listItemContent = await processContent(item.content)
            // 确保第一个段落有项目符号
            if (listItemContent.length > 0) {
              const firstParagraph = listItemContent[0]
              const newParagraph = new Paragraph({
                bullet: { level: 0 },
                children: (firstParagraph as any).options?.children || []
              })
              listItemContent[0] = newParagraph
            }
            paragraphs.push(...listItemContent)
          }
        }
      }
      break
      
    case 'listItem':
      if (node.content) {
        // 直接处理列表项内容
        paragraphs.push(...(await processContent(node.content)))
      }
      break
      
    case 'chart':
      const chartNode = node as ChartNode
      // 为图表添加标题
      paragraphs.push(new Paragraph({
        children: [
          new TextRun({ 
            text: `[${chartNode.attrs.type === 'bar' ? '柱状图' : chartNode.attrs.type === 'line' ? '折线图' : '饼图'}]`,
            bold: true
          })
        ]
      }))
      
      try {
        // 创建临时canvas渲染图表
        const canvas = document.createElement('canvas')
        canvas.width = 800
        canvas.height = 400
        
        // 根据图表类型渲染
        const ctx = canvas.getContext('2d')
        if (ctx) {
          const chart = new ChartJS(ctx, {
            type: chartNode.attrs.type,
            data: chartNode.attrs.data,
            options: {
              responsive: false,
              animation: false,
              plugins: {
                legend: {
                  display: true,
                  position: 'top'
                }
              }
            }
          })
          
          // 等待图表渲染完成
          await new Promise(resolve => setTimeout(resolve, 100))
          
          // 转换为base64图片
          const imageData = canvas.toDataURL('image/png')
          
          // 添加图片到文档
          const base64Data = imageData.split(',')[1]
          const binaryString = window.atob(base64Data)
          const bytes = new Uint8Array(binaryString.length)
          for (let i = 0; i < binaryString.length; i++) {
            bytes[i] = binaryString.charCodeAt(i)
          }
          
          paragraphs.push(new Paragraph({
            children: [
              new ImageRun({
                data: bytes,
                transformation: {
                  width: 600,
                  height: 300
                },
                type: 'png'
              })
            ]
          }))
          
          chart.destroy()
        }
      } catch (error) {
        console.error('图表转换失败:', error)
        // 如果转换失败，退回到文本模式
        const chartData = chartNode.attrs.data
        chartData.labels?.forEach((label: string, index: number) => {
          const value = chartData.datasets[0].data[index]
          paragraphs.push(new Paragraph({
            children: [new TextRun({ text: `${label}: ${value}` })]
          }))
        })
      }
      break
  }
  
  return paragraphs
}

export async function exportChartToWord(editorContent: any[]): Promise<Blob> {
  const sections: Paragraph[] = []
  
  // 处理所有节点
  const processedSections = []
  for (const node of editorContent || []) {
    processedSections.push(...(await processNode(node)))
  }
  sections.push(...processedSections)

  const doc = new Document({
    sections: [{
      properties: {},
      children: sections
    }]
  })

  return await Packer.toBlob(doc)
} 