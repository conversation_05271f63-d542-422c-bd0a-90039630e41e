import axios, { AxiosError } from 'axios'
import { UserService } from '~/services/user'
import { API_CONFIG } from './config'


const http = axios.create({
  // baseURL: API_CONFIG.BASE_URL,
  baseURL: API_CONFIG.getBaseUrl(),
  timeout: 15 * 60 * 1000,
  withCredentials: true,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
})

// 用于存储等待token刷新的请求
let refreshPromise: Promise<string> | null = null

// 请求拦截器
http.interceptors.request.use(
  (config) => {
    // const userStore = useUserStore()
    // if (userStore.accessToken) {
    //   config.headers.Authorization = `Bearer ${userStore.accessToken}`
    // }
    config.headers.Authorization = 'demo-user'
    // if (config.url && config?.url.includes('/xiaoin/') > 0) {
    config.headers.token = `Bearer ${UserService.getToken()}`
    // }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
http.interceptors.response.use(
  (response) => {
    return response
  },
  async (error: AxiosError) => {

    // 处理其他错误
    // const errorMessage = (error.response?.data as { detail?: string })?.detail || '请求失败'
    // message.error(errorMessage)
    return Promise.reject(error)
  }
)

export default http 