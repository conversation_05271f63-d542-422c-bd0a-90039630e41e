import type { Book } from '@/types/book'
import { getChapterFullTitle } from '@/utils/book/book_utils'
import { Chart as ChartJS } from 'chart.js'
import { Document, HeadingLevel, ImageRun, Packer, Paragraph, Table, TableCell, TableRow, TextRun, WidthType } from 'docx'

interface TextNode {
  type: 'text'
  text: string
  marks?: Array<{
    type: string
  }>
}

interface ParagraphNode {
  type: 'paragraph'
  content?: Array<TextNode>
}

interface HeadingNode {
  type: 'heading'
  attrs: {
    level: number
  }
  content?: Array<TextNode>
}

interface ChartNode {
  type: 'chart'
  attrs: {
    type: 'bar' | 'line' | 'pie'
    data: any
  }
}

interface TableNode {
  type: 'table'
  content: Array<TableRowNode>
}

interface TableRowNode {
  type: 'tableRow'
  content: Array<TableCellNode>
}

interface TableCellNode {
  type: 'tableHeader' | 'tableCell'
  attrs: {
    colspan: number
    rowspan: number
    colwidth: number[] | null
  }
  content: Array<ParagraphNode>
}

type EditorNode = ParagraphNode | HeadingNode | ChartNode | TableNode

// 处理编辑器节点
async function processNode(node: EditorNode): Promise<Array<Paragraph | Table>> {
  const elements: Array<Paragraph | Table> = []

  switch (node.type) {
    case 'table':
      const rows: TableRow[] = []

      for (const rowNode of node.content) {
        const cells: TableCell[] = []

        for (const cellNode of rowNode.content) {
          const cellContent = await processCellContent(cellNode.content)

          cells.push(new TableCell({
            children: cellContent,
            columnSpan: cellNode.attrs.colspan,
            rowSpan: cellNode.attrs.rowspan,
            verticalAlign: 'center',
          }))
        }

        rows.push(new TableRow({
          children: cells,
        }))
      }

      elements.push(new Table({
        rows,
        width: {
          size: 100,
          type: WidthType.PERCENTAGE,
        },
        margins: {
          top: 100,
          bottom: 100,
          left: 100,
          right: 100,
        },
      }))
      break

    case 'chart':

      try {
        // 创建临时canvas渲染图表
        const canvas = document.createElement('canvas')
        canvas.width = 800
        canvas.height = 400

        // 根据图表类型渲染
        const ctx = canvas.getContext('2d')
        if (ctx) {
          const chart = new ChartJS(ctx, {
            type: node.attrs.type,
            data: node.attrs.data,
            options: {
              responsive: false,
              animation: false,
              plugins: {
                legend: {
                  display: true,
                  position: 'top'
                }
              }
            }
          })

          // 等待图表渲染完成
          await new Promise(resolve => setTimeout(resolve, 100))

          // 转换为base64图片
          const imageData = canvas.toDataURL('image/png')

          // 添加图片到文档
          const base64Data = imageData.split(',')[1]
          const binaryString = window.atob(base64Data)
          const bytes = new Uint8Array(binaryString.length)
          for (let i = 0; i < binaryString.length; i++) {
            bytes[i] = binaryString.charCodeAt(i)
          }

          elements.push(new Paragraph({
            children: [
              new ImageRun({
                data: bytes,
                transformation: {
                  width: 600,
                  height: 300
                },
                type: 'png'
              })
            ],
            spacing: {
              after: 200,
            }
          }))

          chart.destroy()
        }
      } catch (error) {
        console.error('图表转换失败:', error)
        // 如果转换失败，退回到文本模式
        const chartData = node.attrs.data
        chartData.labels?.forEach((label: string, index: number) => {
          const value = chartData.datasets[0].data[index]
          elements.push(new Paragraph({
            children: [
              new TextRun({
                text: `${label}: ${value}`,
                size: 24,
                color: '000000'
              })
            ],
            spacing: { line: 360 }
          }))
        })
      }
      break;

    case 'paragraph':
      if (node.content) {
        const children = node.content.map(item => {
          if (item.type === 'text') {
            const isBold = item.marks?.some(mark => mark.type === 'bold')
            const isItalic = item.marks?.some(mark => mark.type === 'italic')
            return new TextRun({
              text: item.text,
              bold: isBold,
              italics: isItalic,
              size: 24,
              color: '000000'
            })
          }
          return new TextRun({ text: '' })
        })

        elements.push(new Paragraph({
          children,
          indent: { firstLine: 480 }, // 2个中文字符的缩进
          spacing: { line: 360 } // 1.5倍行距
        }))
      }
      break

    case 'heading':
      if (node.content) {
        const children = node.content.map(item => {
          if (item.type === 'text') {
            return new TextRun({
              text: item.text,
              bold: true,
              size: node.attrs.level === 1 ? 32 :
                node.attrs.level === 2 ? 28 : 24,
              color: '000000'
            })
          }
          return new TextRun({ text: '' })
        })

        elements.push(new Paragraph({
          heading: node.attrs.level === 1 ? HeadingLevel.HEADING_1 :
            node.attrs.level === 2 ? HeadingLevel.HEADING_2 :
              HeadingLevel.HEADING_3,
          children,
          spacing: {
            before: 400,
            after: 200,
          }
        }))
      }
      break
  }

  return elements
}

// 处理表格单元格内容
async function processCellContent(content: Array<ParagraphNode>): Promise<Paragraph[]> {
  const paragraphs: Paragraph[] = []

  for (const node of content) {
    if (node.content) {
      const children = node.content.map(item => {
        if (item.type === 'text') {
          const isBold = item.marks?.some(mark => mark.type === 'bold')
          const isItalic = item.marks?.some(mark => mark.type === 'italic')
          return new TextRun({
            text: item.text,
            bold: isBold,
            italics: isItalic,
            size: 24,
            color: '000000'
          })
        }
        return new TextRun({ text: '' })
      })

      paragraphs.push(new Paragraph({
        children,
        spacing: { line: 360 }
      }))
    }
  }

  return paragraphs
}

// 添加递归处理章节的函数
async function processChapter(chapter: any, book: Book): Promise<Array<Paragraph | Table>> {
  const elements: Array<Paragraph | Table> = []

  // 添加章节标题
  elements.push(
    new Paragraph({
      heading: chapter.level === 1 ? HeadingLevel.HEADING_1 :
        chapter.level === 2 ? HeadingLevel.HEADING_2 :
          HeadingLevel.HEADING_3,
      children: [
        new TextRun({
          text: getChapterFullTitle(chapter, book),
          bold: true,
          size: chapter.level === 1 ? 32 :
            chapter.level === 2 ? 28 : 24,
          color: '000000'
        }),
      ],
      spacing: {
        before: 400,
        after: 200,
      }
    })
  )

  // 处理章节内容
  if (chapter.content) {
    try {
      const content = chapter.content
      if (content?.type === 'doc' && Array.isArray(content?.content)) {
        for (const node of content.content) {
          if (node && typeof node === 'object') {
            elements.push(...(await processNode(node)))
          }
        }
      } else {
        elements.push(
          new Paragraph({
            children: [
              new TextRun({
                text: String(chapter.content),
                size: 24,
                color: '000000'
              }),
            ],
            indent: { firstLine: 480 },
            spacing: { line: 360 }
          })
        )
      }
    } catch (error) {
      console.error('Error processing chapter content:', error)
      elements.push(
        new Paragraph({
          children: [
            new TextRun({
              text: String(chapter.content),
              size: 24,
              color: '000000'
            }),
          ],
          indent: { firstLine: 480 },
          spacing: { line: 360 }
        })
      )
    }
  }

  // 递归处理子章节
  if (chapter.children && Array.isArray(chapter.children)) {
    for (const childChapter of chapter.children) {
      elements.push(...(await processChapter(childChapter, book)))
    }
  }

  return elements
}

// 修改导出Word文档的函数
export const exportToWord = async (book: Book) => {
  // 创建文档
  const doc = new Document({
    sections: [{
      properties: {},
      children: [
        // 书名
        new Paragraph({
          heading: HeadingLevel.TITLE,
          children: [
            new TextRun({
              text: book.title || '未命名书籍',
              bold: true,
              size: 40,
              color: '000000'
            }),
          ],
          spacing: {
            after: 400,
          },
          alignment: 'center'
        }),
        // 递归处理所有章节
        ...(await Promise.all((book?.chapters || []).map((chapter: any) => processChapter(chapter, book)))).flat()
      ],
    }],
  })

  // 生成文件名
  const fileName = `${book.title || '未命名书籍'}.docx`

  // 导出为blob并下载
  const buffer = await Packer.toBlob(doc)
  const url = URL.createObjectURL(buffer)
  const a = document.createElement('a')
  a.href = url
  a.download = fileName
  a.click()
  URL.revokeObjectURL(url)

  return fileName
} 