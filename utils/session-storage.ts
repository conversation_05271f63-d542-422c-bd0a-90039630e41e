export const sessionStore = {
  get(key: string, optionalDefaultValue?: any): any {
    if (import.meta.client) {
      const ret = sessionStorage.getItem(key);
      if (!ret && optionalDefaultValue) {
        return optionalDefaultValue;
      }
      if (ret) {
        try {
          const obj = JSON.parse(ret);
          return obj?.data ?? "";
        } catch (error) {
          return "";
        }
      }
      return "";
    }
    return optionalDefaultValue ?? "";
  },

  set(key: string, value: any): any {
    if (import.meta.client) {
      const _val = JSON.stringify({ data: value });
      sessionStorage.setItem(key, _val);
    }
  },

  remove(key: string): void {
    if (import.meta.client) {
      sessionStorage.removeItem(key);
    }
  },
}; 