import { useChannelStore } from '@/stores/channelId'
import { useCoinPlansStore } from '@/stores/coinPlans'
import { useSearchStore } from '@/stores/search'
import { useSwitchAccountStore } from '@/stores/switchAccount'

import { useUserStore } from '@/stores/user'
import { useVipPlansStore } from '@/stores/vipPlans'
import { useVisitorIdStore } from '@/stores/visitorId'

export const resetAllStores = () => {
    const searchStore = useSearchStore()
    const userStore = useUserStore()
    const vipPlansStore = useVipPlansStore()
    const coinPlansStore = useCoinPlansStore()

    const switchAccountStore = useSwitchAccountStore();
    const visitorIdStore = useVisitorIdStore();
    const channelStore = useChannelStore();

    // 重置每个 store 到初始状态
    searchStore.$reset()
    userStore.$reset()
    vipPlansStore.$reset()
    coinPlansStore.$reset()
    switchAccountStore.$reset()
    visitorIdStore.$reset()
    channelStore.$reset()
}