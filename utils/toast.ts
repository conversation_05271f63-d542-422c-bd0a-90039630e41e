// interface ToastOptions {
//   title: string
//   type?: 'success' | 'error' | 'warning' | 'info'
//   timeout?: number
// }

// const defaultOptions = {
//   timeout: 3000,
//   position: 'center',
// }

// export const toast = {
//   show({ title, type = 'info', timeout = 3000 }: ToastOptions) {
//     const toast = useToast()
//     switch (type) {
//       case 'success':
//         toast.add({ title, type: 'success', ...defaultOptions })
//         break
//       case 'error':
//         toast.add({ title, type: 'error', ...defaultOptions })
//         break
//       case 'warning':
//         toast.add({ title, type: 'warning', ...defaultOptions })
//         break
//       case 'info':
//         toast.add({ title, type: 'info', ...defaultOptions })
//         break
//       default:
//         toast.add({ title, type: 'info', ...defaultOptions })
//     }
//   },

//   success(title: string, timeout?: number) {
//     this.show({ title, type: 'success', timeout })
//   },

//   error(title: string, timeout?: number) {
//     this.show({ title, type: 'error', timeout })
//   },

//   warning(title: string, timeout?: number) {
//     this.show({ title, type: 'warning', timeout })
//   },

//   info(title: string, timeout?: number) {
//     this.show({ title, type: 'info', timeout })
//   },
// }
