import { createOrder, payOrder } from '@/api/recharge'
import type { ExtraParams, WebPayQrcode, XiaoinGoodInfo } from '@/services/types/recharge'
import { UserService } from '@/services/user'
import { useCoinPlansStore } from '@/stores/coinPlans'
import { useRechargeStore } from '@/stores/recharge'

import { useUserStore } from '@/stores/user'
import { useVipPlansStore } from '@/stores/vipPlans'
import { getAppId, HTTP_STATUS, RechargeModalTab } from '@/utils/constants'
import { getPlatform } from '@/utils/utils'
import { message } from 'ant-design-vue'

export enum PaymentResult {
  SUCCESS = 'SUCCESS',
  NEED_BIND_MOBILE = 'NEED_BIND_MOBILE',
  ERROR = 'ERROR'
}

export interface PaymentResponse {
  result: PaymentResult
  orderId?: string
  payInfo?: WebPayQrcode
  amount?: number
}

export async function processPayment(plan: XiaoinGoodInfo, payChannel: string, maxNumber: number = 1): Promise<PaymentResponse> {
  if (!UserService.isLogined() || !plan?.id) {
    return { result: PaymentResult.ERROR }
  }

  try {
    const store = useUserStore()
    // 准备订单参数
    let extraParams: ExtraParams = {
      platform: getPlatform(),
      openUrl: store.openRecordUrl,
      returnUrl: 'https://xiaoin.com.cn/pages/xiaoin/recharge/index'
    }
    if (!store.channel) {
      store.channel = UserService.getChannel()
    }
    if (!store.clickId) {
      store.clickId = UserService.getClickId()
    }
    if (!store.sourceId) {
      store.sourceId = UserService.get360SourceId()
    }
    if (!store.qhclickId) {
      store.qhclickId = UserService.get360QhclickId()
    }
    extraParams = {
      ...extraParams,
      channel: store.clickId ? `baidu:${store.channel}` : store.channel,
      clickId: store.clickId || '',
      sourceId: store.sourceId || '',
    }
    if (document.referrer) {
      extraParams['referrer'] = document.referrer
    }
    if (store.qhclickId) {
      extraParams.channel = `360:${store.channel}`
      extraParams.clickId = store.qhclickId
    }
    if (store.clickId) {
      extraParams.channel = `baidu:${store.channel}`
      extraParams.clickId = store.clickId
    }
    if (UserService.getSharerUserId()) {
      extraParams.inviteUserId = UserService.getSharerUserId()
      extraParams.inviteUserIdUpdateTime = UserService.getSharerUserIdUpdateTime()
    }
    const paramsOrder: any = {
      goodsList: [{
        goodsId: plan.id,
        number: maxNumber
      }],
      remark: '',
      extraParams
    }

    // 创建订单
    const createOrderRes = await createOrder(paramsOrder)
    // console.log("createOrderRes==>", createOrderRes)

    // 处理创建订单的响应
    if (!createOrderRes.success) {

      if (createOrderRes?.data?.code === HTTP_STATUS.MOBILE_NOT_BOUND) {
        return { result: PaymentResult.NEED_BIND_MOBILE }
      }
      throw new Error(createOrderRes.message || '创建订单失败')
    }

    const orderId = createOrderRes.data?.orderId
    if (!orderId) {
      throw new Error('订单ID不存在')
    }

    // 发起支付
    const payOrderRes = await payOrder({
      appid: getAppId(),
      orderId,
      payChannel
    })

    if (!payOrderRes.ok) {
      // 获取当前tab并清理对应store的状态
      const rechargeStore = useRechargeStore()
      const currentTab = rechargeStore.currentTab

      if (currentTab === RechargeModalTab.vip) {
        const vipStore = useVipPlansStore()
        vipStore.webPayQrcodeLink = {
          linkUrl: '',
          isQrCode: false,
          isError: true
        }
        vipStore.isQrcodeImageError = true
      }
      if (currentTab === RechargeModalTab.coin) {
        const coinStore = useCoinPlansStore()
        coinStore.webPayQrcodeLink = {
          linkUrl: '',
          isQrCode: false,
          isError: true
        }
        coinStore.isQrcodeImageError = true
      }

      if (payOrderRes.code === HTTP_STATUS.FAILED_SUBMIT_AGAIN) {
        throw new Error('请重新提交')
      }
      throw new Error(payOrderRes.message || '获取支付失败')
    }

    // 处理支付链接
    let url = payOrderRes.data.picUrl || payOrderRes.data.codeUrl || ''
    if (!url) {
      throw new Error('支付链接不存在')
    }

    const isQrCode = Boolean(payOrderRes.data?.codeUrl)
    if (!isQrCode && url.startsWith('http://')) {
      url = url.replace(/^http:/, 'https:')
    }

    return {
      result: PaymentResult.SUCCESS,
      orderId,
      amount: createOrderRes.data?.amount,
      payInfo: {
        linkUrl: url,
        isQrCode,
        isError: false
      }
    }

  } catch (error) {
    console.error('支付处理失败:', error)
    if (error instanceof Error) {
      message.error(error.message)
    }
    return { result: PaymentResult.ERROR }
  }
} 