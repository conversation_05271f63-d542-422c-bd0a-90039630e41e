import http from '~/utils/book/http'

export interface ChapterContentSaveParams {
  chapter_key: string
  content: any
}

export interface ChapterContentSaveResponse {
  updated: boolean
  message: string
  error?: string
}

export const saveChapterContent = async (params: ChapterContentSaveParams): Promise<ChapterContentSaveResponse> => {
  try {
    const response = await http.post('/api/chapters/save_content/', params)
    return response.data
  } catch (error) {
    console.error('保存章节内容失败:', error)
    throw error
  }
} 