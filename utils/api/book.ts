import type { Book, Chapter, CreateBookData, ReferenceResponse, ReferenceUploadParams } from '~/types/book'
import http from '../book/http'

interface BookListResponse {
  count: number
  next: string | null
  previous: string | null
  results: Book[]
}

interface CreateChapterData {
  book: string
  parent?: string | null
  title: string
  level: number
  order: number
  content?: any
}

interface UpdateChapterData {
  book: string
  parent?: string | null
  title: string
  level?: number
  order?: number
  content?: any
}

interface SwapChapterOrderResponse {
  message: string
  chapter1: Chapter
  chapter2: Chapter
}

export const BookService = {
  // 获取书籍列表
  async getBooks(): Promise<BookListResponse> {
    const response = await http.get<BookListResponse>('/api/books/')
    return response.data
  },

  // 获取单本书籍详情
  async getBook(key: string): Promise<Book | null> {
    try {
      const response = await http.get<Book>(`/api/books/${key}/`)
      return response.data
    } catch (error) {
      console.error('获取书籍详情出错:', error)
      return null
    }
  },

  async getBookInfo(key: string): Promise<Book | null> {
    // try {
    const response = await http.get<Book>(`/api/books/info/`, {
      params: {
        book_key: key
      }
    })
    return response.data
    // } catch (error) {
    //   console.error('获取书籍详情出错:', error)
    //   return null
    // }
  },

  // 创建新书籍
  async createBook(bookData: CreateBookData): Promise<Book | null> {
    try {
      const response = await http.post<Book>('/api/books/', bookData)
      return response.data
    } catch (error) {
      console.error('创建书籍出错:', error)
      return null
    }
  },

  // 更新书籍信息
  async updateBook(key: string, bookData: Partial<CreateBookData>): Promise<Book | null> {
    try {
      const response = await http.put<Book>(`/api/books/${key}/`, bookData)
      return response.data
    } catch (error) {
      console.error('更新书籍出错:', error)
      return null
    }
  },

  // 删除书籍
  async deleteBook(key: string): Promise<boolean> {
    try {
      await http.delete(`/api/books/${key}/`)
      return true
    } catch (error) {
      console.error('删除书籍出错:', error)
      return false
    }
  },

  // 获取书籍大纲
  async getBookOutline(params: any): Promise<Chapter[]> {
    try {
      const response = await http.get<Chapter[]>(`/api/books/get_outline/`, {
        params: params
      })
      return response.data
    } catch (error) {
      console.error('获取书籍大纲出错:', error)
      throw error
    }
  },

  // 更新书籍大纲
  async updateBookOutline(key: string, chapters: Chapter[]): Promise<Chapter[]> {
    try {
      const response = await http.put<Chapter[]>(`/api/books/${key}/outline/`, chapters)
      return response.data
    } catch (error) {
      console.error('更新书籍大纲出错:', error)
      throw error
    }
  },

  // 创建章节
  async createChapter(chapterData: CreateChapterData): Promise<Chapter | null> {
    try {
      const response = await http.post<Chapter>('/api/chapters/', chapterData)
      return response.data
    } catch (error) {
      console.error('创建章节失败:', error)
      throw error
    }
  },

  // 更新章节
  async updateChapter(key: string, chapterData: Partial<UpdateChapterData>): Promise<Chapter | null> {
    try {
      const response = await http.patch<Chapter>(`/api/chapters/${key}/`, chapterData)
      return response.data
    } catch (error) {
      console.error('更新章节失败:', error)
      throw error
    }
  },
  // 删除章节根据id, 如果章节有生成的内容，就
  async deleteChapterById(params: any): Promise<any> {
    let response;
    try {
      response = await http.post(`/api/chapters/del_by_key/`, params)
    } catch (error: any) {
      response = error?.response || {}
    }
    return response?.data
  },
  // 删除章节
  async deleteChapter(key: string): Promise<boolean> {
    try {
      await http.delete(`/api/chapters/${key}/`)
      return true
    } catch (error) {
      console.error('删除章节失败:', error)
      return false
    }
  },

  // 交换章节顺序
  async swapChapterOrder(chapterKey: string, targetChapterKey: string): Promise<SwapChapterOrderResponse | null> {
    try {
      const response = await http.post<SwapChapterOrderResponse>(
        `/api/chapters/${chapterKey}/swap_order/`,
        { target_chapter_key: targetChapterKey }
      )
      return response.data
    } catch (error) {
      console.error('交换章节顺序失败:', error)
      return null
    }
  },

  // 上传参考文献 /reference/upload/
  async referenceUpload(referenceUploadParams: ReferenceUploadParams): Promise<any> {
    try {
      const response = await http.post(
        `/api/reference/upload/`,
        referenceUploadParams
      )
      return response.data
    } catch (error) {
      console.error('更新书籍大纲出错:', error)
      throw error
    }
  },

  async getReferenceInfo(bookKey: string): Promise<ReferenceResponse> {
    const response = await http.get<ReferenceResponse>('/api/reference/info/', {
      params: {
        book_key: bookKey
      }
    })
    return response.data
  },

  async getFilesByIds(referenceUploadParams: any): Promise<any> {
    try {
      const response = await http.post(
        `/api/xiaoin/get_files_by_ids/`,
        referenceUploadParams
      )
      return response.data
    } catch (error) {
      console.error('getFilesByIds:', error)
      throw error
    }
  },

  // 删除参考资料
  async deleteReference(key: string): Promise<boolean> {
    try {
      await http.delete(`/api/references/${key}/`)
      return true
    } catch (error) {
      console.error('删除参考资料失败:', error)
      return false
    }
  },


  async updateOutline(data: any): Promise<boolean> {
    try {
      const response = await http.post(
        `/api/books/update_outline/`,
        data
      )
      return response.data
    } catch (error) {
      return false
    }
  },

}



