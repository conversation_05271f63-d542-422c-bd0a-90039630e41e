import type { Chapter } from '~/types/book'
import http from '~/utils/book/http'

export interface GenerateOutlineParams {
  book_key: string
  user_prompt?: string
}

export const generateOutline = async (params: GenerateOutlineParams): Promise<Chapter[]> => {
  try {
    const response = await http.post('/api/ai/generate_outline/', params)
    return response.data
  } catch (error) {
    console.error('生成大纲失败:', error)
    throw error
  }
}


