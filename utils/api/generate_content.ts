import http from '~/utils/book/http'

export interface GenerateContentParams {
  chapter_key: string
  user_prompt?: string
  attachments: []
}

export interface GenerateContentCreateResponse {
  success: boolean
  content: any
}

export const generateContentCreate = async (params: GenerateContentParams): Promise<GenerateContentCreateResponse> => {
  try {
    const response = await http.post('/api/ai/generate_content_create/', params)
    return response.data
  } catch (error) {
    console.error('生成内容失败:', error)
    throw error
  }
}


export const generateContentModify = async (params: GenerateContentParams): Promise<GenerateContentCreateResponse> => {
  try {
    const response = await http.post('/api/ai/generate_content_modify/', params)
    return response.data
  } catch (error) {
    console.error('修改内容失败:', error)
    throw error
  }
}


export const getChapterCoin = async (params: any): Promise<any> => {
  try {
    const response = await http.post('/api/chapters/calc_coin/', params)
    return response.data
  } catch (error) {
    throw error
  }
}


