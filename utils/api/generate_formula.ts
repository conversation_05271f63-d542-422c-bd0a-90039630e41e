import http from '~/utils/book/http'
interface GenerateFormulaParams {
  chapter_key: string
  user_prompt: string
}

interface GenerateFormulaResponse {
  success: boolean
  // formula: string
  // description?: string
  content: any
}

export const generateFormula = async (params: any): Promise<GenerateFormulaResponse> => {
  const response = await http.post<GenerateFormulaResponse>('/api/ai/generate_formula/', params)
  return response.data
} 