import http from '~/utils/book/http'
interface GenerateImgParams {
  chapter_key: string
  user_prompt: string
}

interface GenerateImgResponse {
  success: boolean
  // formula: string
  // description?: string
  content: any
}

export const generateImg = async (params: GenerateImgParams): Promise<GenerateImgResponse> => {
  const response = await http.post<GenerateImgResponse>('/api/ai/generate_img/', params)
  return response.data
} 