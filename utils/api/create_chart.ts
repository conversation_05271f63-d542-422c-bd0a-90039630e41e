import type { Book, Chapter } from '~/types/book'
import { getChapterTitlePrefix } from '../book_utils'
import { difyClient } from '../dify'

const apiKeyOfBlock = 'app-XL53LwnEXljWaVrlVM4ALHEA'

const chartTemplateOfBar = {
    "type": "doc",
    "content": [
        {
            "type": "chart",
            "attrs": {
                "type": "bar",
                "data": {
                    "labels": [
                        "一月",
                        "二月",
                        "三月",
                        "四月",
                        "五月"
                    ],
                    "datasets": [
                        {
                            "label": "销售数据",
                            "data": [
                                65,
                                59,
                                80,
                                81,
                                56
                            ],
                            "backgroundColor": "rgba(75, 192, 192, 0.2)",
                            "borderColor": "rgb(75, 192, 192)",
                            "borderWidth": 1
                        }
                    ]
                }
            }
        }
    ]
}

const chartTemplateOfLine = {
    "type": "doc",
    "content": [
        {
            "type": "chart",
            "attrs": {
                "type": "line",
                "data": {
                    "labels": [
                        "一月",
                        "二月",
                        "三月",
                        "四月",
                        "五月"
                    ],
                    "datasets": [
                        {
                            "label": "趋势数据",
                            "data": [
                                65,
                                59,
                                80,
                                81,
                                56
                            ],
                            "borderColor": "rgb(75, 192, 192)",
                            "tension": 0.1,
                            "fill": false
                        }
                    ]
                }
            }
        }
    ]
}

const chartTemplateOfPie = {
    "type": "doc",
    "content": [
        {
            "type": "chart",
            "attrs": {
                "type": "pie",
                "data": {
                    "labels": [
                        "红色",
                        "蓝色",
                        "黄色"
                    ],
                    "datasets": [
                        {
                            "data": [
                                300,
                                50,
                                100
                            ],
                            "backgroundColor": [
                                "rgb(255, 99, 132)",
                                "rgb(54, 162, 235)",
                                "rgb(255, 205, 86)",
                                "rgb(75, 192, 192)",
                                "rgb(153, 102, 255)"
                            ]
                        }
                    ]
                }
            }
        }
    ]
}



export async function invokeCreateChart(chapter: Chapter, book: Book, type: 'bar' | 'line' | 'pie', userPrompt?: string): Promise<[boolean, string | null, string]> {
    const promptTemplate = `
# 规则
请根据书籍的标题和章节名称，生成一段图表内容，以便用户插入到tiptap文档编辑器中。

# 章节信息
  1. 书籍名称：${book.title}
  2. 书籍写作整体要求：${book.description}
  2. 本章节的父章节标题：(${getAncestorChaptersFullTitles(chapter, book)})
  3. 章节编号：${getChapterTitlePrefix(chapter, book)}
  4. 章节名称：${chapter.title}
  5. 本章节现有的内容：{currentContent}
  6. 用户对本图表的要求：{userPrompt}
  7. 图表类型：{chartType}
# 输出格式
  1. 输出语言为中文。
  2. 输出格式为tiptap编辑器内容格式的json对象。
  3. 格式示例: {chartTemplate}

# 要求：
  1. 直接输出JSON格式的图表内容
  2. JSON格式语法要符合tiptap编辑器内容格式的json对象
`
    const chartType = type === 'bar' ? '柱状图' : type === 'line' ? '折线图' : '饼图'
    const chartTemplate = type === 'bar' ? chartTemplateOfBar : type === 'line' ? chartTemplateOfLine : chartTemplateOfPie

    console.log('chapter.content', chapter.content)
    let prompt = promptTemplate.replace('{currentContent}', "```" + JSON.stringify(chapter.content || {}) + "```")
    prompt = prompt.replace('{userPrompt}', "```" + (userPrompt || '') + "```")
    prompt = prompt.replace('{chartType}', chartType)
    prompt = prompt.replace('{chartTemplate}', "```" + (JSON.stringify(chartTemplate)) + "```")

    console.log('prompt', prompt)

    const [success, answer, conversationId, response] = await difyClient.getAnswer(prompt, apiKeyOfBlock)

    if (success) {
        try {
            console.log('服务器返回的tiptap编辑器图表内容:', answer)
            const jsonContent = extractJsonFromText(answer)
            if (!jsonContent) {
                return [false, null, conversationId]
            }
            const contentAsJson = JSON.parse(jsonContent)
            console.log('contentAsJson', contentAsJson)

            return [true, contentAsJson, conversationId]
        } catch (e) {
            console.error('解析章节数据失败:', e)
        }
    }

    return [false, null, conversationId]
} 