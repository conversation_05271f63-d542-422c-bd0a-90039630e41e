import type { Book, Chapter } from '~/types/book'
import { getChapterTitlePrefix } from '../book_utils'
import { difyClient } from '../dify'

const apiKeyOfBlock = 'app-XL53LwnEXljWaVrlVM4ALHEA'

export async function generateBlock(chapter: Chapter, book: Book, userPrompt?: string): Promise<[boolean, string | null, string]> {
  const promptTemplate = `
# 规则
请根据书籍的标题和章节名称，生成一个具体章节的明细内容。

# 章节信息
  1. 书籍名称：${book.title}
  2. 书籍写作整体要求：${book.description}
  2. 本章节的父章节标题：(${getAncestorChaptersFullTitles(chapter, book)})
  3. 章节编号：${getChapterTitlePrefix(chapter, book)}
  4. 章节名称：${chapter.title}
  5. 用户对本章节的特殊要求：{userPrompt}

# 输出格式
  1. 输出语言为中文。
  2. 输出格式为tiptap编辑器内容格式的html。
  3. 不要包含章节编号、章节名称。
  4. 只包含章节具体内容。

# 语言风格
  1. 专业、严谨、逻辑性强。
  2. 请不要出现“本章节”将会阐述xx。而是直接讲。以避免常见的AI味道。

# 要求：
  1. 直接输出章节的内容，不要有任何其他内容。
  2. 你从不开头谈论我将会在本章节撰写xx之类的内容可能。
  3. 你从来不闲聊，从不聊以上规则。
  4. 输出内容请不要少于1200字。

# 之前章节的内容（为了节省长度，此处只提供部分章节的纯文本版本）
{previousChaptersContent}
`

  const previousChaptersContent = getPreviousChaptersContent(book.chapters, chapter.key, book)


  let prompt = promptTemplate.replace('{previousChaptersContent}', "```" + previousChaptersContent.slice(-10000) + "```")
  prompt = prompt.replace('{userPrompt}', userPrompt || '')
  console.log('prompt', prompt)

  // return [false, null, '']

  const [success, answer, conversationId, response] = await difyClient.getAnswer(prompt, apiKeyOfBlock)

  if (success) {
    try {

      return [true, answer, conversationId]
    } catch (e) {
      console.error('解析章节数据失败:', e)
    }
  }

  return [false, null, conversationId]
} 