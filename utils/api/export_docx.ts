import http from '~/utils/book/http'

export interface ExportDocxParams {
  book_key: string
  format?: string // 添加可选的format参数，用于指定导出格式
}

export const exportDocx = (params: ExportDocxParams) => {
  // return http.post('/api/export/docx/', params, {
  //   responseType: 'arraybuffer'  // 指定响应类型为二进制数组
  // })

  return http.post('/api/export/docx/remote/', params, {
    responseType: 'arraybuffer'  // 指定响应类型为二进制数组
  })
}

export const exportFile = (params: ExportDocxParams) => {
  return http.post('/api/export/file/remote/', params)
} 