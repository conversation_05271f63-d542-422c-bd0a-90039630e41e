import http from '~/utils/book/http'

export type ChartType = 'bar' | 'line' | 'pie'

export interface GenerateChartParams {
  chapter_key: string
  chart_type: ChartType
  user_prompt: string
}

export interface GenerateChartResponse {
  success: boolean
  content: any
}

export const generateChart = async (params: any): Promise<GenerateChartResponse> => {
  try {
    const response = await http.post('/api/ai/generate_chart/', params)
    return response.data
  } catch (error) {
    console.error('生成图表失败:', error)
    throw error
  }
} 