import http from '~/utils/book/http'

export interface GenerateTableParams {
  chapter_key: string
  user_prompt: string
}

export interface GenerateTableResponse {
  success: boolean
  content: any
}

export const generateTable = async (params: any): Promise<GenerateTableResponse> => {
  try {
    const response = await http.post('/api/ai/generate_table/', params)
    return response.data
  } catch (error) {
    console.error('生成表格失败:', error)
    throw error
  }
} 