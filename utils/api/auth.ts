import http from '../http'

interface LoginParams {
  username: string
  password: string
}

interface LoginResponse {
  access: string
  refresh: string
}

interface RefreshResponse {
  access: string
}

export const login = async (params: LoginParams): Promise<LoginResponse> => {
  const { data } = await http.post<LoginResponse>('/api/auth/login/', params)
  return data
}

export const logout = async () => {
  await http.post('/api/auth/logout/')
}

export const refreshToken = async (refresh: string): Promise<RefreshResponse> => {
  const { data } = await http.post<RefreshResponse>('/api/auth/refresh/', {
    refresh
  })
  return data
}

export const AuthService = {
  async logout(refresh: string): Promise<boolean> {
    try {
      const response = await http.post('/api/auth/logout/', { refresh })
      return response.status === 200
    } catch (error) {
      console.error('退出登录失败:', error)
      return false
    }
  }
} 