import type { Book, Chapter } from '~/types/book'
import { getChapterTitlePrefix } from '../book_utils'
import { difyClient } from '../dify'

const apiKeyOfBlock = 'app-XL53LwnEXljWaVrlVM4ALHEA'

const tableTemplate = {
    "type": "doc",
    "content": [
        {
            "type": "table",
            "content": [
                {
                    "type": "tableRow",
                    "content": [
                        {
                            "type": "tableHeader",
                            "attrs": {
                                "colspan": 1,
                                "rowspan": 1,
                                "colwidth": null
                            },
                            "content": [
                                {
                                    "type": "paragraph",
                                    "content": [
                                        {
                                            "type": "text",
                                            "text": "q"
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            "type": "tableHeader",
                            "attrs": {
                                "colspan": 1,
                                "rowspan": 1,
                                "colwidth": null
                            },
                            "content": [
                                {
                                    "type": "paragraph",
                                    "content": [
                                        {
                                            "type": "text",
                                            "text": "q"
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            "type": "tableHeader",
                            "attrs": {
                                "colspan": 1,
                                "rowspan": 1,
                                "colwidth": null
                            },
                            "content": [
                                {
                                    "type": "paragraph"
                                }
                            ]
                        }
                    ]
                },
                {
                    "type": "tableRow",
                    "content": [
                        {
                            "type": "tableCell",
                            "attrs": {
                                "colspan": 1,
                                "rowspan": 1,
                                "colwidth": null
                            },
                            "content": [
                                {
                                    "type": "paragraph",
                                    "content": [
                                        {
                                            "type": "text",
                                            "text": "s"
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            "type": "tableCell",
                            "attrs": {
                                "colspan": 1,
                                "rowspan": 1,
                                "colwidth": null
                            },
                            "content": [
                                {
                                    "type": "paragraph",
                                    "content": [
                                        {
                                            "type": "text",
                                            "text": "s"
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            "type": "tableCell",
                            "attrs": {
                                "colspan": 1,
                                "rowspan": 1,
                                "colwidth": null
                            },
                            "content": [
                                {
                                    "type": "paragraph",
                                    "content": [
                                        {
                                            "type": "text",
                                            "text": "c"
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                },
                {
                    "type": "tableRow",
                    "content": [
                        {
                            "type": "tableCell",
                            "attrs": {
                                "colspan": 1,
                                "rowspan": 1,
                                "colwidth": null
                            },
                            "content": [
                                {
                                    "type": "paragraph"
                                }
                            ]
                        },
                        {
                            "type": "tableCell",
                            "attrs": {
                                "colspan": 1,
                                "rowspan": 1,
                                "colwidth": null
                            },
                            "content": [
                                {
                                    "type": "paragraph"
                                }
                            ]
                        },
                        {
                            "type": "tableCell",
                            "attrs": {
                                "colspan": 1,
                                "rowspan": 1,
                                "colwidth": null
                            },
                            "content": [
                                {
                                    "type": "paragraph"
                                }
                            ]
                        }
                    ]
                }
            ]
        }
    ]
}



export async function invokeCreateTable(chapter: Chapter, book: Book, userPrompt?: string): Promise<[boolean, string | null, string]> {
    const promptTemplate = `
# 规则
请根据书籍的标题和章节名称，生成一段表格内容，以便用户插入到tiptap文档编辑器中。

# 章节信息
  1. 书籍名称：${book.title}
  2. 书籍写作整体要求：${book.description}
  2. 本章节的父章节标题：(${getAncestorChaptersFullTitles(chapter, book)})
  3. 章节编号：${getChapterTitlePrefix(chapter, book)}
  4. 章节名称：${chapter.title}
  5. 本章节现有的内容：{currentContent}
  6. 用户对本表格的要求：{userPrompt}

# 输出格式
  1. 输出语言为中文。
  2. 输出格式为tiptap编辑器内容格式的json对象。
  3. 格式示例: {tableTemplate}

# 要求：
  1. 直接输出JSON格式的表格内容
  2. JSON格式语法要符合tiptap编辑器内容格式的json对象
`


    let prompt = promptTemplate.replace('{currentContent}', "```" + JSON.stringify(chapter.content) + "```")
    prompt = prompt.replace('{userPrompt}', "```" + (userPrompt) + "```")
    prompt = prompt.replace('{tableTemplate}', "```" + (JSON.stringify(tableTemplate)) + "```")
    console.log('prompt', prompt)

    const [success, answer, conversationId, response] = await difyClient.getAnswer(prompt, apiKeyOfBlock)

    if (success) {
        try {
            console.log('AI修改后的章节内容:', answer)
            const jsonContent = extractJsonFromText(answer)
            if (!jsonContent) {
                return [false, null, conversationId]
            }
            const contentAsJson = JSON.parse(jsonContent)
            console.log('contentAsJson', contentAsJson)

            return [true, contentAsJson, conversationId]
        } catch (e) {
            console.error('解析章节数据失败:', e)
        }
    }

    return [false, null, conversationId]
} 