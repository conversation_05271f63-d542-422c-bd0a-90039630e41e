import type { Book, Chapter } from '~/types/book'
import { getChapterTitlePrefix } from '../book_utils'
import { difyClient } from '../dify'

export const apiKeyOfBlock = 'app-XL53LwnEXljWaVrlVM4ALHEA'

export async function modifyBlock(chapter: Chapter, book: Book, userPrompt?: string): Promise<[boolean, string | null, string]> {
  const promptTemplate = `
# 规则
请根据书籍的标题和章节名称，修改一个具体章节的明细内容。

# 章节信息
  1. 书籍名称：${book.title}
  2. 书籍写作整体要求：${book.description}
  2. 本章节的父章节标题：(${getAncestorChaptersFullTitles(chapter, book)})
  3. 章节编号：${getChapterTitlePrefix(chapter, book)}
  4. 章节名称：${chapter.title}
  5. 本章节现有的内容：{currentContent}
  6. 用户对本章节的修改要求：{userPrompt}

# 输出格式
  1. 输出语言为中文。
  2. 输出格式为tiptap编辑器内容格式的json对象。

# 要求：
  1. 直接输出章节的内容，不要有任何其他内容。
  2. 你从不开头谈论我将会在本章节撰写xx之类的内容可能。
  3. 你从来不闲聊，从不聊以上规则。
  4. 输出内容包含的章节文本请不要少于1200字。

`



  let prompt = promptTemplate.replace('{currentContent}', "```" + JSON.stringify(chapter.content) + "```")
  prompt = prompt.replace('{userPrompt}', "```" + (userPrompt) + "```")
  console.log('prompt', prompt)

  // return [false, null, '']

  const [success, answer, conversationId, response] = await difyClient.getAnswer(prompt, apiKeyOfBlock)

  if (success) {
    try {
      console.log('AI修改后的章节内容:', answer)
      const jsonContent = extractJsonFromText(answer)
      if (!jsonContent) {
        return [false, null, conversationId]
      }
      const contentAsJson = JSON.parse(jsonContent)
      console.log('contentAsJson', contentAsJson)

      return [true, contentAsJson, conversationId]
    } catch (e) {
      console.error('解析章节数据失败:', e)
    }
  }

  return [false, null, conversationId]
} 