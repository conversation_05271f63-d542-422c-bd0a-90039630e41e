import type { AppUserInfo, KnowledgeAssistantMemberInfo } from '~/services/types/loginMobileRes'

// Cookie 键名常量
export const AUTH_COOKIES = {
  TOKEN: 'auth_token',
  USER_INFO: 'user_info',
  KNOWLEDGE_ASSISTANT_INFO: 'knowledge_assistant_info',
  IS_LOGGED_IN: 'is_logged_in',

} as const



// 认证相关的 Cookie 接口
export interface AuthCookieData {
  token?: string
  userInfo?: AppUserInfo
  knowledgeAssistantInfo?: KnowledgeAssistantMemberInfo
  isLoggedIn?: boolean

}

/**
 * Cookie 认证管理工具类
 */
export class CookieAuthManager {
  private static readonly BASE_COOKIE_OPTIONS = {
    httpOnly: false,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax' as const,
    maxAge: 60 * 60 * 24 * 7 // 7天
  }

  private static readonly STRING_COOKIE_OPTIONS = {
    ...this.BASE_COOKIE_OPTIONS,
    default: () => null as string | null
  }

  private static readonly OBJECT_COOKIE_OPTIONS = {
    ...this.BASE_COOKIE_OPTIONS,
    default: () => null as any,
    serializer: {
      read: (value: string) => {
        try {
          return JSON.parse(value)
        } catch {
          return null
        }
      },
      write: (value: any) => {
        return value ? JSON.stringify(value) : ''
      }
    }
  }

  /**
   * 设置认证 token
   */
  static setToken(token: string) {
    const tokenCookie = useCookie(AUTH_COOKIES.TOKEN, this.STRING_COOKIE_OPTIONS)
    tokenCookie.value = token
  }

  /**
   * 获取认证 token
   */
  static getToken(): string | null {
    const tokenCookie = useCookie(AUTH_COOKIES.TOKEN, this.STRING_COOKIE_OPTIONS)
    return tokenCookie.value || null
  }

  /**
   * 设置用户信息
   */
  static setUserInfo(userInfo: AppUserInfo) {
    const userInfoCookie = useCookie(AUTH_COOKIES.USER_INFO, this.OBJECT_COOKIE_OPTIONS)
    userInfoCookie.value = userInfo
  }

  /**
   * 获取用户信息
   */
  static getUserInfo(): AppUserInfo | null {
    const userInfoCookie = useCookie(AUTH_COOKIES.USER_INFO, this.OBJECT_COOKIE_OPTIONS)
    return userInfoCookie.value || null
  }

  /**
   * 设置知识助手会员信息
   */
  static setKnowledgeAssistantInfo(info: KnowledgeAssistantMemberInfo) {
    const infoCookie = useCookie(AUTH_COOKIES.KNOWLEDGE_ASSISTANT_INFO, this.OBJECT_COOKIE_OPTIONS)
    infoCookie.value = info
  }

  /**
   * 获取知识助手会员信息
   */
  static getKnowledgeAssistantInfo(): KnowledgeAssistantMemberInfo | null {
    const infoCookie = useCookie(AUTH_COOKIES.KNOWLEDGE_ASSISTANT_INFO, this.OBJECT_COOKIE_OPTIONS)
    return infoCookie.value || null
  }

  /**
   * 设置登录状态
   */
  static setLoginStatus(isLoggedIn: boolean) {
    const loginStatusCookie = useCookie(AUTH_COOKIES.IS_LOGGED_IN, this.STRING_COOKIE_OPTIONS)
    loginStatusCookie.value = isLoggedIn.toString()
  }

  /**
   * 获取登录状态
   */
  static getLoginStatus(): boolean {
    const loginStatusCookie = useCookie(AUTH_COOKIES.IS_LOGGED_IN, this.STRING_COOKIE_OPTIONS)
    return loginStatusCookie.value === 'true'
  }



  /**
   * 清理所有认证相关的 cookies
   */
  static clearAll() {
    const tokenCookie = useCookie(AUTH_COOKIES.TOKEN, this.STRING_COOKIE_OPTIONS)
    const userInfoCookie = useCookie(AUTH_COOKIES.USER_INFO, this.OBJECT_COOKIE_OPTIONS)
    const knowledgeAssistantCookie = useCookie(AUTH_COOKIES.KNOWLEDGE_ASSISTANT_INFO, this.OBJECT_COOKIE_OPTIONS)
    const loginStatusCookie = useCookie(AUTH_COOKIES.IS_LOGGED_IN, this.STRING_COOKIE_OPTIONS)

    tokenCookie.value = null
    userInfoCookie.value = null
    knowledgeAssistantCookie.value = null
    loginStatusCookie.value = null

  }

  /**
   * 批量设置认证数据
   */
  static setAuthData(data: AuthCookieData) {
    if (data.token) {
      this.setToken(data.token)
    }
    if (data.userInfo) {
      this.setUserInfo(data.userInfo)
    }
    if (data.knowledgeAssistantInfo) {
      this.setKnowledgeAssistantInfo(data.knowledgeAssistantInfo)
    }
    if (data.isLoggedIn !== undefined) {
      this.setLoginStatus(data.isLoggedIn)
    }
  }

  /**
   * 批量获取认证数据
   */
  static getAuthData(): AuthCookieData {
    return {
      token: this.getToken() || undefined,
      userInfo: this.getUserInfo() || undefined,
      knowledgeAssistantInfo: this.getKnowledgeAssistantInfo() || undefined,
      isLoggedIn: this.getLoginStatus(),
    }
  }
}
