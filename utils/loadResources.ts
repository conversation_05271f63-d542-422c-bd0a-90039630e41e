/**
 * Created by 飞呀 on 2018/1/9.
 */
//加载css与js文件
export default function asyncLoadJs(url: string, type = 'text/javascript') {
  return new Promise<void>((resolve, reject) => {
    const srcArr = document.getElementsByTagName('script')
    let hasLoaded = false
    for (let i = 0; i < srcArr.length; i++) {
      //判断当前js是否加载上
      hasLoaded = srcArr[i].src == url ? true : false
    }
    if (hasLoaded) {
      resolve()
      return
    }
    const script = document.createElement('script')
    script.type = type
    script.src = url
    document.body.appendChild(script)
    script.onload = () => {
      resolve()
    }
    script.onerror = () => {
      reject()
    }
  })
}

export function loadCss(url: string) {
  const css = document.createElement('link')
  css.href = url
  css.rel = 'stylesheet'
  css.type = 'text/css'
  document.head.appendChild(css)
}
const cdnBaseUrl = '//static-1256600262.file.myqcloud.com'
export const workerSrc = `${cdnBaseUrl}/lib/pdf-legacy/pdf.worker.min.mjs`
export const CMAP_URL = `${cdnBaseUrl}/lib/pdf/cmaps/`
export const konvajs = `${cdnBaseUrl}/lib/pdf/konva.min.js`
export function loadPdfJs() {
  //加载css
  loadCss(`${cdnBaseUrl}/lib/pdf-legacy/pdf_viewer_v2.css?v=1`)
  //加载js
  return new Promise<void>((resolve, reject) => {
    asyncLoadJs(`${cdnBaseUrl}/lib/pdf-legacy/pdf.min.mjs`, 'module') //开发用
      /*asyncLoadJs(appDomainRoot + "/minemapapi/demo/js/minemap-wmts.js")//部署用*/
      .then(() => {
        return asyncLoadJs(`${cdnBaseUrl}/lib/pdf-legacy/pdf_viewer.mjs`, 'module')
      })
      .then(() => {
        return asyncLoadJs(`${konvajs}`)
      })
      .then(() => {
        resolve()
      })
      .catch((err) => {
        reject(err)
      })
  })
  // return asyncLoadJs(`${cdnBaseUrl}/lib/pdf/pdf.mjs`, 'module')
}
export async function loadErudaJs() {
  await asyncLoadJs('//cdn.jsdelivr.net/npm/eruda')
  window['eruda'].init()
}