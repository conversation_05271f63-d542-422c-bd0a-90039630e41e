// 检查操作系统
export function isIos() {
    const userAgent = window.navigator.userAgent.toLowerCase()
    if (
        userAgent.includes('mac') ||
        userAgent.includes('ipad') ||
        userAgent.includes('iphone') ||
        userAgent.includes('ipod')
    ) {
        return true
    }

    return false
}

//请求后端接口（包含请求和响应回调）
export function callNativeMethodWithCb(cmd: any, cb: any) {
    console.log(cmd, '1')

    //  内置浏览器获取数据后公用方法
    function onDeviceCallback(evt: any) {
        const res = typeof evt === 'object' ? evt : JSON.parse(evt)
        if (res.code == 0) {
            // c++接口返回
            const result = {
                code: res.code,
                data: res.data && JSON.parse(res.data),
                request: res.request || ''
            }
            cb(result)
        }
    }
    window['onDeviceCallback'] = onDeviceCallback
    const query = {
        request: cmd,
        onSuccess: isIos() ? 'onDeviceCallback' : onDeviceCallback
    }

    let call: any
    if (isIos()) {
        // IOS系统
        window.webkit.messageHandlers.CefViewQuery.postMessage(query)
        return
    } else {
        call = (window.CefViewQuery && window.CefViewQuery) || null
    }
    call && call(query)
}
