import type { AxiosRequestConfig } from 'axios'

import crypto from 'crypto-js'

// 应用 ApiAppKey
const apiAppKey = 'APIqE9UEZAFyrenCydu9bundG8ACJywcfPc'
// 应用 ApiAppSecret
const apiAppSecret = 'TU8aRdK5i6urXUHpd7QFgRno1RIsYdWF'

// 请求拦截器
export const requestSignHandler = (
  config: AxiosRequestConfig
): AxiosRequestConfig | Promise<AxiosRequestConfig> => {
  // console.log(crypto.HmacSHA1)
  let dateTime = new Date().toUTCString()
  // const contentType = config.headers['Content-Type'] || config.headers.post['Content-Type']
  const sortedQuery = getSortedQuery(config.params)
  let contentMD5 = ''
  // console.log('contentType', contentType)
  if ((config.method == 'post' || config.method == 'put') && config.data != null) {
    if (typeof config.data === 'object') {
      contentMD5 = crypto.MD5(JSON.stringify(config.data)).toString(crypto.enc.Base64)
      if (config.data?.timestamp && !isNaN(config.data?.timestamp)) {
        dateTime = new Date(config.data.timestamp).toUTCString()
      }
    } else {
      contentMD5 = crypto.MD5(config.data).toString(crypto.enc.Base64)
      if (config.url && config.url.indexOf('/login/getVerifyCode?') > -1) {
        try {
          const _data = JSON.parse(config.data)
          if (_data?.timestamp && !isNaN(_data?.timestamp)) {
            dateTime = new Date(_data.timestamp).toUTCString()
          }
        } catch (error) {
          console.log(error)
        }
      }
    }
  }
  // Tue, 30 Aug 2022 07:47:37 GMT
  // POST
  // /lc/userOpenRecord/save
  // a=123
  // DGzDyMjsrGKvv+g+WovGYA==
  let url = config.url?.startsWith('/') ? config.url : `/${config.url}`
  if (url.indexOf('?') > -1) {
    url = url.substring(0, url.indexOf('?'))
  }
  const signingStr = [config.method?.toUpperCase(), dateTime, contentMD5, url, sortedQuery].join(
    '\n'
  )
  // console.log(signingStr)
  const signing = crypto.HmacSHA1(signingStr, apiAppSecret).toString(crypto.enc.Base64)
  const sign = `hmac id="${apiAppKey}", algorithm="hmac-sha1", headers="i-date", signature="${signing}"`
  if (config?.headers) {
    config.headers['i-version'] = '2'
    config.headers['i-date'] = dateTime
    config.headers['i-authorization'] = sign
    config.headers['Content-MD5'] = contentMD5
  }
  return config
}

function sortObject(query: any) {
  if (!query) {
    return []
  }
  const keys = Object.keys(query).sort()
  const signKeys = []
  for (let i = 0; i < keys.length; i++) {
    const val = query[keys[i]]
    if (val !== null && val !== undefined) {
      signKeys.push(keys[i])
    }
  }
  // 按字典序排序
  return signKeys.sort()
}

function getSortedQuery(obj: any) {
  const sortedKeys = sortObject(obj)
  if (sortedKeys.length == 0) {
    return ''
  }
  return sortedKeys
    .map((item) => {
      return `${item}=${obj[item]}`
    })
    .join('&')
}
