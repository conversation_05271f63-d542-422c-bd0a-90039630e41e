import type {
    KnowledgeAssistantMemberInfo,
    TranslateListData,
} from '@/services/types/loginMobileRes'
import type { Response, ResponsePagination } from '@/services/types/reponse'
import type {
    NewsInfo,
    NewsListParams,
    NewsQueryByIdParams,
    NewsTagInfo,
    RepositoryFile,
    RepositoryFolder
} from '@/services/types/repositoryFile'
import request from '@/utils/request'

export function repositoryGetInfo(): Promise<Response<KnowledgeAssistantMemberInfo>> {
    return request.get('/repository/getInfo')
}

export async function repositoryFileList(params: any): Promise<ResponsePagination<RepositoryFile>> {
    return request.get('/repositoryFile/list', { params })
}

export async function addHtml(params: any): Promise<Response<string>> {
    return request.post('/repositoryFile/addHtml', params)
}

export function getNewsList(params: NewsListParams): Promise<ResponsePagination<NewsInfo>> {
    return request.get('/news/list', { params })
}

export function getTagType(): Promise<Response<any>> {
    // 获取类型
    return request.get('/newsUserTag/getTagType')
}

export function getTagList(): Promise<Response<NewsTagInfo>> {
    // 获取资讯订阅标签
    return request.get('/news/tagList')
}

export function getMyTagList(): Promise<Response<NewsTagInfo>> {
    // 我关注的标签列表
    return request.get('/newsUserTag/list')
}

export function newsUserTagAdd(params: any): Promise<Response<any>> {
    // 添加关注
    return request.post('/newsUserTag/add', params)
}
export function newsUserTagRemove(params: any): Promise<Response<any>> {
    // 删除关注
    return request.post('/newsUserTag/remove', params)
}

export function setTagType(params: any): Promise<Response<any>> {
    // 调整类型
    return request.post('/newsUserTag/setTagType', params)
}

export function newsQueryById(params: NewsQueryByIdParams): Promise<Response<NewsInfo>> {
    return request.get('/news/queryById', { params })
}

export async function repositoryFileAddNews(params: any): Promise<Response<string>> {
    return request.post('/repositoryFile/addNews', params)
}

export function repositoryFileDelete(spaceId: string, params: any): Promise<Response<string>> {
    return request.delete(`/space/${spaceId}/file/delete`, { params })
}

export function repositoryFileGetDetail(params: any): Promise<Response<RepositoryFile>> {
    return request.get('/repositoryFile/getDetail', { params })
}

// 思维导图
export async function repositoryFileStartInspiration(spaceId: string, params: any): Promise<Response<string>> {
    return request.post(`/space/${spaceId}/file/startInspiration`, params);
}

// 开始翻译
export async function startTranslate(spaceId: string, params: any): Promise<Response<string>> {
    return request.post(`/space/${spaceId}/file/translate/start`, params)
}

// 翻译列表
export async function getTranslateList(spaceId: string, params: any): Promise<Response<TranslateListData>> {
    return request.get(`/space/${spaceId}/file/translate/list`, { params })
}

// 重新翻译
export async function restartTranslate(spaceId: string, params: any): Promise<Response<string>> {
    return request.post(`/space/${spaceId}/file/translate/restart`, params)

}
export async function addFiles(params: any): Promise<Response<string>> {
    return request.post('/repositoryFile/addFiles', params)
}

// 检查文件sha256
export async function checkFileHash(params: any): Promise<Response<any>> {
    return request.post('/userFile/checkFileSha256', params)
}

export function repositorySaveEditorData(spaceId: string, params: any): Promise<Response<string>> {
    return request.post(`/space/${spaceId}/file/saveEditorData`, params)
}

export function getExportCode(spaceId: string, params: any): Promise<Response<string>> {
    return request.post(`/space/${spaceId}/file/getExportCode`, params)
}

export function getSourceMd(id: string): Promise<Response<any>> {
    return request.get(`/news/getSourceMd?id=${id}`)
}

export async function nameUpdate(spaceId: string, params: any): Promise<Response<string>> {
    return request.post(`/space/${spaceId}/file/rename`, params)
}

// 团队版

// 把文件添加到文件夹
export function addFileToFolder(spaceId: string, params: { folderId: string, fileId: string }): Promise<Response<any>> {
    return request.get(`/space/${spaceId}/folder/addFile`, { params });
}

// 新增文件夹 
export function addFolder(spaceId: string, params: { folderName: string, parentId?: string, remark?: string }): Promise<Response<any>> {
    return request.post(`/space/${spaceId}/folder/addFolder`, params);
}

// 文件 - 移动文件和文件夹
export function changeFolder(spaceId: string, params: { items: { id: string, type: string }[], fromFolderId: string, toFolderId: string }): Promise<Response<any>> {
    return request.post(`/space/${spaceId}/folder/changeFolder`, params);
}

// 删除文件夹
export function deleteFolder(spaceId: string, params: { folderId: string }): Promise<Response<any>> {
    return request.delete(`/space/${spaceId}/folder/delete`, { params });
}

// 查询文件列表
export function listFile(spaceId: string, params: { folderId: string, pageNo: number, pageSize: number }): Promise<any> {
    return request.get(`/space/${spaceId}/folder/listFile`, { params });
}


export function listAll(params: any): Promise<any> {
    return request.get(`/space/${params.spaceId}/folder/listAll`, { params });
}

export function listAllFiles(params: any): Promise<Response<any>> {
    return request.get(`/space/${params.spaceId}/folder/listAllFiles`, { params });
}


// 查询子文件夹列表
export function getSubFolders(spaceId: string, params: { folderId: string }): Promise<Response<any>> {
    return request.get(`/space/${spaceId}/folder/listFolder`, { params });
}

// 文件夹-重命名
export function updateFolder(spaceId: string, params: { folderId: string, folderName: string }): Promise<Response<any>> {
    return request.post(`/space/${spaceId}/folder/rename`, params);
}

// 文件夹 - 搜索  spaceId: string, params: { keyword: string, searchTypes: string[] 
export function folderSearch(params: any): Promise<Response<any>> {
    return request.post(`/space/${params.spaceId}/folder/search`, params);
}

// 文件夹 - 搜索  spaceId: string, params: { keyword: string, searchTypes: string[] 
export function folderSearchV2(params: any): Promise<Response<any>> {
    return request.post(`/space/${params.spaceId}/folder/search/v2`, params);
}

export function getFolder(spaceId: string, params: { folderId: string }): Promise<Response<RepositoryFolder>> {
    return request.get(`/space/${spaceId}/folder/info`, { params });
}

// 获取空间信息
export function getSpaceBytes(spaceId: string): Promise<Response<any>> {
    return request.get(`/space/${spaceId}/info`);
}

// 下载文件
export function getDownloadLink(params: { id: string }): Promise<Response<any>> {
    return request.get(`/repositoryFile/getDownloadLink`, { params });
}
//根据id列表查询文件状态
export function getFilesByIds(params: any): Promise<any> {
    return request.post(`/space/${params.spaceId}/file/getFilesByIds`, params);
}

