import http from '~/utils/http'

// 参考资料API的响应类型
export interface ReferenceResponse {
  success: boolean
  message?: string
  error?: string
  data?: any
}

// 参考资料列表项的类型
export interface ReferenceItem {
  id: string | number
  url: string
  fileName?: string
}

/**
 * 获取参考文献列表
 * @param bookKey 书籍key
 */
export const getReferenceList = async (bookKey: string): Promise<ReferenceResponse> => {
  try {
    const response = await http({
      url: '/api/reference/referesh_list/',
      method: 'get',
      params: {
        book_key: bookKey
      }
    })
    return response.data
  } catch (error) {
    console.error('获取参考资料列表失败:', error)
    throw error
  }
}

/**
 * 上传参考文献
 * @param bookKey 书籍key
 * @param attachments 附件列表，包含url和fileName
 */
export const uploadReferences = async (bookKey: string, attachments: {url: string, fileName?: string}[]): Promise<ReferenceResponse> => {
  try {
    const response = await http({
      url: '/api/reference/upload/',
      method: 'post',
      data: {
        book_key: bookKey,
        attachments
      }
    })
    return response.data
  } catch (error) {
    console.error('上传参考资料失败:', error)
    throw error
  }
}

/**
 * 删除参考文献
 * @param referenceId 参考文献ID
 * @returns 成功返回true，失败抛出异常
 */
export const deleteReference = async (referenceId: string | number): Promise<boolean> => {
  try {
    const response = await http({
      url: `/api/reference/${referenceId}`,
      method: 'delete'
    })
    
    // 删除成功返回204状态码
    return response.status === 204
  } catch (error) {
    console.error('删除参考资料失败:', error)
    throw error
  }
} 