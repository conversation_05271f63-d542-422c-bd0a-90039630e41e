import type { CosSignResultInfo } from '@/services/types/appMessage';
import type { Response } from '@/services/types/reponse';
import request from '@/utils/request';

export async function uploadByUrl(params: any): Promise<Response<any>> {
  if (!params.fileName) {
    params.fileName = '万能小in'
  }
  return request.post('/userFile/uploadByUrl', params)
}

/**
 * 获取OSS上传签名
 * @param params 请求参数
 * @returns Promise<Response<CosSignResultInfo>>
 */
export async function ossGetSign(params: any): Promise<Response<CosSignResultInfo>> {
  return request.get('/userFile/oss/getSign', {
    params: {
      ...params,
      filename: params?.filename ? encodeURIComponent(params.filename) : ''
    }
  })
}

export async function generatePutUrl(params: any): Promise<Response<any>> {
  return request.get('/userFile/generatePutUrl', {
    params: {
      ...params,
      filename: params?.filename ? encodeURIComponent(params.filename) : ''
    }
  })
}
// 添加一个post /pptTpl/extraPptTpl form-data 用于上传文件
export function extraPptTpl(
  file: File,
  pages: string = "0,1,-1"
): Promise<Response<any>> {
  const formData = new FormData();
  formData.append("file", file);
  formData.append("pages", "0,1,-1");
  // 0,1,-1 表示抓取第一张 第二张 最后一张
  return request.post("/pptTpl/extraPptTpl", formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}

