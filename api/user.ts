import type {
  A<PERSON><PERSON>ontent,
  AppLoginResultInfo,
  AppUserInfo,
  BindMousseDeviceInfo,
  BindPhoneUserInfo,
  LoginMobileRes,
  SendVerifyCodeResultInfo
} from '@/services/types/loginMobileRes'
import type { Response } from '@/services/types/reponse'
import { getAppId, wechatAppAppIdCJ } from '@/utils/constants'
import request from '@/utils/request'
import type { LoginThunderobotParams, TeamInfo } from '~/services/types/user'

export async function loginMobile(params: any): Promise<Response<AppLoginResultInfo>> {
  return request.post('/login/loginMobile', params)
}

export async function getUserInfo(): Promise<Response<AppUserInfo>> {
  return request.get('/user/getUserInfo', {
    params: {
      appid: getAppId(),
    },
  })
}

export async function addDesktop(): Promise<Response<any>> {
  return request.post('/user/addDesktop')
}

// 用户行为打点记录
export async function saveActionLog(params: any): Promise<Response<any>> {
  return request.post('/user/actionLog/add', params)
}

export async function getBalance(): Promise<Response<any>> {
  return request.get('/user/coin/getBalance')
}

export async function createMpQrLogin(params: any): Promise<Response<any>> {
  return request.get('/login/createMpQrLogin', { params })
}
export async function checkMpQrLogin(params: any): Promise<Response<AppLoginResultInfo>> {
  return request.post('/login/checkMpQrLogin', params)
}
export async function getVerifyCode(params: any): Promise<Response<SendVerifyCodeResultInfo>> {
  return request.post('/login/getVerifyCode', params)
}
export async function bindMobile(params: any): Promise<Response<LoginMobileRes>> {
  return request.post('/user/bindMobile', params)
}

export async function getMobileBindInfo(params: any): Promise<Response<BindPhoneUserInfo>> {
  return request.post('/user/getMobileBindInfo', params)
}

export async function mergeUser(params: any): Promise<Response<LoginMobileRes>> {
  return request.post('/user/mergeUser', params)
}

export async function saveFeedback(params: any): Promise<Response<any>> {
  return request.post('/feedback/add', params)
}
export async function updateUserInfo(params: any): Promise<Response<AppUserInfo>> {
  return request.post('/user/updateUserInfo', params)
}

export async function saveId2MetaVerify(params: any): Promise<Response<any>> {
  return request.post('/user/id2MetaVerify', params)
}

// 用户注销
export async function deleteAccount(): Promise<Response<any>> {
  return request.post('/user/deleteAccount', {})
}

export async function getAddressList(): Promise<Response<any>> {
  return request.get('/userAddress/list')
}

export async function getLogistics(params: any): Promise<Response<any>> {
  return request.get('/order/getLogistics', { params })
}

// 添加地址
export async function addUserAddress(params: any): Promise<Response<any>> {
  return request.post('/userAddress/add', params)
}

export async function areaList(params: any): Promise<Response<any>> {
  return request.get('/area/list', { params })
}

export async function setOrderAddress(params: any): Promise<Response<any>> {
  return request.post('/order/setOrderAddress', params)
}
export async function openRecordAdd(params: any): Promise<Response<any>> {
  return request.post('/user/openRecord/add', params)
}
export async function bindMouse(params: any): Promise<Response<BindMousseDeviceInfo>> {
  return request.post('/device/bindMouse', params)
}


export async function generateSchemeLink(params: any): Promise<Response<any>> {
  return request.post(`/wechat/ma/${wechatAppAppIdCJ}/generateSchemeLink`, params)
}

export async function getTeamList(): Promise<Response<TeamInfo>> {
  return request.get('/team/list')
}

export async function getChannelId(params: any): Promise<Response<any>> {
  return request.post(`/channel/getChannelId`, params)
}

export async function loginThunderobot(params: LoginThunderobotParams): Promise<Response<AppLoginResultInfo>> {
  return request.post('/login/loginThunderobot', params)
}


export function queryContentByCode(params: any): Promise<Response<AppContent>> {
  return request.get('/content/queryByCode', { params })
}

