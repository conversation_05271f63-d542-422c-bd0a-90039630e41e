import type { Response } from '@/services/types/reponse'
import request from '@/utils/request'

export const getGoodsList = (params: any): Promise<Response<any>> => {
  return request.get(`/goods/list`, { params })
}

export async function createOrder(params: any): Promise<Response<any>> {
  return request.post('/order/create', params)
}

export async function testCreateOrder(params: any): Promise<Response<any>> {
  // return request.post('/poster/fakeCreateOrderAndPay', params)
  return request.post('/trade/testPayOrder', params)
}

export async function testGiftCreateOrder(params: any): Promise<Response<any>> {
  return request.post('/trade/testPayOrder', params)
}

export async function payOrder(params: any): Promise<Response<any>> {
  return request.post('/trade/payOrder', params)
}

export const getRecordList = (params: any): Promise<Response<any>> => {

  return request.get(`/user/coin/listRecord`, { params })
}

export const getCoinDetail = (params: any): Promise<Response<any>> => {
  return request.get(`/user/coin/getDetail`, { params })
}

export const isBoughtGoods = (params: any): Promise<Response<any>> => {
  return request.get(`/order/isBoughtGoods`, { params })
}

export const getOrderDetail = (params: any): Promise<Response<any>> => {
  return request.get(`/order/queryById`, { params })
}
