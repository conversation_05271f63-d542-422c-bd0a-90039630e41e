import request from '@/utils/request'
import { type StrictUseAxiosReturn, useAxios } from '@vueuse/integrations/useAxios'
import type { AxiosRequestConfig } from 'axios'
import type { PageResult } from './typing'

// 分页请求
export function basePageRequest<T>(
  url: string,
  config: AxiosRequestConfig<any>
): StrictUseAxiosReturn<PageResult<T>> {
  return useAxios<PageResult<T>>(url, config, request, {
    immediate: false,
  })
}
// 单独请求
export function baseRequest<T>(
  url: string,
  config: AxiosRequestConfig<any>
): StrictUseAxiosReturn<T> {
  return useAxios<T>(url, config, request, {
    immediate: false,
  })
}
