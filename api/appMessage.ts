import type { AppCategory, AppMessage, AppMessageRecord, OneMessageForShareInfo } from '@/services/types/appMessage'
import type { Response } from '@/services/types/reponse'
import request from '@/utils/request'
import type { PageResult } from './typing'
import { usePage, type InSchoolUsePageReturn } from './usePage'

export function pageAppMessages(params: any): InSchoolUsePageReturn<AppMessage> {
  return usePage<AppMessage>('/ask/getMessageList', {
    params,
  })
}

export function getMessageSessionList(params: any): Promise<Response<PageResult<AppMessageRecord>>> {

  return request.get('/messageSession/list', { params })
}

export function deleteMessageRecord(params: any): Promise<Response<any>> {
  return request.delete('/messageSession/delete', { params })
}

export function getSuggestCreator(params: any): Promise<Response<AppCategory[]>> {
  return request.post('/ask/suggestCreator', params)
}

export function deleteMessage(params: any): Promise<Response<AppMessage>> {
  return request.delete('/ask/deleteMessage', { params })
}
//对话消息-查询一个消息
export function fetchOneMessageForShare(params: any): Promise<Response<OneMessageForShareInfo>> {
  return request.get('/ask/getOneMessageForShare', { params })
}
export function newSessionId(): Promise<Response<any>> {
  return request.get('/agent/newSessionId')
}
export function getBySessionId(params: any): Promise<Response<AppMessageRecord>> {
  return request.get('/messageSession/getBySessionId', { params })
}

export function messageSessionUpdate(params: any): Promise<Response<any>> {
  return request.post('/messageSession/update', params)
}
