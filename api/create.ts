import type { Response } from '@/services/types/reponse'
import type { PPTTemplateInfo, preparePayInfo, SubmissionInfo } from '~/services/types/submission'
import { UserService } from '~/services/user'
import type { PageResult } from './typing'

// 提交写作请求
export function createSubmission(params?: any): Promise<Response<SubmissionInfo>> {
  return request.post('/submission/createSubmission', params)
}

// 查询生成结果
export function getCreateSubmissionResult(params?: any): Promise<Response<SubmissionInfo>> {
  return request.get('/submission/getResult', { params })
}

// 支付写作ppt
export function paySubmission(params?: any): Promise<Response<SubmissionInfo>> {
  return request.post('/submission/paySubmission', params)
}

export function loadOutlineData(params: any): Promise<Response<any>> {
  return request.post('/submissionTask/getOutline', params)
}
// ppt模板
export function getPPTTemplateList(params?: any): Promise<Response<PageResult<PPTTemplateInfo>>> {
  return request.get('/pptTpl/list', { params })
}

// 发送邮件
export function sendEmail(params?: any): Promise<Response<SubmissionInfo>> {
  return request.post('/submission/sendEmail', params)
}

// 获取最近一条需要支付的写作
export function getFirstNeedPayOrder(params?: any): Promise<Response<SubmissionInfo>> {
  return request.get('/submission/getNeedPay', { params })
}


export function updateAnswerContent(params: any): Promise<Response<any>> {
  return request.post('/submission/updateAnswerContent', params)
}


export function updateInspiration(params: any): Promise<Response<any>> {
  return request.post(`/space/${UserService.getSelfUserId()}/file/updateInspiration`, params)
}


// 获取当前提交的附件
export function preparePay(params?: any): Promise<Response<preparePayInfo>> {
  return request.post('/submission/preparePay', params)
}

export function getWordToUrl(params?: any): Promise<Response<any>> {
  return request.get('/submissionEdit/exportWordToUrl', { params })
}

