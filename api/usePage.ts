import type { StrictUseAxiosReturn } from '@vueuse/integrations/useAxios'
import type { AxiosRequestConfig } from 'axios'
import { type Ref, ref, watch } from 'vue'
import { basePageRequest } from './index'
import type { PageResult } from './typing'

export interface InSchoolUsePageReturn<T> extends StrictUseAxiosReturn<PageResult<T>> {
  /**
   * Manually call the axios request
   */
  query(conf: InSchoolUserPageConfig): void
  refresh(conf: InSchoolUserPageConfig): void

  /**
   * 当前页 从1开始 修改后自动调用query
   */
  pageNo: Ref<number>

  /**
   * 当前每页个数 修改后自动调用query
   */
  pageSize: Ref<number>

  /**
   * 是否还有更多数据
   */
  hasMore: Ref<boolean>

  /**
   * 分页请求返回数据
   */
  pageReturn: Ref<PageResult<T> | undefined>

  /**
   * 合并后的列表数据
   */
  // dataList: Ref<T[] | undefined>
  dataList: Ref<T[]>
}

export interface InSchoolUserPageConfig extends AxiosRequestConfig<any> {
  /**
   * 在刷新请求前清空数据
   */
  clearBeforeRefresh?: boolean
}

export function usePage<T>(url: string, config: InSchoolUserPageConfig): InSchoolUsePageReturn<T> {
  const pageReturn = ref<PageResult<T> | undefined>({ records: [] }) as Ref<
    PageResult<T> | undefined
  >
  const dataList = ref<T[]>([]) as Ref<T[]>
  const pageNo = ref(config.params?.pageNo || 1)
  const pageSize = ref(config.params?.pageSize || 10)
  const hasMore = ref(true)
  const current = ref(0)
  const pages = ref(0)

  const request = basePageRequest<T>(url, config)

  // 监听结果变化
  watch(request.isFinished, (newValue, _oldValue) => {
    const ret = request.data.value
    if (newValue && ret) {
      pageReturn.value = ret
      current.value = parseInt(ret?.current || '0')
      pages.value = parseInt(ret?.pages || '0')
      if (current.value === 1) {
        dataList.value = ret?.records
      } else {
        dataList.value?.push(...(ret?.records || []))
      }
      hasMore.value = current.value < pages.value
    }
  })

  // 监听分页变化
  watch([pageNo, pageSize], ([curNo, curSize], [_prevNo, prevSize]) => {
    if (pageNo.value === 1 && config.clearBeforeRefresh) {
      dataList.value = []
    }
    if (curSize === prevSize && curNo > pages.value) {
      return
    }
    config.params = Object.assign(config.params, { pageNo: pageNo.value, pageSize: pageSize.value })
    query(config)
  })

  // 手动执行
  const query = (conf: InSchoolUserPageConfig) => {
    if (!request.isLoading.value) {
      request.execute(undefined, Object.assign(config, conf))
    }
  }

  const refresh = (conf: InSchoolUserPageConfig) => {
    config = Object.assign(config, conf)
    pageNo.value = 1
  }

  return {
    ...request,
    query,
    refresh,
    hasMore,
    pageReturn,
    dataList,
    pageNo,
    pageSize,
  }
}
