import type { Response } from '@/services/types/reponse';
import request from '@/utils/request';

export function seoFileBuy(params: { site: string, sourceUrl: string, articleId: string }): Promise<Response<any>> {
    return request.post(`/seoFile/buy`, params);
}

export function userBuyFileList(params: any): Promise<any> {

    return request.get('/userBuyFile/list', { params })
}

export function getDownloadInfo(id: string): Promise<any> {
    return request.get(`/userBuyFile/getDownloadInfo?id=${id}`);
}

export function userBuyFileDelete(id: string): Promise<any> {
    return request.delete(`/userBuyFile/delete?id=${id}`);
}

export function getPayedFile(site: string, articleId: string): Promise<any> {
    return request.get(`/seoFile/getPayedFile?site=${site}&articleId=${articleId}`);
}
