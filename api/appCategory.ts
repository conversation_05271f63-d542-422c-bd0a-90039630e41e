import type { CreatorsInfo, DiscoverItemInfo, SearchCategoryInfo } from '@/services/types/appMessage'
import type { Response } from '@/services/types/reponse'
import request from '@/utils/request'

// 查询机器人列表
export function getCreatorsDetail(params: any): Promise<Response<CreatorsInfo>> {
  return request.get('/creator/getCreatorDetail', { params })
}

export function reportUseApp(params: any): Promise<Response<any>> {
  return request.post('/userOperation/add', params)
}

export function searchCategory(params: any): Promise<Response<SearchCategoryInfo>> {
  return request.get('/home/<USER>/v2', { params })
}


export function listHotCreator(params: any): Promise<Response<DiscoverItemInfo[]>> {
  return request.get('/creator/listByHot', { params })
}