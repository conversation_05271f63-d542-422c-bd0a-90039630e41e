FROM node:18-alpine

RUN mkdir -p /home/<USER>/app/node_modules && chown -R node:node /home/<USER>/app

WORKDIR /home/<USER>/app


# 复制当前目录下的所有文件到工作目录
COPY . /home/<USER>/app
# RUN npm config set registry https://registry.npmmirror.com && npm install pm2 -g
RUN npm config set registry https://registry.npmjs.org && npm install pm2 -g

# COPY pnpm-lock.yaml /home/<USER>/app
# # USER node

# RUN npm config set registry https://registry.npmmirror.com && npm install pm2 -g && npm install -g pnpm@9.15.0 && pnpm install

# COPY --chown=node:node . .


# # 构建生产版本

# RUN pnpm build

EXPOSE 3001

CMD npm run deploydocker
