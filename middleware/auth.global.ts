import { UserService } from '~/services/user'
import { useUserStore } from '~/stores/user'

export default defineNuxtRouteMiddleware(async () => {
  // if (to.path.startsWith("/profile")) {
  //   // 检查用户是否已登录
  //   // 如果未登录，可以重定向到登录页
  //   // return navigateTo('/login')
  // }

  // 客户端：检查并同步用户状态
  if (import.meta.client) {
    const isLogined = UserService.isLogined()
    if (isLogined) {
      const user = useUserStore()

      if (!user.isLogined) {
        // 先尝试从 cookies 初始化用户状态
        await user.initFromCookies()

        // 如果还是没有状态，则加载用户信息
        if (!user.isLogined) {
          await UserService.loadUserInfoAndAssistantMemberInfo()
        }
      }
    }
  }
})
