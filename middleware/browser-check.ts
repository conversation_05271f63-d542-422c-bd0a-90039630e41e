import { defineNuxtRouteMiddleware, navigateTo } from 'nuxt/app';

export default defineNuxtRouteMiddleware((to, from) => {

    if (import.meta.client) {
        const userAgent = navigator.userAgent;

        if (!userAgent.match(/(phone|pod|iPhone|iPod|ios|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i)) {
            const ua = userAgent.toLocaleLowerCase();

            const safariVersion = (function () {
                const matched = ua.match(/Version\/(.*) Safari/i);
                if (matched) {
                    return parseFloat(matched[1]);
                }
                return Infinity;
            })();

            const chromeVersion = (function () {
                const matched = ua.match(/Chrome\/(\d+)/i);
                if (matched) {
                    return parseFloat(matched[1]);
                }
                return Infinity;
            })();

            const firefoxVersion = (function () {
                const matched = ua.match(/Firefox\/(.*)/i);
                if (matched) {
                    return parseFloat(matched[1]);
                }
                return Infinity;
            })();

            const edgeVersion = (function () {
                const matched = ua.match(/Edge\/(.*)/i);
                if (matched) {
                    return parseFloat(matched[1]);
                }
                return Infinity;
            })();

            if (ua.match(/msie/) != null || ua.match(/trident/) != null) {
                return navigateTo("/upgrade"); // 跳转到升级提示页面
            } else if (safariVersion < 11 || edgeVersion < 79 || firefoxVersion < 71 || chromeVersion < 76) {
                return navigateTo("/upgrade"); // 跳转到升级提示页面
            }
            // // 简单检测 IE 浏览器
            // const isOldIE = /MSIE|Trident/.test(userAgent);

            // // 检测 Chrome、Firefox、Safari 的低版本（可根据项目需求自定义）
            // const isOldBrowser =
            //     (userAgent.includes("Chrome") && /Chrome\/([0-9]+)/.exec(userAgent)?.[1] && parseInt(RegExp.$1) < 60) ||
            //     (userAgent.includes("Firefox") && /Firefox\/([0-9]+)/.exec(userAgent)?.[1] && parseInt(RegExp.$1) < 50) ||
            //     (userAgent.includes("Safari") && /Version\/([0-9]+)/.exec(userAgent)?.[1] && parseInt(RegExp.$1) < 11);

            // if (isOldIE || isOldBrowser) {
            //     return navigateTo("/upgrade"); // 跳转到升级提示页面
            // }
        };

    }
}); 