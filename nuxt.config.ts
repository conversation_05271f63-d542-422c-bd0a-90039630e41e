// https://nuxt.com/docs/api/configuration/nuxt-config
import { AntDesignVueResolver } from 'unplugin-vue-components/resolvers'
import Components from 'unplugin-vue-components/vite'

const isProd = process.env.DEPLOY_ENV === 'prod'

let publicAssetsBaseUrl = '/'

console.log('process.env.DEPLOY_ENV', process.env.DEPLOY_ENV)
if (process.env.DEPLOY_ENV === 'prod') {
  publicAssetsBaseUrl = 'https://static.xiaoin.cn/prod/'
} else if (process.env.DEPLOY_ENV === 'pre') {
  // publicAssetsBaseUrl = 'https://static.xiaoin.cn/pre/'
} else if (process.env.DEPLOY_ENV === 'test') {
  publicAssetsBaseUrl = 'https://static.xiaoin.cn/test/'
}

console.log('publicAssetsBaseUrl', publicAssetsBaseUrl)
export default defineNuxtConfig({
  experimental: {
    componentIslands: false,
  },
  features: {
    inlineStyles: false
  },
  compatibilityDate: '2024-11-01',
  devtools: { enabled: false },
  devServer: {
    port: 3001,
    host: '0.0.0.0',  // 启动本地ip
  },
  build: {
    transpile: ['lodash-es'], // Add lodash-es to transpile
  },
  css: ['~/assets/css/main.css'],
  postcss: {
    plugins: {
      tailwindcss: {},
      autoprefixer: {},
    },
  },
  ssr: true,
  modules: [
    '@nuxtjs/robots',
    'nuxt-simple-sitemap',
    '@nuxt/icon',
    '@pinia/nuxt',
    '@nuxt/ui',
  ],
  app: {
    head: {
      charset: 'utf-8',
      viewport: 'width=device-width, initial-scale=1',
      title: '万能小in - 我的AI外脑',
      meta: [
        {
          name: 'description',
          content: '万能小in - 我的AI外脑',
        },
        { name: 'keywords', content: '万能小in, AI写作助手, 个人知识库, AI外脑' },
      ],
    },
    baseURL: '/',
    cdnURL: publicAssetsBaseUrl  // 使用同一个变量控制
  },
  site: {
    url: isProd ? 'https://xiaoin.com.cn' : 'https://test.xiaoin.com.cn',
  },
  robots: {
    UserAgent: '*',
    Allow: '/',
    Sitemap: 'https://xiaoin.com.cn/sitemap.xml',
  },
  nitro: {
    publicAssets: [{
      baseURL: publicAssetsBaseUrl
    }]
  },
  components: true,
  plugins: [
    '~/plugins/auth-ssr.server.ts',
    '~/plugins/auth-client.client.ts',
    '~/plugins/antd.ts',
    '~/plugins/baiduAnalytics.js',
    '~/plugins/umeng.js',
    '~/plugins/biying.js',
    '~/plugins/error-handler.ts'
  ],
  runtimeConfig: {
    public: {
      apiBase: process.env.NUXT_PUBLIC_API_BASE_URL,
      bookApiBase: process.env.NUXT_BOOK_API_BASE_URL,
      version: '1.0.1398'
    },
  },
  vite: {
    esbuild: {
      drop: process.env.DEPLOY_ENV === 'prod' || process.env.DEPLOY_ENV === 'pre' ? ['console'] : [],
    },
    plugins: [
      Components({
        // 指定组件位置，默认是 src/components
        dirs: ['components'],
        // 组件的有效文件扩展名
        extensions: ['vue'],
        // 配置文件生成位置
        dts: 'types/components.d.ts',
        // 解析的配置
        resolvers: [
          AntDesignVueResolver({
            importStyle: false,
            resolveIcons: true,
          }),
        ],
      }),
    ],
  },
})
