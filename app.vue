<template>
    <!-- <ConfigProvider :locale="$antdLocale"> -->
    <!-- <UContainer class="h-full max-w-full p-0 px-0 m-0 mx-0 lg:px-0 sm:px-0">

  </UContainer> -->

    <!-- </ConfigProvider> -->

    <NuxtLayout>
        <a-style-provider hash-priority="high" :transformers="[legacyLogicalPropertiesTransformer]">
            <NuxtPage />
        </a-style-provider>
    </NuxtLayout>

    <UNotifications />
</template>

<script lang="ts" setup>
import { legacyLogicalPropertiesTransformer } from 'ant-design-vue';

import '~/assets/css/main.css';
import { ShareService } from './services/share';
import { UserService } from './services/user';
// const { $antdLocale } = useNuxtApp();
import { useTracking } from '@/composables/useTracking';
import { loadErudaJs } from '@/utils/loadResources';
import { StarloveConstants } from '@/utils/starloveConstants';
import { StarloveUtil } from "@/utils/util";
import { onMounted } from 'vue';
import { getPlatformNew, useApp } from './composables/useApp';
import { useVisitorIdStore } from './stores/visitorId';
import { autoLoginThunderobot } from './utils/autoLogin';
import { UTM_SOURCE_VALUE } from './utils/constants';
import { mouseCheckVersion } from './utils/mouse_biz';
import { trackUniqueVisitor } from './utils/uvTracker';

const { initWorker, dispose } = useTracking();

const { public: { apiBase, bookApiBase, version } } = useRuntimeConfig()
const app = useApp()
const scriptList: any = [
    {
        innerHTML: `window.__RUNTIME_CONFIG__ = ${JSON.stringify({
            apiBase: apiBase,
            bookApiBase: bookApiBase
        })}`,
        type: 'text/javascript'
    },
];
if (app.value?.isMouse) {
    scriptList.push({
        src: '/js/sendDeviceInfo.js', // 替换为你的外部 JS 文件 URL
        async: false, // 可选，是否异步加载
        defer: false, // 可选，是否延迟加载
    })
}

useHead({
    script: scriptList
})
// const version = config.public.version
onMounted(async () => {
    if (import.meta.client) {
        // Assuming ShareService is a service that needs to be imported
        ShareService.saveSharer()
        ShareService.saveChannel()
        ShareService.saveClickId()
        ShareService.save360QhclickId()
        // 已和运营确认，目前暂时没有用了
        ShareService.save360SourceId()
        // 用于 bilibili 渠道
        ShareService.saveTrackId()
        // trackInitialReferer()


        // ShareService.getShareTimelineInRedirectUrl()
        const utm_source = ShareService.saveUtmSourceLenovo();


        UserService.loadOpenRecordAdd()
        ShareService.removeAnythingOlderThanOneDaySharer()


        // 获取渠道id
        ShareService.getChannelIdByUserInfo()

        // 生成 VisitorId
        const visitorIdStore = useVisitorIdStore()
        visitorIdStore.setVisitorId()

        if (utm_source == UTM_SOURCE_VALUE.THUNDEROBOT) {
            ShareService.saveUtmPhoneThunderobot();
            setTimeout(() => {
                autoLoginThunderobot()
            }, 300);
        }
        initWorker()
        setTimeout(async () => {
            // 鼠标端逻辑 需要检查升级， 把设备信息 window['sendDeviceInfo']，保存到本地
            if (app.value?.isMouse) {
                const href = window.location.href
                // lsg 保存时，检查是否包含/pdf-preview， 如果是，则截取50个， 后端太长了报错了
                const ignoreUrlList = ['/pdf-preview', '/file-preview']
                let isFlag = false
                ignoreUrlList.forEach(item => {
                    if (href.indexOf(item) > 0) {
                        isFlag = true
                    }
                })
                if (!isFlag) {
                    if (StarloveUtil.isInTestServer()) {
                        await loadErudaJs()
                    }
                    mouseCheckVersion(version)
                }
                if (window && window.sessionStorage) {
                    sessionStorage.setItem(StarloveConstants.keyOflocalStorage.platform, getPlatformNew())
                }
            }
            trackUniqueVisitor()
        }, 1000)
    }
})


onBeforeUnmount(() => {
    dispose()
})

</script>
