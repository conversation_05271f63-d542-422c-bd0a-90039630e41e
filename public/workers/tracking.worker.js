// 定义 TrackingWorker 类，用于处理事件的存储和发送
class TrackingWorker {
  constructor() {
    // 存储 IndexedDB 数据库实例，初始值为 null
    this.db = null
    // 定时器ID
    this.checkTimer = null
    // 标记是否正在发送数据
    this.isSending = false
    // 批量发送的最大数量
    this.BATCH_SIZE = 10

    this.initDb()
      .then(() => {
        self.postMessage({ type: 'INIT_SUCCESS' })
        // 启动定时器
        this.startCheckTimer()
        // 初始检查一次
        this.checkAndSendEvents()
      })
      .catch((error) => {
        self.postMessage({ type: 'INIT_ERROR', error: error.message })
      })
  }

  // 启动定时检查
  startCheckTimer() {
    if (this.checkTimer === null) {
      this.checkTimer = self.setInterval(() => {
        // console.log('定时器触发，检查数据库中的事件');
        this.checkAndSendEvents()
      }, 30000) // 30秒
    }
  }

  // 停止定时检查
  stopCheckTimer() {
    if (this.checkTimer !== null) {
      self.clearInterval(this.checkTimer)
      this.checkTimer = null
    }
  }

  // 初始化 IndexedDB 数据库的方法，返回一个 Promise
  initDb() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('trackingDB', 1)
      request.onerror = () => {
        reject(request.error)
      }
      request.onsuccess = (event) => {
        this.db = event.target.result
        resolve()
      }
      request.onupgradeneeded = (event) => {
        const db = event.target.result
        if (!db.objectStoreNames.contains('events')) {
          const store = db.createObjectStore('events', { keyPath: 'id' })
          store.createIndex('time', 'time')
        }
      }
    })
  }

  // 将多个事件存储到 IndexedDB 中的方法，返回一个 Promise
  storeEvents(events) {
    return new Promise((resolve, reject) => {
      // 检查数据库是否已经初始化
      if (!this.db) {
        // 如果数据库未初始化，拒绝 Promise 并抛出错误
        reject(new Error('数据库未初始化'))
        return
      }
      // 创建一个读写事务，作用于 'events' 对象存储
      const transaction = this.db.transaction(['events'], 'readwrite')
      // 从事务中获取 'events' 对象存储
      const store = transaction.objectStore('events')
      // 遍历事件数组
      events.forEach((event) => {
        // 向对象存储中添加单个事件
        const request = store.add(event)
        // 监听添加请求的错误事件
        request.onerror = () => {
          // 如果添加请求出错，拒绝 Promise 并抛出错误
          reject(request.error)
        }
      })
      // 监听事务完成事件
      transaction.oncomplete = () => {
        // 存储完成后，检查数据库中的事件数量
        this.checkEventCount()
        // 完成后，解决 Promise
        resolve()
      }
      // 监听事务错误事件
      transaction.onerror = () => {
        // 如果事务出错，拒绝 Promise 并抛出错误
        reject(transaction.error)
      }
    })
  }

  // 检查数据库中的事件数量，如果超过批量大小则立即发送
  checkEventCount() {
    if (!this.db || this.isSending) {
      return
    }

    const transaction = this.db.transaction(['events'], 'readonly')
    const store = transaction.objectStore('events')
    const countRequest = store.count()

    countRequest.onsuccess = () => {
      if (countRequest.result >= this.BATCH_SIZE) {
        // console.log(`数据库中有 ${countRequest.result} 条事件，超过批量大小 ${this.BATCH_SIZE}，立即发送`);
        this.checkAndSendEvents()
      } else {
        // console.log(`数据库中有 ${countRequest.result} 条事件，未达到批量大小 ${this.BATCH_SIZE}，等待更多数据`);
      }
    }
  }

  // 检查并发送事件数据到服务器的方法，返回一个 Promise
  checkAndSendEvents() {
    // 如果正在发送数据，检查是否超时
    if (this.isSending) {
      const now = Date.now()
      if (!this.sendingStartTime || now - this.sendingStartTime > 30000) {
        // 30秒超时
        console.warn('发送操作超时，重置状态')
        this.isSending = false
        this.sendingStartTime = null
      } else {
        return Promise.resolve()
      }
    }

    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('数据库未初始化'))
        return
      }

      this.isSending = true
      this.sendingStartTime = Date.now()

      const transaction = this.db.transaction(['events'], 'readonly')
      const store = transaction.objectStore('events')
      const countRequest = store.count()

      const cleanup = () => {
        this.isSending = false
        this.sendingStartTime = null
      }

      countRequest.onsuccess = () => {
        if (countRequest.result > 0) {
          const index = store.index('time')
          const request = index.getAll()

          request.onsuccess = () => {
            const events = request.result
            if (events.length > 0) {
              // 分批处理数据，每次最多处理 BATCH_SIZE 条
              const batches = []
              for (let i = 0; i < events.length; i += this.BATCH_SIZE) {
                batches.push(events.slice(i, i + this.BATCH_SIZE))
              }

              // 串行处理每个批次
              const processBatch = async (batchEvents) => {
                try {
                  const response = await fetch(`${location.protocol}//${location.host}/api/track`, {
                    method: 'POST',
                    headers: {
                      'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(
                      batchEvents.map((e) => ({
                        event: e.event,
                        time: e.time,
                        channel: e.channel,
                        is_new_user: e.is_new_user,
                        user_id: e.user_id,
                        visitor_id: e.visitor_id,
                        target_id: e.target_id,
                        team_id: e.team_id,
                        channel_id: e.channel_id,
                        platform: e.platform,
                      }))
                    ),
                  })

                  if (response.ok) {
                    await this.deleteEvents(batchEvents.map((e) => e.id))
                    console.log(`成功发送 ${batchEvents.length} 条数据`)
                  } else {
                    throw new Error('服务器响应错误')
                  }
                } catch (error) {
                  console.error('批次处理失败:', error)
                  throw error
                }
              }

              // 依次处理所有批次
              Promise.all(batches.map((batch) => processBatch(batch)))
                .then(() => {
                  cleanup()
                  resolve()
                })
                .catch((error) => {
                  console.error('数据发送过程出错:', error)
                  cleanup()
                  reject(error)
                })
            } else {
              cleanup()
              resolve()
            }
          }

          request.onerror = () => {
            cleanup()
            reject(request.error)
          }
        } else {
          cleanup()
          resolve()
        }
      }

      countRequest.onerror = () => {
        cleanup()
        reject(countRequest.error)
      }
    })
  }

  // 从 IndexedDB 中删除指定 ID 的事件的方法
  deleteEvents(ids) {
    if (!this.db) {
      throw new Error('数据库未初始化')
    }
    const transaction = this.db.transaction(['events'], 'readwrite')
    const store = transaction.objectStore('events')
    ids.forEach((id) => {
      store.delete(id)
    })
  }

  // 处理主线程发送的消息的方法
  async handleMessage(e) {
    const { type, data } = e.data
    try {
      if (type === 'TRACK') {
        const trackEvents = data.map((singleEvent) => ({
          id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          event: singleEvent.event,
          time: singleEvent.time,
          channel: singleEvent.channel,
          is_new_user: singleEvent.is_new_user,
          channel_id: singleEvent.channel_id,
          visitor_id: singleEvent.visitor_id,
          user_id: singleEvent.user_id,
          target_id: singleEvent.target_id,
          team_id: singleEvent.team_id,
          platform: singleEvent.platform,
        }))

        await this.storeEvents(trackEvents)
        self.postMessage({ type: 'TRACK_SUCCESS' })
      }
    } catch (error) {
      self.postMessage({
        type: 'TRACK_ERROR',
        error: error instanceof Error ? error.message : '未知错误',
      })
    }
  }
}

// 创建 TrackingWorker 实例
const worker = new TrackingWorker()

// 监听主线程发送的消息，并调用 handleMessage 方法处理
self.onmessage = (e) => worker.handleMessage(e)
