{"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll": "explicit", "source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "eslint.validate": ["javascript", "typescript", "vue"], "[vue]": {"editor.defaultFormatter": "Vue.volar"}, "[typescript]": {"editor.defaultFormatter": "vscode.typescript-language-features"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}}