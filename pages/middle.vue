<template>
    <view class="middle-page">
        <view class="logo-area">
            <image class="logo" src="https://static.xiaoin.cn/prod/logo.png" />
        </view>
        <view class="title">万能小in</view>
        <view class="desc">正在跳转到微信...</view>
        <view class="desc">如未自动打开微信请点击下方按钮</view>

        <!-- 按钮区域 -->
        <view class="skip-button">
            <button class="primary-button" :disabled="disabled" @click="gotoWechatXiaoin">
                打开微信
            </button>
        </view>
    </view>
</template>
<script setup lang="ts">
import { generateSchemeLink } from '@/api/user'
import { message } from 'ant-design-vue'
import { computed, onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { UserService } from '~/services/user'

const disabled = ref(false)
const router = useRouter()
const openWechatXiaoinLink = ref()
const sharerUserId = computed(() => UserService.getSharerUserId() || router.currentRoute.value.query.sharerUserId)

onMounted(() => {
    setTimeout(() => {
        loadGenerateSchemeLinkData()
    }, 50)
})

const loadGenerateSchemeLinkData = async () => {
    const params = {
        jumpWxa: {
            path: 'pages/xiaoin/square/index',
            query: `sharerUserId=${sharerUserId.value ? sharerUserId.value : '1859121823127334913'}`,
            envVersion: 'release'
        },
        isExpire: true,
        expireTime: new Date().getTime() + 24 * 60 * 1000
    }
    // console.log('loadGenerateSchemeLinkData params', params)
    const res = await generateSchemeLink(params)
    // console.log('loadGenerateSchemeLinkData res', res)
    if (!res.ok || !res.data) {
        message.warning(res.message || '参数获取失败')
        return
    }
    openWechatXiaoinLink.value = res.data
    window.location.replace(res.data)
}

const gotoWechatXiaoin = () => {
    if (!openWechatXiaoinLink.value) {
        loadGenerateSchemeLinkData()
    }
    window.location.replace(openWechatXiaoinLink.value)
}
</script>

<style lang="scss" scoped>
.middle-page {
    display: flex;
    justify-content: center;
    flex-direction: column;
    // padding-top: 100px;
    background-color: #ffffff;
    height: 100vh;

    .logo-area {
        display: flex;
        justify-content: center;

        .logo {
            text-align: center;
            width: 100px;
            height: 100px;
            border-radius: 50px;
        }
    }



    .title {
        margin-top: 20px;
        font-size: 14px;
        line-height: 21px;
        font-weight: 500;
        text-align: center;
    }

    .desc {
        text-align: center;
        margin-top: 20px;
        font-size: 12px;
        line-height: 17px;
        color: #817f7f;
    }

    .skip-button {
        text-align: center;
        margin-top: 20px;
        display: flex;
        justify-content: center;

        .primary-button {
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 8px 24px;
            font-size: 14px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;

            &:hover {
                background-color: #40a9ff;
            }

            &:disabled {
                background-color: #d9d9d9;
                cursor: not-allowed;
            }
        }
    }
}
</style>