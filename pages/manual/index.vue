<template>
    <div class="h-full bg-slate-50 overflow-hidden flex flex-col">
        <!-- 固定头部区域 -->
        <div class="sticky top-0 z-50 bg-slate-50">
            <!-- 页面标题区 -->
            <div class="bg-white/80 rounded-xl px-4 py-2 border border-blue-700/20 m-4">
                <div class="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-4">
                    <!-- 标题部分 -->
                    <div class="flex items-center">
                        <div
                            class="w-10 h-10 bg-blue-700 rounded-xl flex items-center justify-center mr-4 shadow-inner">
                            <write theme="filled" size="24" fill="#FFFFFF" />
                        </div>
                        <div class="w-[280px]">
                            <h1
                                class="text-lg font-medium text-transparent bg-gradient-to-r from-blue-700 to-indigo-700 bg-clip-text">
                                写作宝典
                            </h1>
                            <div class="relative">
                                <ClientOnly>
                                    <VerticalMarquee :color="'#111827'" class="w-full text-sm text-gray-900"
                                        :texts="marqueeTexts" />
                                </ClientOnly>
                            </div>
                        </div>
                    </div>

                    <!-- 立即体验 -->
                    <div class="flex items-center space-x-4 order-2 lg:order-3">
                        <!-- <NuxtLink :to="`/create?channel=comcn-manual`" target="_blank"
                            class="px-4 py-2 bg-blue-700 text-white rounded-lg">
                            立即体验
                        </NuxtLink> -->

                        <UserAvatar />
                    </div>
                </div>
            </div>
        </div>

        <!-- 可滚动的主要内容区域 -->
        <div class="flex-1 overflow-y-auto">
            <div class="min-h-full flex flex-col items-center">
                <!-- 栏目导航 -->
                <div class="w-[90%] bg-white shadow-sm rounded-[7px]">
                    <div class="px-4 py-2.5">
                        <div class="flex flex-wrap items-center">
                            <template v-for="(category, index) in categoriesF" :key="category.id">
                                <div
                                    class="py-2 w-1/2 sm:w-1/3 md:w-1/4 lg:w-1/5 xl:w-[10%] flex items-center justify-center">
                                    <NuxtLink :to="`/manual/${category.slug}`"
                                        class="w-full text-[16px] font-normal text-[#333333] hover:text-blue-600 transition-all duration-200 text-center">
                                        {{ category.name }}
                                    </NuxtLink>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>

                <!-- 分类卡片区域 -->
                <div class="w-[90%] m-5">
                    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-5">
                        <div v-for="category in filteredCategories" :key="category.id"
                            class="bg-white rounded-[7px] shadow-sm border border-gray-100 hover:border-blue-200 hover:shadow transition-all duration-200 p-4">
                            <NuxtLink :to="`/manual/${category.slug}`"
                                class="px-3 py-2 border-b border-gray-100/80 flex items-center justify-between bg-[#F5F7FF] transition-all duration-200 rounded-[7px]">
                                <div class="flex items-center">
                                    <span class="w-[3px] h-4 bg-[#2551B5] mr-2"></span>
                                    <h3 class="text-sm font-bold text-gray-800">{{ category.name }}</h3>

                                </div>
                                <div class="text-sm text-[#2551B5] flex items-center">
                                    更多
                                    <div class="w-4 h-4 ml-0.5">
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd"
                                                d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z"
                                                clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                </div>
                            </NuxtLink>
                            <div class="p-2">
                                <div v-if="categoryPosts && categoryPosts[category.id]?.length" class="space-y-1.5">
                                    <NuxtLink v-for="post in categoryPosts[category.id]" :key="post.id"
                                        :to="`/manual/${post.category_slug || category.slug}/${post.id}`"
                                        class="block hover:bg-gray-50 rounded px-2 py-1.5 transition-colors">
                                        <h4 class="text-sm text-[#333333] line-clamp-1">{{ post.title.rendered
                                        }}</h4>
                                        <div class="text-xs text-gray-400 mt-0.5">
                                            {{ formatDate(post.date) }}
                                        </div>
                                    </NuxtLink>
                                </div>
                                <div v-else class="py-4 text-center text-sm text-gray-400">
                                    暂无文章
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 底部区域 -->
                <Footer class="mt-auto" />
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import VerticalMarquee from '@/components/Common/VerticalMarquee.vue';
import { Write } from '@icon-park/vue-next';

import Footer from '~/components/index/Footer.vue';
import { dateFormatS } from '~/utils/utils';


const marqueeTexts = [
    '写作必备指南，精选优质范文',
    'AI原创，无版权风险',
    '海量应用模板，想写什么就写什么',
    '150 + 应用，立即体验写同款内容'
]


// 指定要显示的分类名称
const targetCategories = ['paper', 'work_report', 'shenqing', 'port_royale', 'sum_up', 'report_work', 'self_comment', 'work_plan', 'report']

interface WPCategory {
    id: number;
    count: number;
    description: string;
    name: string;
    meta: any[];
    slug: string;
}

interface WPPost {
    id: number;
    title: {
        rendered: string;
    };
    date: string;
    link: string;
    categories?: number[];
    category_slug?: string;
}

useHead({
    title: '写作宝典下载-范文大全-万能小in官网',
    meta: [
        {
            name: 'keywords',
            content: '写作,写作宝典,范文,范文下载,范文大全',
            tagPriority: 'critical', // 提高优先级
        },
        {
            name: 'description',
            content: '万能小in写作宝典为您提供论文、心得体会、工作总结、工作计划等分类文章写作指导及范文示例，您可以直接下载范文使用或在线修改，帮助您轻松完成内容写作。',
            tagPriority: 'critical', // 提高优先级
        }
    ]
})

// 添加日期格式化函数
const formatDate = (date: string) => {
    return dateFormatS(new Date(date), 'yyyy/MM/dd')
}

// 使用 useAsyncData 获取分类数据
const { data: categories } = await useAsyncData('categories', async () => {
    try {
        const response = await $fetch('https://www.xiaoin.com.cn/wp-json/wp/v2/categories', {
            params: {
                _fields: 'id,name,count,description,meta,slug',
                per_page: 100
            }
        }) as WPCategory[];

        return response
            .filter(cat => cat.count > 0)
            .sort((a, b) => b.count - a.count);
    } catch (error) {
        console.error('Failed to fetch categories:', error);
        return [];
    }
});

const categoriesF = computed(() => {
    return (categories.value || []).filter(cat => cat.description)
})

// 保持原有的过滤逻辑
const filteredCategories = computed(() => {
    return categories.value?.filter(cat => targetCategories.includes(cat.description)) || [];
});

// 使用 useAsyncData 获取所有分类的文章
const { data: categoryPosts } = await useAsyncData<Record<number, WPPost[]>>('categoryPosts', async () => {
    const posts: Record<number, WPPost[]> = {};
    const categorySlugMap: Record<number, string> = {};

    if (categories.value) {
        categories.value.forEach(cat => {
            categorySlugMap[cat.id] = cat.slug;
        });
    }

    if (filteredCategories.value) {
        await Promise.all(
            filteredCategories.value.map(async (category) => {
                try {
                    const childCategories = await $fetch<WPCategory[]>('https://www.xiaoin.com.cn/wp-json/wp/v2/categories', {
                        params: {
                            parent: category.id,
                            _fields: 'id,slug',
                            per_page: 100
                        }
                    });

                    childCategories.forEach(childCat => {
                        categorySlugMap[childCat.id] = childCat.slug;
                    });

                    const allCategoryIds = [category.id, ...childCategories.map(cat => cat.id)];

                    const response = await $fetch<WPPost[]>(`https://www.xiaoin.com.cn/wp-json/wp/v2/posts`, {
                        params: {
                            categories: allCategoryIds.join(','),
                            per_page: 5,
                            _fields: 'id,title,date,link,categories',
                            orderby: 'date',
                            order: 'desc'
                        }
                    });

                    response.forEach(post => {
                        if (post.categories && post.categories.length > 0) {
                            const categoryId = post.categories[0];
                            post.category_slug = categorySlugMap[categoryId] || category.slug;
                        } else {
                            post.category_slug = category.slug;
                        }
                    });

                    posts[category.id] = response;
                } catch (error) {
                    console.error(`Failed to fetch posts for category ${category.id}:`, error);
                    posts[category.id] = [];
                }
            })
        );
    }

    return posts;
});
</script>