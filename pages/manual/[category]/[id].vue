<template>
    <div class="h-full bg-slate-50 overflow-hidden flex flex-col">
        <!-- 固定头部区域 -->
        <div class="sticky top-0 z-50 bg-slate-50">
            <!-- 页面标题区 -->
            <div class="bg-white/80 rounded-xl px-4 py-2 border border-blue-700/20 m-4">
                <div class="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-4">
                    <!-- 标题部分 -->
                    <div class="flex items-center">
                        <div
                            class="w-10 h-10 bg-blue-700 rounded-xl flex items-center justify-center mr-4 shadow-inner">
                            <write theme="filled" size="24" fill="#FFFFFF" />
                        </div>
                        <div class="w-[280px]">
                            <h1
                                class="text-lg font-medium text-transparent bg-gradient-to-r from-blue-700 to-indigo-700 bg-clip-text">
                                {{ category?.name || '写作宝典' }}
                            </h1>
                            <div class="relative">
                                <ClientOnly>
                                    <VerticalMarquee :color="'#111827'" class="w-full text-sm text-gray-900"
                                        :texts="marqueeTexts" />
                                </ClientOnly>
                            </div>
                        </div>
                    </div>

                    <!-- 用户信息区域 -->
                    <div class="flex items-center space-x-4 order-2 lg:order-3">
                        <UserAvatar />
                    </div>
                </div>
            </div>

            <!-- 面包屑导航 -->
            <div class="container mx-auto px-4">
                <nav class="flex items-center space-x-2 text-sm text-gray-500 mb-4">
                    <NuxtLink to="/manual" class="hover:text-blue-600 transition-colors">写作宝典</NuxtLink>
                    <span class="text-gray-300">/</span>
                    <template v-for="(cat, index) in breadcrumbCategories" :key="cat.id">
                        <NuxtLink :to="`/manual/${cat.slug}`"
                            class="text-gray-500 hover:text-blue-600 transition-colors">
                            {{ cat.name }}
                        </NuxtLink>
                        <span class="text-gray-300" v-if="index < breadcrumbCategories.length - 1">/</span>
                    </template>
                    <span class="text-gray-300">/</span>
                    <span class="text-gray-900" v-html="(post?.title?.rendered || '加载中...')"></span>
                </nav>
            </div>
        </div>

        <!-- 可滚动的主要内容区域 -->
        <div class="flex-1 overflow-y-auto" ref="scrollContainer">
            <div class="min-h-full flex flex-col">
                <!-- 主要内容区域 - 左右分栏 -->
                <div class="container mx-auto mb-4 px-4 flex-1">
                    <div class="flex flex-col lg:flex-row gap-6 relative">
                        <!-- 左侧内容区域 -->
                        <div class="lg:w-[64%]">
                            <div class="bg-white rounded-lg shadow-sm p-6">
                                <h1 class="text-2xl font-medium text-gray-900 mb-4"
                                    v-html="(post?.title?.rendered || '')">
                                </h1>
                                <div class="flex items-center text-sm text-gray-500 mb-6 space-x-4">
                                    <time>{{ post?.date ? formatDate(post.date) : '' }}</time>
                                    <div class="flex items-center">
                                        <eyes theme="outline" size="16" class="mr-1" />
                                        <span>{{ post?.meta?.views || 0 }}</span>
                                    </div>
                                </div>

                                <div class="prose prose-blue max-w-none select-none" v-html="processedContent"
                                    v-if="post?.content?.rendered" @click="handleAnchorClick" @selectstart.prevent
                                    @copy.prevent @contextmenu.prevent>
                                </div>
                                <div v-else class="text-center py-8 text-gray-500">加载中...</div>
                            </div>

                            <!-- 上一篇下一篇导航 -->
                            <div class="mt-4 mb-20">
                                <div class="flex justify-between">
                                    <button @click="navigateToPost(prevPost?.id)" :disabled="!prevPost"
                                        class="inline-flex items-center px-4 py-2 border rounded-md text-sm transition-colors max-w-[25%]"
                                        :class="[
                                            prevPost
                                                ? 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                                                : 'bg-gray-100 border-gray-200 text-gray-400 cursor-not-allowed'
                                        ]">
                                        <left-c theme="outline" size="16" class="mr-1 flex-shrink-0" />
                                        <span class="truncate">{{ prevPost?.title?.rendered }}</span>
                                    </button>

                                    <button @click="navigateToPost(nextPost?.id)" :disabled="!nextPost"
                                        class="inline-flex items-center px-4 py-2 border rounded-md text-sm transition-colors max-w-[25%]"
                                        :class="[
                                            nextPost
                                                ? 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                                                : 'bg-gray-100 border-gray-200 text-gray-400 cursor-not-allowed'
                                        ]">
                                        <span class="truncate">{{ nextPost?.title?.rendered }}</span>
                                        <right-c theme="outline" size="16" class="ml-1 flex-shrink-0" />
                                    </button>
                                </div>
                            </div>

                            <!-- 底部固定的 AI写同款按钮 -->
                            <div ref="fixedButtons" :style="{ bottom: footerHeight || '50px' }"
                                class="fixed left-1/2 -translate-x-1/2 lg:left-[42%] z-50 w-full lg:w-[40%] px-4 lg:px-0 lg:pb-0 flex gap-3 lg:gap-5">
                                <NuxtLink :to="`/create/${breadcrumbCategories[0]?.slug}`"
                                    :tag="isMobileDevice() ? 'div' : 'a'" @click="handleAIButtonClick"
                                    class="h-[48px] lg:h-[59px] w-[48%] bg-gradient-to-br from-[#5B69E5] to-[#7FA9FF] rounded-[10px] shadow-lg flex justify-center items-center hover:shadow-xl transition-shadow">
                                    <img width="20px" height="20px" class="mr-[2px]"
                                        src="https://static-1256600262.file.myqcloud.com/xiaoin-pc/images/seo/writing-code.png"
                                        alt="">
                                    <button
                                        class="text-[18px] lg:text-[22px] font-medium text-white flex justify-center items-center leading-[37px]">AI写同款</button>
                                </NuxtLink>

                                <div class="h-[48px] lg:h-[59px] w-[48%] bg-white border-2 border-[#2551B5] rounded-[10px] shadow-lg flex justify-center items-center hover:shadow-xl transition-shadow cursor-pointer"
                                    @click="handleDownloadClick">
                                    <img width="20px" height="20px" class="mr-[2px]"
                                        src="https://static-1256600262.file.myqcloud.com/xiaoin-pc/images/seo/download-code.png"
                                        alt="">
                                    <span
                                        class="text-[18px] lg:text-[22px] font-medium text-[#2551B5] flex justify-center items-center leading-[37px]">下载此文档</span>
                                </div>
                            </div>
                        </div>

                        <!-- 右侧边栏 -->
                        <div class="lg:w-[34%]">
                            <div>
                                <RightSidebar @scrollToTop="handleScrollToTop" :parent-category-id="currentCategoryId"
                                    :show-all="false" :code="breadcrumbCategories[0]?.slug"
                                    :category="breadcrumbCategories[0]" />
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 底部区域 -->
                <Footer ref="footer" class="mt-auto" />
            </div>
        </div>

        <!-- 下载弹窗 -->
        <DownloadModal v-model="showDownloadModal" />
    </div>
</template>

<script setup lang="ts">
import { useAsyncData, useHead, useRoute, useRouter } from '#imports';
import VerticalMarquee from '@/components/Common/VerticalMarquee.vue';
import { Eyes, LeftC, RightC, Write } from '@icon-park/vue-next';
import { message } from 'ant-design-vue';
import { computed, nextTick, onMounted, onUnmounted, ref, watch } from 'vue';
import { getPayedFile } from '~/api/seo';
import DownloadModal from '~/components/Manual/DownloadModal.vue';
import RightSidebar from '~/components/Manual/RightSidebar.vue';
import { useTracking } from '~/composables/useTracking';
import type { ViewData, WPCategory, WPPost } from '~/services/types/wp';
import { UserService } from '~/services/user';
import { useUserStore } from '~/stores/user';
import { isMobileDevice, loadSeoFriendlySchemeLink } from '~/utils/manual/manual';
import { dateFormatS } from '~/utils/utils';

const route = useRoute();
const router = useRouter();
const postId = computed(() => route.params.id);
const categoryId = computed(() => route.params.category);


const { track } = useTracking();


const store = useUserStore();

const showDownloadModal = ref(false);
const footerHeight = ref('');
const footer = ref<FooterRef>(null);
let observer: MutationObserver | null = null;  // 添加 observer 变量

// 添加上一篇下一篇文章的状态
const prevPost = ref<WPPost | null>(null);
const nextPost = ref<WPPost | null>(null);

const scrollContainer = ref<HTMLElement | null>(null);

onMounted(() => {


    nextTick(() => {
        const anchors = document.querySelectorAll('a.ez-toc-link[data-href]');
        anchors.forEach(anchor => {
            anchor.addEventListener('click', e => {
                e.preventDefault();
                const href = anchor.getAttribute('data-href');
                const hashIndex = href?.indexOf('#');
                if (hashIndex === -1) return;
                const hash = decodeURIComponent(href?.substring((hashIndex ?? 0) + 1) || '');
                // 使用混合匹配策略查找对应的标题元素
                const targetEl = findHeadingElement(hash);
                if (targetEl) {
                    targetEl.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                    history.replaceState(null, '', window.location.pathname + window.location.search);
                } else {
                    console.warn('未找到匹配的标题元素:', hash);
                }
            });
        });
    });

    // updateUserRecord();
    // 使用 requestAnimationFrame 确保 DOM 完全渲染
    requestAnimationFrame(() => {
        observeFooter();
        updateFooterHeight();
        handleContentLinks();
    });

    window.addEventListener('resize', updateFooterHeight);
    track('manual_detail', postId.value.toString(), '写作宝典文章查看')
});



const marqueeTexts = [
    '写作必备指南，精选优质范文',
    'AI原创，无版权风险',
    '海量应用模板，想写什么就写什么',
    '150 + 应用，立即体验写同款内容'
]






// 添加新的类型定义
type FooterRef = {
    $el: HTMLElement;
} | null;

// 添加日期格式化函数
const formatDate = (date: string) => {
    return dateFormatS(new Date(date), 'yyyy/MM/dd')
}

// 提供默认值
const defaultPost: WPPost = {
    id: 0,
    title: { rendered: '' },
    content: { rendered: '' },
    date: '',
    categories: [],
    meta: {
        views: 0,
        custom_title: '',
        custom_keywords: '',
        custom_description: ''
    },
    _links: {},
    _embedded: {}
}

// 添加 TDK 数据解析函数
const parseTDKData = (data: string) => {
    try {
        const keywordsRegex = /s:8:"keywords";s:\d+:"(.*?)"/;
        const descriptionRegex = /s:11:"description";s:\d+:"(.*?)"/;
        const titleRegex = /s:5:"title";s:\d+:"(.*?)"/;

        const keywordsMatch = data.match(keywordsRegex);
        const descriptionMatch = data.match(descriptionRegex);
        const titleMatch = data.match(titleRegex);

        return {
            custom_title: titleMatch ? titleMatch[1] : '',
            custom_keywords: keywordsMatch ? keywordsMatch[1] : '',
            custom_description: descriptionMatch ? descriptionMatch[1] : ''
        };
    } catch (error) {
        console.error('解析 TDK 数据失败:', error);
        return {
            custom_title: '',
            custom_keywords: '',
            custom_description: ''
        };
    }
};



// 获取分类数据
const { data: categories } = await useAsyncData(
    `categories-${categoryId.value}`,
    async () => {
        try {
            const response = await $fetch<WPCategory[]>(`${seo}wp-json/wp/v2/categories`, {
                params: {
                    _fields: 'id,name,count,description,parent,meta,slug',
                    per_page: 100,
                }
            })


            if (!response || !Array.isArray(response)) {
                router.push('/404');
                return null;
            }


            // 先尝试通过 description 匹配
            let foundCategory = response.find(cat => cat.slug === categoryId.value);

            if (!foundCategory) {
                router.push('/404');
                return null;
            }
            response.push(foundCategory)

            return response
        } catch (error) {
            console.error('获取分类信息失败:', error)
            return []
        }
    }
)

// 计算当前分类和子分类
const category = computed(() => {
    if (!categories.value || !Array.isArray(categories.value) || categories.value.length === 0) {
        return null;
    }
    return categories.value[categories.value.length - 1];
});

// // 等待分类计算完成后再获取文章数据
// watchEffect(async () => {
//     if (category.value === null) {
//         return;
//     }
// });
// 获取文章数据
const { data: post, refresh: refreshPost } = await useAsyncData<WPPost>(
    `post-${postId.value}`,
    async () => {
        if (!postId.value) {
            router.push('/404');
            return defaultPost;
        }
        try {
            // 添加验证：检查文章 ID 是否为有效数字
            const id = Number(postId.value);
            if (isNaN(id) || id <= 0) {
                router.push('/404');
                return defaultPost;
            }

            const [postResponse, viewCountResponse, viewData] = await Promise.all([
                $fetch<WPPost>(`${seo}wp-json/wp/v2/posts/${postId.value}`, {
                    params: {
                        _fields: 'id,title,content,date,categories,meta,acf,_links,_embedded',
                        _embed: 'wp:term'
                    }
                }),
                $fetch<{ message: string; post_id: string; updated_views: number }>(
                    `${seo}wp-json/custom/v1/update-views-count?post_id=${postId.value}`,
                    { method: 'GET' }
                ).catch(() => ({ message: '', post_id: '', updated_views: 0 })),
                $fetch<ViewData>(`${seo}wp-json/custom/v1/post-meta/${postId.value}`)
            ]);

            // 验证文章响应数据
            if (!postResponse || !postResponse.id) {
                router.push('/404');
                return defaultPost;
            }



            // 验证文章分类是否匹配当前路由的分类
            const categorySlug = route.params.category;
            const postCategories = postResponse._embedded?.['wp:term']?.[0] || [];
            const categoryMatch = postCategories.some((cat: any) => cat.slug === categorySlug);
            if (!categoryMatch) {
                router.push('/404');
                return defaultPost;
            }

            // 解析 TDK 数据
            const tdkData = viewData._superfast_singular_meta?.[0]
                ? parseTDKData(viewData._superfast_singular_meta[0])
                : { custom_title: '', custom_keywords: '', custom_description: '' };

            const result: WPPost = {
                ...postResponse,
                meta: {
                    views: viewCountResponse.updated_views || 0,
                    ...tdkData
                }
            };
            return result;
        } catch (error) {
            console.log('获取文章数据失败:', error);
            router.push('/404');
            return defaultPost;
        }
    },
    {
        watch: [postId],
        server: true,
        transform: (data) => data as WPPost
    }
);

// 添加路由监听
// watch(
//     () => route.params,
//     async (newParams) => {
//         if (newParams.id !== postId.value) {
//             // 当 URL 中的 ID 发生变化时重新获取数据
//             await refreshPost();
//         }
//     },
//     { deep: true }
// );

// 优化数据监听逻辑
watch([post], ([newPost]) => {
    // 检查是否所有请求都已完成
    if (newPost) {
        if (newPost.id === defaultPost.id && postId.value) {
            router.push('/404');
            return;
        }
    }
}, { immediate: true });

// 计算当前分类的ID
const currentCategoryId = computed(() => {
    // 如果有文章数据，使用文章的分类ID
    if (post.value && post.value.categories && post.value.categories.length > 0) {
        return post.value.categories[0];
    }
    // 否则使用当前路由的分类ID
    return category.value?.id || 0;
});

// 计算面包屑层级
const breadcrumbCategories = computed(() => {
    if (!post.value?.categories?.[0] || !categories.value || !Array.isArray(categories.value)) {
        return [];
    }

    const result: WPCategory[] = [];
    let currentCatId = post.value.categories[0];

    // 查找当前分类及其所有父级
    while (currentCatId) {
        const cat = categories.value.find(c => c.id === currentCatId);

        if (cat) {
            result.unshift(cat);
            currentCatId = cat.parent;
        } else {
            break;
        }
    }

    return result;
});

const isPurchased = ref(false);
const purchaseData = ref<any>(null);
const checkArticlePurchaseStatus = async () => {
    try {
        const res = await getPayedFile('https://www.xiaoin.com.cn', postId.value as string);
        isPurchased.value = !!res.data;
        purchaseData.value = res.data;
    } catch (error) {
        // console.error('检查购买状态失败:', error);
        isPurchased.value = false;
        purchaseData.value = null;
    }
}
// 处理下载点击
const handleDownloadClick = async () => {

    if (!UserService.isLogined()) {
        showDownloadModal.value = true;
        return;
    }
    await checkArticlePurchaseStatus();
    if (isPurchased.value && purchaseData.value?.id) {
        // 如果已购买，直接跳转到下载页面
        router.push(`/manual/detail/${purchaseData.value.id}`);
    } else {
        // 如果未购买，显示购买弹窗
        showDownloadModal.value = true;
    }
    track('manual_download', postId.value.toString(), '下载按钮点击')
}

// 处理滚动到顶部
const handleScrollToTop = () => {
    if (!scrollContainer.value) return;

    try {
        scrollContainer.value.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    } catch (e) {
        // 降级处理：如果 scrollTo 不支持，直接设置 scrollTop
        scrollContainer.value.scrollTop = 0;
    }
}

// 更新底部高度
const updateFooterHeight = () => {
    if (!footer.value?.$el) return;

    requestAnimationFrame(() => {
        if (footer.value?.$el) {
            footerHeight.value = isMobileDevice()
                ? '5%'  // 移动设备使用百分比
                : `${footer.value.$el.getBoundingClientRect().height}px`;  // PC设备使用像素
        }
    });
}

// 监听 Footer 变化
const observeFooter = () => {
    if (!footer.value?.$el) return;

    // 如果已经存在 observer，先断开连接
    if (observer) {
        observer.disconnect();
    }

    observer = new MutationObserver(() => {
        updateFooterHeight();
    });

    observer.observe(footer.value.$el, {
        attributes: true,
        childList: true,
        subtree: true
    });
}

// 组件卸载时清理
onUnmounted(() => {
    window.removeEventListener('resize', updateFooterHeight);
    if (observer) {
        observer.disconnect();
        observer = null;
    }
});

// 监听路由变化
watch(() => route.path, () => {
    nextTick(() => {
        updateFooterHeight();
    });
});

// SEO 优化
useHead({
    title: computed(() => post.value?.meta?.custom_title || `${post.value?.title?.rendered || '文章'} `),
    meta: [
        {
            name: 'keywords',
            content: computed(() => post.value?.meta?.custom_keywords || `${post.value?.title?.rendered || ''}`),
            tagPriority: 'critical',
        },
        {
            name: 'description',
            content: computed(() => post.value?.meta?.custom_description || `${post.value?.title?.rendered || ''} `),
            tagPriority: 'critical',
        }
    ],
    script: [
        {
            src: '//static-1256600262.file.myqcloud.com/lib/wp/browser.min.js', // 替换为你的外部 JS 文件 URL
            async: false, // 可选，是否异步加载
            defer: false, // 可选，是否延迟加载
        }, {
            type: 'application/ld+json',
            tagPriority: 'critical', // 提高优先级
            children: JSON.stringify({
                "@context": "https://schema.org",
                "@type": "Article",
                "headline": post.value?.title?.rendered,
                "datePublished": post.value?.date,
                "dateModified": post.value?.date,
                "author": {
                    "@type": "Person",
                    "name": "万能小in"
                },
                "publisher": {
                    "@type": "Organization",
                    "name": "万能小in",

                    "description": post.value?.meta?.custom_description,
                    "mainEntityOfPage": {
                        "@type": "WebPage",
                        "@id": `${seo}manual/${route.params.category}/${postId.value}`
                    }
                }
            }, null, 2)
        }
    ]
});

// 获取上一篇下一篇文章
const fetchAdjacentPosts = async () => {
    if (!post.value?.id || !currentCategoryId.value) return;

    try {
        const currentDate = post.value?.date;
        if (!currentDate) return;

        const [prevPosts, nextPosts] = await Promise.all([
            // 获取上一篇
            $fetch<WPPost[]>(`${seo}wp-json/wp/v2/posts`, {
                params: {
                    categories: currentCategoryId.value,
                    before: currentDate,
                    per_page: 1,
                    order: 'desc',
                    _fields: 'id,title,date'
                }
            }),
            // 获取下一篇
            $fetch<WPPost[]>(`${seo}wp-json/wp/v2/posts`, {
                params: {
                    categories: currentCategoryId.value,
                    after: currentDate,
                    per_page: 1,
                    order: 'asc',
                    _fields: 'id,title,date'
                }
            })
        ]);

        prevPost.value = prevPosts?.[0] || null;
        nextPost.value = nextPosts?.[0] || null;
    } catch (error) {
        // console.error('获取相邻文章失败:', error);
        prevPost.value = null;
        nextPost.value = null;
    }
};

// 导航到指定文章
const navigateToPost = async (id: number | undefined) => {
    if (!id) return;

    try {
        await router.push({
            path: `/manual/${route.params.category}/${id}`,
            force: true
        });
        // handleScrollToTop();
    } catch (error) {
        console.error('导航失败:', error);
    }
};

// 监听路由参数变化
watch(() => route.params.id, async (newId, oldId) => {
    if (newId === oldId) return;

    try {
        // 先滚动到顶部
        handleScrollToTop();

        // 获取相邻文章
        if (post.value?.id) {
            await fetchAdjacentPosts();
        }
    } catch (error) {
        console.error('路由参数变化处理失败:', error);
    }
}, { immediate: true });

const processedContent = computed(() => {
    if (!post.value?.content?.rendered) return '';

    let content = post.value.content.rendered;

    // const regex = /href="([^"]*?)comcn-0([^"]*?)"/g;

    // content = content.replace(regex, (match, prefix, suffix) => {
    //     const newHref = `href="${prefix}comcn-${postId.value}${suffix}"`;
    //     return newHref;
    // });

    // // 如果上面的正则没有匹配到，尝试另一种格式
    // if (content === post.value.content.rendered) {
    //     const alternativeRegex = /href=(['"'])(.*?comcn-0.*?)\1/g;
    //     content = content.replace(alternativeRegex, (match, quote, url) => {
    //         const newUrl = url.replace('comcn-0', `comcn-${postId.value}`);
    //         return `href=${quote}${newUrl}${quote}`;
    //     });
    // }

    // // 添加处理AI相关文字的逻辑，为其添加链接
    // // 定义需要添加链接的文字和对应的URL地址
    // // const keywordUrlMappings = [
    // //     { keyword: "市场营销", url: "https://xiaoin.com.cn/create/paper" },
    // //     { keyword: "AI论文", url: "https://xiaoin.com.cn/create/paper" },
    // //     { keyword: "论文AI", url: "https://xiaoin.com.cn/create/paper" },
    // //     { keyword: "AI写论文", url: "https://xiaoin.com.cn/create/paper" },
    // //     { keyword: "AI生成论文", url: "https://xiaoin.com.cn/create/paper" }
    // //     // 可以根据需要继续添加更多映射
    // // ];

    // // // 依次处理每个关键词
    // // keywordUrlMappings.forEach(mapping => {
    // //     // 使用正则表达式查找所有指定文本，但排除已经在<a>标签内的文本
    // //     // 使用负向前瞻和负向后顾确保关键词不在链接内
    // //     const regex = new RegExp(`(?<!<a[^>]*>)(?<!<a[^>]*>[^<]*)${mapping.keyword}(?![^<]*</a>)`, 'g');
    // //     content = content.replace(regex, `<a href="${mapping.url}" target="_blank" class="text-blue-600 hover:underline">${mapping.keyword}</a>`);
    // // });

    // 处理所有链接：
    // 1. 删除URL中的?channel=及其后面的所有查询参数
    // 2. 删除URL中的comcn-0部分（如果有）
    const linkRegex = /href=(['"])(https?:\/\/[^'"]*?)((?:\?channel=.*)|(?:comcn-0[^'"]*))(['"])/g;
    content = content.replace(linkRegex, (match, openQuote, baseUrl, queryPart, closeQuote) => {
        return `href=${openQuote}${baseUrl}${closeQuote}`;
    });

    return content;
});

// 方案3：混合匹配法 - 按优先级查找标题元素

// 1. ID属性直接匹配
const findHeadingById = (targetId: string): HTMLElement | null => {
    console.log("尝试ID匹配:", targetId);

    // 直接通过ID查找
    const element = document.getElementById(targetId);
    if (element && ['H1', 'H2', 'H3', 'H4', 'H5', 'H6'].includes(element.tagName)) {
        console.log("找到ID匹配的标题元素:", element);
        return element;
    }

    // 尝试查找带有相同ID的标题元素
    const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
    for (const heading of headings) {
        if (heading.id === targetId) {
            console.log("找到ID匹配的标题:", heading);
            return heading as HTMLElement;
        }
    }

    console.log("ID匹配失败");
    return null;
};

// 2. 增强的文本标准化函数
const normalizeText = (text: string): string => {
    return text
        .trim()
        .replace(/\s+/g, ' ') // 将多个空格替换为单个空格
        .replace(/[""'']/g, '"') // 统一引号格式
        // .replace(/[^\w\s\u4e00-\u9fff]/g, '') // 移除特殊字符，保留中文、英文、数字
        .toLowerCase();
};

// 3. 增强文本匹配
const findHeadingByEnhancedText = (targetText: string): HTMLElement | null => {
    const normalizedTarget = normalizeText(targetText);
    const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
    for (const heading of headings) {
        const headingText = normalizeText(heading.textContent || '');
        // 完全匹配
        if (headingText === normalizedTarget) {
            return heading as HTMLElement;
        }
    }
    return null;
};

// 4. 模糊匹配（关键词匹配）
const findHeadingByFuzzyMatch = (targetText: string): HTMLElement | null => {
    console.log("尝试模糊匹配:", targetText);

    const normalizedTarget = normalizeText(targetText);
    const targetWords = normalizedTarget.split(' ').filter(word => word.length > 1);

    if (targetWords.length === 0) {
        console.log("模糊匹配失败：无有效关键词");
        return null;
    }

    const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
    let bestMatch: HTMLElement | null = null;
    let bestScore = 0;

    for (const heading of headings) {
        const headingText = normalizeText(heading.textContent || '');
        const headingWords = headingText.split(' ');

        // 计算匹配分数
        let score = 0;
        for (const targetWord of targetWords) {
            for (const headingWord of headingWords) {
                if (headingWord.includes(targetWord) || targetWord.includes(headingWord)) {
                    score++;
                }
            }
        }

        // 计算匹配率
        const matchRate = score / targetWords.length;
        if (matchRate > bestScore && matchRate >= 0.5) { // 至少50%的关键词匹配
            bestScore = matchRate;
            bestMatch = heading as HTMLElement;
        }
    }

    if (bestMatch) {
        console.log("找到模糊匹配的标题:", bestMatch, "匹配分数:", bestScore);
        return bestMatch;
    }

    console.log("模糊匹配失败");
    return null;
};

// 5. 主查找函数：混合匹配策略
const findHeadingElement = (targetText: string): HTMLElement | null => {
    console.log("开始混合匹配查找:", targetText);

    // 策略1：尝试ID匹配
    let element = findHeadingById(targetText);
    if (element) return element;

    // 策略2：尝试增强文本匹配
    element = findHeadingByEnhancedText(targetText);
    if (element) return element;

    // 策略3：尝试模糊匹配
    element = findHeadingByFuzzyMatch(targetText);
    if (element) return element;

    console.log("所有匹配策略都失败了");
    return null;
};

// 添加处理锚点点击的函数
const handleAnchorClick = (event: MouseEvent) => {
    const target = event.target as HTMLElement;
    const anchor = target.closest('a');
    if (anchor && anchor.getAttribute('href')?.startsWith('#')) {
        event.preventDefault();
        const id = anchor.getAttribute('href')?.substring(1);
        const element = document.getElementById(id || '');
        if (element) {
            element.scrollIntoView({ behavior: 'smooth' });
        }
    }
}

// 处理 AI 按钮点击
const handleAIButtonClick = () => {
    if (isMobileDevice()) {
        setTimeout(() => {
            handleShare();
            track('manual_aisameparagraph', postId.value.toString(), 'AI写同款按钮点击')
        }, 50);
    } else {
        // PC端跳转到创建页面
        console.log('PC端跳转到创建页面');
        navigateTo(`/create/${breadcrumbCategories.value[0]?.slug}`);
        track('manual_aisameparagraph', postId.value.toString(), 'AI写同款按钮点击')
    }
}

const handleShare = () => {
    console.log('handleShare跳转小程序');
    loadSeoFriendlySchemeLink(breadcrumbCategories.value[0]?.slug || '', `channel=comcn-${postId.value}`)
}
//11

// 处理文章内容中的链接点击
const handleContentLinks = () => {
    if (!isMobileDevice()) return;
    const content = document.querySelector('.prose');
    if (!content) return;

    const links = content.querySelectorAll('a');
    links.forEach(link => {
        const href = link.getAttribute('href');
        // 处理包含 comcn- 或 create 的链接
        if (href && (href.includes('create'))) {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                // 创建一个临时按钮并触发点击
                const tempButton = document.createElement('button');
                tempButton.style.display = 'none';
                document.body.appendChild(tempButton);
                tempButton.addEventListener('click', () => {
                    handleAIButtonClick();
                    document.body.removeChild(tempButton);
                });
                tempButton.click();
            });
        }
        // 处理以@开头的链接
        // else if (href && href.startsWith('@')) {
        //     link.addEventListener('click', (e) => {
        //         e.preventDefault();
        //         // 去掉@前缀后导航到目标URL
        //         const targetUrl = href.substring(1);
        //         window.open(targetUrl, '_blank');
        //     });
        // }
    });
}

// 监听文章内容变化，重新处理链接
// watch(() => post.value?.content?.rendered, () => {
//     nextTick(() => {
//         handleContentLinks();
//     });
// });
</script>