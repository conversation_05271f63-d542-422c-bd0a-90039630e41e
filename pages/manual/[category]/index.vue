<template>
    <div class="h-full bg-slate-50 flex flex-col">
        <!-- 固定头部区域 -->
        <div class="sticky top-0 z-50 bg-slate-50">
            <!-- 页面标题区 -->
            <div class="bg-white/80 rounded-xl px-4 py-2 border border-blue-700/20 m-4">
                <div class="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-4">
                    <!-- 标题部分 -->
                    <div class="flex items-center">
                        <div
                            class="w-10 h-10 bg-blue-700 rounded-xl flex items-center justify-center mr-4 shadow-inner">
                            <write theme="filled" size="24" fill="#FFFFFF" />
                        </div>
                        <div class="w-[280px]">
                            <h1
                                class="text-lg font-medium text-transparent bg-gradient-to-r from-blue-700 to-indigo-700 bg-clip-text">
                                {{ currentCategory?.name || '写作宝典' }}
                            </h1>
                            <div class="relative">
                                <ClientOnly>
                                    <VerticalMarquee :color="'#111827'" class="w-full text-sm text-gray-900"
                                        :texts="marqueeTexts" />
                                </ClientOnly>
                            </div>
                        </div>
                    </div>

                    <!-- 用户信息区域 -->
                    <div class="flex items-center space-x-4 order-2 lg:order-3">
                        <UserAvatar />
                    </div>
                </div>
            </div>

            <!-- 面包屑导航 -->
            <div class="container mx-auto px-4">
                <nav class="flex items-center space-x-2 text-sm text-gray-500 mb-4">
                    <NuxtLink to="/manual" class="hover:text-blue-600 transition-colors">写作宝典</NuxtLink>
                    <span class="text-gray-300">/</span>
                    <template v-if="parentCategory">
                        <NuxtLink :to="`/manual/${parentCategory?.slug}`" class="hover:text-blue-600 transition-colors">
                            {{ parentCategory?.name }}
                        </NuxtLink>
                        <span class="text-gray-300">/</span>
                    </template>
                    <span class="text-gray-900">{{ currentCategory?.name || '加载中...' }}</span>
                </nav>
            </div>

            <!-- 子栏目入口 -->
            <div class="container mx-auto px-4">
                <div v-if="subCategories && getValidSubCategories.length > 0" class="mb-4">
                    <div class="flex flex-wrap gap-2">
                        <a v-for="category in getValidSubCategories" :key="category.id"
                            :href="`#category-${category.id}`"
                            class="px-3 py-1.5 rounded-full text-sm text-gray-600 hover:bg-blue-50 hover:text-blue-600 border border-gray-200 hover:border-blue-200 transition-all duration-200 cursor-pointer"
                            @click.prevent="scrollToCategory(category.id)">
                            {{ category.name }}
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 可滚动的主要内容区域 -->
        <div class="flex-1 overflow-y-auto">
            <div class="min-h-full flex flex-col">
                <!-- 主要内容区域 - 左右分栏 -->
                <div class="container mx-auto px-4 flex-1">
                    <div class="flex flex-col lg:flex-row gap-6">
                        <!-- 左侧内容区域 -->
                        <div class="lg:w-[64%] ">
                            <!-- 文章列表区域 -->
                            <template v-if="isLoading">
                                <div class="space-y-4">
                                    <div class="bg-white rounded-lg p-4 animate-pulse">
                                        <!-- 标题骨架屏 -->
                                        <div class="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
                                        <!-- 内容骨架屏 -->
                                        <div class="space-y-3">
                                            <div v-for="i in 3" :key="i" class="flex items-center justify-between">
                                                <div class="h-3 bg-gray-100 rounded w-1/2"></div>
                                                <div class="h-3 bg-gray-100 rounded w-20"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </template>
                            <template v-else>
                                <!-- 子栏目文章列表 -->
                                <div v-if="subCategories && getValidSubCategories.length > 0"
                                    class="space-y-3 pr-4 -mr-4">
                                    <div v-for="category in getValidSubCategories" :key="category.id"
                                        :id="`category-${category.id}`"
                                        class="bg-white rounded-lg shadow-sm border border-gray-100 hover:border-blue-200 hover:shadow transition-all duration-200">
                                        <!-- 卡片头部 -->
                                        <div class="px-4 py-3 border-b border-gray-100/80">
                                            <div class="flex items-center justify-between">
                                                <h3 class="text-base font-medium text-gray-800">{{ category.name }}</h3>
                                                <NuxtLink :to="`/manual/${category.slug}`"
                                                    class="text-sm text-blue-600 hover:text-blue-700 flex items-center">
                                                    更多
                                                    <div class="w-4 h-4 ml-0.5">
                                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                                                            fill="currentColor">
                                                            <path fill-rule="evenodd"
                                                                d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z"
                                                                clip-rule="evenodd" />
                                                        </svg>
                                                    </div>
                                                </NuxtLink>
                                            </div>
                                        </div>
                                        <!-- 文章列表 -->
                                        <div class="p-4">
                                            <div v-if="getCategoryPostsComputed(category.id)?.length" class="space-y-3">
                                                <NuxtLink v-for="post in getCategoryPostsComputed(category.id)"
                                                    :key="post.id" :to="`/manual/${category.slug}/${post.id}`"
                                                    class="block hover:bg-gray-50 rounded-lg px-3 py-2 transition-colors">
                                                    <div class="flex items-center justify-between">
                                                        <h4 class="text-sm text-gray-800" v-html="post.title.rendered">
                                                        </h4>
                                                        <div class="text-xs text-gray-400">
                                                            {{ new Date(post.date).toLocaleDateString() }}
                                                        </div>
                                                    </div>
                                                </NuxtLink>
                                            </div>
                                            <div v-else class="py-4 text-center text-sm text-gray-400">
                                                暂无文章
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 无子栏目时的文章列表 -->
                                <div v-else class="space-y-3">
                                    <div
                                        class="bg-white rounded-lg shadow-sm border border-gray-100 hover:border-blue-200 hover:shadow transition-all duration-200">
                                        <!-- 当前栏目标题 -->
                                        <div class="px-3 py-2 border-b border-gray-100/80">
                                            <div class="flex items-center justify-between">
                                                <h3 class="text-sm font-medium text-gray-800">{{ currentCategory?.name
                                                }}
                                                </h3>
                                            </div>
                                        </div>
                                        <!-- 文章列表 -->
                                        <div class="p-4">
                                            <div v-if="currentPosts?.length" class="space-y-3">
                                                <NuxtLink v-for="post in currentPosts" :key="post.id"
                                                    :to="`/manual/${categoryId}/${post.id}`"
                                                    class="block hover:bg-gray-50 rounded-lg px-3 py-2 transition-colors">
                                                    <div class="flex items-center justify-between">
                                                        <h4 class="text-sm text-gray-800" v-html="post.title.rendered">
                                                        </h4>
                                                        <div class="text-xs text-gray-400">
                                                            {{ new Date(post.date).toLocaleDateString() }}
                                                        </div>
                                                    </div>
                                                </NuxtLink>
                                            </div>
                                            <div v-else class="py-4 text-center text-sm text-gray-400">
                                                暂无文章
                                            </div>
                                        </div>
                                    </div>
                                    <!-- 分页 -->
                                    <div class="container mx-auto px-4 mb-8" v-if="currentPosts?.length">
                                        <Pagination v-model:current="currentPage" :total="total" :pageCount="15"
                                            @change="handlePageChange" />
                                    </div>
                                </div>
                            </template>
                        </div>

                        <!-- 右侧边栏 -->
                        <div class="lg:w-[34%]">
                            <div>
                                <RightSidebar @scrollToTop="handleScrollToTop"
                                    :parent-category-id="currentCategory?.id || 0" :show-all="true"
                                    :code="parentCategory?.slug || currentCategory?.slug" :category="parentCategory" />
                            </div>
                        </div>
                    </div>
                </div>

                <Footer class="mt-auto" />
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { definePageMeta, useAsyncData, useHead, useRoute, useRouter } from '#imports';
import VerticalMarquee from '@/components/Common/VerticalMarquee.vue';
import { Write } from '@icon-park/vue-next';
import { computed, nextTick, onMounted, ref, watch, watchEffect } from 'vue';
import Footer from '~/components/index/Footer.vue';
import RightSidebar from '~/components/Manual/RightSidebar.vue';
import { useTracking } from '~/composables/useTracking';
import { useUserStore } from '~/stores/user';

const { track } = useTracking();

// WordPress 数据接口定义
interface WPPost {
    id: number
    title: {
        rendered: string
    }
    excerpt: {
        rendered: string
    }
    date: string
    meta: {
        post_views_count: number
    }
}

interface WPCategory {
    id: number
    name: string
    description: string
    count: number
    parent: number
    slug: string
    meta?: {
        category_slug?: string
    }
}

// TDK 数据接口
interface TDKData {
    title?: string;
    description?: string;
    keywords?: string;
}

// 状态变量
const route = useRoute();
const router = useRouter();
const store = useUserStore()

const categoryId = computed(() => route.params.category as string)

// 状态变量
const currentPage = ref(1)
const total = ref(0)
const postsMap = ref(new Map<number, WPPost[]>())
const currentPosts = ref<WPPost[]>([])
const isLoading = ref(true)

// const updateUserRecord = () => {
//     if (categoryId.value) {
//         store.setChannel(`comcn-${categoryId.value}`)
//     }
// }

// 添加参数验证
onMounted(() => {
    if (!categoryId.value) {
        router.push('/404')
    }
    track('manual_category', currentCategory.value?.id.toString() || '', '写作宝典分类')
    // updateUserRecord()
})

// 监听 categoryId 变化
watch(categoryId, () => {
    // updateUserRecord()
})

const marqueeTexts = [
    '写作必备指南，精选优质范文',
    'AI原创，无版权风险',
    '海量应用模板，想写什么就写什么',
    '150 + 应用，立即体验写同款内容'
]


// 获取分类数据
const { data: categories } = await useAsyncData(
    `categories-${categoryId.value}`,
    async () => {
        try {
            // 首先获取当前分类信息
            const currentCategoryResponse = await $fetch<WPCategory[]>('https://www.xiaoin.com.cn/wp-json/wp/v2/categories', {
                params: {
                    slug: categoryId.value,
                    _fields: 'id,name,count,description,parent,meta,slug',
                }
            })
            if (!currentCategoryResponse || currentCategoryResponse.length === 0) {
                console.error('找不到当前分类:', categoryId.value)
                return []
            }

            const currentCat = currentCategoryResponse[0]

            // 用于存储所有需要的分类（当前分类、子分类和父分类）
            const allNeededCategories = [currentCat]

            // 获取子分类
            const childCategoriesResponse = await $fetch<WPCategory[]>('https://www.xiaoin.com.cn/wp-json/wp/v2/categories', {
                params: {
                    parent: currentCat.id,
                    _fields: 'id,name,count,description,parent,meta,slug',
                    per_page: 100,
                }
            })
            allNeededCategories.push(...childCategoriesResponse)

            // 如果当前分类有父类，获取父类信息
            if (currentCat.parent > 0) {
                try {
                    const parentCategoryResponse = await $fetch<WPCategory>(`https://www.xiaoin.com.cn/wp-json/wp/v2/categories/${currentCat.parent}`, {
                        params: {
                            _fields: 'id,name,count,description,parent,meta,slug',
                        }
                    })
                    allNeededCategories.push(parentCategoryResponse)
                } catch (error) {
                    console.error('获取父分类信息失败:', error)
                }
            }

            return allNeededCategories
        } catch (error) {
            console.error('获取分类信息失败:', error)
            return []
        }
    }
)


// 计算当前分类
const currentCategory = computed(() => {
    if (!categories.value || !Array.isArray(categories.value)) {
        return null
    }

    const category = categories.value.find(cat => {
        if (cat.slug === categoryId.value) {
            return cat;
        }
    })

    if (!category) {
        router.push('/404')
    }

    if (category) {
        total.value = category.count
        // 获取 TDK 数据
        fetchTDKData(category.id)
    }
    return category || null
})

// TDK 数据
const tdkData = ref<TDKData>({})
// 获取 TDK 数据的函数
const fetchTDKData = async (categoryId: number) => {
    try {
        const response = await $fetch<{ page_title: string, description: string, metakey: string, metas: string }>(`https://www.xiaoin.com.cn/wp-json/custom/v1/category-tdk/${categoryId}`)
        tdkData.value.title = response.page_title
        tdkData.value.keywords = response.metakey
        tdkData.value.description = response.description
    } catch (error) {
        console.error('获取 TDK 数据失败:', error)
    }
}

// 计算子分类
const subCategories = computed(() => {
    if (!currentCategory.value?.id || !categories.value || !Array.isArray(categories.value)) {
        return []
    }
    return categories.value
        .filter(cat => cat.parent === currentCategory.value!.id)
        .map(cat => ({
            ...cat,
            slug: cat.slug
        }))
})

// 获取有效的子分类
const getValidSubCategories = computed(() => {
    return subCategories.value.filter(cat => cat.count > 0)
})

// 根据分类ID获取文章
const getCategoryPostsComputed = (categoryId: number) => {
    return postsMap.value.get(categoryId) || []
}

// 加载当前分类文章（无子分类时）
const { data: currentCategoryPosts, pending: currentPostsLoading } = await useAsyncData(
    `posts-${categoryId.value}-${currentPage.value}`,
    async () => {
        if (!currentCategory.value?.id) return []

        try {
            return await $fetch<WPPost[]>('https://www.xiaoin.com.cn/wp-json/wp/v2/posts', {
                params: {
                    categories: currentCategory.value.id,
                    per_page: 15,
                    page: currentPage.value,
                    _fields: 'id,title,content,date,excerpt,meta,_links,_embedded',
                    _embed: 'wp:term'
                }
            })
        } catch (error) {
            console.error('获取当前分类文章失败:', error)
            return []
        }
    },
    {
        watch: [currentPage]
    }
)

// 加载子分类文章
const { data: subCategoriesPosts, pending: subPostsLoading } = await useAsyncData(
    `subcategory-posts-${categoryId.value}`,
    async () => {
        if (!subCategories.value || subCategories.value.length === 0) return new Map<number, WPPost[]>()

        const postsData = new Map<number, WPPost[]>()

        isLoading.value = true
        try {
            const fetchPromises = subCategories.value.map(async (category) => {
                const posts = await $fetch<WPPost[]>('https://www.xiaoin.com.cn/wp-json/wp/v2/posts', {
                    params: {
                        categories: category.id,
                        per_page: 10,
                        _fields: 'id,title,content,date,excerpt,meta,_links,_embedded',
                        _embed: 'wp:term'
                    }
                })
                postsData.set(category.id, posts)
            })

            await Promise.all(fetchPromises)
            return postsData
        } catch (error) {
            console.error('获取子分类文章失败:', error)
            return postsData
        }
    }
)

// 监听数据变化并更新视图
watchEffect(() => {
    isLoading.value = currentPostsLoading.value || subPostsLoading.value

    // 更新当前文章
    if (currentCategoryPosts.value && !subCategories.value?.length) {
        currentPosts.value = currentCategoryPosts.value
    }

    // 更新子分类文章
    if (subCategoriesPosts.value) {
        postsMap.value = subCategoriesPosts.value
    }
})

// 处理分页变化
const handlePageChange = (page: number) => {
    currentPage.value = page
}

// 滚动到指定分类
const scrollToCategory = (categoryId: number) => {
    nextTick(() => {
        const element = document.getElementById(`category-${categoryId}`)
        if (element) {
            element.scrollIntoView({ behavior: 'smooth', block: 'start' })
            // 补偿固定头部的高度
            setTimeout(() => {
                window.scrollBy({
                    top: -180,
                    behavior: 'smooth'
                })
            }, 100)
        }
    })
}

// 处理回到顶部
const handleScrollToTop = () => {
    if (process.client) {
        const scrollContainer = document.querySelector('.flex-1.overflow-y-auto')
        if (scrollContainer) {
            scrollContainer.scrollTo({
                top: 0,
                behavior: 'smooth'
            })
        }
    }
}



// 设置页面标题和元信息
useHead(() => ({
    title: tdkData.value.title + `-万能小in官网` || `${currentCategory.value?.name || '文章列表'}-万能小in官网`,
    meta: [

        {
            name: 'keywords',
            content: tdkData.value.keywords || currentCategory.value?.name || '写作宝典,万能小in',
            tagPriority: 'critical',
        },
        {
            name: 'description',
            content: tdkData.value.description || currentCategory.value?.description || '万能小in写作宝典文章列表',
            tagPriority: 'critical'
        }
    ]
}))
definePageMeta({
    validate: async (route) => {
        const { category } = route.params
        if (route.params.subcategory) {
            return false
        }
        return Boolean(category)
    }
})

// 计算当前分类的父类
const parentCategory = computed(() => {
    if (!currentCategory.value?.parent || !categories.value || !Array.isArray(categories.value)) {
        return null
    }
    return categories.value.find(cat => cat.id === currentCategory.value!.parent) || null
})
</script>