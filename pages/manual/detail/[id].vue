<template>
    <div class="flex flex-col min-h-screen bg-gray-50">
        <!-- 顶部标题区域 - 添加固定定位 -->
        <div class="fixed top-0 left-0 right-0 z-10 bg-white border-b border-gray-200 shadow-sm">
            <div class="p-6">
                <div class="max-w-4xl mx-auto">
                    <div class="flex items-center mb-4">
                        <button @click="router.back()" class="mr-4 p-2 hover:bg-gray-100 rounded-lg transition-colors">
                            <left-small theme="outline" size="24" fill="#666" />
                        </button>
                        <div v-if="loading" class="h-8 w-64 bg-gray-200 animate-pulse rounded"></div>
                        <h1 v-else class="text-2xl font-medium text-gray-800 truncate">{{ articleData.title || '加载中...'
                            }}</h1>
                    </div>
                    <div class="flex items-center text-sm text-gray-500 space-x-4">
                        <time v-if="!loading">{{ formatDate(articleData.createTime) }}</time>
                        <div v-else class="h-4 w-32 bg-gray-200 animate-pulse rounded"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 中间内容区域 - 添加上边距以避免被固定头部遮挡 -->
        <div class="flex-1 p-6 mt-[132px]">
            <div class="max-w-4xl mx-auto bg-white rounded-lg shadow-sm p-6">
                <div v-if="loading" class="space-y-4">
                    <div class="h-4 bg-gray-200 rounded w-3/4"></div>
                    <div class="h-4 bg-gray-200 rounded"></div>
                    <div class="h-4 bg-gray-200 rounded w-5/6"></div>
                </div>
                <div v-else-if="error" class="text-center py-8">
                    <div class="text-red-500 text-lg mb-4">{{ error }}</div>
                    <button @click="getArticleDetail"
                        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                        重试
                    </button>
                </div>
                <div v-else class="prose max-w-none" v-html="articleData.content"></div>
            </div>
        </div>

        <!-- 底部操作区域 -->
        <div class="sticky bottom-0 p-4 bg-white border-t border-gray-200 shadow-sm">
            <div class="max-w-4xl mx-auto flex justify-between space-x-4">
                <!-- 下载按钮 -->
                <a v-if="articleData.wordUrl" :href="articleData.wordUrl" target="_blank">
                    <button
                        class="px-4 py-2 text-sm text-gray-600 bg-white border border-gray-300 rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 font-medium transition-colors">
                        <download theme="outline" size="16" fill="currentColor"
                            class="mr-1 inline-block align-text-bottom" />
                        下载Word
                    </button>
                </a>

                <!-- AI 在线编辑按钮 -->
                <button v-if="articleData.wordUrl" @click="handleAIEdit"
                    class="px-4 py-2 text-sm text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 font-medium transition-colors">
                    <write theme="outline" size="16" fill="currentColor" class="mr-1 inline-block align-text-bottom" />
                    AI在线编辑
                </button>

                <!-- 右侧预留空间，保持布局平衡 -->
                <button v-if="articleData.category" @click="handleAIButtonClick"
                    class="px-4 py-2 text-sm text-gray-600 bg-white border border-gray-300 rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 font-medium transition-colors">
                    AI写同款
                </button>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { getDownloadInfo } from '@/api/seo'
import { uploadByUrl } from '@/api/upload'
import { Download, LeftSmall, Write } from '@icon-park/vue-next'
import { message } from 'ant-design-vue'
import { computed, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { createSubmission, getCreateSubmissionResult, paySubmission } from '~/api/create'
import { useApp } from '~/composables/useApp'
import { useTracking } from '~/composables/useTracking'
import { loadSeoFriendlySchemeLink, isMobileDevice as manualIsMobileDevice } from '~/utils/manual/manual'
import { StarloveUtil } from '~/utils/util'


const { track } = useTracking();

const app = useApp()
const router = useRouter()
const route = useRoute()

interface WPPost {
    id: number;
    title: {
        rendered: string;
    };
    content: {
        rendered: string;
    };
    date: string;
    categories: number[];
}

interface ArticleData {
    id: string
    title: string
    content: string
    createTime: string
    wordUrl?: string
    category?: WPCategory
}

interface WPCategory {
    id: number;
    description: string;
    name: string;
    parent?: number;
}

const articleData = ref<ArticleData>({
    id: '',
    title: '',
    content: '',
    createTime: ''
})

const loading = ref(true)
const error = ref<string | null>(null)

// 格式化日期
const formatDate = (date: string) => {
    if (!date) return ''
    return new Date(date).toLocaleDateString('zh-CN')
}

// 获取文章详情
const getArticleDetail = async () => {
    loading.value = true
    error.value = null

    try {
        const res = await getDownloadInfo(route.params.id as string);
        if (!res.ok) {
            error.value = '未知文件'
            message.error('获取文件信息失败')
            return
        }

        // 提取文件 ID
        const idMatch = res.data.fileUrl.match(/\/docx\/(\d+)\//);
        const fileId = idMatch ? idMatch[1] : '';

        if (!fileId) {
            error.value = '无效的文件ID'
            message.error('无效的文件ID')
            return
        }

        // 获取文章详情
        const postData = await $fetch<Omit<WPPost, 'meta'>>(`https://www.xiaoin.com.cn/wp-json/wp/v2/posts/${fileId}`, {
            params: {
                _fields: 'id,title,content,date,categories'
            }
        });

        // 获取分类信息
        if (postData.categories && postData.categories.length > 0) {
            const categoryData = await $fetch<WPCategory>(`https://www.xiaoin.com.cn/wp-json/wp/v2/categories/${postData.categories[0]}`, {
                params: {
                    _fields: 'id,name,description,parent'
                }
            });

            // 如果当前分类没有description且有父分类，获取父分类信息
            if (!categoryData.description && categoryData.parent) {
                const parentCategory = await $fetch<WPCategory>(`https://www.xiaoin.com.cn/wp-json/wp/v2/categories/${categoryData.parent}`, {
                    params: {
                        _fields: 'id,name,description'
                    }
                });

                // 使用父分类的description
                categoryData.description = parentCategory.description;
            }

            // 更新文章数据（包含分类）
            articleData.value = {
                id: postData.id.toString(),
                title: postData.title.rendered,
                content: postData.content.rendered,
                createTime: postData.date,
                wordUrl: res.data.fileUrl,
                category: categoryData
            };
        } else {
            // 更新文章数据（不含分类）
            articleData.value = {
                id: postData.id.toString(),
                title: postData.title.rendered,
                content: postData.content.rendered,
                createTime: postData.date,
                wordUrl: res.data.fileUrl
            };
        }
    } catch (error) {
        console.error('Failed to fetch article:', error);
        message.error('获取文章详情失败');
    } finally {
        loading.value = false
    }
}

onMounted(() => {
    getArticleDetail();
})

// 设置页面元数据
useHead({
    title: computed(() => `${articleData.value.title || '文章'} - 写作宝典 - 万能小in`),
    meta: [
        {
            name: 'description',
            content: computed(() => articleData.value.title || '写作宝典'),
            tagPriority: 'critical', // 提高优先级
        }
    ]
})

// 添加类型声明
declare global {
    interface Window {
        lottie: any
    }
}

// AI 在线编辑处理函数
const handleAIEdit = async () => {

    let loadingWindow: Window | null = null;
    try {
        // 创建加载窗口
        loadingWindow = window.open('', '_blank')
        if (!loadingWindow) {
            throw new Error('无法创建新窗口，请检查浏览器设置')
        }

        const doc = loadingWindow.document
        doc.open()
        doc.write('<!DOCTYPE html><html lang="zh-CN"><head><title>加载中...</title></head><body></body></html>')
        doc.close()

        // 添加样式
        const style = doc.createElement('style')
        style.textContent = `
            body {
                margin: 0;
                display: flex;
                align-items: center;
                justify-content: center;
                flex-direction: column;
                height: 100vh;
                background-color: #f5f5f5;
            }
            #animation {
                width: 200px;
                height: 200px;
            }
            h3, p {
                text-align: center;
                font-family: system-ui, -apple-system, sans-serif;
            }
        `
        doc.head.appendChild(style)

        // 创建动画容器
        const animationContainer = doc.createElement('div')
        animationContainer.id = 'animation'
        doc.body.appendChild(animationContainer)

        // 加载动画脚本
        await new Promise((resolve, reject) => {
            const script = doc.createElement('script')
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/lottie-web/5.12.0/lottie.min.js'
            script.onload = () => {
                loadingWindow?.lottie.loadAnimation({
                    container: doc.getElementById('animation'),
                    renderer: 'svg',
                    loop: true,
                    autoplay: true,
                    path: 'https://static-1256600262.cos.ap-shanghai.myqcloud.com/xiaoin-h5/file/animation.json'
                })
                resolve(null)
            }
            script.onerror = reject
            doc.head.appendChild(script)
        })

        const text = doc.createElement('h3')
        text.textContent = '加载中，请稍候...'
        doc.body.appendChild(text)

        // 1. 上传文件
        const update = await uploadByUrl({
            fileName: articleData.value.title,
            fileUrl: articleData.value.wordUrl
        })

        if (!update.success) {
            throw new Error(`${articleData.value.title}上传失败`)
        }

        // 2. 创建订单
        const params = {
            creatorCode: 'online_editing',
            formData: {
                topic: '',
                size: 'small',
                isUseLargeWorker: false,
                isPayFirst: false,
                params: {
                    language: 'cn',
                    upload: '手动上传',
                    topic: ''
                },
                attachments: [
                    {
                        fileId: update.data?.id
                    }
                ]
            }
        }

        const res = await createSubmission(params)
        if (!res.ok || !res.data) {
            throw new Error(res.message || '创建订单失败')
        }

        await sleep(1000)
        // 3. 支付订单
        const respay = await paySubmission({
            submissionId: res.data?.id,
            attachments: [{ fileId: res.data?.formData.attachments[0].fileId }]
        })

        if (!respay.ok || !respay.data) {
            throw new Error(respay.message || '支付失败')
        }

        // 4. 轮询查询结果
        let timeoutReached = false
        const timeoutId = setTimeout(() => {
            timeoutReached = true
            message.error('处理超时，请重试')
        }, 60000)

        const intervalId = setInterval(async () => {
            if (timeoutReached) {
                clearInterval(intervalId)
                return
            }

            try {
                const result = await getCreateSubmissionResult({ submissionId: res.data?.id })

                if (result.ok && result.data && result.data?.status === 'done') {
                    clearInterval(intervalId)
                    clearTimeout(timeoutId)

                    // 5. 打开编辑器
                    const aiEditorUrl = `${StarloveUtil.getAIEditorBaseUrl()}/?id=${result.data?.id}`
                    if (loadingWindow && !loadingWindow.closed) {
                        loadingWindow.location.href = aiEditorUrl
                    } else {
                        window.open(aiEditorUrl, '_blank')
                    }
                }
            } catch (err) {
                clearInterval(intervalId)
                clearTimeout(timeoutId)
                message.error('AI处理失败，请重试')
                if (loadingWindow && !loadingWindow.closed) {
                    const text = loadingWindow.document.querySelector('h3')
                    if (text) {
                        text.textContent = 'AI处理失败，请重试'
                        text.style.color = '#dc2626'
                    }
                }
            }
        }, 2000)

    } catch (err) {
        if (loadingWindow && !loadingWindow.closed) {
            const text = loadingWindow.document.querySelector('h3')
            if (text) {
                text.textContent = err instanceof Error ? err.message : 'AI处理失败，请重试'
                text.style.color = '#dc2626'
            }
        }
        message.error(err instanceof Error ? err.message : 'AI处理失败')
    }
}

// 修改 handleAIButtonClick 方法
const handleAIButtonClick = () => {
    if (manualIsMobileDevice()) {
        setTimeout(() => {
            handleShare();
            track('manual_aisameparagraph', articleData.value.id, 'AI写同款按钮点击')
        }, 50);
    } else {
        // PC端跳转到创建页面
        console.log('PC端跳转到创建页面');
        navigateTo(`/create/${articleData.value.category?.description}?channel=comcn-${articleData.value.id}`);
        track('manual_aisameparagraph', articleData.value.id, 'AI写同款按钮点击')
    }
}

const handleShare = () => {
    console.log('handleShare跳转小程序');
    loadSeoFriendlySchemeLink(articleData.value.category?.description || '', `channel=comcn-${articleData.value.id}`)
}

</script>

<style>
.prose {
    max-width: 100%;
    color: #374151;
}

.prose p {
    margin-bottom: 1em;
    line-height: 1.75;
}

/* 添加标题文本溢出处理 */
.truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
</style>