<template>
    <div class="file-preview-single" v-if="knowledgeDetail?.id">
        <FilePreview :url="getUrl" :suffix="fileSuffix" :original-url="knowledgeDetail.fileUrl"
            :title="knowledgeDetail.fileName" :page-num="pageNum" :meta="queryMeta" />
    </div>
</template>
<script setup lang="ts">
import { repositoryFileGetDetail } from '@/api/repositoryFile'
import FilePreview from '@/components/Library/FilePreview.vue'
import type { RepositoryFile } from '@/services/types/repositoryFile'
import { useUserStore } from '@/stores/user'
import { TOKENNAME } from '@/utils/constants'
import { storage } from '@/utils/local-storage'
import { getURLParameters } from '@/utils/utils'
import { message } from 'ant-design-vue'
import { computed, onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'

const knowledgeDetail = ref<RepositoryFile | null>(null)
const title = computed(() => {
    if (knowledgeDetail.value?.processData?.title) {
        return knowledgeDetail.value.processData.title
    }
    if (knowledgeDetail.value?.fileName) {
        return knowledgeDetail.value.fileName
    }
    return '无标题'
})
const getUrl = computed(() => {
    return knowledgeDetail.value?.parsedUrl || knowledgeDetail.value?.fileUrl
})
const fileSuffix = computed(() => {
    const url = knowledgeDetail.value?.parsedUrl || knowledgeDetail.value?.fileUrl
    if (!url) {
        return ''
    }
    // 去掉查询字符串
    const cleanUrl = url.split('?')[0]
    return cleanUrl.substring(cleanUrl.lastIndexOf('.') + 1).toLowerCase()
})
const router = useRouter()
const pageNum = computed(() => router.currentRoute.value.query.pageNum)
const queryMeta = computed(() => router.currentRoute.value.query.meta)
const knowledgeAssistantId = ref('')
const pdfPageTotals = ref()

const user = useUserStore()

const onloadPdf = (params: any) => {
    pdfPageTotals.value = params.pageTotals
}


const spaceId = computed(() => {

    return user.currentLoginInfo?.id || ''

})


const getKnowledgeAssistantDetail = async () => {
    const result = await repositoryFileGetDetail({ spaceId: spaceId.value, id: knowledgeAssistantId.value })

    if (!result.ok || !result.data) {
        message.error(result.message || '加载知识库详情错误')
        return
    }
    knowledgeDetail.value = result.data
}
const setupData = async () => {
    await getKnowledgeAssistantDetail()
}
onMounted(async () => {
    const data: any = getURLParameters(window.location.href)
    if (!data) {
        return
    }
    if (!data.fileId || !data.token) {
        message.error('参数错误')
        return
    }
    knowledgeAssistantId.value = data.fileId
    if (data.token) {
        storage.set(TOKENNAME, encodeURIComponent(data.token))
    }
    setTimeout(() => {
        setupData()
    }, 300)
})
definePageMeta({
    layout: 'empty'
})
useHead({
    meta: [
        {
            name: 'referrer',
            content: 'never'
        }
    ]
})
</script>

<style scoped>
.file-preview-single {
    height: 100vh;
}
</style>