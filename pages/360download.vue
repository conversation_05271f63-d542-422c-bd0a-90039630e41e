<template>
    <div class="h-full bg-slate-50 overflow-hidden flex flex-col">
        <!-- 页面标题区 -->
        <div class="bg-white/80 rounded-xl px-4 py-2 border border-blue-700/20 m-4">
            <div class="flex items-center justify-between gap-2">
                <!-- 标题部分 -->
                <div class="flex items-center">
                    <div
                        class="w-8 h-8 md:w-10 md:h-10 bg-blue-700 rounded-xl flex items-center justify-center mr-2 md:mr-4 shadow-inner">
                        <download theme="outline" size="20" md:size="24" fill="#FFFFFF" />
                    </div>
                    <div>
                        <h1
                            class="text-base md:text-lg font-medium text-transparent bg-gradient-to-r from-blue-700 to-indigo-700 bg-clip-text">
                            下载页面
                        </h1>
                        <p class="text-xs md:text-sm text-gray-500">我的AI外脑</p>
                    </div>
                </div>

                <!-- 用户信息区域 -->
                <div class="flex items-center">
                    <UserAvatar />
                </div>
            </div>
        </div>

        <div
            class="flex-1 overflow-auto p-4 bg-[url('https://static-1256600262.file.myqcloud.com/xiaoin-pc/images/seo/beijing.png')] bg-cover bg-center bg-no-repeat">
            <!-- 主要内容区域 -->
            <div class="text-center mt-8">
                <h1
                    class="text-[60px] md:text-[60px] text-[40px] font-bold text-[#2551B5] leading-[1.5] md:leading-[113px] tracking-[2px]">
                    万能小in</h1>
                <div
                    class="text-[30px] md:text-[45px] font-bold text-[#15264C] leading-[1.5] md:leading-[83px] tracking-[2px] mb-2">
                    基于个人知识库的AI生产力工具
                </div>
                <div
                    class="text-[20px] md:text-[30px] font-normal text-[#5A6170] leading-[1.5] md:leading-[53px] tracking-[2px]">
                    让每个人拥有自己的AI外脑</div>
            </div>

            <div class="flex justify-center gap-4 md:gap-8 my-8 md:my-12">
                <img src="https://static-1256600262.file.myqcloud.com/xiaoin-pc/images/seo/xdj.png" alt=""
                    class="w-[60px] md:w-[80px] h-[25px] md:h-[35px] object-contain">
                <img src="https://static-1256600262.file.myqcloud.com/xiaoin-pc/images/seo/cdz.png" alt=""
                    class="w-[60px] md:w-[80px] h-[25px] md:h-[35px] object-contain">
                <img src="https://static-1256600262.file.myqcloud.com/xiaoin-pc/images/seo/xdh.png" alt=""
                    class="w-[60px] md:w-[80px] h-[25px] md:h-[35px] object-contain">
            </div>

            <div class="flex justify-center items-center w-full mb-8 md:mb-12">
            </div>

            <!-- 下载选项区域 -->
            <div
                class="px-[40px] py-[30px] bg-white shadow-[0px_1px_20px_0px_rgba(199,_226,_254,_0.4)] rounded-[20px] mx-[25px] mb-[25px] text-center ">
                <div class="flex items-center pb-5">
                    <img src="//static-1256600262.file.myqcloud.com/xiaoin-h5/mouse/download-win.png"
                        class="w-[80px] h-[80px]">
                    <div class="ml-[15px]">
                        <div class="mb-[10px] font-bold text-[18px] text-[#333333] leading-[29px] text-left">Windows
                        </div>
                    </div>
                </div>
                <a target="_blank"
                    href="//static-1256600262.file.myqcloud.com/xiaoin-h5/dowload/desktop/xiaoin_1.0.13_for360.exe">
                    <button
                        class="text-sm w-full sm:w-auto px-8 py-2 bg-blue-700 text-white rounded-full hover:bg-blue-800 transition-colors">
                        直接下载 </button>
                </a>

            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { Download } from '@icon-park/vue-next';
import { onMounted } from 'vue';
import { useUserStore } from '~/stores/user';
const store = useUserStore()
// 在页面加载完成后设置 channel
const updateUserRecord = () => {
    store.setChannel('comcn-download')
    // store.setOpenRecordUrl(`${window.location.href}?channel=comcn-download`)
}

onMounted(() => {
    updateUserRecord()
})
</script>
