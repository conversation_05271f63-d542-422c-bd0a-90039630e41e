<template>
    <div class="container mx-auto p-6">
        <h1 class="text-2xl font-bold mb-6">Tiptap Markdown 编辑器</h1>

        <div class="bg-white rounded-lg shadow-md p-4">
            <!-- 编辑器工具栏 -->
            <div class="flex flex-wrap gap-2 p-2 mb-4 border-b border-gray-200">
                <button @click="editor?.chain().focus().toggleBold().run()"
                    :class="{ 'bg-blue-100': editor?.isActive('bold') }" class="p-2 rounded hover:bg-gray-100">
                    <span class="font-bold">B</span>
                </button>

                <button @click="editor?.chain().focus().toggleItalic().run()"
                    :class="{ 'bg-blue-100': editor?.isActive('italic') }" class="p-2 rounded hover:bg-gray-100">
                    <span class="italic">I</span>
                </button>

                <button @click="editor?.chain().focus().toggleUnderline().run()"
                    :class="{ 'bg-blue-100': editor?.isActive('underline') }" class="p-2 rounded hover:bg-gray-100">
                    <span class="underline">U</span>
                </button>

                <button @click="editor?.chain().focus().toggleHeading({ level: 1 }).run()"
                    :class="{ 'bg-blue-100': editor?.isActive('heading', { level: 1 }) }"
                    class="p-2 rounded hover:bg-gray-100">
                    H1
                </button>

                <button @click="editor?.chain().focus().toggleHeading({ level: 2 }).run()"
                    :class="{ 'bg-blue-100': editor?.isActive('heading', { level: 2 }) }"
                    class="p-2 rounded hover:bg-gray-100">
                    H2
                </button>

                <button @click="editor?.chain().focus().toggleBulletList().run()"
                    :class="{ 'bg-blue-100': editor?.isActive('bulletList') }" class="p-2 rounded hover:bg-gray-100">
                    • 列表
                </button>

                <button @click="editor?.chain().focus().toggleOrderedList().run()"
                    :class="{ 'bg-blue-100': editor?.isActive('orderedList') }" class="p-2 rounded hover:bg-gray-100">
                    1. 编号
                </button>

                <button @click="editor?.chain().focus().toggleBlockquote().run()"
                    :class="{ 'bg-blue-100': editor?.isActive('blockquote') }" class="p-2 rounded hover:bg-gray-100">
                    引用
                </button>

                <button @click="editor?.chain().focus().setHorizontalRule().run()"
                    class="p-2 rounded hover:bg-gray-100">
                    分隔线
                </button>

                <button @click="handleInsertImage" class="p-2 rounded hover:bg-gray-100">
                    图片
                </button>
            </div>

            <!-- Markdown编辑区域 -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- 可视化编辑区域 -->
                <div class="border rounded-lg p-4 min-h-[400px]">
                    <h3 class="text-sm font-medium text-gray-700 mb-2">可视化编辑器</h3>
                    <editor-content v-if="editor" :editor="editor" class="prose max-w-none h-[370px] overflow-y-auto" />
                </div>

                <!-- Markdown预览区域 -->
                <div class="border rounded-lg p-4 min-h-[400px] relative">
                    <h3 class="text-sm font-medium text-gray-700 mb-2">Markdown预览</h3>
                    <div class="absolute top-3 right-3">
                        <button @click="copyMarkdown" class="text-xs bg-gray-100 hover:bg-gray-200 p-1 rounded">
                            复制
                        </button>
                    </div>
                    <textarea v-model="markdownContent"
                        class="w-full h-[370px] font-mono text-sm p-2 bg-gray-50 border rounded resize-none"
                        @input="handleMarkdownInput" placeholder="Markdown内容将显示在这里..."></textarea>
                </div>
            </div>

            <!-- 编辑器控制区域 -->
            <div class="mt-2 mb-2">
                <div class="flex items-center">
                    <input v-model="promptInput" type="text" placeholder="输入问题，例如：1+1的答案是什么？"
                        class="flex-grow p-2 border rounded mr-2" />
                    <select v-model="enableStream" class="p-2 border rounded mr-2">
                        <option :value="true">流式输出</option>
                        <option :value="false">完整输出</option>
                    </select>
                </div>
            </div>

            <!-- 底部按钮 -->
            <div class="mt-4 flex justify-between">
                <div>
                    <input type="file" ref="fileInput" accept=".md" class="hidden" @change="importMarkdownFile" />
                    <button @click="triggerFileInput"
                        class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded mr-2">
                        导入Markdown
                    </button>
                    <button @click="exportMarkdownFile"
                        class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded">
                        导出Markdown
                    </button>
                </div>
                <div class="flex gap-2">
                    <button @click="handleGenerateContent"
                        class="bg-green-500 hover:bg-green-600 text-white font-medium py-2 px-4 rounded"
                        :disabled="isGenerating">
                        {{ isGenerating ? `生成中...` : '生成' }}
                    </button>
                    <button @click="handleClear"
                        class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded">
                        清空
                    </button>
                </div>
            </div>
        </div>

        <!-- 图片插入弹窗 -->
        <div v-if="showImageDialog" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white rounded-lg p-6 w-96">
                <h2 class="text-xl font-bold mb-4">插入图片</h2>
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-bold mb-2" for="imageUrl">
                        图片URL
                    </label>
                    <input v-model="imageUrl" id="imageUrl" type="text"
                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                        placeholder="请输入图片链接">
                </div>
                <div class="flex justify-end gap-2">
                    <button @click="showImageDialog = false"
                        class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded">
                        取消
                    </button>
                    <button @click="insertImage"
                        class="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded">
                        确定
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { fetchEventSource } from '@microsoft/fetch-event-source'
import Image from '@tiptap/extension-image'
import TextAlign from '@tiptap/extension-text-align'
import Underline from '@tiptap/extension-underline'
import StarterKit from '@tiptap/starter-kit'
import { EditorContent, useEditor } from '@tiptap/vue-3'
import { Markdown } from 'tiptap-markdown'
import { onBeforeUnmount, ref, watch } from 'vue'

const markdownContent = ref('')
const isMarkdownInputProgrammatic = ref(false)
const promptInput = ref('编写一篇完整的校友会策划方案。期望直接输出markdown格式。不要闲聊。不要谈论要求。注意markdown的开头和结尾不要有任何```标记之类的。小标题要加粗。段落正文不要有特殊标记。')
const enableStream = ref(true)
const isGenerating = ref(false)

// 编辑器实例
const editor = useEditor({
    extensions: [
        StarterKit,
        Image,
        Underline,
        TextAlign.configure({
            types: ['heading', 'paragraph'],
        }),
        Markdown.configure({
            html: true,                  // 允许HTML输入/输出
            tightLists: true,            // Markdown输出中<li>内无<p>
            bulletListMarker: '-',       // Markdown输出中<li>前缀
            linkify: true,               // 自动将URL转为链接
            breaks: true,                // Markdown输入中的换行符(\n)转为<br>
            transformPastedText: true,   // 允许粘贴Markdown文本到编辑器
            transformCopiedText: false,  // 复制的文本自动转为Markdown
        }),
    ],
    content: '',
    autofocus: true,
    editable: true,
    onUpdate: ({ editor }) => {
        // 只有在编辑器内容变化时更新markdown内容
        if (!isMarkdownInputProgrammatic.value) {
            updateMarkdownContent()
        }
    },
})

// 初始化时更新Markdown内容
watch(editor, () => {
    if (editor.value) {
        updateMarkdownContent()
    }
}, { immediate: true })

// 更新Markdown内容
const updateMarkdownContent = () => {
    if (editor.value) {
        markdownContent.value = editor.value.storage.markdown.getMarkdown()
    }
}

// 从Markdown更新编辑器内容
const handleMarkdownInput = () => {
    if (!editor.value) return

    isMarkdownInputProgrammatic.value = true

    // 使用markdown内容更新编辑器
    editor.value.commands.setContent(markdownContent.value, false, { preserveWhitespace: 'full' })

    // 重置标志
    setTimeout(() => {
        isMarkdownInputProgrammatic.value = false
    }, 10)
}

// 复制Markdown
const copyMarkdown = () => {
    navigator.clipboard.writeText(markdownContent.value)
    alert('Markdown内容已复制到剪贴板')
}

// 文件输入引用
const fileInput = ref<HTMLInputElement | null>(null)

// 触发文件选择
const triggerFileInput = () => {
    if (fileInput.value) {
        fileInput.value.click()
    }
}

// 导入Markdown文件
const importMarkdownFile = (event: Event) => {
    const input = event.target as HTMLInputElement
    const file = input.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e: ProgressEvent<FileReader>) => {
        const content = e.target?.result
        if (typeof content === 'string') {
            markdownContent.value = content
            handleMarkdownInput()
        }
    }
    reader.readAsText(file)

    // 重置文件输入，以便可以重新选择同一个文件
    input.value = ''
}

// 导出Markdown文件
const exportMarkdownFile = () => {
    const blob = new Blob([markdownContent.value], { type: 'text/markdown' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'document.md'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
}

// 图片插入相关
const showImageDialog = ref(false)
const imageUrl = ref('')

const handleInsertImage = () => {
    showImageDialog.value = true
    imageUrl.value = ''
}

const insertImage = () => {
    if (imageUrl.value) {
        editor.value?.chain().focus().setImage({ src: imageUrl.value }).run()
    }
    showImageDialog.value = false
}

// 生成内容相关
const handleGenerateContent = async () => {
    if (isGenerating.value || !editor.value || !promptInput.value.trim()) return

    isGenerating.value = true

    // 设置编辑器为只读状态
    editor.value.setEditable(false)

    try {
        // 准备请求数据
        const apiUrl = 'http://127.0.0.1:8000/api/ai/generate_content_sse/'

        // 创建段落用于放置生成内容
        editor.value.chain().focus().createParagraphNear().run()

        if (enableStream.value) {
            // 流式处理响应
            handleStreamResponse(apiUrl, promptInput.value)
        } else {
            // 非流式处理响应 - 对于本地API，直接使用流式接口
            handleStreamResponse(apiUrl, promptInput.value)
        }
    } catch (error) {
        console.error('生成内容出错:', error)
        alert('生成内容时出错，请稍后重试')

        // 恢复编辑器可编辑状态
        editor.value.setEditable(true)
        isGenerating.value = false
    }
}

// 处理流式响应
const handleStreamResponse = (apiUrl: string, prompt: string) => {
    // 构建URL及查询参数
    const url = new URL(apiUrl)
    url.searchParams.append('prompt', prompt)

    // 为了更好地处理Markdown格式，我们使用一个累积的方法
    let accumulatedContent = ''

    // 节流更新相关变量
    let lastUpdateTime = 0
    const updateThreshold = 300 // 更新间隔阈值(ms)
    let updatePending = false

    // 清理和修复Markdown内容
    const sanitizeMarkdownContent = (content: string) => {
        // 如果最后是列表项开始但没结束，添加临时内容避免解析错误
        if (/(?:^|\n)- $/.test(content)) {
            return content + "临时内容"
        }

        // 如果有未完成的列表项，修复它
        if (/(?:^|\n)- [^\n]*$/.test(content) && !content.endsWith('\n')) {
            return content + "\n"
        }

        // 检查未闭合的代码块
        const codeBlockMatches = content.match(/```([^`]*)$/g)
        if (codeBlockMatches && !content.endsWith('\n```')) {
            return content + "\n```"
        }

        // 检查未完成的标题
        if (/(?:^|\n)#{1,6} $/.test(content)) {
            return content + "临时标题"
        }

        return content
    }

    // 智能更新函数 - 带节流功能
    const updateEditorContent = () => {
        const now = Date.now()

        // 检查是否需要立即更新
        const shouldUpdateNow = accumulatedContent.endsWith('\n\n') || // 空行结束
            /(?:^|\n)#{1,6} [^\n]+$/.test(accumulatedContent) || // 标题结束
            /(?:^|\n)- [^\n]+$/.test(accumulatedContent) || // 列表项结束
            now - lastUpdateTime > updateThreshold // 时间阈值

        if (shouldUpdateNow && !updatePending) {
            updatePending = true

            // 使用requestAnimationFrame确保在浏览器渲染周期中平滑更新
            requestAnimationFrame(() => {
                if (editor.value) {
                    try {
                        // 验证和处理Markdown内容，修复潜在问题
                        const processedContent = sanitizeMarkdownContent(accumulatedContent)

                        // 将处理后的内容直接设置到markdownContent，触发预览更新
                        markdownContent.value = processedContent

                        // 每500ms才完整更新编辑器内容，减少编辑器视图的闪动
                        if (now - lastUpdateTime > 500) {
                            isMarkdownInputProgrammatic.value = true

                            try {
                                // 使用try-catch包裹内容设置操作
                                editor.value.commands.setContent(processedContent, false, {
                                    preserveWhitespace: 'full'
                                })
                            } catch (error) {
                                console.warn('编辑器内容设置失败，将尝试修复:', error)
                                // 出错后尝试使用更安全的方式设置内容
                                try {
                                    // 创建一个新的段落作为安全回退
                                    editor.value.commands.clearContent()
                                    editor.value.commands.setContent(`<p>${processedContent}</p>`, false)
                                } catch (fallbackError) {
                                    console.error('无法恢复编辑器内容:', fallbackError)
                                }
                            }

                            // 延迟重置标记，避免与handleMarkdownInput中的标记冲突
                            setTimeout(() => {
                                isMarkdownInputProgrammatic.value = false
                            }, 10)

                            lastUpdateTime = now
                        }

                        // 滚动编辑器和预览区域到底部
                        scrollToBottom()
                    } catch (error) {
                        console.error('更新编辑器内容时出错:', error)
                    }

                    updatePending = false
                }
            })
        }
    }

    // 滚动到底部函数
    const scrollToBottom = () => {
        // 确保DOM已更新
        setTimeout(() => {
            // 滚动编辑器区域到底部
            const editorElement = document.querySelector('.ProseMirror')
            if (editorElement) {
                editorElement.scrollTop = editorElement.scrollHeight
            }

            // 同时滚动Markdown预览区域到底部
            const previewElement = document.querySelector('textarea')
            if (previewElement) {
                previewElement.scrollTop = previewElement.scrollHeight
            }
        }, 10)
    }

    // 使用fetchEventSource处理流式响应
    fetchEventSource(url.toString(), {
        method: 'GET',
        headers: {
            'accept': 'application/json',
            'X-CSRFTOKEN': '90AgTm4n4CpDJIJkitLIk0WcCsmXT3tR'
        },

        // 收到消息的处理
        onmessage: (event) => {
            try {
                // 解析响应数据
                const data = JSON.parse(event.data)
                const content = data.content || ''

                if (content) {
                    try {
                        // 将新内容添加到累积内容
                        accumulatedContent += content

                        // 尝试智能更新编辑器内容
                        updateEditorContent()
                    } catch (error) {
                        console.warn('处理流内容出错:', error)
                        // 即使出错，也继续累积内容，但暂时不更新编辑器
                    }
                }
            } catch (e) {
                console.warn('解析流数据出错:', e)
            }
        },

        // 错误处理
        onerror: (err) => {
            console.error('处理流响应出错:', err)
            alert('处理流响应时出错，请稍后重试')

            try {
                // 最后一次更新确保内容完整，使用安全处理方式
                if (editor.value && accumulatedContent) {
                    const safeContent = sanitizeMarkdownContent(accumulatedContent)
                    markdownContent.value = safeContent
                    editor.value.commands.setContent(safeContent, false, {
                        preserveWhitespace: 'full'
                    })
                }
            } catch (error) {
                console.error('恢复编辑器内容失败:', error)
            }

            // 恢复编辑器可编辑状态
            if (editor.value) {
                editor.value.setEditable(true)
            }
            isGenerating.value = false

            // 中止连接，不进行重试
            throw err
        },

        // 连接关闭
        onclose: () => {
            // 最后一次更新，确保所有内容都显示出来
            if (editor.value && accumulatedContent) {
                try {
                    const safeContent = sanitizeMarkdownContent(accumulatedContent)
                    markdownContent.value = safeContent
                    editor.value.commands.setContent(safeContent, false, {
                        preserveWhitespace: 'full'
                    })
                } catch (error) {
                    console.error('最终内容更新失败:', error)
                }

                // 确保最后也滚动到底部
                scrollToBottom()
            }

            // 恢复编辑器可编辑状态
            if (editor.value) {
                editor.value.setEditable(true)
            }
            isGenerating.value = false
        }
    }).catch(error => {
        console.error('建立流连接出错:', error)
        alert('连接服务器时出错，请稍后重试')

        // 恢复编辑器可编辑状态
        if (editor.value) {
            editor.value.setEditable(true)
        }
        isGenerating.value = false
    })
}

// 清空编辑器内容
const handleClear = () => {
    editor.value?.commands.clearContent()
    markdownContent.value = ''
}

// 组件卸载前销毁编辑器实例
onBeforeUnmount(() => {
    editor.value?.destroy()
})
</script>

<style>
/* 自定义编辑器样式 */
.ProseMirror {
    outline: none;
    min-height: 370px;
    max-height: 370px;
    overflow-y: auto;
}

.ProseMirror p {
    margin: 1em 0;
}

.ProseMirror img {
    max-width: 100%;
    height: auto;
}

.ProseMirror blockquote {
    border-left: 3px solid #ddd;
    padding-left: 1rem;
    color: #666;
}

.ProseMirror hr {
    margin: 1rem 0;
    border: none;
    border-top: 2px solid #ddd;
}

.ProseMirror pre {
    background-color: #f5f5f5;
    padding: 0.75rem;
    border-radius: 0.25rem;
    font-family: monospace;
    overflow-x: auto;
}

.ProseMirror code {
    background-color: #f5f5f5;
    padding: 0.2rem 0.4rem;
    border-radius: 0.25rem;
    font-family: monospace;
}

.ProseMirror pre code {
    padding: 0;
    background-color: transparent;
}
</style>