<template>
    <div class="h-screen flex flex-col bg-[#F5F7FF]">
        <div class="px-1 sm:px-4 md:px-6 lg:px-[8%] pt-4 sm:pt-6 md:pt-8">
            <!-- 返回按钮 -->
            <button @click="router.back()"
                class="flex items-center text-sm sm:text-base text-[#333333] hover:text-[#2551B5]">
                <left theme="outline" :size="18" class="text-inherit sm:hidden" :fill="'currentColor'" />
                <left theme="outline" :size="22" class="text-inherit hidden sm:block" :fill="'currentColor'" />
                <span>学术搜索</span>
            </button>
            <!-- 顶部区域 -->
            <div
                class="w-full h-[80px] sm:h-[100px] md:h-[110px] lg:h-[120px] px-2 sm:px-6 md:px-8 lg:px-12 flex items-center justify-between">
                <!-- 标题：防止换行 -->
                <h1
                    class="text-[#2551B5] font-bold text-lg sm:text-xl md:text-2xl lg:text-[26px] whitespace-nowrap mr-3 sm:mr-4 md:mr-6">
                    学术搜索
                </h1>

                <!-- 搜索框区域 -->
                <div
                    class="flex-1 h-[50px] sm:h-[60px] md:h-[70px] lg:h-[75px] pl-3 sm:pl-4 md:pl-6 pr-2 sm:pr-3 flex items-center justify-between bg-white shadow-[0_1px_20px_0_#C7D6FE] rounded-xl border border-[#2551B5]">
                    <input v-model="searchQuery" placeholder="搜索专业学术文献，一键学习使用" maxlength="300"
                        class="flex-1 h-full text-sm sm:text-base text-[#999999] outline-none ring-0 bg-transparent focus:outline-none"
                        type="text" @keyup.enter="handleSearch" />
                    <button class="relative group" @click="handleSearch" :disabled="!hasContent">
                        <!-- 发光边框效果 -->
                        <div
                            class="absolute -inset-0.5 bg-gradient-to-r from-blue-500/30 to-indigo-600/30 rounded-lg blur-sm group-hover:blur-md transition-all duration-300">
                        </div>

                        <!-- 按钮主体 -->
                        <div class="relative bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 w-7 h-7 sm:w-8 sm:h-8 md:w-9 md:h-9 lg:w-10 lg:h-10 rounded-lg leading-none flex items-center justify-center transition-all duration-300"
                            :class="hasContent ? 'text-white' : 'text-gray-400 cursor-not-allowed'">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"
                                class="w-2.5 h-2.5 sm:w-3 sm:h-3 md:w-3.5 md:h-3.5 lg:w-4 lg:h-4 group-hover:scale-110 transition-transform duration-300 rotate-[-45deg]">
                                <path
                                    d="M3.478 2.404a.75.75 0 0 0-.926.941l2.432 7.905H13.5a.75.75 0 0 1 0 1.5H4.984l-2.432 7.905a.75.75 0 0 0 .926.94 60.519 60.519 0 0 0 18.445-8.986.75.75 0 0 0 0-1.218A60.517 60.517 0 0 0 3.478 2.404Z" />
                            </svg>
                        </div>
                    </button>
                </div>
            </div>
        </div>


        <div
            class="flex-1 sm:px-4 md:px-6 lg:px-[8%]  overflow-hidden flex flex-col space-y-6 sm:space-y-8 md:space-y-10">
            <!-- 中间区域 -->
            <ScholarSearchContent ref="searchContentRef" :search-query="searchQuery" />
        </div>
    </div>
</template>

<script setup lang="ts">
import { Left } from '@icon-park/vue-next';
import { computed, nextTick, onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

const searchQuery = ref('')
const router = useRouter()
const route = useRoute()
const searchContentRef = ref()

const hasContent = computed(() => searchQuery.value.trim().length > 0)

// 处理搜索
const handleSearch = () => {
    if (!hasContent.value) return
    // 直接调用子组件的搜索方法
    searchContentRef.value?.performSearch(searchQuery.value)
}

onMounted(() => {
    if (route.query.keyword) {
        searchQuery.value = route.query.keyword as string
        // 使用 nextTick 确保子组件已经挂载
        nextTick(() => {
            handleSearch()
        })
    }
})
</script>
