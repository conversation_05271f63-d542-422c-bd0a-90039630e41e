<template>
    <div
        class="h-screen bg-[#F5F7FF] overflow-hidden flex flex-col px-4 sm:px-8 md:px-12 lg:px-16 xl:px-[20%] pt-8 sm:pt-12 md:pt-16 lg:pt-[10%] space-y-6 sm:space-y-8 md:space-y-10">
        <h1 class="text-xl sm:text-2xl md:text-3xl text-[#2551B5] font-bold text-center">小in专业学术搜索</h1>
        <div
            class="w-full h-[60px] sm:h-[65px] md:h-[70px] lg:h-[75px] p-4 sm:p-5 md:p-6 flex items-center justify-between bg-white shadow-[0_1px_20px_0_#C7D6FE] rounded-xl border border-[#2551B5]">
            <input v-model="searchQuery" placeholder="搜索专业学术文献，一键学习使用" maxlength="300"
                class="flex-1 w-full h-full text-sm sm:text-base text-[#999999] outline-none ring-0 focus:outline-none focus:ring-0 focus:border-transparent"
                type="text" @keyup.enter="handleSearch" />
            <button class="relative group" @click="handleSearch">
                <!-- 发光边框效果 -->
                <div
                    class="absolute -inset-0.5 bg-gradient-to-r from-blue-500/30 to-indigo-600/30 rounded-lg blur-sm group-hover:blur-md transition-all duration-300">
                </div>

                <!-- 按钮主体 -->
                <div class="relative bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 w-8 h-8 sm:w-9 sm:h-9 md:w-10 md:h-10 rounded-lg leading-none flex items-center justify-center transition-all duration-300"
                    :class="hasContent ? 'text-white' : 'disabled-chat-input text-gray-500 cursor-not-allowed'">
                    <!-- 旋转后的箭头图标（右上方向） -->
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"
                        class="w-3 h-3 sm:w-3.5 sm:h-3.5 md:w-4 md:h-4 group-hover:scale-110 transition-transform duration-300 rotate-[-45deg]">
                        <path
                            d="M3.478 2.404a.75.75 0 0 0-.926.941l2.432 7.905H13.5a.75.75 0 0 1 0 1.5H4.984l-2.432 7.905a.75.75 0 0 0 .926.94 60.519 60.519 0 0 0 18.445-8.986.75.75 0 0 0 0-1.218A60.517 60.517 0 0 0 3.478 2.404Z" />
                    </svg>
                </div>
            </button>
        </div>
    </div>
</template>

<script setup lang="ts">

// 搜索查询内容
const searchQuery = ref('')

// 计算属性：判断是否有内容
const hasContent = computed(() => {
    return searchQuery.value.trim().length > 0
})

// 处理搜索点击事件
const handleSearch = () => {
    if (hasContent.value) {
        // 这里可以添加搜索逻辑
        // console.log('搜索内容:', searchQuery.value)
        // TODO: 实现搜索功能
        // 检测是否登陆
        const userStore = useUserStore()
        if (!userStore.isLogined) {
            userStore.openLoginModal()
            return
        }
        navigateTo(`/scholar/search?keyword=${searchQuery.value}`)
    }
}

</script>

<style lang="scss" scoped>
.disabled-chat-input {
    background: #f1f1f1;
}
</style>