<template>
    <client-only>
        <div class="pdf-preview" :style="`height: ${height}`">
            <div id="viewerContainer" @click="handleViewer">
                <div id="viewer" class="pdfViewer"></div>
            </div>
            <div id="loadingBar" v-if="pageTotals > 0">
                <template v-if="isError">
                    <div class="pdf-preview-error">当前浏览器不支持打开pdf，请用最新版chrome, 360打开</div>
                </template>
                <template v-else>
                    <!-- <a-progress :percent="percent" /> -->
                </template>
            </div>
            <template v-if="pageTotals > 0">
                <div class="pdf-preview-footer">
                    <!-- <div class="pdf-preview-footer-l" v-show="pageTotals < bujuMaxSize + 1">
                    <a-button type="link" @click="handleToggleExpanded" size="small">
                        <Iconfont name="buju" :iconName="`page-icon `" />
                    </a-button>
                </div> -->
                    <div class="pdf-preview-footer-m">
                        <a-pagination :current="pageCurrent" simple :page-size="pageSize" :total="pageTotals"
                            @change="onChange" :showQuickJumper="false" />
                    </div>
                    <div class="pdf-preview-footer-r">
                        <a-button type="link" @click="zoomOut" size="small">
                            <svg t="1734963075783" class="icon" viewBox="0 0 1024 1024" version="1.1"
                                xmlns="http://www.w3.org/2000/svg" p-id="4110"
                                xmlns:xlink="http://www.w3.org/1999/xlink" width="16" height="16">
                                <path
                                    d="M997.052632 296.421053v431.157894a269.473684 269.473684 0 0 1-269.473685 269.473685H296.421053a269.473684 269.473684 0 0 1-269.473685-269.473685v-431.157894a269.473684 269.473684 0 0 1 269.473685-269.473685h431.157894a269.473684 269.473684 0 0 1 269.473685 269.473685z m-909.473685 6.736842v417.68421a215.578947 215.578947 0 0 0 215.578948 215.578948h417.68421a215.578947 215.578947 0 0 0 215.578948-215.578948v-417.68421a215.578947 215.578947 0 0 0-215.578948-215.578948h-417.68421a215.578947 215.578947 0 0 0-215.578948 215.578948z m636.604632 178.445473a30.396632 30.396632 0 1 1 0 60.739369H299.816421a30.342737 30.342737 0 1 1 0-60.685474l424.367158-0.053895z"
                                    fill="#777777" p-id="4111"></path>
                            </svg>
                        </a-button>
                        <span class="pdf-scale">{{ Math.floor(pageScale * 100) }}%</span>
                        <a-button type="link" @click="zoomIn" size="small">
                            <svg t="1734963199538" class="icon" viewBox="0 0 1024 1024" version="1.1"
                                xmlns="http://www.w3.org/2000/svg" p-id="4323" width="16" height="16"
                                xmlns:xlink="http://www.w3.org/1999/xlink">
                                <path
                                    d="M997.052632 296.421053v431.157894a269.473684 269.473684 0 0 1-269.473685 269.473685H296.421053a269.473684 269.473684 0 0 1-269.473685-269.473685v-431.157894a269.473684 269.473684 0 0 1 269.473685-269.473685h431.157894a269.473684 269.473684 0 0 1 269.473685 269.473685z m-909.473685 6.736842v417.68421a215.578947 215.578947 0 0 0 215.578948 215.578948h417.68421a215.578947 215.578947 0 0 0 215.578948-215.578948v-417.68421a215.578947 215.578947 0 0 0-215.578948-215.578948h-417.68421a215.578947 215.578947 0 0 0-215.578948 215.578948zM512 269.473684c16.761263 0 30.342737 13.581474 30.342737 30.342737v181.786947h181.840842a30.396632 30.396632 0 1 1 0 60.739369H542.342737v181.840842a30.342737 30.342737 0 1 1-60.739369 0v-181.840842H299.762526a30.342737 30.342737 0 1 1 0-60.685474l181.786948-0.053895V299.816421C481.603368 283.055158 495.238737 269.473684 512 269.473684z"
                                    fill="#777777" p-id="4324"></path>
                            </svg>
                        </a-button>
                    </div>
                </div>

                <div :class="`thumbs ${isVisibleSilder ? 'thumbs-show' : ''}`">
                    <div class="thumbs-mask"></div>
                    <div class="thumbs-list">
                        <div v-for="thumb in thumbList"
                            :class="`thumbs-item ${thumb.page === pageCurrent ? 'thumbs-selected' : ''}`"
                            :key="thumb.page" :id="`thumb-page${thumb.page}`"
                            @click="queueRenderPage(thumb.page, true)">
                            <img :src="thumb.img" alt="" />
                        </div>
                    </div>
                </div>
            </template>
        </div>
    </client-only>
</template>
<script lang="ts" setup>
import { scrollIntoView } from '@/components/Library/pdf/PDFThumbnailViewer.js'
import { CMAP_URL, loadPdfJs, workerSrc } from '@/utils/loadResources'
import { message } from 'ant-design-vue'
import { nextTick, onMounted, onUnmounted, ref } from 'vue'
import { useRouter } from 'vue-router'

// 声明全局Window接口扩展
declare global {
    interface Window {
        pdfjsLib: any;
        pdfjsViewer: any;
        Konva: any;
    }
}

const router = useRouter()
const pdfUrl = decodeURIComponent(router.currentRoute.value.query.pdfUrl as string)
const _height = (router.currentRoute.value.query.height as string) || '100vh'
const height = ref(_height)
const emit = defineEmits(['onload'])
const pageTotals = ref(0)
const pageSize = ref(1)
const pageCurrent = ref(1)
const pageScale = ref(1)
const isVisibleSilder = ref(false)
const percent = ref(0)
const isLoading = ref(true)
const isError = ref(false)
const isPageChangeFromInitKonav = ref(false)

const DEFAULT_URL =
    pdfUrl || 'https://static-1256600262.file.myqcloud.com/lib/pdf/tracemonkey-pldi-09.pdf'

const CMAP_PACKED = true
const SEARCH_FOR = '' // 'JavaScript'
const ENABLE_XFA = true

let pdfDocument, pdfjsViewer, container, loadingTask, stage
let eventBus: any;
let pdfSinglePageViewer: any;

interface IThumb {
    page: number
    img: any
}
const thumbList = ref<IThumb[]>([])


const onChange = (page: number) => {
    if (page > 0 && page <= Math.ceil(pageTotals.value / pageSize.value)) {
        // 进行实际的页面数据获取操作
        queueRenderPage(page)
    } else {
        // 如果页码不合法，可以提示用户或者重置为合法值
        message.error('请输入有效的页码！')
    }
}
// const handleToggleExpanded = () => {
//     isVisibleSilder.value = !isVisibleSilder.value
// }
const queueRenderPage = async (num: number, isManual = false) => {
    pageCurrent.value = num
    pdfSinglePageViewer.currentPageNumber = num
    if (isVisibleSilder.value && !isManual) {
        scrollIntoView(document.getElementById(`thumb-page${num}`), {
            top: -10
        })
    }
}
// 超过这个值，不显示布局
// const bujuMaxSize = 50
// const handlePageUp = async () => {
//   const _pageCurrent = pageCurrent.value - 1
//   if (_pageCurrent < 1) {
//     return
//   }
//   queueRenderPage(_pageCurrent)
// }
// const handlePageDown = async () => {
//   const _pageCurrent = pageCurrent.value + 1
//   if (_pageCurrent > pageTotals.value) {
//     return
//   }
//   queueRenderPage(_pageCurrent)
// }
const zoomIn = () => {
    // 缩小PDF页面
    console.log(pageScale.value, 'newScale')

    const newScale = Math.min(pageScale.value + 0.1, 5)
    pageScale.value = newScale
    pdfSinglePageViewer.currentScaleValue = newScale
}
const zoomOut = () => {
    // 缩小PDF页面
    console.log(pageScale.value, 'newScale')

    const newScale = Math.max(pageScale.value - 0.1, 0.1)
    pageScale.value = newScale
    pdfSinglePageViewer.currentScaleValue = newScale
}

// const getThumbs = async (numPages) => {
//     for (let index = 1; index < numPages + 1; index++) {
//         const desiredWidth = 595

//         const page = await pdfDocument.getPage(index)

//         // const viewport1 = page.getViewport(pageScale.value)
//         // const scale = desiredWidth / viewport1.viewBox[2]
//         const viewport = page.getViewport({ scale: 1 })
//         const width = viewport.viewBox[2]
//         const height = viewport.viewBox[3]
//         const outputScale = window.devicePixelRatio || 1
//         const canvas = document.createElement('canvas')
//         const context = canvas.getContext('2d')

//         canvas.width = Math.floor(width * outputScale)
//         canvas.height = Math.floor(height * outputScale)
//         canvas.style.width = Math.floor(width) + 'px'
//         canvas.style.height = Math.floor(height) + 'px'
//         const transform = outputScale !== 1 ? [outputScale, 0, 0, outputScale, 0, 0] : null

//         // canvas.width = viewport.viewBox[2]
//         // canvas.height = viewport.viewBox[3]
//         const renderContext = {
//             canvasContext: context,
//             transform,
//             viewport: viewport
//         }
//         await page.render(renderContext).promise
//         const src = canvas.toDataURL() // 这里是个base64
//         thumbList.value.push({ page: index, img: src })
//         canvas.remove()
//     }
//     //   setTimeout(() => {
//     //     console.log([...thumbList.value])
//     //   }, 1000)
// }

//"bbox": [
// 218,
//                 1056,
//                 1056,
//                 1314
//             ]
// xmin,ymin,xmax,ymax
// 在更高的DPI，如150像素/英寸时，A4纸的像素尺寸是1240x1754像素。

const handleViewer = () => {
    var element = document.getElementById('konva-render-id')
    if (element) {
        element.remove()
    }
    clearHightLight()
}
const initKonav = async ({ bbox, page, sourceContent }: { bbox: any, page: number, sourceContent: string }) => {
    // onChange(page)
    // searchKeywords(sourceContent, page)
    await nextTick()
    console.log(bbox, page, sourceContent, 'initKonav')
    const Konva = window.Konva
    // let height = document.getElementById('viewerContainer')?.scrollHeight
    // let width = document.getElementById('viewerContainer')?.scrollWidth
    // if (!height) {
    //   height = window.innerHeight
    // }
    // if (!width) {
    //   width = window.innerWidth
    // }
    // 检查页面上是否存在 id 为 'konva-render-id' 的元素
    clearHightLight()
    const page_container = document.querySelector(`.pdf-preview .page[data-page-number="${page}"]`)

    // 获取容器的宽度和高度
    const containerWidth = page_container!.clientWidth
    const containerHeight = page_container!.clientHeight

    // 创建一个新的 div 元素，用于放置 Konva 的 stage
    const konvaContainer = document.createElement('div')
    konvaContainer.id = 'konva-render-id'
    konvaContainer.style.position = 'absolute'
    konvaContainer.style.top = '0'
    konvaContainer.style.left = '0'
    konvaContainer.style.width = '100%'
    konvaContainer.style.height = '100%'
    konvaContainer.style.pointerEvents = 'none' // 让点击事件穿透到下面的内容
    konvaContainer.addEventListener('click', function () {
        clearHightLight()
    })
    // 将新的 div 添加到原容器中
    page_container!.appendChild(konvaContainer)
    stage = new Konva.Stage({
        container: konvaContainer,
        width: containerWidth,
        height: containerHeight
    })
    // add canvas element
    const layer = new Konva.Layer()
    stage.add(layer)
    const rate = pageScale.value * 0.642
    // const baseHeight = 1754 * (page - 1) * pageScale.value * 0.62

    const newBbox = {
        x: bbox[0] * rate,
        y: bbox[1] * rate,
        width: (bbox[2] - bbox[0]) * rate,
        height: (bbox[3] - bbox[1]) * rate
    }

    console.log(newBbox)

    // create shape
    const box = new Konva.Rect({
        ...newBbox,
        fill: '#b0d1ec',
        // stroke: 'black',
        // strokeWidth: 4,
        draggable: false,
        opacity: 0.3
    })
    layer.add(box)
    // 渲染图层
    layer.draw()

    setTimeout(() => {
        scrollToHighlightIfNeeded(page_container!, bbox)
    }, 200);

    // stage.on('click', function () {
    //   isHightlight.value = false
    //   console.log('you clicked me!')
    //   layer.remove(box)

    //   stage.remove(layer)
    //   stage.destroy()
    // })
    if (pageCurrent.value != page) {
        isPageChangeFromInitKonav.value = true
        onChange(page)
    }
}
const clearHightLight = () => {
    const old_elements = document.querySelectorAll('[name="konva-render-name"]')
    old_elements.forEach(function (_element) {
        _element.remove()
    })
    let old_lement = document.getElementById('konva-render-id')

    // 如果元素存在，则移除它
    if (old_lement) {
        old_lement.remove()
    }
}
const renderHightLight = async (options: any) => {
    await nextTick()
    const { meta } = options
    const bboxs = meta.bboxs
    if (!bboxs || !bboxs.length) {
        return
    }
    const pages = meta.pages
    clearHightLight()
    bboxs.forEach((bbox: any, index: number) => {
        const page = pages[index]
        console.log(bbox, page, 'initKonav')
        const Konva = window.Konva

        const page_container = document.querySelector(`.pdf-preview .page[data-page-number="${page}"]`)

        // 获取容器的宽度和高度
        const containerWidth = page_container!.clientWidth
        const containerHeight = page_container!.clientHeight

        // 创建一个新的 div 元素，用于放置 Konva 的 stage
        const konvaContainer = document.createElement('div')
        konvaContainer.setAttribute('name', 'konva-render-name')
        konvaContainer.style.position = 'absolute'
        konvaContainer.style.top = '0'
        konvaContainer.style.left = '0'
        konvaContainer.style.width = '100%'
        konvaContainer.style.height = '100%'
        konvaContainer.style.pointerEvents = 'none' // 让点击事件穿透到下面的内容
        konvaContainer.addEventListener('click', function () {
            clearHightLight()
        })
        // 将新的 div 添加到原容器中
        page_container!.appendChild(konvaContainer)
        stage = new Konva.Stage({
            container: konvaContainer,
            width: containerWidth,
            height: containerHeight
        })
        // add canvas element
        const layer = new Konva.Layer()
        stage.add(layer)
        const rate = pageScale.value * 0.642
        // const baseHeight = 1754 * (page - 1) * pageScale.value * 0.62

        const newBbox = {
            x: bbox[0] * rate,
            y: bbox[1] * rate,
            width: (bbox[2] - bbox[0]) * rate,
            height: (bbox[3] - bbox[1]) * rate
        }
        // create shape
        const box = new Konva.Rect({
            ...newBbox,
            fill: '#b0d1ec',
            // stroke: 'black',
            // strokeWidth: 4,
            draggable: false,
            opacity: 0.3
        })
        layer.add(box)
        // 渲染图层
        layer.draw()

        if (index == 0 && pageCurrent.value != page) {
            onChange(page)
        }
    })
}

const scrollToHighlightIfNeeded = (page_container: Element, bbox: any) => {
    const viewerContainer = document.getElementById('viewerContainer')
    if (!viewerContainer) return

    const pageRect = page_container.getBoundingClientRect()
    const viewerRect = viewerContainer.getBoundingClientRect()

    const rate = pageScale.value * 0.642
    const highlightTop = pageRect.top + bbox[1] * rate
    const highlightBottom = highlightTop + (bbox[3] - bbox[1]) * rate

    const isVisible = highlightTop >= viewerRect.top && highlightBottom <= viewerRect.bottom

    if (!isVisible) {
        const scrollTop = viewerContainer.scrollTop + (highlightTop - viewerRect.top) - (viewerRect.height - (bbox[3] - bbox[1]) * rate) / 2
        viewerContainer.scrollTo({
            top: scrollTop,
            behavior: 'smooth'
        })
    }
}


const setupPdf = async () => {
    // https://cdn.jsdelivr.net/npm/core-js-bundle@3.37.1/minified.min.js
    try {
        const pdfjsLib = window.pdfjsLib
        pdfjsViewer = window.pdfjsViewer
        pdfjsLib.GlobalWorkerOptions.workerSrc = workerSrc

        // console.log('pdfjsLib', pdfjsLib)

        container = document.getElementById('viewerContainer')

        eventBus = new pdfjsViewer.EventBus()

        // (Optionally) enable hyperlinks within PDF files.
        const pdfLinkService = new pdfjsViewer.PDFLinkService({
            eventBus
        })

        // (Optionally) enable find controller.
        const pdfFindController = new pdfjsViewer.PDFFindController({
            eventBus,
            linkService: pdfLinkService
        })

        // (Optionally) enable scripting support.
        const pdfScriptingManager = new pdfjsViewer.PDFScriptingManager({
            eventBus
            // sandboxBundleSrc: SANDBOX_BUNDLE_SRC
        })
        const l10n = new pdfjsViewer.GenericL10n()
        pdfSinglePageViewer = new pdfjsViewer.PDFViewer({
            container,
            eventBus,
            linkService: pdfLinkService,
            l10n,
            findController: pdfFindController,
            scriptingManager: pdfScriptingManager
        })
        pdfLinkService.setViewer(pdfSinglePageViewer)
        const pdfHistory = new pdfjsViewer.PDFHistory({
            eventBus,
            pdfLinkService
        })
        pdfLinkService.setHistory(pdfHistory)

        pdfScriptingManager.setViewer(pdfSinglePageViewer)

        eventBus.on('pagesinit', function () {
            // We can use pdfSinglePageViewer now, e.g. let's change default scale.
            pdfSinglePageViewer.currentScaleValue = 'page-width'
            pageScale.value = pdfSinglePageViewer.currentScale
            emit('onload', { pageTotals: numPages })

            const pageNum = router.currentRoute.value.query.pageNum
            if (pageNum && !isNaN(Number(pageNum))) {
                onChange(Number(pageNum))
            }
            const meta = router.currentRoute.value.query.meta
            if (meta) {
                setTimeout(() => {
                    console.log(meta, 'props.meta')
                    const decodedString = decodeURIComponent(meta.toString())
                    try {
                        const parsedData = JSON.parse(decodedString)
                        console.log(parsedData, 'parsedData')
                        renderHightLight({ meta: parsedData })
                    } catch (error) {
                        console.error('Error parsing JSON:', error)
                    }
                }, 1000)
            }
            // initKonav()
            // We can try searching for things.
            if (SEARCH_FOR) {
                eventBus.dispatch('find', { type: '', query: SEARCH_FOR })
            }
        })
        eventBus.on(
            'pagechanging',
            function (evt: any) {
                const page = evt.pageNumber
                pageCurrent.value = page
                if (isVisibleSilder.value) {
                    scrollIntoView(document.getElementById(`thumb-page${page}`), {
                        top: -10
                    })
                }
                // 向父窗口发送页码变化消息
                if (!isPageChangeFromInitKonav.value) {
                    window.parent.postMessage({
                        type: 'pdf-preview-page-change',
                        value: page
                    }, '*');
                }
                // 重置标志
                isPageChangeFromInitKonav.value = false
            },
            true
        )
        // 设置DPI为150，这将使得1个canvas像素对应于150个实际设备像素
        // pdfjsLib.pdfBlob.canvasContext.scale = 1 / 150
        //     1 　　　　　"disableAutoFetch": true, //是否禁用自动获取，true为禁用自动获取，开启分页
        // 2         "disableFontFace": false,
        // 3         "disableRange": false, //是否禁用range获取文件，false表示支持分页请求头
        // 4         "disableStream": true, //分页关键，是否禁用流的形式加载

        // Loading document.
        loadingTask = pdfjsLib.getDocument({
            url: DEFAULT_URL,
            disableRange: false,
            rangeChunkSize: 65536 * 16,
            disableAutoFetch: true,
            disableStream: true,
            cMapUrl: CMAP_URL,
            cMapPacked: CMAP_PACKED,
            enableXfa: ENABLE_XFA
        })
        isLoading.value = true
        loadingTask.onProgress = function (progressData: any) {
            const _percent = Math.floor((progressData.loaded / progressData.total) * 100)
            percent.value = _percent
            if (_percent == 100) {
                setTimeout(() => {
                    isLoading.value = false
                }, 200)
            }
        }
        pdfDocument = await loadingTask.promise
        const numPages = await pdfDocument.numPages
        pageTotals.value = numPages
        console.log(numPages, 'numPages')
        // Document loaded, specifying document for the viewer and
        // the (optional) linkService.
        pdfSinglePageViewer.setDocument(pdfDocument)

        pdfLinkService.setDocument(pdfDocument, null)
        // if (numPages < bujuMaxSize + 1) {
        //     getThumbs(numPages)
        // }
    } catch (error) {
        isError.value = true
        console.log(error)
    }
}
// const searchKeywords = (keywords: string, pageIndex = 0) => {
//     if (!keywords) {
//         return
//     }
//     eventBus.dispatch('find', {
//         type: '',
//         query: keywords,
//         phraseSearch: true, // 是否开启精确匹配查找
//         caseSensitive: false, // 是否开启大小写敏感查找
//         entireWord: false, // 是否开启整词匹配查找
//         findPrevious: false, // 是否查找上一个匹配项（向上搜索）
//         highlightAll: true, // 是否高亮所有匹配项
//         pageIndex // 开始查找的页面索引（从0开始计数）
//     })
// }
function onMessage(e: any) {
    const { command, value } = e.data

    switch (command) {
        case 'changePageNum': {
            if (!value) return
            onChange(value.pageNum)
            break
        }
        case 'highLightTranslate': {
            if (!value) return
            initKonav(value)
            break
        }
        case 'renderHightLightQuestion': {
            if (!value) return
            renderHightLight(value)
            break
        }
    }
}
onMounted(() => {
    if (window.pdfjsLib) {
        setupPdf()
    } else {
        loadPdfJs().then(() => {
            setupPdf()
        })
    }
    window.addEventListener('message', onMessage)
})
onUnmounted(() => {
    window.removeEventListener('message', onMessage)
})
definePageMeta({
    layout: 'empty'
})
</script>
<style>
.taro-view-core {
    background-color: #fff !important;
}

.pdf-preview {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    height: 100%;
    width: 100%;
    position: relative;
    background-color: #fff;

    #viewerContainer {
        overflow: auto;
        flex: 1;
        position: absolute;
        width: 100%;
        height: calc(100% - 69px);
        background: rgb(212 212 215);
    }

    &-footer {
        position: absolute;
        left: 0;
        bottom: 0;
        padding: 0 15px;
        width: 100%;
        height: 69px;
        background: #fafafa;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 14px;
        color: #777777;

        &>div {
            flex: 1;
        }

        &-m,
        &-r,
        &-l {
            display: flex;
            align-items: center;
        }

        &-m {
            z-index: 1;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            justify-content: center;
            background: #fafafa;
        }

        &-r {
            justify-content: flex-end;
        }

        /* @media screen and (max-width: 768px) {

            &-r,
            &-l {
                display: none;
            }
        } */

        &-separator {
            padding: 0 15px;
        }

        &-num {
            min-width: 30px;
        }

        &-current {
            text-align: right;
        }

        &-totals {
            text-align: left;
        }

        .page-icon {
            margin-top: 4px;
        }

        .disabled-icon {
            color: #ccc;
        }

        .pdf-page-input {
            width: 41px;
            text-align: center;
        }
    }

    .pdf-scale {
        line-height: 23px;
        width: 40px;
        text-align: center;
    }

    .thumbs {
        position: absolute;
        left: 0;
        top: 0;
        height: calc(100% - 69px);
        width: 0;
        overflow: hidden;
        background-color: rgb(212 212 215 / 0.9);
        transition: all 0.3s ease-in-out;

        &-item {
            margin: 10px auto;
            width: 120px;
            cursor: pointer;
        }

        img {
            width: 100%;
        }

        &-list {
            position: absolute;
            top: 0;
            left: 0;
            overflow-y: auto;
            height: 100%;
            width: 100%;
        }

        &-selected {
            border: 7px solid rgb(0 0 0 / 0.2);
        }
    }

    .thumbs-show {
        width: 150px;
    }

    /* 设置滚动条的颜色 */
    ::-webkit-scrollbar-thumb {
        background: rgb(121 121 123);
        /* 灰色滚动条 */
    }

    #loadingBar {
        top: 50%;
        left: 50%;
        width: 80%;
        position: absolute;
        transform: translateX(-50%);
    }

    &-error {
        text-align: center;
        color: #777777;
        font-size: 14px;
    }

    #konvaContainer {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 99;
        width: 100%;
        height: 100%;
    }
}
</style>