<template>
    <div class="outline-body">
        <client-only>
            <outline-textarea-h5 :is-h5="true" ref="outlineTextareaRefs" :placeholder="outlineDescData"
                :outline-list="againEditOutlineList" @onBlur="onOutlineBlur"
                :max-level="getMaxLevelByCreatorRequireFieldInfoList(fieldOutline) || 1"></outline-textarea-h5>
        </client-only>
        <button
            class="w-full my-10 px-16 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white rounded-xl text-base font-medium relative group overflow-hidden"
            @click="handlePostMessage">确定</button>
    </div>
</template>

<script setup lang="ts">
import { getCreatorsDetail } from '@/api/appCategory'
import OutlineTextareaH5 from '@/components/Create/component/OutlineTextareaH5.vue'
import { ToastService } from '@/services/toast'
import type { CreatorsInfo } from '@/services/types/appMessage'
import { getMaxLevelByCreatorRequireFieldInfoList, getURLParameters } from '@/utils/utils'
import { computed, onMounted, ref } from 'vue'
const againEditOutlineList = ref([])
const pListValue = ref([])
const outlineTextareaRefs = ref()
const submissionCode = ref('paper')

const pptModeType = ref()

const isDdMiniApp = ref(false)

const creatorData = ref<CreatorsInfo | undefined>(undefined)

const onOutlineBlur = (list: any) => {
    if (list.length == 0) {
        return
    }
    pListValue.value = list
    return list
}

const fieldOutline = computed(() => {
    if (!creatorData.value) {
        return
    }
    const list = creatorData.value.details.filter((item) => item.fieldCode == 'outline')
    if (list.length == 0) {
        return
    }
    return list[0]
})

const outlineDescData = computed(() => {
    if (!creatorData.value) {
        return ''
    }
    // const data = creatorData.value.details.filter((item) => item.fieldCode == 'outline')
    if (!fieldOutline.value) {
        return ''
    }
    if (!fieldOutline.value.placeholder) {
        return ''
    }
    const placeholder = fieldOutline.value.placeholder //.split('；')
    return placeholder
})

const creatorDetails = computed(() => {
    if (!creatorData.value) {
        return []
    }
    console.log('creatorDetails ==>', creatorData.value)
    return creatorData.value.details as any
})

const handlePostMessage = () => {
    // 在这里处理消息并回传给小程序
    ToastService.loading()
    setTimeout(() => {
        const list = outlineTextareaRefs.value.formatOutlineTextContent()
        // console.log('handlePostMessage list ==>', list)
        if (!list || list.length == 0) {
            ToastService.warn('请输入大纲')
            return
        }
        try {
            if (isDdMiniApp.value) {
                dd.postMessage({
                    data: {
                        list: JSON.stringify(list),
                        pptModeType: pptModeType.value
                    }
                })
                ToastService.hideLoading()
                dd.navigateBack()
                return
            }
            // Taro.setStorageSync('outlineList', JSON.stringify(list))
            wx.miniProgram.postMessage({
                data: {
                    list: JSON.stringify(list),
                    pptModeType: pptModeType.value
                }
            })
            ToastService.hideLoading()
            wx.miniProgram.navigateBack({
                url: '/pages/pc/home/<USER>/submission/index?code=' + submissionCode.value
            })

        } catch (error: any) {
            ToastService.error(error?.message || '')
            ToastService.hideLoading()
        }

    }, 1000)
}


const loadCreatorsDetail = async () => {
    const res = await getCreatorsDetail({ code: submissionCode.value })
    if (!res.ok) {
        return
    }
    if (!res.data) {
        return
    }
    creatorData.value = res.data
}

onMounted(() => {
    const data: any = getURLParameters(window.location.href)
    if (!data || !data.submissionCode) {
        return
    }
    if (data.isdd) {
        isDdMiniApp.value = true
    }
    submissionCode.value = data.submissionCode
    // console.log('data.outlineList ==>', data.outlineList)
    try {
        againEditOutlineList.value = JSON.parse(decodeURIComponent(data.outlineList));
    } catch (error) {
        console.error('解析 outlineList 时出错:', error);
        againEditOutlineList.value = []; // 或者根据需要设置为其他默认值
    }
    if (data.pptModeType) {
        pptModeType.value = pptModeType
    }
    loadCreatorsDetail()
})

const scriptList = [
    {
        src: '//res.wx.qq.com/open/js/jweixin-1.6.2.js', // 替换为你的外部 JS 文件 URL
        async: false, // 可选，是否异步加载
        defer: false, // 可选，是否延迟加载
    },
    {
        src: 'https://appx/web-view.min.js', // 替换为你的外部 JS 文件 URL
        async: false, // 可选，是否异步加载
        defer: false, // 可选，是否延迟加载
    }
]
useHead({
    script: scriptList,
})
definePageMeta({
    layout: 'empty'
})
</script>

<style>
.outline-body {
    padding: 13px;
}

.outline-title-desc {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    font-size: 14px;
    color: #777777;
    line-height: 21px;
}

.confirm {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 10px 50px;
    text-align: center;
    background: linear-gradient(270deg, #42e5b5 0%, #249cff 100%);
    border-radius: 5px;
    border: none;
    font-size: 16px;

    font-weight: 400;
    color: #ffffff;
    line-height: 27px;
    margin-top: 15px;
}
</style>