<template>
    <div class="flex h-screen flex-1 w-full">
        <!-- 移动端菜单开关按钮 -->
        <button @click="toggleLeftMenu" class="md:hidden fixed left-4 top-20 z-20 p-2 bg-white rounded-lg shadow-md">
            <ApplicationMenu theme="outline" size="24" fill="#666" />
        </button>

        <!-- 左侧扩展面板 - 固定宽度 -->
        <div class="hidden md:block flex-shrink-0">
            <LeftPanel ref="leftPanelRef" />
        </div>

        <div class="flex-1 flex flex-col">
            <!-- 页面标题区 -->
            <div
                class="m-3 bg-white/80 backdrop-blur-sm rounded-xl px-3 sm:px-5 py-2 border border-blue-700/20 mb-3 md:mb-4">
                <div class="flex items-center">
                    <div
                        class="w-10 h-10 bg-blue-700 rounded-xl flex items-center justify-center mr-3 md:mr-4 shadow-inner">
                        <Edit theme="outline" size="24" md:size="32" fill="#FFFFFF" />
                    </div>
                    <div>
                        <h1 class="text-lg font-bold text-blue-700">
                            写作记录
                        </h1>
                        <p class="text-sm text-gray-500">管理您写作的所有内容</p>
                    </div>
                </div>
            </div>
            <CreationsList />
        </div>

    </div>
</template>

<script setup lang="ts">
import CreationsList from '@/components/Profile/CreationsList.vue';
import { ApplicationMenu, Edit } from '@icon-park/vue-next';
import LeftPanel from '~/components/Create/Paper/LeftPanel.vue';
const leftPanelRef = ref<InstanceType<typeof LeftPanel> | null>(null)
const toggleLeftMenu = () => {
    leftPanelRef.value?.toggleMenu()
}


definePageMeta({
    layout: 'default'
})
</script>

<style scoped></style>