<template>
  <div class="flex h-screen">

    <!-- 移动端菜单开关按钮 -->
    <button @click="toggleLeftMenu" class="md:hidden fixed left-4 top-20 z-20 p-2 bg-white rounded-lg shadow-md">
      <ApplicationMenu theme="outline" size="24" fill="#666" />
    </button>

    <!-- 左侧扩展面板 - 固定宽度 -->
    <div class="hidden md:block flex-shrink-0">
      <LeftPanel ref="leftPanelRef" :creatorData="creatorData" />
    </div>


    <div class="flex-1 flex flex-row" v-if="creatorData">
      <div class="md:w-3/4 w-full flex-1 flex flex-col">
        <AppCreateFormTopNav :creator="creatorData?.creator" class="flex-shrink-0" />

        <AppSubmissionForm v-if="!isSubmitFormLoaded" :currentCode="currentCode" :creatorData="creatorData"
          :isSubmitLoading="isSubmitLoading" @submitForm="onSubmitForm" />
      </div>
      <div class="hidden md:block md:w-1/4 overflow-y-auto">
        <AppDefaultSideBar :creatorData="creatorData" />
      </div>

    </div>
  </div>
</template>

<script setup>
import { UserService } from '@/services/user';
import { HTTP_STATUS } from '@/utils/constants';
import { getAIEditorBaseUrl } from '@/utils/utils';
import {
  ApplicationMenu
} from '@icon-park/vue-next';
import { message } from 'ant-design-vue';
import { ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { getCreatorsDetail } from '~/api/appCategory';
import { createSubmission, getCreateSubmissionResult, paySubmission } from '~/api/create';



import { useRecentAppsStore } from '~/stores/recentApps';
import { useUserStore } from '~/stores/user';

import { seoConfigDefault } from '@/utils/constants';
import AppCreateFormTopNav from '~/components/Create/Paper/AppCreateFormTopNav.vue';
import AppDefaultSideBar from '~/components/Create/Paper/AppDefaultSideBar.vue';
import AppSubmissionForm from '~/components/Create/Paper/AppSubmissionForm.vue';
import LeftPanel from '~/components/Create/Paper/LeftPanel.vue';
import { getPlatformNew } from '~/composables/useApp';

const route = useRoute()

const router = useRouter()
const currentCode = ref(route.params.id || '')

const user = useUserStore()
const creatorData = ref(undefined)

const leftPanelRef = ref(null)
const isSubmitFormLoaded = ref(false)
const isSubmitLoading = ref(false)

const recentAppsStore = useRecentAppsStore()


const toggleLeftMenu = () => {
  leftPanelRef.value?.toggleMenu()
}

const config = useRuntimeConfig()
const { data: asyncData } = await useAsyncData('fetchData', async () => {
  try {
    const controller = new AbortController()
    const timeout = setTimeout(() => controller.abort(), 8000) // 8秒超时

    const res = await $fetch(`${config.public.apiBase}/creator/getCreatorDetail?platform=${getPlatformNew()}&code=${currentCode.value}`)
    clearTimeout(timeout)
    return res
  } catch (error) {
    console.error('获取首页数据失败:', error)
    // 可以根据需要处理错误，比如上报或提示
    return { success: false, code: null, result: {}, message: '请求失败' }
  }

}, {
  server: true, // 是否在服务端运行
  lazy: false,  // 延迟加载 (适合在用户操作后加载)
  immediate: true, // 是否在组件加载时立即执行
  default: () => ({ success: false, code: null, result: {}, message: '' })
})

const jsonLdData = computed(() => {
  const creator = asyncData.value?.result?.creator || {}
  const seoJsonLD = creator.seoConfig?.jsonLD || {}

  return {
    '@context': 'https://schema.org',
    '@type': ['HowTo', 'WebPage'],
    name: creator.name || '',
    description: creator.description || '',
    url: seoJsonLD.url || '',
    step: seoJsonLD.step || [],
    creator: {
      '@type': 'Organization',
      name: '万能小in',
      url: 'https://xiaoin.com.cn',
      logo: {
        '@type': 'ImageObject',
        url: 'https://xiaoin.com.cn/logo.png',
      }
    },
    potentialAction: seoJsonLD.potentialAction || {}
  }
})


const title = computed(() => asyncData.value?.result?.creator?.seoConfig?.title || seoConfigDefault.title)
const description = computed(() => asyncData.value?.result?.creator?.seoConfig?.description || seoConfigDefault.description)
const keywords = computed(() => asyncData.value?.result?.creator?.seoConfig?.keywords || seoConfigDefault.keywords)

useHead(() => {
  const seoJsonLD = asyncData.value?.result?.creator?.seoConfig?.jsonLD || {}
  const isJsonLdEmpty = !seoJsonLD || Object.keys(seoJsonLD).length === 0

  const headObj = {
    title: title.value,
    meta: [
      { name: 'description', content: description.value, tagPriority: 'critical' },
      { name: 'keywords', content: keywords.value, tagPriority: 'critical' }
    ]
  }

  if (!isJsonLdEmpty) {
    headObj.script = [
      {
        type: 'application/ld+json',
        children: JSON.stringify(jsonLdData.value, null, 2),
        tagPriority: 'critical'
      }
    ]
  }

  return headObj
})



// 写作按钮点击处理函数
const onSubmitForm = async (params) => {
  try {

    if (params.creatorCode == 'online_editing') {
      isSubmitLoading.value = true
    }

    const res = await createSubmission(params)

    if (res.code == HTTP_STATUS.FAILED_SUBMIT_AGAIN) {
      message.error(res.message || '请勿重复提交')
      return
    }

    if (res?.code === HTTP_STATUS.MOBILE_NOT_BOUND) {
      user.setShowPhoneBoundModal({
        status: BindPhoneModal.SHOW_BINDING
      })
      return
    }

    if (!res.ok || !res.data) {
      message.error(res.message || '操作失败')
      return
    }

    if (!res.data.id || res.data.id == undefined || res.data.id == 'undefined') {
      message.error('写作失败，请重试')
      return
    }



    deleteSubmissionDraft(params.creatorCode)

    if (res.data.creatorCode == 'online_editing') {
      try {
        isSubmitLoading.value = true

        if (app.value?.isDesktop) {
          // 跳转到结果页面
          router.push({
            path: '/create/detail',
            query: {
              id: res.data.id,
              code: params.creatorCode
            }
          })

          const paymentResult = await handlePayment(res.data)
          if (!paymentResult.success) {
            message.error(paymentResult.message)
            isSubmitLoading.value = false
            return
          }
          isSubmitLoading.value = false
          return
        }

        // 支付处理
        const paymentResult = await handlePayment(res.data)
        if (!paymentResult.success) {
          message.error(paymentResult.message)
          isSubmitLoading.value = false
          return
        }

        // 等待PPT结果
        await loadPPTresult(res.data?.id)
      } catch (error) {
        console.error('在线编辑处理失败:', error)
        message.error(error.message || '订单提交失败，请重试')
        isSubmitLoading.value = false
        return
      }
    }

    // 跳转到结果页面
    router.push({
      path: '/create/detail',
      query: {
        id: res.data.id,
        code: params.creatorCode
      }
    })
  } catch (error) {
    message.error('操作失败')
    if (params.creatorCode === 'online_editing') {
      isSubmitLoading.value = false
    }
  }
}

// 在现有的 ref 导入后添加
// const writingLanguage = ref('zh')
// const articleLength = ref('medium') // 默认选中中篇
// const outlineType = ref('ai')
// const referenceType = ref('ai')


const loadCreatorsDetailAndReport = async () => {
  if (!currentCode.value) {
    return
  }
  const res = await getCreatorsDetail({ code: currentCode.value })
  // console.log('res creatorData.value = ', res)
  if (!res.ok) {
    message.error(res.message || '加载失败')
    return
  }
  if (!res.data) {
    return
  }
  creatorData.value = res.data

  // 获取当前时间，并以指定格式格式化
  const currentDateTime = new Date()
  const formattedDateTime = formatDateTimeString(currentDateTime)

  // console.log("creatorData.value.creator ==>", creatorData.value.creator)
  // 假设应用信息来自 props 或其他数据源
  recentAppsStore.addToRecentList({ ...creatorData.value.creator, avatar: creatorData.value.creator.icon })

  UserService.reportUseApp({
    ...creatorData.value.creator,
    type: 'creator',
    avatar: creatorData.value.creator.icon || '',
    lastTime: formattedDateTime
  })
  // console.log(' creatorData.value = ', creatorData.value)
}

onMounted(() => {

  // 此id为链接地址的code id。
  currentCode.value = route.params.id || ''

  loadCreatorsDetailAndReport()
})


// -------------- 处理AI改稿 -----------------

const app = useApp()
const loadPPTresult = async (submissionId) => {
  try {
    // 轮询检查结果
    let result = null
    let retryCount = 0
    const maxRetries = 30 // 最多轮询30次

    while (retryCount < maxRetries) {
      const res = await getCreateSubmissionResult({ submissionId })
      if (res.ok && res.data && res.data?.status == SubmissionStatus.done) {
        result = res.data
        break
      }

      if (!res.ok) {
        throw new Error(res.message || '写作失败，请重试')
      }

      // 等待2秒再次检查
      await new Promise(resolve => setTimeout(resolve, 2000))
      retryCount++
    }

    if (!result) {
      throw new Error('处理超时，请稍后在"我的创作"中查看结果')
    }

    // 准备导航到AI编辑器
    let aiEditorUrl = `${getAIEditorBaseUrl()}/?id=${result.id}`
    if (app.value?.isDesktop) {
      aiEditorUrl += `&t=${UserService.getToken()}`
    }

    // 直接导航到编辑器页面
    window.location.href = aiEditorUrl

  } catch (error) {
    message.error(error.message || '处理失败，请重试')
    console.error('在线编辑处理失败:', error)
    isSubmitLoading.value = false // 关闭按钮加载状态
  }
}

const handlePayment = async (submissionData) => {
  await sleep(1000)
  const paymentResponse = await paySubmission({
    submissionId: submissionData?.id,
    attachments: [{ fileId: submissionData?.formData.attachments[0].fileId }]
  })

  if (!paymentResponse.ok) {
    message.error(paymentResponse.message || '支付失败，请重试')
    return {
      success: false,
      message: paymentResponse.message || '支付失败，请重试'
    }
  }

  return {
    success: true,
    data: paymentResponse.data
  }
}
// -------------- 处理AI改稿 -----------------

</script>

<style scoped>
@keyframes slide {
  from {
    transform: translateY(0);
  }

  to {
    transform: translateY(-50%);
  }
}

.animate-slide {
  animation: slide 20s linear infinite;
}
</style>