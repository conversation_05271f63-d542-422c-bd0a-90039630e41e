<template>
    <div class="flex h-screen overflow-hidden">
        <!-- 移动端菜单开关按钮 -->
        <button @click="toggleLeftMenu" class="fixed z-20 p-2 bg-white rounded-lg shadow-md md:hidden left-4 top-20">
            <ApplicationMenu theme="outline" size="24" fill="#666" />
        </button>

        <!-- 左侧扩展面板 - 固定宽度 -->
        <div class="flex-shrink-0 hidden overflow-y-auto md:block">
            <LeftPanel ref="leftPanelRef" :creatorData="creatorData" />
        </div>
        <!-- 中间和右侧内容区域 -->
        <div class="flex flex-col flex-1 overflow-hidden">
            <!-- 中右两栏布局容器 -->
            <div class="flex flex-1 overflow-hidden" v-if="creatorData">
                <!-- 中间内容区域 - 论文写作表单 -->
                <div class="flex flex-col w-full overflow-y-auto border-r md:w-3/4 border-blue-100/50">
                    <!-- 顶部导航 -->
                    <AppCreateFormTopNav :creator="creatorData?.creator" />

                    <!-- 论文写作表单 -->
                    <AppSubmissionForm ref="appSubmissionFormRefs" v-if="!isClickCreateOutlinButton" currentCode="ppt"
                        :creatorData="creatorData" @createOutline="onCreateOutline" />
                    <div v-else>
                        <div class="flex flex-col items-start relative min-h-[600px]">

                            <div v-if="isPPTOutlineLoading"
                                class="absolute inset-0 z-50 flex items-start pt-10 bg-white/80 backdrop-blur-sm">
                                <div class="w-full text-center">
                                    <div
                                        class="flex items-center justify-center w-16 h-16 mx-auto mb-3 rounded-full bg-blue-50">
                                        <loading theme="outline" size="32" fill="#3b82f6" class="animate-spin" />
                                    </div>
                                    <h3 class="text-base font-medium text-gray-600">正在生成大纲...</h3>
                                </div>
                            </div>

                            <div class="w-full px-4 mt-2">
                                <OutlineExplain :code="'ppt'" :fieldItem="fieldItem"
                                    :maxLevel="getMaxLevelByCreatorRequireFieldInfoList(fieldItem) || 3"
                                    @playVideo="onPlayVideo">
                                </OutlineExplain>
                            </div>

                            <PPTOutline v-if="!isLoadPPTOutlineError" :outlineData="againEditOutlineList"
                                :creatorDetail="creatorData" @loadOutline="onCreateOutline"
                                @submit="onSelectTemplateSubmit">
                            </PPTOutline>
                            <div v-else class="w-full mt-10 text-center">
                                <button @click="handleReloadOutline"
                                    class="inline-flex items-center gap-2 px-6 py-2 text-base font-medium text-white bg-gradient-to-r from-blue-400 to-indigo-400 hover:from-blue-500 hover:to-indigo-500 rounded-xl">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" viewBox="0 0 20 20"
                                        fill="currentColor">
                                        <path fill-rule="evenodd"
                                            d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z"
                                            clip-rule="evenodd" />
                                    </svg>
                                    <span>大纲生成失败，点击重试</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 右侧面板 -->
                <div class="hidden overflow-y-auto md:block md:w-1/4">
                    <AppDefaultSideBar :creatorData="creatorData" />
                </div>
            </div>
        </div>

        <OutlineVideoTutorialModal v-model="showOutlineVideoModal" :maxLevel="3" code="ppt"></OutlineVideoTutorialModal>
    </div>
</template>

<script setup lang="ts">
import OutlineVideoTutorialModal from '@/components/Create/component/OutlineVideoTutorialModal.vue'
import { UserService } from '@/services/user'
import { seoConfigDefault } from '@/utils/constants'
import { ApplicationMenu, Loading } from '@icon-park/vue-next'
import { message } from 'ant-design-vue'
import { onBeforeUnmount, onMounted, onUnmounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { getCreatorsDetail } from '~/api/appCategory'
import { createSubmission, getCreateSubmissionResult, getWordToUrl, loadOutlineData } from '~/api/create'
import { uploadByUrl } from '~/api/upload'
import OutlineExplain from '~/components/Create/component/OutlineExplain.vue'
import AppCreateFormTopNav from '~/components/Create/Paper/AppCreateFormTopNav.vue'
import AppDefaultSideBar from '~/components/Create/Paper/AppDefaultSideBar.vue'
import AppSubmissionForm from '~/components/Create/Paper/AppSubmissionForm.vue'
import LeftPanel from '~/components/Create/Paper/LeftPanel.vue'
import PPTOutline from '~/components/Create/PPT/PPTOutline.vue'
import { useRecentAppsStore } from '~/stores/recentApps'

import { useUserStore } from '~/stores/user'
import { getFileExtension, getMaxLevelByCreatorRequireFieldInfoList, transformToUserOutline } from '~/utils/utils'

const router = useRouter()
const route = useRoute();
const currentCode = ref('ppt')
const appSubmissionFormRefs = ref()

const creatorData = ref<any>(undefined)

const leftPanelRef = ref()
const againEditOutlineList = ref()

const recentAppsStore = useRecentAppsStore()

const isPPTOutlineLoading = ref(false)
const user = useUserStore()

const isClickCreateOutlinButton = ref(false)
const isLoadPPTOutlineError = ref(false)

const needSubmitFormData = ref()
const showOutlineVideoModal = ref(false)

const toggleLeftMenu = () => {
    leftPanelRef.value?.toggleMenu()
}

const fieldItem = computed(() => {
    return creatorData.value.details.filter((item: any) => item.code === 'outline')
})

const onLoginSuccess = () => {
}

const onPlayVideo = () => {
    showOutlineVideoModal.value = true
}

const handleReloadOutline = () => {
    submitFormData()
}

// 从服务器端获取tdk逻辑
const config = useRuntimeConfig()
const { data: asyncData } = useAsyncData('fetchData', async () => {
    try {
        const controller = new AbortController()
        const timeout = setTimeout(() => controller.abort(), 8000) // 8秒超时

        const res = await $fetch(`${config.public.apiBase}/creator/getCreatorDetail?platform=${getPlatformNew()}&code=${currentCode.value}`)
        clearTimeout(timeout)
        return res
    } catch (error) {
        console.error('获取首页数据失败:', error)
        // 可以根据需要处理错误，比如上报或提示
        return { success: false, code: null, result: {}, message: '请求失败' }
    }
}, {
    server: true, // 是否在服务端运行
    lazy: false,  // 延迟加载 (适合在用户操作后加载)
    immediate: true, // 是否在组件加载时立即执行
    default: () => ({ success: false, code: null, result: {}, message: '' })
})


// 从服务端获取的数据中提取SEO信息
const jsonLdData = computed(() => {
    const creator = (asyncData.value as any)?.result?.creator || {}
    const seoJsonLD = creator.seoConfig?.jsonLD || {}

    return {
        '@context': 'https://schema.org',
        '@type': ['HowTo', 'WebPage'],
        name: creator.name || '',
        description: creator.description || '',
        url: seoJsonLD.url || '',
        step: seoJsonLD.step || [],
        creator: {
            '@type': 'Organization',
            name: '万能小in',
            url: 'https://xiaoin.com.cn',
            logo: {
                '@type': 'ImageObject',
                url: 'https://xiaoin.com.cn/logo.png',
            }
        },
        potentialAction: seoJsonLD.potentialAction || {}
    }
})

const title = computed(() => (asyncData.value as any)?.result?.creator?.seoConfig?.title || seoConfigDefault.title)
const description = computed(() => (asyncData.value as any)?.result?.creator?.seoConfig?.description || seoConfigDefault.description)
const keywords = computed(() => (asyncData.value as any)?.result?.creator?.seoConfig?.keywords || seoConfigDefault.keywords)

// 完全按照[id].vue的方式实现
useHead(() => {
    const seoJsonLD = (asyncData.value as any)?.result?.creator?.seoConfig?.jsonLD || {}
    const isJsonLdEmpty = !seoJsonLD || Object.keys(seoJsonLD).length === 0

    const headObj = {
        title: title.value,
        meta: [
            { name: 'description', content: description.value, tagPriority: 'critical' },
            { name: 'keywords', content: keywords.value, tagPriority: 'critical' }
        ],
        link: [
            {
                href: '//at.alicdn.com/t/c/font_4853281_u0u6jlfrt1g.css',
                rel: 'stylesheet',
            }
        ]
    } as any

    if (!isJsonLdEmpty) {
        headObj.script = [
            {
                type: 'application/ld+json',
                children: JSON.stringify(jsonLdData.value, null, 2),
                tagPriority: 'critical'
            }
        ]
    }

    return headObj
})

const getStorePPTOutlineList = () => {
    const data = storage.get(StarloveConstants.keyOflocalStorage.aMaximumOfThreePPTOutlineData)
    if (!data) {
        return []
    }
    return JSON.parse(data)
}

const onCreateOutline = async (data: any) => {

    if (!UserService.isLogined()) {
        user.openLoginModal()
        return
    }
    console.log('onCreateOutline data = ', data)
    if (data) {
        needSubmitFormData.value = data
    }
    await submitFormData()
}

const submitFormData = async () => {
    isPPTOutlineLoading.value = true
    isClickCreateOutlinButton.value = true
    isLoadPPTOutlineError.value = false
    await onLoadOutline()
}

const onLoadOutline = async () => {
    try {
        const params: any = {
            keywords: needSubmitFormData.value.formData.params.ask,
            topic: needSubmitFormData.value.formData.topic,
            size: needSubmitFormData.value.formData.size,
            language: needSubmitFormData.value.formData.params.language,
            creatorName: creatorData.value?.creator?.name || 'PPT',
            category: CreateOutlineCategory.pptOutline
        }
        if (needSubmitFormData.value.formData.attachments?.length > 0) {
            params['fileIds'] = needSubmitFormData.value.formData.attachments.map((item: any) => item.fileId)
        }
        // console.log('params 123= ', params)
        const res = await loadOutlineData(params)
        if (!res.ok || !res.data) {
            isLoadPPTOutlineError.value = true
            message.error(res.message || '大纲获取失败，请重试')
            return
        }

        againEditOutlineList.value = res.data

        if (!getStorePPTOutlineList() || getStorePPTOutlineList().length == 0) {
            storage.set(
                StarloveConstants.keyOflocalStorage.aMaximumOfThreePPTOutlineData,
                JSON.stringify([res.data])
            )
        } else {
            storage.set(
                StarloveConstants.keyOflocalStorage.aMaximumOfThreePPTOutlineData,
                JSON.stringify([...getStorePPTOutlineList(), res.data])
            )
        }

    } catch (error) {
        // console.error(error)
        message.error('生成大纲失败')
        isLoadPPTOutlineError.value = true
    } finally {
        isPPTOutlineLoading.value = false
    }
}

const onSelectTemplateSubmit = async (templateAndOutlineTreeInfo: any) => {
    // console.log('onSelectTemplateSubmit templateAndOutlineTreeInfo = ', templateAndOutlineTreeInfo)
    try {
        if (
            !templateAndOutlineTreeInfo.topic &&
            templateAndOutlineTreeInfo.topic.trim().length == 0
        ) {
            message.warning('请输入标题')
            return
        }

        if (
            !templateAndOutlineTreeInfo.outlineTree ||
            templateAndOutlineTreeInfo.outlineTree.length == 0
        ) {
            message.warning('请输入写作大纲')
            return
        }

        if (!templateAndOutlineTreeInfo.templateFileName || !templateAndOutlineTreeInfo.templateName) {
            message.warning('请选择ppt模版')
            return
        }
        if (Array.isArray(templateAndOutlineTreeInfo?.outlineTree)) {
            for (let index = 0; index < templateAndOutlineTreeInfo.outlineTree.length; index++) {
                let element = templateAndOutlineTreeInfo.outlineTree[index]
                if (!element || !element?.name || element.name.trim().length === 0) {
                    message.warning('请填写大纲内容，请勿出现空白内容')
                    return
                }
            }
        }

        const outline = transformToUserOutline(templateAndOutlineTreeInfo.outlineTree)
        const params: any = {
            creatorCode: 'ppt',
            formData: {
                ...needSubmitFormData.value.formData,
                params: {
                    ...needSubmitFormData.value.formData.params,
                    template: templateAndOutlineTreeInfo.templateFileName,
                    templateName: templateAndOutlineTreeInfo.templateName,
                    pptData: templateAndOutlineTreeInfo?.pptData || null
                },
                customSizeLength: templateAndOutlineTreeInfo.pptPageNumber,
                isPro: false,
                size: 'custom',
                topic: templateAndOutlineTreeInfo.topic,
                userOutline: JSON.stringify(outline)
            }
        }

        // console.log('createSubmission  params ==>', params)
        const res = await createSubmission(params)
        // console.log('createSubmission  res ==>', res)
        if (res.code == HTTP_STATUS.FAILED_SUBMIT_AGAIN) {
            message.error(res.message || '请勿重复提交')
            return
        }
        if (res?.code === HTTP_STATUS.MOBILE_NOT_BOUND) {
            // webUserBindPhoneVisible.value = true
            user.setShowPhoneBoundModal({
                status: BindPhoneModal.SHOW_BINDING
            })
            message.error('请先绑定手机号')
            return
        }
        if (!res.ok || !res.data) {
            message.error(res.message || '操作失败')
            return
        }
        if (!res.data.id || res.data.id == undefined || res.data.id == 'undefined') {
            message.error('写作失败，请重试')
            return
        }
        // message.success('提交成功')

        router.push({
            path: '/create/detail',
            query: {
                id: res.data.id,
                code: 'ppt'
            }
        })

    } catch (error) {
        console.log("onSelectTemplateSubmit error = ", error)
        message.error('提交失败')
    }
}

const loadCreatorsDetailAndReport = async () => {
    const res = await getCreatorsDetail({ code: currentCode.value })
    if (!res.ok) {
        message.error(res.message || '加载失败')
        return
    }
    if (!res.data) {
        return
    }
    creatorData.value = res.data
    console.log('ppt  res creatorData.value = ', res)
    // 获取当前时间，并以指定格式格式化
    const currentDateTime = new Date()
    const formattedDateTime = formatDateTimeString(currentDateTime)

    recentAppsStore.addToRecentList({ ...creatorData.value.creator, avatar: creatorData.value.creator.icon })


    UserService.reportUseApp({
        ...creatorData.value.creator,
        type: 'creator',
        avatar: creatorData.value.creator.icon || '',
        lastTime: formattedDateTime,
    })
    // console.log(' creatorData.value = ', creatorData.value)
}

const loadSubmissionInfo = async () => {
    const _submissionId = route.query?.id
    if (!_submissionId) {
        return
    }
    if (!UserService.isLogined()) {
        return
    }

    deleteSubmissionDraft('ppt')

    const res = await getCreateSubmissionResult({ submissionId: _submissionId })
    if (!res.ok || !res.data) {
        return
    }
    // console.log("onMounted  res data ==>", res.data)
    const params: {
        creatorCode: any;
        formData: any;
        attachments?: any;
    } = {
        creatorCode: 'ppt',
        formData: res.data.formData,
    }

    const fileCode = route.query?.fileCode
    try {
        if (fileCode) {
            const wordRes = await getWordToUrl({ code: fileCode })
            console.log("wordRes  res ==>", wordRes)
            if (!wordRes.ok || !wordRes.data) {
                message.error('文件获取失败')
                return
            }
            const uploadParams = {
                fileName: res.data.formData.topic,
                fileUrl: wordRes.data
            }
            const uploadRes = await uploadByUrl(uploadParams)
            // console.log("uploadRes ==>", uploadRes)
            if (uploadRes.ok && uploadRes.data) {
                params['attachments'] = [{
                    fileName: `${uploadRes.data.fileName || '万能小in'}.${getFileExtension(uploadRes.data.fileUrl)}`,
                    wordCount: uploadRes.data.wordCount,
                    id: uploadRes.data.id,
                }]
            }
        }
    } catch (error) {
        message.error('文件获取失败')
    }

    saveSubmissionDraft(params)
    setTimeout(() => {
        if (appSubmissionFormRefs.value) {
            appSubmissionFormRefs.value.setPPTDraftData()
        }
    }, 200)

}

onBeforeUnmount(() => {

})

onUnmounted(() => {

})


onMounted(async () => {


    await loadSubmissionInfo()

    isClickCreateOutlinButton.value = false
    loadCreatorsDetailAndReport()

    storage.remove(StarloveConstants.keyOflocalStorage.aMaximumOfThreePPTOutlineData)
    storage.remove(StarloveConstants.keyOflocalStorage.numberOfTimesThePPTOutlineHasBeenLoaded)

})
</script>

<style scoped>
@keyframes slide {
    from {
        transform: translateY(0);
    }

    to {
        transform: translateY(-50%);
    }
}

.animate-slide {
    animation: slide 20s linear infinite;
}
</style>
