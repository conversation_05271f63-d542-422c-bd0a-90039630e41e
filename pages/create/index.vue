<template>
  <div class="flex flex-col bg-gray-50 h-full">
    <!-- 顶部 AI写作中心 -->

    <div class="pt-4 bg-gray-50">

      <div class="bg-gradient-to-br from-blue-800 to-indigo-600 rounded-xl p-5 border border-blue-200/70 mx-2">
        <div class="flex items-center justify-between flex-wrap gap-4">
          <!-- 左侧标题部分 -->
          <div class="flex items-center flex-1 min-w-[280px]">
            <div class="w-12 h-12 bg-blue-50 rounded-xl flex items-center justify-center mr-4 shadow-inner">
              <write theme="outline" size="32" fill="#1e40af" />
            </div>
            <div class="w-full">
              <h1 class="text-2xl font-bold text-white">
                AI写作中心
              </h1>
              <div class="relative mt-2">
                <VerticalMarquee class="w-full" :texts="marqueeTexts" />
              </div>
            </div>
          </div>

          <!-- 搜索框移到中间 -->
          <div class="relative flex-1 min-w-[280px] lg:min-w-[400px]">
            <input v-model="searchStore.searchQuery" type="text" placeholder="搜索写作应用，如论文助手，PPT…" class="w-full pl-12 pr-4 py-3.5 bg-white border border-blue-200/50 rounded-2xl text-gray-800 placeholder-gray-400
              focus:outline-none focus:border-blue-300/50 focus:ring-2 focus:ring-blue-200/50 transition-all duration-200
              shadow-md text-base" @keyup.enter="searchStore.handleSearch" />
            <div class="absolute left-3.5 top-1/2 -translate-y-1/2 flex items-center text-gray-400">
              <search theme="outline" size="24" fill="#3b82f6" />
            </div>
            <button @click="searchStore.handleSearch"
              class="absolute right-3.5 top-1/2 -translate-y-1/2 bg-gradient-to-r from-blue-500 to-indigo-600 px-4 py-2.5 rounded-lg text-white text-sm font-medium transition-all duration-200 hover:bg-blue-600 hover:from-blue-600 hover:to-indigo-700">
              <span class="hidden lg:inline">搜索写作应用</span>
              <span class="lg:hidden">搜索</span>
            </button>


          </div>

          <!-- 用户信息栏-->
          <!-- <UserInfoArea /> -->

        </div>
      </div>
    </div>

    <!-- 主体内容区域 main -->
    <div class="flex flex-1 overflow-hidden">
      <!-- 左侧固定导航 mainleft -->
      <div class="hidden md:flex flex-col w-56 overflow-y-auto py-4">
        <div class="flex flex-col flex-1">

          <div class="px-4 mb-4">
            <h3 class="text-sm font-medium text-gray-600">写作分类</h3>
          </div>
          <nav class="space-y-2 px-2">
            <button v-for="(section, key) in sections" :key="section.groupId"
              class="w-full px-3 py-2.5 rounded-full transition-all duration-200 flex items-center text-sm" :class="[
                activeSection === section.groupId
                  ? 'bg-blue-600 text-white shadow-sm'
                  : 'hover:bg-blue-50 text-gray-600'
              ]" @click="scrollToSection(section.groupId)">
              <div class="w-6 h-6 rounded-full flex items-center justify-center mr-2.5 text-gray-700 font-medium">

                <component :is="section.icon === 'Star' ? Star :
                  section.icon === 'School' ? School :
                    section.icon === 'BookOne' ? BookOne :
                      section.icon === 'CityOne' ? CityOne :
                        section.icon === 'Log' ? Log :
                          section.icon === 'OnlineMeeting' ? OnlineMeeting :
                            section.icon === 'MarketAnalysis' ? MarketAnalysis : null" theme="outline" size="20"
                  :strokeWidth="3" :fill="activeSection === section.groupId ? '#fff' : '#64748b'" />
              </div>
              {{ section.title }}
            </button>
          </nav>

          <!-- 最近使用 -->
          <RecentUsed :maxItems="3" />

        </div>

        <!-- 我的写作记录 -->
        <div class="mt-6 flex justify-center">
          <button @click="toCreateRecordsPage"
            class="flex items-center px-4 py-2 bg-white border border-blue-500 text-blue-500 rounded-full hover:bg-blue-50 transition-colors duration-200">
            <History theme="outline" size="18" class="mr-1.5" />
            <span class="text-sm">我的写作记录</span>
          </button>
        </div>

      </div>


      <!-- 右侧内容区域 mainright -->
      <div class="flex-1 p-4 pb-24 md:pb-4 bg-blue-50/20 border-l border-gray-200/70 overflow-y-auto">
        <!-- 搜索结果展示 -->
        <template v-if="searchStore.isShowSearch">
          <template v-if="searchStore.searchResults && searchStore.searchResults.list?.length > 0">
            <div class="mb-8">
              <div class="flex items-center justify-between mb-4">
                <div class="flex items-center">
                  <div class="w-8 h-8 bg-blue-50 rounded-lg flex items-center justify-center mr-3">
                    <search theme="outline" size="20" fill="#10b981" />
                  </div>
                  <h2 class="text-lg font-bold text-blue-800">请选择合适的应用模版，开始写作吧~</h2>
                </div>
                <!-- 清除搜索按钮 -->
                <button @click="searchStore.clearSearch"
                  class="text-sm text-blue-600 hover:text-blue-800 transition-colors duration-200">
                  清除搜索
                </button>
              </div>

              <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                <template v-for="(app, index) in searchStore.searchResults.list" :key="index">
                  <AppCard v-if="app?.code" :icon="app.avatar" :title="app.name" :desc="app.description"
                    :to="toCreatePage(app.code)" :code="app.code" border-color="border-blue-200/50"
                    icon-bg-color="bg-blue-50" icon-color="#3b82f6" title-color="text-blue-800" />
                </template>
              </div>

              <template v-if="(searchStore.searchResults as unknown as SearchResults)?.recommendList?.length > 0">
                <div class="text-lg font-bold text-black-800 mt-10 mb-4">或者试试以下应用吧~</div>
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                  <template v-for="(app, index) in searchStore.searchResults.recommendList" :key="index">
                    <AppCard v-if="app?.code" :icon="app.avatar" :title="app.name" :desc="app.description"
                      :to="toCreatePage(app.code)" :code="app.code" border-color="border-blue-200/50"
                      icon-bg-color="bg-blue-50" icon-color="#3b82f6" title-color="text-blue-800" />
                  </template>
                </div>
              </template>
            </div>
          </template>

          <!-- 搜索无结果展示 -->
          <template v-else>
            <div class="flex flex-col items-center justify-center py-16">
              <div class="w-24 h-24 bg-blue-50 rounded-full flex items-center justify-center mb-6">
                <search theme="outline" size="48" fill="#3b82f6" />
              </div>
              <h3 class="text-xl font-bold text-gray-700 mb-2">未找到相关写作应用</h3>
              <p class="text-gray-500 mb-6">换个关键词试试，或者看看以下推荐</p>

              <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4"
                v-if="(searchStore.searchResults as unknown as SearchResults)?.recommendList?.length > 0">
                <template v-for="(app, index) in (searchStore.searchResults as unknown as SearchResults)?.recommendList"
                  :key="index">
                  <AppCard v-if="app?.code" :icon="app.avatar" :title="app.name" :desc="app.description"
                    :to="toCreatePage(app.code)" :code="app.code" border-color="border-blue-200/50"
                    icon-bg-color="bg-blue-50" icon-color="#3b82f6" title-color="text-blue-800" />
                </template>
              </div>
            </div>
          </template>
        </template>

        <!-- 原有内容 -->
        <template v-else>
          <!-- :id="homeData[0]?.groupId" -->
          <FeaturedApps id="000000000001" />
          <!-- 各个分类区域 -->
          <div :id="item.groupId" class="mb-8" v-for="item in homeData" :key="item.groupId">
            <template v-if="item.groupId != '000000000001'">
              <div class="flex items-center justify-between mb-4">
                <div class="flex items-center">
                  <div class="w-8 h-8 bg-blue-50 rounded-lg flex items-center justify-center mr-3">
                    <component :is="getIconByGroupId(item.groupId)" theme="outline" size="20" :fill="'#10b981'"
                      :strokeWidth="3" />
                  </div>
                  <h2 class="text-lg font-bold text-blue-800">{{ item.title }}</h2>
                </div>
              </div>

              <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                <template v-for="app in item.list" :key="app.code">
                  <AppCard :icon="app.avatar" :title="app.name" :desc="app.description" :to="toCreatePage(app.code)"
                    :code="app.code" border-color="border-blue-200/50" icon-bg-color="bg-blue-50" icon-color="#3b82f6"
                    title-color="text-blue-800" />
                </template>
              </div>
            </template>
          </div>
        </template>
      </div>
    </div>


    <!-- 添加小屏幕下的底部导航 -->
    <div class="md:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 py-2">
      <nav class="flex justify-around">
        <button v-for="section in sections" :key="section.groupId" class="flex flex-col items-center px-3 py-2"
          :class="[activeSection === section.groupId ? 'text-blue-600' : 'text-gray-600']"
          @click="scrollToSection(section.groupId)">
          <component :is="section.icon === 'Star' ? Star :
            section.icon === 'School' ? School :
              section.icon === 'BookOne' ? BookOne :
                section.icon === 'CityOne' ? CityOne :
                  section.icon === 'Log' ? Log :
                    section.icon === 'OnlineMeeting' ? OnlineMeeting :
                      section.icon === 'MarketAnalysis' ? MarketAnalysis : null" theme="outline" size="20"
            :strokeWidth="3" :fill="activeSection === section.groupId ? '#2551B5' : '#333'" />
          <span class="text-xs mt-1">{{ section.title }}</span>
        </button>
      </nav>
    </div>

  </div>
</template>

<script setup lang="ts">
import { useAsyncData } from '#app';
import VerticalMarquee from '@/components/Common/VerticalMarquee.vue';
import { UserService } from '@/services/user';
import type { SearchResults } from '~/services/types/create';
import { useHomeStore } from '~/stores/home';

import {
  BookOne, CityOne,
  History,
  Log,
  MarketAnalysis,
  OnlineMeeting,
  School,
  Search,
  Star,
  Write
} from '@icon-park/vue-next';
import { onMounted, onUnmounted, ref } from 'vue';
import AppCard from '~/components/AppCard.vue';
import RecentUsed from '~/components/Create/Paper/RecentUsed.vue';
import FeaturedApps from '~/components/FeaturedApps.vue';

import { apiHomePath } from '~/api/home';
import { useSearchStore } from '~/stores/search';


// const { operationGroups } = storeToRefs(useOperationStore())

useHead({
  title: 'AI写作专家-万能小in官网',
  meta: [
    {
      name: 'description',
      content: '万能小in是AI长文写作的开创者，基于个人知识库实现个性化写作，专业、规范、准确，一键写作初稿，AI在线编辑改稿。支持150+写作应用模版，支持自定义篇幅、语言、文风等，文笔质量高，无AI痕迹。',
      tagPriority: 'critical', // 提高优先级
    },
    {
      name: 'keywords',
      content: 'AI写作一键生成,人工智能写作,AI写作生成器',
      tagPriority: 'critical', // 提高优先级
    }
  ],
  script: [
    {
      type: 'application/ld+json',
      tagPriority: 'critical', // 提高优先级
      children: JSON.stringify({
        "@context": "https://schema.org",
        "@type": "SoftwareApplication",
        "name": "AI写作工具",
        "url": "https://xiaoin.com.cn/create",
        "description": "万能小in是AI长文写作的开创者，基于个人知识库实现个性化写作，专业、规范、准确，一键写作初稿，AI在线编辑改稿。支持150+写作应用模版，支持自定义篇幅、语言、文风等，文笔质量高，无AI痕迹。",
        "applicationCategory": "WritingApplication",
        "operatingSystem": "Web",
        "offers": {
          "@type": "Offer",
          "price": "10",
          "priceCurrency": "CNY"
        },
        "featureList": "AI写作,模板库,文风定制,多语言支持",
        "publisher": {
          "@type": "Organization",
          "name": "万能小in",
          "logo": {
            "@type": "ImageObject",
            "url": "https://static.xiaoin.cn/prod/logo.png",
            "width": 112,
            "height": 112
          }
        }
      }, null, 2)
    }
  ]
})

// 分类导航配置
const sections = {
  featured: {
    title: '推荐写作',
    icon: 'Star',
    groupId: '000000000001'
  },
  academic: {
    title: '学术教育',
    icon: 'School',
    groupId: '000019970421'
  },
  student: {
    title: '学生常用',
    icon: 'BookOne',
    groupId: '000019970422'
  },
  workplace: {
    title: '职场精选',
    icon: 'CityOne',
    groupId: '000019970423'
  },
  government: {
    title: '机关单位',
    icon: 'Log',
    groupId: '000019970424'
  },
  media: {
    title: '媒体文学',
    icon: 'OnlineMeeting',
    groupId: '000019970425'
  },
  business: {
    title: '商业分析',
    icon: 'MarketAnalysis',
    groupId: '000019970426'
  }
}

// 当前激活的分类
const activeSection = ref('000000000001')


// const isHomeDataLoading = ref(true)

// 添加一个变量来控制是否启用 observer
const enableObserver = ref(true)
let observer: IntersectionObserver | null = null

const homeStore = useHomeStore()
// const operationStore = useOperationStore()
const { homeData } = storeToRefs(useHomeStore())

const config = useRuntimeConfig()

// 在 useAsyncData 之前添加接口定义
interface AsyncDataResponse {
  success: boolean;
  code: number | null;
  result: { id: string; title: string; items: any[] }[];
  message?: string;
}

const { data: asyncData } = useAsyncData<AsyncDataResponse>('fetchData', async () => {
  // 检查并加载 operationGroup 数据
  // if (operationStore.operationGroups.length === 0) {
  //   // await operationStore.loadOperationGroups()
  //   await homeStore.loadHomeData({}, `${config.public.apiBase}${apiHomePath}`)

  // }

  // // 检查并加载 homeStore 数据
  if (homeStore.homeData.length === 0) {
    await homeStore.loadHomeData({}, `${config.public.apiBase}${apiHomePath}`)
  }
  // homeData.value = homeStore.homeData

  return {
    success: true,
    code: null,
    result: homeStore.homeData,
    message: ''
  }
}, {
  server: true,
  lazy: false,
  immediate: true,
  default: () => ({ success: false, code: null, result: [], message: '' })
})

// 使用 computed 简化解构
// const success = computed(() => asyncData.value?.success || false)
// const code = computed(() => asyncData.value?.code || null)
// const message = computed(() => asyncData.value?.message || '')

// 根 groupId 获取对应的图标组件
const getIconByGroupId = (groupId: string) => {
  // 查找匹配的 section
  const matchedSection = Object.values(sections).find(section => section.groupId === groupId)
  // 如果找到匹配的 section，返回对应的图标组件
  if (matchedSection) {
    switch (matchedSection.icon) {
      case 'Star': return Star
      case 'School': return School
      case 'BookOne': return BookOne
      case 'CityOne': return CityOne
      case 'Log': return Log
      case 'OnlineMeeting': return OnlineMeeting
      case 'MarketAnalysis': return MarketAnalysis
      default: return null
    }
  }
  return null
}

// 修改滚动方法
const scrollToSection = (sectionId: string) => {
  searchStore.clearSearch()
  // 获取目标分类区域元素
  const targetSection = document.getElementById(sectionId)
  if (!targetSection) return

  // 在滚动开始前禁用 observer
  enableObserver.value = false

  // 更新激活状态
  activeSection.value = sectionId

  // 获取右侧滚动容器
  const mainContent = document.querySelector('.flex-1.overflow-y-auto')
  if (mainContent) {
    // 计算目标元素距离容器顶部的距离
    const containerTop = mainContent.getBoundingClientRect().top
    const targetTop = targetSection.getBoundingClientRect().top
    const scrollTop = (mainContent as Element).scrollTop + (targetTop - containerTop)

    // 执行滚动
    mainContent.scrollTo({
      top: scrollTop - 20,
      behavior: 'smooth'
    })
  }

  // 滚动结束后重新启用 observer
  setTimeout(() => {
    enableObserver.value = true
  }, 1000)
}

const toCreatePage = (code: string) => {
  if (code == 'paper') {
    return '/create/paper'
  }
  if (code == 'ppt') {
    return '/create/ppt'
  }
  return `/create/${code}`
}

const router = useRouter()
const toCreateRecordsPage = () => {
  if (!UserService.isLogined()) {
    const userStore = useUserStore()
    userStore.openLoginModal()
    return
  }
  return router.push({ path: '/create/history' })
}

// 修改滚动监听逻辑
onMounted(async () => {

  observer = new IntersectionObserver((entries: IntersectionObserverEntry[]) => {
    if (!enableObserver.value) return

    // 找到最接近视口中心的元素
    let maxIntersection = 0
    let mostVisibleSection: Element | null = null

    entries.forEach(entry => {
      if (entry.isIntersecting && entry.intersectionRatio > maxIntersection) {
        maxIntersection = entry.intersectionRatio
        mostVisibleSection = entry.target
      }
    })

    // 只有当找到可见元素时更新激活状态
    if (mostVisibleSection) {
      const element = mostVisibleSection as unknown as { id: string }
      activeSection.value = element.id || '000000000001'
    }
  }, {
    threshold: [0.1, 0.2, 0.3, 0.4, 0.5],
    rootMargin: '-20% 0px -30% 0px'
  })

  // 观察所有分类区域，包括推荐写作区域
  const allSections = document.querySelectorAll('[id^="00"]')
  allSections.forEach(element => {
    if (element && observer) {
      observer.observe(element)
    }
  })
})

onBeforeUnmount(() => {
  searchStore.clearSearch()
})

// 清空函数
onUnmounted(() => {
  if (observer) {
    observer.disconnect()
  }
})

const marqueeTexts = [
  '联网+知识库检索参考信息，专业全面',
  '直出Word，格式规范、美观',
  '可灵活定义篇幅、大纲、背景文档、语言',
  '海量应用模板，想写什么就写什么',
  'AI原创，无版权风险',
  '支持AI在线编辑，快速改稿',
  '已全面接入Deepseek R1满血版推理模型'
]

const searchStore = useSearchStore()
</script>

<style>
/* 替换原有的 slide-fade 相关样式 */
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  position: absolute;
  width: 100%;
}

.slide-fade-enter-from {
  transform: translateY(30px);
  opacity: 0;
}

.slide-fade-leave-to {
  transform: translateY(-30px);
  opacity: 0;
}

.slide-fade-enter-to,
.slide-fade-leave-from {
  transform: translateY(0);
  opacity: 1;
}
</style>