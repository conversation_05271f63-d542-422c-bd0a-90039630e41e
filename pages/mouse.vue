<template>
    <div class="relative mouse-desc-view-page">
        <picture>
            <source :srcset="mobileTabletImage" media="(max-width: 1000px)" />
            <img :src="desktopImage" alt="适配图片" class="w-full h-auto object-cover" />
        </picture>
        <div class="upgrade-button">
            <button class="button" @click="handleOpenRecharge">升级为至尊会员，免费得AI鼠标</button>
        </div>

        <!-- 充值弹窗 -->
        <RechargeModal v-if="rechargeStore.rechargeModalVisible" v-model="rechargeStore.rechargeModalVisible"
            @recharge="handleRecharge" />
    </div>
</template>

<script lang="ts" setup>
import RechargeModal from '~/components/Auth/RechargeModal.vue';
import { useRechargeStore } from '~/stores/recharge';

import { useVipPlansStore } from '@/stores/vipPlans';

const vipPlansStore = useVipPlansStore()

const rechargeStore = useRechargeStore()
const mobileTabletImage =
    '//static-1256600262.cos.ap-shanghai.myqcloud.com/xiaoin-h5/icons/new-pc/mouse/mouse-phone-image.jpeg'
const desktopImage =
    '//static-1256600262.cos.ap-shanghai.myqcloud.com/xiaoin-h5/icons/new-pc/mouse/mouse-image1.jpeg'
const handleOpenRecharge = () => {
    rechargeStore.openRechargeModal(RechargeModalTab.vip)
}
const handleRecharge = (data: any) => {
    console.log('充值信息：', data)
    // 处理充值逻辑
}

onMounted(() => {
    vipPlansStore.from = 'mouse'
})
onUnmounted(() => {
    vipPlansStore.from = ''
})

definePageMeta({
    layout: 'empty'
})
</script>

<style scoped>
.mouse-desc-view-page {
    position: relative;

    .upgrade-button {
        text-align: center;
        position: fixed;
        bottom: 57px;
        left: 0;
        right: 0;

        .button {
            width: 600px;
            height: 59px;
            font-size: 21px;
            font-weight: 500;

            border-radius: 12px;
            background: linear-gradient(180deg, #ffd48a 0%, #ffaa69 100%);
            color: #17192a;
            border: none;
            transition: all 0.3s ease;
        }
    }

    @media screen and (min-width: 1000px) and (max-width: 1200px) {
        .upgrade-button {
            position: fixed;
            bottom: 87px;
        }
    }

    @media screen and (max-width: 1000px) {
        .upgrade-button {
            position: fixed;
            bottom: 87px;

            .button {
                width: 300px;
                height: 40px;
                font-size: 16px;
            }
        }
    }
}
</style>