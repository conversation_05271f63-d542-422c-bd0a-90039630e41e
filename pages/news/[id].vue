<template>
    <div class="flex h-screen">
        <!-- 左侧区域：包含顶部导航和网页预览 -->
        <div class="flex flex-col w-1/2">
            <!-- 顶部导航区域 -->
            <div class="p-4 bg-white border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <button @click="router.push('/news')"
                            class="mr-4 p-2 hover:bg-gray-100 rounded-lg transition-colors">
                            <left-small theme="outline" size="24" fill="#666" />
                        </button>
                        <div>
                            <!-- <h1 class="text-2xl font-bold bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent">
                {{ news.title }}
              </h1> -->
                            <!-- <p class="text-gray-500 mt-1">
                {{ news.createTime }}
              </p> -->
                        </div>
                    </div>

                    <div class="flex flex-row justify-center items-center gap-2">
                        <button @click="handleConfirmAddToLibrary"
                            class="flex items-center px-2 py-1 text-sm text-gray-600 hover:text-blue-700 hover:bg-blue-50 rounded-full transition-colors">
                            <folder-plus theme="outline" size="16" class="mr-1" />
                            <span class="whitespace-nowrap inline">添加到知识库</span>
                        </button>

                        <button @click="viewArticle(news.url)"
                            class="flex items-center px-2 py-1 text-sm text-gray-600 hover:text-blue-700 hover:bg-blue-50 rounded-full transition-colors">
                            <view-grid-detail theme="outline" size="16" class="mr-1" />
                            <span class="whitespace-nowrap inline">查看原文</span>
                        </button>

                    </div>
                </div>
            </div>

            <!-- 将 iframe 改为直接显示文章内容 -->
            <div class="flex-1 overflow-auto bg-white px-6">

                <ClientOnly>
                    <div v-if="loading" class="flex items-center justify-center h-full">
                        <div class="animate-spin rounded-full h-12 w-12 border-4 border-blue-500 border-t-transparent">
                        </div>
                    </div>
                    <div v-else-if="error" class="p-4 text-red-500">
                        {{ error }}
                    </div>
                    <NewsContentRaw v-else :content="mdContent" />
                </ClientOnly>
            </div>
        </div>

        <!-- 右侧功能区域 -->
        <div class="w-1/2 flex flex-col border-l border-gray-200 overflow-y-auto">
            <ClientOnly>
                <GuideInfo :showNotes="false" :content="news.content" />
            </ClientOnly>
        </div>
    </div>
</template>

<script setup lang="ts">
import { getSourceMd, newsQueryById, repositoryFileAddNews } from '@/api/repositoryFile'
import GuideInfo from '@/components/Library/GuideInfo.vue'
import NewsContent from '@/components/News/NewsContent.vue'
import { FolderPlus, LeftSmall, ViewGridDetail } from '@icon-park/vue-next'
import { message } from 'ant-design-vue'
import { markRaw, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '~/stores/user'

const router = useRouter()
const route = useRoute()

const user = useUserStore()
const avatar = computed(() => {
    return user.currentLoginInfo?.avatar || ''
})

const loading = ref(true)
const error = ref(null)
const articleContent = ref('')

const news = ref<{
    title: string
    author: string
    wordCount: number
    createTime: string
    url: string
    content: string
}>({
    title: '',
    author: '',
    wordCount: 0,
    createTime: '',
    url: '',
    content: '',
})

const mdContent = ref<any>('')

const NewsContentRaw = markRaw(NewsContent)

// 获取文章内容
const fetchArticle = async () => {
    try {
        loading.value = true

        const newsId = route.params.id as string // 从路由参数中获取动态 ID

        const res = await getSourceMd(newsId)

        if (!res.ok) {
            loading.value = false
            return
        }
        // console.log(res.data)
        mdContent.value = res.data || ''
        loading.value = false
    } catch (err) { }
}

const setupData = async () => {
    const newsId = route.params.id as string // 从路由参数中获取动态 ID

    try {
        const res = await newsQueryById({ id: newsId })
        if (!res.ok) {
            console.log('查询失败')
            return
        }

        news.value.url = res.data?.sourceUrl || ''
        news.value.content = res.data?.content || ''
        news.value.title = res.data?.title || ''
        news.value.createTime = res.data?.createTime || ''
    } catch (err) {
        console.error('获取新闻数据失败:', err)
    }
}

const viewArticle = (url: string) => {
    // 使用 window.open 打开新页面
    window.open(url, '_blank')
    console.log('查看文章:', url)
}


const spaceId = computed(() => {

    return user.currentLoginInfo?.id || ''

})



const handleConfirmAddToLibrary = async () => {
    const userStore = useUserStore()
    if (!userStore.isLogined) {
        userStore.openLoginModal()
        return
    }

    const newsId = route.params.id as string // 从路由参数中获取动态 ID

    const res = await repositoryFileAddNews({
        id: newsId,
        spaceId: spaceId.value,
        folderId: '0'
    })

    if (res?.code == HTTP_STATUS.MOBILE_NOT_BOUND) {
        // 判断是否需要绑定手机号
        user.setShowPhoneBoundModal({
            status: BindPhoneModal.SHOW_BINDING,
        })
        return
    }


    if (!res.ok) {
        return
    }

    message.success(res.message || '添加成功')
    console.log('添加到知识库:')
}

onMounted(() => {
    setupData()
    fetchArticle()
})


useHead({
    title: '基于知识库的AI问答助手-资讯详情-万能小in官网',
    meta: [
        {
            name: 'referrer',
            content: 'never'
        },
        {
            name: 'description',
            content: '万能小in的资讯导读功能，可以基于你的订阅兴趣标签，每日自动抓取高质量知识资讯信息，并提供原文参考+AI导读+关键要点提炼，帮你快速阅读资讯，支持一键添加到知识库。',
            tagPriority: 'critical', // 提高优先级
        },
        {
            name: 'keywords',
            content: '资讯,热门资讯,有效阅读,学术资讯,AI导读',
            tagPriority: 'critical', // 提高优先级
        },
    ]
})
</script>

<style>
/* 添加必要的样式来确保文章内容正确显示 */
.rich_media_content {
    font-size: 16px;
    line-height: 1.8;
}

.rich_media_content img {
    max-width: 100%;
    height: auto;
    margin: 1rem 0;
}

.rich_media_content p {
    margin: 1em 0;
}
</style>
