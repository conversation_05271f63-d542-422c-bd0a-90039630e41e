<template>
    <div class="h-full  bg-slate-50 overflow-hidden flex flex-col">
        <!-- 页面标题区 -->
        <div class="bg-white/80 rounded-xl px-4 py-2 border border-blue-700/20 m-4">
            <div class="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-4">
                <!-- 标题部分 -->
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-blue-700 rounded-xl flex items-center justify-center mr-4 shadow-inner">
                        <newspaper-folding theme="outline" size="24" fill="#FFFFFF" />
                    </div>
                    <div>
                        <h2
                            class="text-lg font-medium text-transparent bg-gradient-to-r from-blue-700 to-indigo-700 bg-clip-text">
                            资讯中心</h2>
                        <p class="text-sm text-gray-500">及时了解前沿资讯动态</p>
                    </div>
                </div>

                <!-- 日期选择部分 -->
                <div class="flex items-center space-x-4 order-3 lg:order-2">
                    <button @click="changeDate(-1)"
                        class="p-1 rounded-full text-gray-600 hover:bg-blue-50 hover:text-blue-700 transition-colors">
                        <left theme="outline" size="24" />
                    </button>

                    <div class="flex items-center space-x-2">
                        <div>
                            <div class="text-sm text-gray-500">{{ date.getFullYear() }}</div>
                            <div class="text-sm text-gray-500">{{ date.getMonth() + 1 }}月</div>
                        </div>
                        <div class="w-10 text-lg font-bold text-blue-700 mr-2">
                            {{ padZero(date.getDate()) }}
                        </div>
                    </div>

                    <button @click="changeDate(1)" :disabled="isToday"
                        :class="{ 'cursor-not-allowed opacity-50': isToday }"
                        class="p-1 rounded-full text-gray-600 hover:bg-blue-50 hover:text-blue-700 transition-colors">
                        <right theme="outline" size="24" />
                    </button>
                </div>

                <!-- 订阅按钮部分 -->
                <div class="flex items-center space-x-4 order-2 lg:order-3">
                    <button @click="handlePressSubscribe"
                        class="flex items-center px-4 py-2 text-sm space-x-2 bg-gradient-to-r from-blue-500 to-indigo-600   text-white rounded-full hover:from-blue-600 hover:to-indigo-700 transition-colors">
                        个性化订阅
                    </button>
                    <!-- <UserInfoArea /> -->
                </div>
            </div>
        </div>

        <div class="flex-1 overflow-auto p-4" ref="messageContainer">
            <!-- 资讯列表 -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div v-for="item in newsList" :key="item.id"
                    class="bg-white/80 backdrop-blur-sm rounded-xl border border-gray-200/70 overflow-hidden group hover:border-blue-700/30 transition-all duration-200">
                    <!-- 文章内容 -->
                    <div class="p-4">
                        <NuxtLink :to="`/news/${item.id}`" @click="track('news_detail', item.id, '资讯详情')">
                            <h3
                                class="title text-ellipsis inline-block max-w-full m-0 text-base  text-gray-800 font-medium line-clamp-2 mb-3 group-hover:text-blue-600">
                                {{ item.title }}
                            </h3>
                            <ArticleContent :content="item.content" />
                        </NuxtLink>

                        <!-- 底部操作区的响应式调整 -->
                        <div
                            class="flex flex-col sm:flex-row items-start sm:items-center justify-between mt-4 pt-3 border-t border-gray-100 gap-3">
                            <!-- 左侧操作按钮组 -->
                            <div class="flex items-center space-x-2">
                                <button @click="handleCopy(item)"
                                    class="flex items-center px-2 py-1 text-sm text-gray-600 hover:text-blue-700 hover:bg-blue-50 rounded-full transition-colors"
                                    :class="{ 'opacity-50 pointer-events-none': copiedStates[item.id] }">
                                    <copy theme="outline" size="16" class="mr-1" />
                                    <span class="inline">{{ copiedStates[item.id] ? '已复制' : '复制' }}</span>
                                </button>

                                <button @click="shareArticle(item)"
                                    class="flex items-center px-2 py-1 text-sm text-gray-600 hover:text-blue-700 hover:bg-blue-50 rounded-full transition-colors"
                                    :class="{ 'opacity-50 pointer-events-none': sharedStates[item.id] }">
                                    <share-one theme="outline" size="16" class="mr-1" />
                                    <span class="inline">{{ sharedStates[item.id] ? '已分享' : '分享' }}</span>
                                </button>
                            </div>

                            <!-- 右侧数据统计 -->
                            <div class="flex items-center space-x-3">

                                <button @click="viewArticle(item)"
                                    class="flex items-center px-2 py-1 text-sm text-gray-600 hover:text-blue-700 hover:bg-blue-50 rounded-full transition-colors">
                                    <view-grid-detail theme="outline" size="16" class="mr-1" />
                                    <span class="whitespace-nowrap inline">查看</span>
                                </button>


                                <button @click="handleConfirmAddToLibrary(item.id)"
                                    class="flex items-center px-2 py-1 text-sm text-gray-600 hover:text-blue-700 hover:bg-blue-50 rounded-full transition-colors">
                                    <folder-plus theme="outline" size="16" class="mr-1" />
                                    <span class="whitespace-nowrap inline">添加到知识库</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 加载更多 -->
            <div class="flex justify-center mt-8" v-if="!isLoading">
                <div v-if="newsList.length === 0">
                    <a-empty :image="simpleImage" />
                </div>
                <div v-else-if="page.pages > page.current">
                    <div v-if="isReachMaxLimit" class="text-sm bg-orange-50 text-orange-600 rounded-full py-2 px-4">
                        今日订阅条数已满，想获取更多可以选择
                        <a class="text-red-500 font-semibold" @click="upgradeAssistant"
                            href="javascript:void(0)">升级助手</a>
                        哦～
                    </div>
                    <button v-else
                        class="relative group flex items-center px-6 py-2 bg-white rounded-full border border-blue-700/20 text-gray-600 group-hover:text-gray-800 transition-colors"
                        @click="handleLoadMore">
                        加载更多
                    </button>
                </div>
                <template v-else>
                    <div v-if="!isReachMaxLimit" class="relative group">
                        <div
                            class="relative flex items-center px-6 py-2 bg-white rounded-full border border-blue-700/20 text-gray-600 group-hover:text-gray-800 transition-colors">
                            <!-- <loading-three theme="outline" size="16" class="mr-2 animate-spin" /> -->
                            <span>今日推送订阅内容已完毕</span>
                        </div>
                    </div>
                </template>
            </div>
            <div v-else class="flex items-center justify-between p-4">
                <div class="space-y-2 flex-1">
                    <USkeleton class="h-8 w-1/4" />
                    <div class="space-y-2">
                        <USkeleton class="h-4 w-3/4" />
                        <USkeleton class="h-4 w-2/4" />
                    </div>
                </div>
                <div class="space-y-2 flex-1">
                    <USkeleton class="h-8 w-1/4" />
                    <div class="space-y-2">
                        <USkeleton class="h-4 w-3/4" />
                        <USkeleton class="h-4 w-2/4" />
                    </div>
                </div>

            </div>
        </div>

        <!-- 添加订阅弹窗组件 -->
        <subscribe-modal v-if="showSubscribeModal" :model-value="showSubscribeModal" @save="handleSubscriptionSave"
            @cancel="handleCancel" />
    </div>
</template>

<script setup lang="ts">
import { getNewsList, getTagType, repositoryFileAddNews } from '@/api/repositoryFile'
import ArticleContent from '@/components/News/ArticleContent.vue'
import SubscribeModal from '@/components/News/SubscribeModal.vue'
import type { NewsInfo } from '@/services/types/repositoryFile'
import { HTTP_STATUS } from '@/utils/constants'
import { dateFormatS } from '@/utils/utils'
import {
    Copy,
    FolderPlus,
    Left,
    NewspaperFolding,
    Right,
    ShareOne,
    ViewGridDetail
} from '@icon-park/vue-next'
import { Empty, message } from 'ant-design-vue'
import { computed, onMounted, reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useTracking } from '~/composables/useTracking'
import { UserService } from '~/services/user'
import { useRechargeStore } from '~/stores/recharge'
import { useUserStore } from '~/stores/user'


const { track } = useTracking();
const user = useUserStore()
const router = useRouter()

const publishDate = ref(dateFormatS(new Date(), 'yyyy-MM-dd'))
const page = reactive({
    current: 1,
    pageSize: 6,
    total: 0,
    pages: 1,
})


watch(() => user.isLogined, async (isLogined) => {
    if (isLogined) {
        console.log('用户登录成功，重新加载数据')
        await setupData()
    }
})


const rechargeStore = useRechargeStore()
const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE
const messageContainer = ref<HTMLElement | null>(null)
const isLoading = ref(true)

const newsList = ref<NewsInfo[]>([])
const isReachMaxLimit = ref(false)
const copiedStates = ref<{ [key: string]: boolean }>({})
const sharedStates = ref<{ [key: string]: boolean }>({})


const knowledgeAssistantMemberInfo = computed(() => {
    return UserService.getKnowledgeAssistantMemberInfo()
})


const loadNewsList = async (options: { isLoadMore?: boolean } = { isLoadMore: false }) => {
    if (!user.isLogined && options.isLoadMore) {
        // 未登录用户只能加载一次
        return
    }

    isLoading.value = true

    const getNewsListParams = {
        publishDate: publishDate.value,
        pageSize: page.pageSize,
        pageNo: page.current,
        orderByTag: !subscribeOpen.value,
        from_id: '',
    }

    if (options.isLoadMore) {
        if (newsList.value.length > 1) {
            getNewsListParams.from_id = newsList.value[newsList.value.length - 1].id
        }
    }
    const res = await getNewsList(getNewsListParams)

    if (!res.ok || !res.data) {
        isLoading.value = false
        return
    }

    publishDate.value = dateFormatS(new Date(res.data.publishDate), 'yyyy-MM-dd')
    isReachMaxLimit.value = (knowledgeAssistantMemberInfo.value?.vipLevel || 0) === 0 && res.data?.reachMaxLimit
    const list = res.data.records || []

    if (options.isLoadMore) {
        list.forEach((d) => {
            newsList.value.push(d)
        })
    } else {
        newsList.value = list || []
    }

    isLoading.value = false

    Object.assign(page, {
        current: res.data.current,
        total: res.data.total,
        pageSize: res.data.size,
        pages: res.data.pages,
    })

    // 针对未登录用户显示一次的限制
    if (!user.isLogined) {
        isReachMaxLimit.value = true
    }
}

const subscribeOpen = ref<boolean>(false)
// 获取类
const getUserTagType = async () => {
    if (!user.isLogined) {
        return
    }
    try {
        const res = await getTagType()
        if (!res.ok) {
            return
        }
        const data = res.data
        if (data == 0 || data == 1) {
            subscribeOpen.value = true
        } else {
            subscribeOpen.value = false
        }
        // console.log('subscribeOpen ==>', subscribeOpen.value)
    } catch (error) {
        return
    }
}

// 新增：订阅相关的数据
const showSubscribeModal = ref<boolean>(false)

const handlePressSubscribe = () => {
    const userStore = useUserStore()
    console.log('点击订阅按钮')
    if (!userStore.isLogined) {
        console.log('用户未登录，打开登录弹窗')
        userStore.openLoginModal()
        return
    }
    console.log('用户已登录，打开订阅弹窗')
    showSubscribeModal.value = true
    console.log('showSubscribeModal:', showSubscribeModal.value) // 添加日志
}

const handleSubscriptionSave = async (data: any) => {
    console.log('保存订阅设置', data)
    showSubscribeModal.value = false
    // 重置列表数据
    newsList.value = []
    // 重置分页
    page.current = 1
    page.total = 0
    page.pages = 1
    // 重新获取用户标签类型
    await getUserTagType()
    // 重新加载列表
    await loadNewsList()
}

const handleCancel = () => {
    console.log('取消订阅设置')
    showSubscribeModal.value = false
}






// 添加一个变量来记录打开弹窗时的 VIP 等级
const previousVipLevel = ref(0)

// 修改 watch
watch(() => rechargeStore.rechargeModalVisible, async (newVal, oldVal) => {
    if (newVal) {
        // 打开弹窗时记录当前 VIP 等级
        previousVipLevel.value = knowledgeAssistantMemberInfo.value?.vipLevel || 0
    } else if (oldVal && !newVal) {
        // 关闭弹窗时，比较新旧 VIP 等级
        const currentVipLevel = knowledgeAssistantMemberInfo.value?.vipLevel || 0
        // 只有当新的 VIP 等级大于旧的 VIP 等级时，才重置限制并加载更多
        if (currentVipLevel > previousVipLevel.value) {
            isReachMaxLimit.value = false
            await handleLoadMore()
        }
    }
})


const handleLoadMore = async () => {
    if (isLoading.value) return // 防止重复触发
    isLoading.value = true

    page.current++
    await loadNewsList({ isLoadMore: true })

    isLoading.value = false
}

// 新增：文章操作相关的方法
const handleCopy = (item: { title: string, content: string, id: string }) => {
    // // 测试数据生成
    // const generateTestData = () => {
    //     const testEvents = [];
    //     const now = Date.now();
    //     for (let i = 0; i < 25; i++) {
    //         testEvents.push({
    //             event: 'test_event',
    //             time: now + i * 1000, // 每条数据间隔1秒
    //             channel: 'test_channel',
    //             user_id: 'test_user',
    //             visitor_id: 'test_visitor',
    //             target_id: `test_target_${i}`,
    //             team_id: 'test_team',
    //             channel_id: 'test_channel_id',
    //             platform: 'test_platform'
    //         });
    //     }
    //     return testEvents;
    // };

    // // 生成测试数据并发送
    // const testEvents = generateTestData();
    // testEvents.forEach(event => {
    //     track('test_event', event.target_id, '测试事件');
    // });

    // // 原有的复制逻辑
    let parsedContent: any[] = []
    try {
        parsedContent = JSON.parse(item.content)
    } catch (error) {
        console.error('解析 JSON 失败:', error)
        message.error('复制失败 - 无法解析内容')
        return
    }

    if (parsedContent.length < 1) {
        message.error('复制失败 - 无效内容')
        return
    }

    const copyContent: string[] = []
    parsedContent.forEach((item: any) => {
        if (item) {
            copyContent.push(item.title)
            copyContent.push(item.content)
        }
    })

    const contentToCopy = copyContent.join('\n')

    navigator.clipboard
        .writeText(contentToCopy)
        .then(() => {
            console.log('文章内容已复制')
            message.success('复制成功')
            // 设置复制状态为已复制
            copiedStates.value[item.id] = true
            // 2秒后恢复状态
            setTimeout(() => {
                copiedStates.value[item.id] = false
            }, 3000)
        })
        .catch((err) => {
            console.error('复制失败:', err)
            message.error('复制失败')
        })

    track('news_copy', item.id, '资讯复制')
}

const shareArticle = (article: any) => {
    let url = `${window.location.origin}/news/${article.id}`
    navigator.clipboard
        .writeText(url)
        .then(() => {
            message.success('链接已复制')
            // 设置分享状态为已分享
            sharedStates.value[article.id] = true
            // 2秒后恢复状态
            setTimeout(() => {
                sharedStates.value[article.id] = false
            }, 3000)
        })
        .catch((err) => {
            console.error('复制失败:', err)
            message.error('复制失败 - 请重试')
        })

    track('news_share', article.id, '资讯分享')
}
let currentId = ''


const spaceId = computed(() => {

    return user.currentLoginInfo?.id || ''

})


const handleConfirmAddToLibrary = async (item: any) => {
    const userStore = useUserStore()
    if (!userStore.isLogined) {
        userStore.openLoginModal()
        return
    }

    if (!item) {
        item = currentId
    } else {
        currentId = item
    }

    const res = await repositoryFileAddNews({
        id: item,
        spaceId: spaceId.value,
        folderId: '0'
    })

    if (res?.code == HTTP_STATUS.MOBILE_NOT_BOUND) {
        // 判断是否需要绑定手机号
        user.setShowPhoneBoundModal({
            status: BindPhoneModal.SHOW_BINDING,
        })
        return
    }


    if (!res.ok) {
        return
    }

    message.success(res.message || '添加成功')
    track('news_addlibrary', item, '资讯内容添加到知识库')
}

// 添加日期相关的数据和方法
const date = ref(new Date())

const isToday = computed(() => {
    const today = new Date()
    return date.value.toDateString() === today.toDateString()
})

const changeDate = (days: any) => {
    isLoading.value = true
    const newDate = new Date(date.value)
    newDate.setDate(newDate.getDate() + days)
    date.value = newDate
    // TODO: 根据新日期获取应的资讯内容
    newsList.value = []
    const day1 = new Date(publishDate.value)
    day1.setDate(day1.getDate() + days)
    publishDate.value = dateFormatS(day1, 'yyyy-MM-dd')
    page.current = 1
    loadNewsList()
}

// 新增：查看文章的方法
const viewArticle = (item: any) => {
    // 使用 window.open 打开新页面
    router.push(`/news/${item.id}`)
    track('news_view', item.id, '资讯查看')
    // window.open(`${window.location.origin}/news/${item.id}`)
    console.log('查看文章:', item)
}

// 日期补零函数
const padZero = (num: any) => {
    return num < 10 ? `0${num}` : num
}

useHead({
    title: '基于知识库的AI问答助手-万能小in官网',
    meta: [
        {
            name: 'description',
            content: '万能小in的资讯导读功能，可以基于你的订阅兴趣标签，每日自动抓取高质量知识资讯信息，并提供原文参考+AI导读+关键要点提炼，帮你快速阅读资讯，支持一键添加到知识库。',
            tagPriority: 'critical', // 提高优先级
        },
        {
            name: 'keywords',
            content: '资讯,热门资讯,有效阅读,学术资讯,AI导读',
            tagPriority: 'critical', // 提高优先级
        },
    ],
})

const setupData = async () => {
    await getUserTagType()
    await loadNewsList()
}

// const handlePressSubscribe = () => {
//     const userStore = useUserStore()
//     if (!userStore.isLogined) {
//         userStore.openLoginModal()
//         return
//     }
//     showSubscribeModal.value = true
// }

const handleScroll = () => {
    if (!messageContainer.value || isLoading.value || isReachMaxLimit.value) return

    const { scrollTop, scrollHeight, clientHeight } = messageContainer.value
    const isBottom = scrollTop + clientHeight >= scrollHeight - 50 // 距离底部 50px 内触发

    if (isBottom && page.current < page.pages) {
        handleLoadMore()
    }
}


const upgradeAssistant = () => {
    const userStore = useUserStore()
    if (!userStore.isLogined) {
        userStore.openLoginModal()
        return
    }
    const rechargeStore = useRechargeStore()

    rechargeStore.openRechargeModal(RechargeModalTab.vip)

}

onMounted(() => {
    setupData()
    if (messageContainer.value) {
        messageContainer.value.addEventListener('scroll', handleScroll)
    }
})


onUnmounted(() => {
    if (messageContainer.value) {
        messageContainer.value.removeEventListener('scroll', handleScroll)
    }
})
</script>
