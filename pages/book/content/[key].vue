<template>
    <div class="flex flex-col h-full chapter-body" v-if="!isBookNotFound">
        <!-- 工具栏 -->
        <div class="flex-shrink-0 z-10 bg-gradient-to-r from-[#DBEAFB] to-[#E1E6FF] p-4 ">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div @click="handleBackToOutline" class="flex items-center pl-6 cursor-pointer">
                        <!-- <Left size="20" fill="#2551B5" /> -->
                        <img src="https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/editor-back.png"
                            alt="back" class="w-8 h-8">
                    </div>

                    <h1 class="text-xl font-semibold text-[#2551B5] text-[17px] max-w-[300px] overflow-hidden text-ellipsis whitespace-nowrap"
                        v-if="chapterStore.currentChapter && chapterStore.bookValue">
                        {{ chapterStore.bookValue.title }}
                    </h1>

                    <div class="bg-[#F5F7FF] text-[#2551B5] text-[17px] px-4 py-0.5 rounded-md whitespace-nowrap">
                        AI写书</div>
                </div>
                <div class="flex items-center space-x-4 overflow-hidden max-h-36">

                    <ClientOnly>
                        <RichEditorTool></RichEditorTool>
                        <div class="pl-20 pr-[20px]">
                            <a-dropdown>
                                <a class="text-[#333333] text-[15px] cursor-pointer whitespace-nowrap" @click.prevent>
                                    导出全文
                                </a>
                                <template #overlay>
                                    <a-menu @click="handlePressExport">
                                        <a-menu-item key="pdf">PDF</a-menu-item>
                                        <a-menu-item key="docx">Word</a-menu-item>
                                    </a-menu>
                                </template>
                            </a-dropdown>
                        </div>

                    </ClientOnly>
                </div>
                <div class="items-center hidden space-x-3 md:flex" ref="userProfileRef"
                    @mouseenter="handleUserProfileMouseEnter" @mouseleave="handleUserProfileMouseLeave">
                    <ClientOnly>
                        <UserAvatar />
                        <span
                            class="ml-0.5 text-sm text-gray-700 truncate max-w-[120px] text-ellipsis whitespace-nowrap overflow-hidden">
                            {{ nickname || '用户昵称' }}
                        </span>
                    </ClientOnly>
                </div>
            </div>
        </div>


        <!-- 内容区域 -->
        <div class="flex flex-1 h-full overflow-hidden">
            <!-- 左侧章节菜单 -->
            <div class="flex-shrink-0 w-2/7 max-w-[340px] h-full overflow-auto">
                <ChaptersMenu @select="handleChapterSelect" ref="chaptersMenuRef" />
            </div>

            <!-- 编辑区域 -->
            <div class="flex-1 w-full h-full min-w-[700px]">
                <BookMain></BookMain>
            </div>

            <!-- 右侧边栏 w-[440px]-->
            <div class=" w-2/7 max-w-[440px] min-w-[375px] flex-shrink-0 overflow-y-auto bg-[#F5F7FF]">
                <EditRightSidebar ref="editRightSidebarRef" />
            </div>
        </div>

        <BookPayModal v-if="chapterStore.showPayModal" v-model="chapterStore.showPayModal"></BookPayModal>

        <!-- Tour引导组件 -->
        <a-config-provider :locale="zhCN">
            <a-tour v-model:current="currentStep" :steps="steps" :open="tourVisible" @finish="onTourFinish"
                @close="onTourFinish" :prevButtonProps="{ style: { display: 'none' } }" />
        </a-config-provider>


        <!--  Popover -->
        <Teleport to="body">
            <Transition name="fade">
                <div v-if="upgradePopoverStore.isVisible" class="fixed z-[1112] shadow-xl" :style="popoverStyle"
                    @mouseenter="handleUserProfileMouseEnter" @mouseleave="handleUserProfileMouseLeave">
                    <UpgradePopover :isOpenNewWindow="true" :showShouldSwitchAccount="false"
                        @onOpenUserCenter="onOpenUserCenter" />
                </div>
            </Transition>
        </Teleport>
    </div>

    <div v-else>
        <div class="flex flex-col items-center justify-center h-full mt-8">
            <a-empty>
                <template #description>
                    <div>本书不存在或没有权限</div>
                    <a-button type="primary" @click="handleBackToBook" class="mt-4">去写书</a-button>
                </template>
            </a-empty>
        </div>
    </div>

</template>

<script setup lang="ts">
import UserAvatar from '@/components/Auth/UserAvatar.vue';
import RichEditorTool from '@/components/Book/common/RichEditorTool.vue';
import { useChapterStore } from '@/stores/chapter';
import { message, type MenuProps, type TourProps } from 'ant-design-vue';
import zhCN from 'ant-design-vue/es/locale/zh_CN';
import { AxiosError } from 'axios';
import { onBeforeUnmount, onMounted, onUnmounted } from 'vue';
import BookMain from '~/components/Book/BookMain.vue';
import BookPayModal from '~/components/Book/BookPayModal.vue';
import ChaptersMenu from '~/components/Book/edit/ChaptersMenu.vue';
import EditRightSidebar from '~/components/Book/edit/EditRightSidebar.vue';
import { UserService } from '~/services/user';
import { useTaskStore } from '~/stores/task';
import { useUpgradePopoverStore } from '~/stores/upgradePopover';
import { useUserStore } from '~/stores/user';
import type { Chapter } from '~/types/book';
import { BookService } from '~/utils/api/book';



const userStore = useUserStore()
// 定义页面元数据
definePageMeta({
    layout: 'book'
})

const route = useRoute()
const router = useRouter()
const chapterStore = useChapterStore()

const upgradePopoverStore = useUpgradePopoverStore()

const taskStore = useTaskStore()

const nickname = computed(() => {
    return userStore?.currentLoginInfo?.nickname || ''
})

const popoverStyle = {
    right: `30px`,
    top: `60px`,
    background: 'linear-gradient( 104deg, #E5F2FF 0%, #DEDBFF 100%)',
    borderRadius: '15px',
    boxShadow: '0px 1px 20px 0px #C7D6FE'
}


// Tour相关状态和方法
const tourVisible = ref(false);
const currentStep = ref(0);
const chaptersMenuRef = ref();
const editRightSidebarRef = ref();

const isBookNotFound = ref(false)

// Tour步骤定义
const steps = ref<TourProps['steps']>([
    {
        title: '编辑大纲',
        description: '在这里可以查看和编辑书籍的章节结构，点击编辑可以进行内容编辑。',
        target: () => chaptersMenuRef.value?.outlineMenuRef,
        placement: 'right',
    },
    {
        title: '参考资料库',
        description: '在此处添加文件到参考资料库，生成专著内容时会根据资料库内的资料进行搜索、使用等。',
        target: () => editRightSidebarRef.value?.fileAreaRef,
        placement: 'left',
    },
    {
        title: 'AI写作助手',
        description: `点击“生成本节点”，生成本节点引言/正式内容。\n点击“生成剩余全部节点”，生成全书剩余全部未创作内容。\n点击“图片生成”等按钮，进行多模态的写作`, target: () => editRightSidebarRef.value?.creativeAssistantRef,
        placement: 'left',
    }
]);

// 判断是否是用户首次访问
const isFirstVisit = () => {
    const visited = storage.get(StarloveConstants.keyOflocalStorage.bookEditorTourShown)
    return !visited;
};

// 完成引导
const onTourFinish = () => {
    tourVisible.value = false;
    // 在完成时将标记存入localStorage
    storage.set(StarloveConstants.keyOflocalStorage.bookEditorTourShown, 'true');
};

// 处理返回大纲页面
const handleBackToOutline = () => {
    router.push(`/create/history`)
}

// 处理返回大纲页面
const handleBackToBook = () => {
    router.push(`/book`)
}

// 处理章节选择
const handleChapterSelect = (chapter: Chapter) => {
    if (taskStore.isGenerating) {
        message.warning('正在生成章节内容，请等待完成后再切换章节')
        return
    }
    if (taskStore.isTraversing) {
        message.warning('正在批量生成章节内容，请等待完成后再切换章节')
        return
    }
    if (chapterStore.currentChapter?.key == chapter.key) {
        return
    }
    chapterStore.bookUploadTemplateFileList = []
    //TODO 切换章节
    chapterStore.loadChapter(chapter.key)

    // chapterStore.userEditCustomPrompt = ''
}


const handlePressExport: MenuProps['onClick'] = ({ key }) => {
    chapterStore.exportToWord(key.toString())
};

const loadBook = async () => {
    const bookKey = route.params.key.toString() || ''

    try {
        const book = await BookService.getBookInfo(bookKey)
        console.log('fetchBookInfo bookData', book)

        if (book) {
            if (book.flattened_chapters.length == 0) {
                router.push({
                    path: `/book/outline/${book.key}`,
                })
                return
            }

            if (book.user_id != userStore.currentLoginInfo?.id) {
                message.warning('您没有权限访问该专著')
                setTimeout(() => {
                    router.push('/book')
                }, 1000)
                return
            }

            chapterStore.currentBook = book
        }
    } catch (error) {
        console.error('获取书籍详情出错:', error)
        if (error instanceof AxiosError) {
            if (error.response?.status == 401) {
                router.push('/book')
            }

            if (error.response?.status == 404) {
                isBookNotFound.value = true
            }
        }
    }
}

const userProfileRef = ref<HTMLElement | null>(null)
const handleUserProfileMouseEnter = () => {
    if (userProfileRef.value instanceof HTMLElement) {
        const rect = userProfileRef.value.getBoundingClientRect()
        upgradePopoverStore.show(rect)
    }
}
const handleUserProfileMouseLeave = () => {
    upgradePopoverStore.hide()
}

const onOpenUserCenter = (event: Event) => {
    // 阻止事件冒泡
    event?.stopPropagation();
    // 先关闭 popover
    upgradePopoverStore.hide();
    // 使用 nextTick 确保状态更新后再跳转
    nextTick(() => {
        // router.push('/profile');
        window.open('/profile', '_blank')
    });
}

// 页面加载时获取数据
onMounted(async () => {

    if (!UserService.isLogined()) {
        router.push('/book')
        return
    }

    await loadBook()

    if (chapterStore.currentBook?.generating_chapter_key) {
        await chapterStore.loadChapter(chapterStore.currentBook?.generating_chapter_key || '')
    } else {
        await chapterStore.loadChapter(chapterStore.currentBook?.flattened_chapters[0].key || '')
    }

    // 等待DOM渲染完成后再判断是否显示引导
    nextTick(() => {
        // 如果是首次访问，显示引导
        if (isFirstVisit()) {
            // 延迟显示，确保DOM已完全加载和数据已经准备好
            setTimeout(() => {
                if (editRightSidebarRef.value?.referenceLibraryRef) {
                    tourVisible.value = true;
                    storage.set(StarloveConstants.keyOflocalStorage.bookEditorTourShown, 'true');
                }
            }, 1000);
        }
    });
})

onBeforeUnmount(() => {
    chapterStore.resetData()
    taskStore.resetData()
})

onUnmounted(() => {
    chapterStore.resetData()
    taskStore.resetData()
})

useHead({
    link: [
        {
            href: '//at.alicdn.com/t/c/font_4853281_u0u6jlfrt1g.css',
            rel: 'stylesheet',
        },
    ],
    script: [
        {
            src: '//at.alicdn.com/t/c/font_4853281_u0u6jlfrt1g.js'
        }
    ]
})
</script>

<style>
.ant-tour .ant-tour-inner .ant-tour-description {
    white-space: pre-line !important;

}
</style>