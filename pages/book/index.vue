<template>
  <div class="flex h-screen overflow-hidden">
    <!-- 移动端菜单开关按钮 -->
    <button @click="toggleLeftMenu" class="md:hidden fixed left-4 top-20 z-20 p-2 bg-white rounded-lg shadow-md">
      <ApplicationMenu theme="outline" size="24" fill="#666" />
    </button>
    <!-- 中间和右侧内容区域 -->
    <div class="flex-1 flex flex-col overflow-hidden">
      <!-- 中右两栏布局容器 -->
      <div class="flex flex-1 overflow-hidden" v-if="creatorData">
        <!-- 中间内容区域 - 论文写作表单 -->
        <div class="md:w-3/4 w-full overflow-y-auto border-r border-blue-100/50 flex flex-col">
          <!-- 顶部导航 -->
          <AppCreateFormTopNav :creator="creatorData?.creator" />

          <!-- 论文写作表单 -->
          <ClientOnly>

            <AppSubmissionForm :currentCode="creatorCode" :isSubmitLoading="isSubmitLoading" :creatorData="creatorData"
              @createOutline="onCreateOutline" ok-text="下一步" />
          </ClientOnly>

        </div>
        <!-- 右侧面板 -->
        <div class="hidden md:block md:w-1/4 overflow-y-auto">
          <AppDefaultSideBar :creatorData="creatorData" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { UserService } from '@/services/user'
import { ApplicationMenu } from '@icon-park/vue-next'
import { message } from 'ant-design-vue'
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { getCreatorsDetail } from '~/api/appCategory'
import { createSubmission, paySubmission } from '~/api/create'
import AppCreateFormTopNav from '~/components/Create/Paper/AppCreateFormTopNav.vue'
import AppDefaultSideBar from '~/components/Create/Paper/AppDefaultSideBar.vue'
import AppSubmissionForm from '~/components/Create/Paper/AppSubmissionForm.vue'
import type { CreatorsInfo } from '~/services/types/appMessage'
import { useOutlineModalStore } from '~/stores/outlineModal'
import { useRecentAppsStore } from '~/stores/recentApps'
import { useUserStore } from '~/stores/user'
import { BookService } from '~/utils/book/book'

// 使用OutlineModal store
const outlineModalStore = useOutlineModalStore()

const router = useRouter()
const creatorCode = 'book'
const currentCode = ref(creatorCode)

const creatorData = ref<CreatorsInfo>()

const leftPanelRef = ref()
const recentAppsStore = useRecentAppsStore()

const isSubmitLoading = ref(false)
const user = useUserStore()

const needSubmitFormData = ref()
const toggleLeftMenu = () => {
  leftPanelRef.value?.toggleMenu()
}

const config = useRuntimeConfig()
const { data: asyncData } = useAsyncData('fetchData', async () => {
  try {
    const controller = new AbortController()
    const timeout = setTimeout(() => controller.abort(), 8000) // 8秒超时

    const res = await $fetch(`${config.public.apiBase}/creator/getCreatorDetail?platform=${getPlatformNew()}&code=book`)
    clearTimeout(timeout)
    return res
  } catch (error) {
    console.error('获取首页数据失败:', error)
    // 可以根据需要处理错误，比如上报或提示
    return { success: false, code: null, result: {}, message: '请求失败' }
  }

}, {
  server: true, // 是否在服务端运行
  lazy: false,  // 延迟加载 (适合在用户操作后加载)
  immediate: true, // 是否在组件加载时立即执行
  default: () => ({ success: false, code: null, result: {}, message: '' })
})
const title = computed(() => `${asyncData.value?.result?.creator?.seoConfig?.title || seoConfigDefault.title}`)
const description = computed(() => `${asyncData.value?.result?.creator?.seoConfig?.description || seoConfigDefault.description}`)
const keywords = computed(() => `${asyncData.value?.result?.creator?.seoConfig?.keywords || seoConfigDefault.keywords}`)

useHead({
  title,
  meta: [
    {
      name: 'description',
      content: description,
      tagPriority: 'critical', // 提高优先级
    },
    {
      name: 'keywords',
      content: keywords,
      tagPriority: 'critical', // 提高优先级
    }
  ],
  script: [
    {
      type: 'application/ld+json',
      tagPriority: 'critical', // 提高优先级
      children: JSON.stringify({
        "@context": "https://schema.org",
        "@type": "SoftwareApplication",
        "name": "AI写作工具", "url": "https://xiaoin.com.cn/create",
        "description": "万能小in是AI长文写作的开创者，基于个人知识库实现个性化写作，专业、规范、准确，一键写作初稿，AI在线编辑改稿。支持150+写作应用模版，支持自定义篇幅、语言、文风等，文笔质量高，无AI痕迹。",
        "applicationCategory": "WritingApplication",
        "operatingSystem": "Web",
        "offers": {
          "@type": "Offer",
          "price": "10",
          "priceCurrency": "CNY"
        },
        "featureList": "AI写作,模板库,文风定制,多语言支持",
        "publisher": {
          "@type": "Organization",
          "name": "万能小in",
          "logo": {
            "@type": "ImageObject",
            "url": "https://static.xiaoin.cn/prod/logo.png", "width": 112,
            "height": 112
          }
        }
      }, null, 2)
    },
    {
      type: 'application/ld+json',
      tagPriority: 'critical',
      children: JSON.stringify([
        {
          "@context": "https://schema.org",
          "@type": "SoftwareApplication",
          "name": "万能小in AI智能写书软件",
          "mainEntityOfPage": {
            "@type": "WebPage",
            "@id": "https://xiaoin.com.cn/book"
          }
        },
        {
          "@type": "HowTo",
          "description": "三步快速生成完整书籍",
          "step": [
            {
              "@type": "HowToStep",
              "name": "输入主题",
              "text": "在输入框填写您的书籍主题关键词（如'AI写作指南'）"
            },
            {
              "@type": "HowToStep",
              "name": "设置参数",
              "text": "选择书籍类型、字数、语言风格"
            },
            {
              "@type": "HowToStep",
              "name": "生成大纲",
              "text": "AI自动生成目录并调整章节"
            }
          ],
          "url": "https://xiaoin.com.cn/book"
        }
      ], null, 2)
    },
  ]
})



const bookId = ref('')
const onCreateOutline = async (data: any) => {

  if (!UserService.isLogined()) {
    user.openLoginModal()
    return
  }

  if (data) {
    needSubmitFormData.value = data
  }
  await submitFormData()
}

const submitFormData = async () => {
  await onLoadOutline()
}
const handlePayment = async (submissionData: any) => {
  await sleep(1000)
  const paymentResponse = await paySubmission({
    submissionId: submissionData?.id,
  })

  if (!paymentResponse.ok) {
    message.error(paymentResponse.message || '支付失败，请重试')
    return {
      success: false,
      message: paymentResponse.message || '支付失败，请重试'
    }
  }

  return {
    success: true,
    data: paymentResponse.data
  }
}
const onLoadOutline = async () => {
  try {
    outlineModalStore.setCreateOutlineNow(true)
    isSubmitLoading.value = true
    // 创建书籍
    if (!bookId.value) {
      const params: any = {
        creatorCode: creatorCode,
        formData: {
          ...needSubmitFormData.value.formData,
        }
      }

      // console.log('createSubmission  params ==>', params)
      const res = await createSubmission(params)

      if (res.code == HTTP_STATUS.FAILED_SUBMIT_AGAIN) {

        message.error(res.message || '请勿重复提交')
        return
      }
      if (res?.code === HTTP_STATUS.MOBILE_NOT_BOUND) {
        // webUserBindPhoneVisible.value = true
        user.setShowPhoneBoundModal({
          status: BindPhoneModal.SHOW_BINDING
        })
        message.error('请先绑定手机号')
        return
      }
      if (!res.ok || !res.data) {
        message.error(res.message || '操作失败')
        return
      }
      if (!res.data.id || res.data.id == undefined || res.data.id == 'undefined') {
        message.error('写作失败，请重试')
        return
      }
      message.success('提交成功')

      await handlePayment(res.data)

      const bookParams = {
        ...res.data.formData.params,
        submission_id: res.data.id,
        title: res.data.formData.topic,
        type: res.data.formData.params?.type == '学术专著' ? 'paper' : 'book',
        description: '',
        status: res.data.status,
        user_id: res.data.userId,
      }
      // 把数据传给python
      const bookCreate = await BookService.createBook(bookParams)
      bookId.value = bookCreate?.key || ''
    }
    // TODO 将写作大纲的接口移动到/book/outline页面中
    // const newOutline = await generateOutline({
    //     book_key: bookId.value
    // })
    console.error('bookId.value = ', bookId.value)
    router.push({
      path: `/book/outline/${bookId.value}`,
    })
  } catch (error) {
    // console.error(error)
    message.error('生成大纲失败')

  } finally {

    isSubmitLoading.value = false
  }
}

const loadCreatorsDetailAndReport = async () => {
  const res = await getCreatorsDetail({ code: currentCode.value })
  if (!res.ok) {
    message.error(res.message || '加载失败')
    return
  }
  if (!res.data) {
    return
  }
  creatorData.value = res.data
  console.log('ppt  res creatorData.value = ', res)
  // 获取当前时间，并以指定格式格式化
  const currentDateTime = new Date()
  const formattedDateTime =
    currentDateTime.toISOString().split('T')[0] + ' ' + currentDateTime.toTimeString().split(' ')[0]

  recentAppsStore.addToRecentList({ ...creatorData.value.creator, avatar: creatorData.value.creator.icon })


  UserService.reportUseApp({
    ...creatorData.value.creator,
    type: 'creator',
    avatar: creatorData.value.creator.icon || '',
    lastTime: formattedDateTime,
  })
  // console.log(' creatorData.value = ', creatorData.value)
}

onMounted(() => {
  loadCreatorsDetailAndReport()
})
</script>

<style scoped>
@keyframes slide {
  from {
    transform: translateY(0);
  }

  to {
    transform: translateY(-50%);
  }
}

.animate-slide {
  animation: slide 20s linear infinite;
}
</style>
