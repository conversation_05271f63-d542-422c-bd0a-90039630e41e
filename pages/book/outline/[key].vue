<template>


  <template v-if="!isBookNotFound">
    <a-layout class="min-h-screen bg-gray-50">
      <a-spin :spinning="isLoading" class="global-loading-spin">
        <a-layout-content class="relative py-6 min-h-64 overflow-x-hidden ">
          <!-- 大纲内容区域 -->
          <div class="px-4 mx-auto text-center max-w-[900px] ">

            <!-- 流式大纲显示区域 -->
            <a-spin :spinning="!isLoading && !outlineLoading && outlineContent.length == 0 && outlineTree.length == 0">
              <div v-if="outlineTree.length == 0"
                class="w-full mx-[20px] mt-12 bg-white rounded-lg p-4 max-h-[600px] min-h-[300px] overflow-y-auto"
                ref="outlineContainer">
                <div class="px-3 py-4 text-[15px] text-left w-full outline-content">
                  <span v-html="outlineContent"></span>
                  <span v-if="outlineLoading" class="typing-cursor"></span>
                </div>
              </div>
            </a-spin>

            <template v-if="outlineTree.length > 0">

              <div class="flex justify-center">
                <div class="w-[500px]">
                  <BookCreateStep :step="1"></BookCreateStep>
                </div>
              </div>

              <div class="w-full mt-6 mx-[20px] pb-2">
                <div class="flex flex-wrap justify-start items-center gap-1 w-full outline-title-desc">
                  <span class="whitespace-nowrap">&middot; 操作说明：点</span>
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-500 flex-shrink-0"
                    viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd"
                      d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z"
                      clip-rule="evenodd" />
                  </svg>
                  <span class="whitespace-nowrap">新增同级和新增子级，最多支持输入四级子标题；</span>
                  <div class="flex items-center justify-start cursor-pointer" @click="showOutlineVideoModal = true">
                    <a class="video whitespace-nowrap">视频演示</a>
                    <img class="w-[23px] h-[18px] pl-[5px] cursor-pointer"
                      src="https://static-1256600262.file.myqcloud.com/h5/icon/video-icon.png" />
                  </div>
                </div>
              </div>
              <!-- 章节列表 -->
              <div class="rounded-lg chapter-list ">
                <ChapterOutline code="book" :language="book?.language" :outline-tree="outlineTree" :maxLevel="4"
                  @update="update" @focus="onFocus" @blur="onBlur" @operate="operate" />
              </div>
            </template>

            <a-empty v-if="isLoadOutlineError" description="获取数据失败">
              <a-button type="primary" @click="reloadOutline">
                重新获取大纲
              </a-button>
            </a-empty>
          </div>

          <!-- 下方按钮区域 - 不再使用fixed定位 -->
          <div class="mt-8 flex justify-center" v-if="!isLoading && book && outlineTree.length > 0">
            <!-- <a-button class="flex items-center" size="large" type="primary" :loading="submitLoading"
            @click="handlePressGotoContent"> -->
            <!-- <template #icon>
              <ArrowRightOutlined />
            </template> -->
            <!-- 下一步
          </a-button> -->
            <button @click="handlePressGotoContent"
              class="relative px-16 py-3 overflow-hidden text-base font-medium text-white bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 rounded-xl group">
              <span class="relative z-10 inline-flex items-center">
                <!-- <loading v-if="submitLoading" theme="outline" size="20" fill="#fff" class="mr-2 animate-spin" /> -->
                <a-spin :spinning="submitLoading" class="flex items-center white-spin mr-2"></a-spin>
                下一步
              </span>
            </button>
          </div>
        </a-layout-content>
      </a-spin>
    </a-layout>

    <OutlineVideoTutorialModal v-model="showOutlineVideoModal" :maxLevel="3"></OutlineVideoTutorialModal>
  </template>
  <div v-else>
    <div class="flex flex-col items-center justify-center h-full mt-8">

      <a-empty>
        <template #description>
          <div>本书不存在或没有权限</div>

          <a-button type="primary" @click="handleBackToBook" class="mt-4">去写书</a-button>

        </template>
      </a-empty>

    </div>
  </div>

</template>

<script setup lang="ts">
import ChapterOutline from '@/components/Create/PPT/ChapterOutline.vue';
import { fetchEventSource } from '@microsoft/fetch-event-source';
import { message, Modal } from 'ant-design-vue';
import { AxiosError } from 'axios';
import markdownit from 'markdown-it';
import { onMounted, onUnmounted, ref } from 'vue';
import { UserService } from '~/services/user';
import { useOutlineModalStore } from '~/stores/outlineModal';
import { useUserStore } from '~/stores/user';
import type { Chapter } from '~/types/book';
import { BookService } from '~/utils/api/book';
import { API_CONFIG } from '~/utils/book/config';

// 检查字符串是否为有效的JSON
const isJSON = (str: string): boolean => {
  try {
    JSON.parse(str);
    return true;
  } catch (e) {
    return false;
  }
};

interface TreeNode {
  level: number;
  name: string;
  key: string;
  children?: TreeNode[];
}

interface Book {
  key: string
  title: string
  type: 'book' | 'paper'
  description: string
  createdAt: string
  updatedAt: string
  chapters: Chapter[],
  language?: string
}

const route = useRoute()
const router = useRouter()


const userStore = useUserStore()
const showOutlineVideoModal = ref(false)


// 使用OutlineModal store
const outlineModalStore = useOutlineModalStore()

const outlineTree = ref<any[]>([])

let ctrl: AbortController | null = null
const outlineLoading = ref(false)
const outlineContent = ref('')

const md = markdownit({
  html: true, // 启用 HTML 标签
  linkify: true, // 自动将 URL 转换为链接
  breaks: true,
  xhtmlOut: true,
  langPrefix: 'language-',
  typographer: true,
})

// 获取大纲容器的DOM引用
const outlineContainer = ref<HTMLElement | null>(null)

const book = ref<Book | null>(null)

const isGeneratingOutline = ref(false)

const isLoadOutlineError = ref(false)

const isLoading = ref(true)
const submitLoading = ref(false)

const isBookNotFound = ref(false)
let bookKey = ''

const reloadOutline = () => {
  isLoadOutlineError.value = false
  outlineTree.value = []
  outlineContent.value = ''
  outlineLoading.value = true

  fetchBookInfo()

}

// 获取著作信息
const fetchBookInfo = async () => {
  try {
    if (!UserService.isLogined()) {
      userStore.openLoginModal()
      return
    }
    if (!bookKey) {
      message.error('书籍ID不能为空')
      return
    }

    isLoading.value = true
    // 获取书籍信息
    const bookData = await BookService.getBookInfo(bookKey)

    if (!bookData) {
      return
    }

    if (bookData.user_id != userStore.currentLoginInfo?.id) {
      message.warning('您没有权限访问该专著')
      setTimeout(() => {
        router.push('/book')
      }, 1000)
      return
    }

    book.value = {
      ...bookData,
      key: bookData.key,
      title: bookData.title,
      type: bookData.type,
      description: bookData.description || '',
      createdAt: bookData.createdAt,
      updatedAt: bookData.updatedAt,
      chapters: bookData.flattened_chapters,
      language: bookData.language
    }
    const params: any = {
      book_key: bookKey,
    }

    // 获取大纲信息
    const outlineData = await BookService.getBookOutline(params)

    if (outlineData.length > 0) {
      isLoading.value = false
      outlineLoading.value = false
      outlineTree.value = outlineData
    } else {
      isLoading.value = false
      outlineLoading.value = true
      outlineContent.value = ''
      invokeGenerateOutline()
    }

  } catch (error) {

    console.error(error)
    isLoadOutlineError.value = true


    if (error instanceof AxiosError) {
      if (error.response?.status == 401) {
        router.push('/book')
      }

      if (error.response?.status == 404) {
        isBookNotFound.value = true
      }
    }
  } finally {
    isLoading.value = false
  }
}

const invokeGenerateOutline = async () => {
  try {
    ctrl = new AbortController() // 创建AbortController实例，以便中止请求
    const url = `${API_CONFIG.getBaseUrl()}/api/sse/auto_generate_outline/`;
    let content = outlineContent.value

    fetchEventSource(url, {
      method: 'POST',
      body: JSON.stringify({ book_key: bookKey }),
      headers: {
        'Content-Type': 'application/json;',
      },
      credentials: 'include',
      openWhenHidden: true,
      signal: ctrl.signal, // AbortSignal

      onopen(response) {
        message.loading('大纲生成中，请勿关闭或刷新网页...', 0)
        return new Promise((resolve) => {
          resolve()
        })
      },
      onmessage(e) {

        if (isJSON(e.data)) {
          const data = JSON.parse(e.data)
          outlineLoading.value = true
          if (e.event == 'error') {
            outlineLoading.value = false
            isLoadOutlineError.value = true
            message.destroy()
            return
          }
          // 返回全量的大纲数据，数组
          if (e.event == 'full') {

            outlineLoading.value = false
            message.destroy()
            outlineTree.value = data.outline
            return
          }
          if (e.event == 'chunk') {
            content += data.content
            outlineContent.value = md.render(content)

            // 添加自动滚动逻辑，在下一个事件循环中执行
            setTimeout(() => {
              if (outlineContainer.value) {
                outlineContainer.value.scrollTop = outlineContainer.value.scrollHeight
              }
            }, 0)
          }
          // 返回全量的markdown
          if (e.event == 'parsing') {

          }
        }
      },
      onerror(error) {
        // console.log('错误 ==>', error)
        ctrl?.abort()
        outlineLoading.value = false
        isLoadOutlineError.value = true
        Modal.confirm({
          title: '大纲生成过程中发生错误，请刷新页面',
          okText: '立即刷新',
          okButtonProps: {
            type: 'primary',
            danger: true
          },
          onOk: () => {
            window.location.reload()
          }
        })
        message.destroy()
        throw error // 直接抛出错误，避免反复调用
      },
      onclose() {
        outlineLoading.value = false
        message.destroy()
      }
    })
  } catch (error) {
    outlineLoading.value = false
    isLoadOutlineError.value = true
    message.destroy()
  } finally {
    outlineLoading.value = false
    message.destroy()
  }
}


//type: 1、向上新增。2、向下新增。3、增加子级
const update = (type: number) => {
}
const onFocus = (value: any) => {

}
const onBlur = (chapter: any, index: number) => {

}
const operate = (children: any, idx: number, type: number) => {

  update(type)
}


// 检查所有标题是否为空
const checkEmptyTitles = (nodes: any[]): { isEmpty: boolean; path: string } => {
  for (let i = 0; i < nodes.length; i++) {
    const node = nodes[i]
    // 检查当前节点标题
    if (!node.name || node.name.trim() === '') {
      return {
        isEmpty: true,
        path: `第 ${i + 1} 章节`
      }
    }
    // 递归检查子节点
    if (node.children && node.children.length > 0) {
      const result = checkEmptyTitles(node.children)
      if (result.isEmpty) {
        return {
          isEmpty: result.isEmpty,
          path: `第 ${i + 1} 章节 > ${result.path}`
        }
      }
    }
  }
  return { isEmpty: false, path: '' }
}

// 进入下一步（编辑页面）
const handlePressGotoContent = async () => {
  if (!UserService.isLogined()) {
    userStore.openLoginModal()
    return
  }
  if (!book.value || !outlineTree.value.length) {
    message.warning('请先添加章节')
    return
  }

  const titleCheck = checkEmptyTitles(outlineTree.value)
  if (titleCheck.isEmpty) {
    message.error(`大纲中存在空标题: 请完善后再继续`)
    return
  }

  submitLoading.value = true

  try {

    const res = await BookService.updateOutline({
      book_key: book.value.key,
      outline: outlineTree.value
    })

    if (res) {
      message.success('大纲保存成功')
      router.push(`/book/content/${book.value.key}`)
    }
  } catch (error) {
    console.error('保存大纲失败:', error)
    message.error('保存大纲失败')
  } finally {
    submitLoading.value = false
  }
}


// 处理返回大纲页面
const handleBackToBook = () => {
  router.push(`/book`)
}

// 页面加载时获取著作信息
onMounted(() => {

  if (!UserService.isLogined()) {
    router.push('/book')
    return
  }

  bookKey = route.params.key as string
  fetchBookInfo()
})

onBeforeUnmount(() => {
  message.destroy()
})

onUnmounted(() => {
  outlineModalStore.setCreateOutlineNow(false)
})
</script>

<style scoped>
/* 添加步骤样式 */
:deep(.ant-steps-item-title) {
  cursor: pointer;
}

:deep(.ant-page-header) {
  padding-left: 24px;
  padding-right: 24px;
}

.chapter-list {
  max-height: calc(100vh - 240px);
  overflow-y: auto;
}

/* 自定义滚动条样式 */
.chapter-list::-webkit-scrollbar {
  width: 6px;
}

.chapter-list::-webkit-scrollbar-thumb {
  background-color: #d9d9d9;
  border-radius: 3px;
}

.chapter-list::-webkit-scrollbar-track {
  background-color: #f0f0f0;
}

/* 添加标签样式 */
:deep(.ant-tag) {
  padding: 4px 8px;
  font-size: 14px;
  transition: all 0.3s;
}

:deep(.ant-tag:hover) {
  border-color: #1890ff;
  color: #1890ff;
}

/* Markdown 标题样式增强 */
:deep(h1) {
  font-size: 1.5em;
  font-weight: bold;

}

:deep(h2) {
  font-size: 1.3em;
  font-weight: 500;
  margin-left: 1.5em;

}

:deep(h3) {
  font-size: 1.1em;
  font-weight: 400;
  margin-left: 3em;

}

:deep(h4) {
  font-size: 1em;
  font-weight: 400;
  margin-left: 3em;

}

:deep(ul),
:deep(ol) {
  padding-left: 2em;
}

/* 打字机闪烁光标效果 */
@keyframes cursor-blink {
  0% {
    opacity: 0;
  }

  50% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

.typing-cursor {
  display: inline-block;
  width: 2px;
  height: 1.2em;
  background-color: #333;
  margin-left: 1px;
  vertical-align: text-bottom;
  animation: cursor-blink 0.7s infinite;
  position: relative;
  top: 1px;
}

/* 确保光标和文本在同一行 */
.outline-content {
  display: inline-block;
  white-space: pre-wrap;
  word-break: break-word;
}

.white-spin :deep(.ant-spin-dot-item) {
  background-color: white !important;
}
</style>