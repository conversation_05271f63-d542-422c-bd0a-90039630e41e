<template>

    <div class="flex flex-col h-full bg-white">
        <!-- 个人中心部分 -->
        <div class="relative flex h-full overflow-y-hidden bg-gray-50">

            <!-- 主内容区域 - 添加响应式 padding -->
            <div class="relative flex-1">
                <NuxtPage></NuxtPage>
            </div>
            <ReferenceSource />
        </div>

    </div>
</template>

<script lang="ts" setup>
import { useChatStore } from '@/stores/chat';
import { onMounted, ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import ReferenceSource from '~/components/Chat/ReferenceSource.vue';
import { UserService } from '~/services/user';
import { useUserStore } from '~/stores/user';

useHead({
    title: '基于知识库的AI问答助手-万能小in官网',
    meta: [
        {
            name: 'keywords',
            content: 'AI问答,智能问答,AI问答助手,AI提问,在线问答,AI自助问答,智能提问',
            tagPriority: 'critical', // 提高优先级
        },
        {
            name: 'description',
            content: '万能小in的提问功能，基于知识库实现 AI 回答的准确性和个性化，可满足各类场景的知识问答和资料检索需求，支持AI提问、图片提问、语音提问等。',
            tagPriority: 'critical', // 提高优先级
        },
    ],
})

const chat = useChatStore()
// 获取路由id的值
const route = useRoute()
const routeId = route.params.id || ''
const currentSessionId = ref(`${routeId}`)
const router = useRouter()
const handleChatChange = (item: { sessionId: string }) => {
    // 使用 shallow 参数来避免页面刷新
    currentSessionId.value = item.sessionId
    chat.setIsNewSessionId(false)
    router.replace(`/chat/${item.sessionId}`)
}

const handleOpenNewQueation = () => {
    currentSessionId.value = ''
    // const sessionId = getCurrentTimestampString()
    chat.setIsNewSessionId(true)
    setTimeout(() => {
        router.replace(`/chat`)
    }, 300)
}
const isLogined = ref(false)
const handleOpenRecords = () => {
    router.push({ path: `/profile/questions` })
}

// 添加侧边栏状态控制
const showSidebar = ref(false)

// 切换侧边栏显示状态
const toggleSidebar = () => {
    showSidebar.value = !showSidebar.value
}
const user = useUserStore()
watch(() => user.isLogined, async (_isLogined) => {
    if (_isLogined) {
        console.log('用户登录成功，重新加载数据')
        isLogined.value = _isLogined
    }
})

onMounted(() => {
    isLogined.value = UserService.isLogined()
})

// 在路由变化时自动关闭移动端侧边栏
watch(() => route.path, () => {
    showSidebar.value = false
})
</script>

<style scoped>
/* 自定义滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background-color: rgba(156, 163, 175, 0.5);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background-color: rgba(156, 163, 175, 0.7);
}

/* 添加媒体查询样式 */
@media (max-width: 768px) {
    .overflow-y-auto {
        -webkit-overflow-scrolling: touch;
    }
}
</style>