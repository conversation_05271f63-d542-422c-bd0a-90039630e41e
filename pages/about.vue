<template>
    <div class="h-full flex flex-col">
        <!-- 页面标题区 -->
        <div
            class="m-3 bg-white/80 backdrop-blur-sm rounded-xl px-3 sm:px-5 py-2 border border-blue-700/20 mb-3 md:mb-4">
            <div class="flex items-center">
                <div
                    class="w-10 h-10 bg-blue-700 rounded-xl flex items-center justify-center mr-3 md:mr-4 shadow-inner">
                    <component :is="getCurrentMenuIcon" theme="outline" size="24" md:size="32" fill="#FFFFFF" />
                </div>
                <div>
                    <h1 class="text-lg font-bold text-blue-700">
                        {{ getCurrentMenuTitle }}
                    </h1>
                    <p class="text-sm text-gray-500">{{ getCurrentMenuDescription }}</p>
                </div>
            </div>
        </div>

        <!-- 主要内容区 - 添加滚动 -->
        <div class="flex-1 min-h-0 overflow-y-auto">
            <div class="m-3 bg-white space-y-6 border border-gray-100 rounded-2xl p-6">

                <!-- 产品特色卡片 -->
                <section class="space-y-3">
                    <h2 class="text-base text-gray-800 font-bold">产品特色</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div v-for="(feature, index) in features" :key="index"
                            class="p-4 bg-white/80 backdrop-blur-sm border border-blue-100 rounded-xl">
                            <div class="flex items-center gap-3 mb-2">
                                <div class="w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center">
                                    <component :is="feature.icon" theme="outline" size="20" class="text-blue-600" />
                                </div>
                                <h3 class="text-sm text-gray-800">{{ feature.title }}</h3>
                            </div>
                            <p class="text-gray-600 text-sm leading-relaxed ml-10">{{ feature.description }}</p>
                        </div>
                    </div>
                </section>

                <!-- 团队介绍卡片 -->
                <section class="space-y-3">
                    <h2 class="text-base text-gray-800 font-bold">我们的团队</h2>
                    <div class="text-gray-700 text-sm leading-relaxed">
                        团队毕业于北大等知名高校，来自阿里、京东等一线互联网大厂，具备业内领先的算法能力、产品能力、研发水平，已获得千万级别天使投资。
                    </div>
                </section>

                <section class="space-y-3">
                    <h2 class="text-base text-gray-800 font-bold">联系我们</h2>
                    <!-- 联系方式卡片 -->
                    <div class="rounded-2xl p-6 space-y-3 border border-blue-100">
                        <div v-for="(contact, index) in contactInfo" :key="index"
                            class="flex items-center gap-3 text-gray-700 hover:text-blue-600 transition-colors text-sm">
                            <component :is="contact.icon" theme="outline" size="18" />
                            <span>{{ contact.text }}</span>
                        </div>
                    </div>
                </section>

                <!-- 版权信息 -->
                <footer class="">
                    <div class="flex flex-col space-y-2">
                        <template v-if="isXiaoin">
                            <a class="text-xs text-gray-600" href="https://beian.miit.gov.cn/"
                                target="_blank">沪ICP备2024049133号-1</a>
                            <a class="text-xs text-gray-600"
                                href="https://www.beian.gov.cn/portal/registerSystemInfo?recordcode=31010402333814"
                                target="_blank">
                                沪公网安备：31010402333814号
                            </a>
                        </template>
                        <template v-else>
                            <a class="text-xs text-gray-600" href="https://beian.miit.gov.cn/"
                                target="_blank">沪ICP备20022513号-6</a>
                            <a class="text-xs text-gray-600"
                                href="https://www.beian.gov.cn/portal/registerSystemInfo?recordcode=31010402333815"
                                target="_blank">
                                沪公网安备：31010402333815号
                            </a>
                            <span class="text-xs text-gray-600">网信算备：310115124334401240013号</span>
                            <span class="text-xs text-gray-600">上线编号：Shanghai-WanNengXiaoin-20240829S0025</span>
                            <span class="text-xs text-gray-600">备案号：Shanghai-Xiaoin-202502050038</span>
                        </template>
                    </div>
                </footer>
            </div>
        </div>
    </div>

</template>

<script setup lang="ts">
import { isXiaoinNew } from '@/utils/utils';
import { Brain, Edit, Info, Mail, MessageOne, NewspaperFolding, Phone, World } from '@icon-park/vue-next';
import { ref } from 'vue';
import { UserService } from '~/services/user';

const isLoggedIn = ref(false)
const url = useRequestURL().href
const isXiaoin = isXiaoinNew(url)
const app = useApp()
const config = useRuntimeConfig()
const version = ref<string | undefined>(config.public.version)
const handleAbout = (url: string) => {
    window.open(url, '_blank')
}

// 菜单配置
const menuItems = [
    { key: 'about', label: '关于我们', icon: Info },
]

// 获取当前菜单信息
const currentMenuItem = computed(() => {
    return menuItems[0]
})

const getCurrentMenuTitle = computed(() => currentMenuItem.value.label)
const getCurrentMenuIcon = computed(() => currentMenuItem.value.icon)
const getCurrentMenuDescription = ref('了解我们的故事')

onMounted(() => {
    isLoggedIn.value = UserService.isLogined()
})

const features = [
    {
        icon: Brain,
        title: '我的知识库：',
        description:
            '基于个人知识文档的AI导读、思维导图、翻译、提问、笔记等文档学习，让知识的学习、整理变得轻松高效。',
    },
    {
        icon: Edit,
        title: '初稿写作：',
        description:
            '几分钟数万字，格式规范、内容专业，上百种写作应用模版，支持基于个人知识库的个性化写作，AI长文写作的开创者。',
    },
    {
        icon: MessageOne,
        title: '检索提问：',
        description:
            '实现联网提问+个人知识库提问，满足各类场景的知识问答和资料检索需求，实现AI回答的准确性和个性化。',
    },
    {
        icon: NewspaperFolding,
        title: '资讯导读：',
        description:
            '自动抓取当日高质量内容信息，AI导读+原文参考，支持将资讯一键添加到个人知识库，基于知识库的个性化推荐，让你不错过每一条价值信息。',
    },
]

const contactInfo = [
    {
        icon: Mail,
        text: '商务合作：<EMAIL>',
    },
    {
        icon: World,
        text: '联系电话：021-61734090',
    },
    {
        icon: Phone,
        text: '公司地址：上海市徐汇区丰谷路315弄24号1-3层',
    },
]

// SEO 优化
useHead({
    title: '关于我们-万能小in官网',
    meta: [
        {
            name: 'keywords',
            content: '万能小in介绍,AI工具介绍,品牌故事',
            tagPriority: 'critical',
        },
        {
            name: 'description',
            content: '万能小in是一款基于个人知识库的AI生产力工具，致力于让每个人拥有自己的AI外脑。团队毕业于北大、山大等知名高校，来自阿里等一线互联网大厂，具备业内领先的算法能力、产品能力、研发水平，已获得千万级别天使投资。',
            tagPriority: 'critical',
        }
    ],
});

</script>
