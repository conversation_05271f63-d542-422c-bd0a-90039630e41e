<template>
    <div class="min-h-screen bg-gray-50">
        <!-- 头部区域 -->
        <div class="bg-white/80 rounded-xl px-4 py-6 border border-blue-700/20 m-4">
            <div class="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-4">
                <!-- 标题部分 -->
                <div class="flex items-center">
                    <div
                        class="w-12 h-12 bg-gradient-to-br from-blue-700 to-indigo-600 rounded-xl flex items-center justify-center mr-4 shadow-lg">
                        <write theme="filled" size="28" fill="#FFFFFF" />
                    </div>
                    <div>
                        <h1
                            class="text-xl font-medium text-transparent bg-gradient-to-r from-blue-700 to-indigo-700 bg-clip-text">
                            写作宝典</h1>
                        <p class="text-sm text-gray-500">文章详情</p>
                    </div>
                </div>

                <!-- <UserInfoArea /> -->
            </div>
        </div>

        <!-- 主体内容区域 -->
        <div class="max-w-4xl mx-auto px-4 py-8">
            <div v-if="loading" class="text-center py-8">
                <div
                    class="inline-block animate-spin rounded-full h-6 w-6 border-3 border-blue-500 border-t-transparent">
                </div>
            </div>

            <div v-else-if="error" class="text-center py-8">
                <div class="text-red-500">{{ error }}</div>
            </div>

            <template v-else>
                <!-- 文章内容卡片 -->
                <div v-if="post" class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-6">
                    <!-- 文章标题 -->
                    <h1 class="text-2xl font-semibold text-gray-900 mb-4">{{ post.title.rendered }}</h1>

                    <!-- 文章元信息 -->
                    <div class="flex items-center text-sm text-gray-500 mb-6">
                        <calendar theme="outline" size="16" class="mr-1" />
                        <span>发布于 {{ formatDate(post.date) }}</span>
                    </div>

                    <!-- 文章内容 -->
                    <div class="prose prose-blue max-w-none" v-html="post.content.rendered"></div>
                </div>

                <!-- 返回按钮 -->
                <div class="text-center">
                    <button @click="router.back()"
                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <left theme="outline" size="16" class="mr-1" />
                        返回
                    </button>
                </div>
            </template>
        </div>
    </div>
</template>

<script setup lang="ts">
import { Calendar, Left, Write } from '@icon-park/vue-next';

const route = useRoute();
const router = useRouter();

interface Post {
    id: number;
    title: {
        rendered: string;
    };
    content: {
        rendered: string;
    };
    date: string;
}

const post = ref<Post | null>(null);
const loading = ref(true);
const error = ref('');

// WordPress API URL
const WP_API_URL = 'https://www.xiaoin.com.cn/wp-json/wp/v2';

// 格式化日期
const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN');
};

// 获取文章详情
const fetchPost = async () => {
    try {
        loading.value = true;
        const response = await fetch(
            `${WP_API_URL}/posts/${route.params.id}?_fields=id,title,content,date`,
            {
                headers: {
                    'Accept': 'application/json',
                }
            }
        );

        if (!response.ok) throw new Error('获取文章详情失败');
        post.value = await response.json();
    } catch (err: any) {
        error.value = err.message;
    } finally {
        loading.value = false;
    }
};

onMounted(() => {
    fetchPost();
});

definePageMeta({
    title: '万能小in - 文章详情'
});
</script>

<style>
/* 确保文章内容中的图片响应式显示 */
.prose img {
    max-width: 100%;
    height: auto;
}
</style>
