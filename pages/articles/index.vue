<template>
    <div class="min-h-screen bg-gray-50">
        <!-- 头部区域 -->
        <div class="bg-white/80 rounded-xl px-4 py-6 border border-blue-700/20 m-4">
            <div class="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-4">
                <!-- 标题部分 -->
                <div class="flex items-center">
                    <div
                        class="w-12 h-12 bg-gradient-to-br from-blue-700 to-indigo-600 rounded-xl flex items-center justify-center mr-4 shadow-lg">
                        <write theme="filled" size="28" fill="#FFFFFF" />
                    </div>
                    <div>
                        <h1
                            class="text-xl font-medium text-transparent bg-gradient-to-r from-blue-700 to-indigo-700 bg-clip-text">
                            写作宝典</h1>
                        <p class="text-sm text-gray-500">文章列表</p>
                    </div>
                </div>

                <!-- <UserInfoArea /> -->
            </div>
        </div>

        <!-- 主体内容区域 -->
        <div class="flex flex-row h-[calc(100vh-116px)]">
            <!-- 左侧分类栏 -->
            <div class="w-80 border-r border-gray-200 bg-white">
                <div v-if="loadingCategories" class="py-3 text-center">
                    <div
                        class="inline-block animate-spin rounded-full h-5 w-5 border-3 border-blue-500 border-t-transparent">
                    </div>
                </div>
                <div v-else-if="categoryError" class="text-red-500 text-xs px-1.5">
                    {{ categoryError }}
                </div>
                <div v-else class="grid grid-cols-3 gap-1 p-3 overflow-y-auto h-full">
                    <button v-for="category in categories" :key="category.id" @click="handleCategoryClick(category.id)"
                        class="border border-gray-200 text-center px-1.5 py-0.5 rounded text-gray-600 hover:bg-gray-50 transition-colors text-sm"
                        :class="{ 'bg-blue-50 text-blue-600 font-medium': selectedCategory === category.id }">
                        <div class="truncate">{{ category.name }}</div>
                    </button>
                </div>
            </div>

            <!-- 右侧文章列表 -->
            <div class="flex-1 flex flex-col">
                <div class="flex-1 overflow-y-auto p-6">
                    <div v-if="loading" class="text-center py-8">
                        <div
                            class="inline-block animate-spin rounded-full h-6 w-6 border-3 border-blue-500 border-t-transparent">
                        </div>
                    </div>

                    <div v-else-if="error" class="text-center py-8 text-red-500 text-sm">
                        {{ error }}
                    </div>

                    <div v-else-if="posts.length === 0" class="text-center py-8 text-gray-500 text-sm">
                        该分类下暂无文章
                    </div>

                    <template v-else>
                        <div class="space-y-4">
                            <div v-for="post in posts" :key="post.id"
                                class="bg-white p-4 rounded-lg shadow-sm hover:shadow transition-shadow border border-gray-100">
                                <h2 class="text-base font-medium text-gray-800 mb-2">
                                    <NuxtLink :to="`/articles/${selectedCategoryDescription}/${post.id}`"
                                        class="hover:text-blue-600 transition-colors">
                                        {{ post.title.rendered }}
                                    </NuxtLink>
                                </h2>
                                <div class="text-xs space-x-4">
                                    <span class="text-gray-400">发布日期: {{ formatDate(post.date) }}</span>
                                    <!-- <span class="text-blue-500">分类: {{ post.categoryDescription }}</span> -->
                                </div>
                            </div>
                        </div>
                    </template>
                </div>

                <!-- 分页器 -->
                <div class="flex-shrink-0 border-t border-gray-100 bg-white p-t-0 pb-8">
                    <Pagination v-model:current="pagination.pageNo" :total-pages="pagination.pages"
                        :total="pagination.total" @change="handlePageChange" />
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { Write } from '@icon-park/vue-next';
import Pagination from '~/components/Pagination.vue';

interface Category {
    id: number
    name: string
    count: number,
    description: string
}

interface Post {
    id: number
    title: {
        rendered: string
    }
    date: string
    link: string
    categories: number[]
    categoryDescription?: string
}

interface PaginationState {
    pageNo: number
    pages: number
    total: number
    pageSize: number
}

const posts = ref<Post[]>([])
const categories = ref<Category[]>([])
const loading = ref(true)
const loadingCategories = ref(true)
const error = ref('')
const categoryError = ref('')
const selectedCategory = ref(0)
const selectedCategoryDescription = ref('')
const route = useRoute()
const router = useRouter()

// 从URL参数中获取初始值
const initFromRoute = () => {
    const categoryId = Number(route.query.category) || 0
    const page = Number(route.query.page) || 1
    selectedCategory.value = categoryId
    pagination.value.pageNo = page
    return { categoryId, page }
}

const pagination = ref<PaginationState>({
    pageNo: Number(route.query.page) || 1,
    pages: 1,
    total: 0,
    pageSize: 10
})

// WordPress站点URL
const WP_API_URL = 'https://www.xiaoin.com.cn/wp-json/wp/v2'

// 格式化日期
const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN')
}

// 获取文章分类
const fetchCategories = async () => {
    try {
        loadingCategories.value = true
        const response = await fetch(`${WP_API_URL}/categories?_fields=id,name,count,description,meta&per_page=100`)
        if (!response.ok) throw new Error('获取分类失败')
        categories.value = await response.json()

        // 只有在URL没有指定分类时，才自动选择第一个分类
        if (categories.value.length > 0 && !route.query.category) {

        }

        handleCategoryClick(categories.value[0].id)
    } catch (err: any) {
        categoryError.value = err.message
    } finally {
        loadingCategories.value = false
    }
}

// 获取文章列表
const fetchPosts = async (categoryId = 0) => {
    try {
        loading.value = true
        const categoryParam = categoryId ? `&categories=${categoryId}` : ''
        const response = await fetch(
            `${WP_API_URL}/posts?_fields=id,title,date,link,categories&per_page=${pagination.value.pageSize}&page=${pagination.value.pageNo}${categoryParam}`,
            {
                headers: {
                    'Accept': 'application/json',
                }
            }
        )

        if (!response.ok) throw new Error('获取文章失败')

        // 获取总页数和总条数
        const totalPages = response.headers.get('X-WP-TotalPages')
        const totalPosts = response.headers.get('X-WP-Total')

        pagination.value.pages = totalPages ? parseInt(totalPages) : 1
        pagination.value.total = totalPosts ? parseInt(totalPosts) : 0

        const postsData = await response.json()

        // 为每篇文章添加分类名称
        posts.value = postsData.map((post: Post) => {
            if (post.categories.length > 0) {
                post.categoryDescription = selectedCategoryDescription.value
            }
            return post
        })
    } catch (err: any) {
        error.value = err.message
    } finally {
        loading.value = false
    }
}

// 处理分类点击
const handleCategoryClick = async (categoryId: number) => {
    selectedCategory.value = categoryId
    selectedCategoryDescription.value = categories.value.find(cat => cat.id === categoryId)?.description || ''
    console.log('selectedCategoryDescription.value', selectedCategoryDescription.value)
    pagination.value.pageNo = 1 // 切换分类时重置页码

    // 更新URL
    await router.push({
        query: {
            ...route.query,
            category: categoryId || undefined,
            page: 1
        }
    })

    fetchPosts(categoryId)
}

// 处理分页变化
const handlePageChange = async (page: number) => {
    pagination.value.pageNo = page

    // 更新URL
    await router.push({
        query: {
            ...route.query,
            page: page
        }
    })

    fetchPosts(selectedCategory.value)
}

// 监听路由变化
watch(
    () => route.query,
    () => {
        const { categoryId, page } = initFromRoute()
        fetchPosts(categoryId)
    },
    { immediate: true }
)

onMounted(() => {
    fetchCategories()
})

definePageMeta({
    title: '万能小in - SEO文章列表'
})
</script>