<template>
    <div class="container mx-auto px-4 py-8">
      <nav class="flex justify-between items-center mb-8">
        <NuxtLink to="/" class="text-blue-500 hover:text-blue-600">
          返回首页
        </NuxtLink>
        <div class="space-x-4">
          <template v-if="isLoggedIn">
            <NuxtLink 
              to="/profile"
              class="text-blue-500 hover:text-blue-600"
            >
              {{ username }}
            </NuxtLink>
          </template>
          <template v-else>
            <NuxtLink 
              to="/login"
              class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
            >
              登录
            </NuxtLink>
          </template>
        </div>
      </nav>
  
      <div class="max-w-3xl mx-auto">
        <h1 class="text-3xl font-bold mb-6">{{ article.title }}</h1>
        <div class="mb-4 text-gray-600">
          <span>发布日期：{{ article.date }}</span>
          <span class="mx-2">|</span>
          <span>作者：{{ article.author }}</span>
        </div>
        <div class="prose prose-lg max-w-none">
          {{ article.content }}
        </div>
      </div>
    </div>
  </template> 

<script setup>
const route = useRoute()
const isLoggedIn = ref(false)
const username = ref('')

onMounted(() => {
  const currentUser = localStorage.getItem('currentUser')
  if (currentUser) {
    isLoggedIn.value = true
    username.value = currentUser
  }
})

// 模拟文章数据
const articles = {
  '1': {
    title: '如何提高学术论文写作效率',
    date: '2024-03-20',
    author: '张教授',
    content: `学术论文写作是一项复杂的工作，需要研究者投入大量的时间和精力。本文将从多个角度探讨如何提高学术论文的写作效率。

首先，合理的时间规划至关重要。建议将写作任务分解成多个小目标，制定详细的时间表，循序渐进地完成每个部分。

其次，充分利用现代化工具。各种文献管理软件、写作辅助工具都可以大大提升写作效率。同时，使用AI辅助工具也可以帮助我们更快地完成初稿。

最后，建立良好的写作习惯也很重要。保持规律的写作时间，创造专注的写作环境，这些都是提高效率的关键因素。`
  },
  '2': {
    title: '学术PPT制作技巧详解',
    date: '2024-03-21',
    author: '李博士',
    content: `学术PPT的制作不仅要求内容专业，还需要注意表现形式。本文将详细介绍学术PPT制作的关键技巧。

在内容安排方面，需要遵循清晰的逻辑结构，确保每页幻灯片传达的信息量适中。建议采用"金字塔原理"来组织内容，先说结论，再展开论述。

在视觉设计方面，应当遵循简洁性原则。选择适当的配色方案，使用清晰的字体，适当运用图表来展示数据。避免过度装饰，保持专业性。

在演示技巧方面，建议准备演讲稿，反复练习，掌握时间节奏。同时，要注意与听众的互动，适时调整演讲节奏。`
  },
  '3': {
    title: '研究方法论探讨',
    date: '2024-03-22',
    author: '王研究员',
    content: `研究方法论是开展学术研究的重要基础，本文将深入探讨各种研究方法的特点和应用场景。

定量研究方法适用于需要通过数据来验证假设的研究。这种方法强调客观性和可重复性，通常需要收集大量数据进行统计分析。

定性研究方法则更适合探索性研究，特别是在研究人类行为和社会现象时。通过深度访谈、参与式观察等方式收集数据，进行归纳分析。

混合研究方法综合了定量和定性方法的优势，能够提供更全面的研究视角。但这种方法要求研究者具备更全面的研究能力。`
  },
  '4': {
    title: '研究方法论探讨',
    date: '2024-03-22',
    author: '王研究员',
    content: `研究方法论是开展学术研究的重要基础，本文将深入探讨各种研究方法的特点和应用场景。

定量研究方法适用于需要通过数据来验证假设的研究。这种方法强调客观性和可重复性，通常需要收集大量数据进行统计分析。

定性研究方法则更适合探索性研究，特别是在研究人类行为和社会现象时。通过深度访谈、参与式观察等方式收集数据，进行归纳分析。

混合研究方法综合了定量和定性方法的优势，能够提供更全面的研究视角。但这种方法要求研究者具备更全面的研究能力。`
  }
}

const article = ref(articles[route.params.id] || {
  title: '文章未找到',
  date: '',
  author: '',
  content: '抱歉，未找到相关文章'
})

useHead({
  title: `${article.value.title} - 学术助手`,
  meta: [
    {
      name: 'description',
      content: article.value.content.substring(0, 100)
    }
  ]
})
</script> 