<template>
    <div>{{ title }}

        {{ status }}
        <div v-if="status == 'success'">
            <div>{{ data?.result?.creator?.seoConfig?.description || '' }}</div>
            <div>{{ data?.result?.creator?.seoConfig?.keywords || '' }}</div>

        </div>
    </div>
</template>
<script lang="ts" setup>
import { getCreatorDetailServer } from '~/api/serverApi';
const {
    data,
    error,
    status
} = await getCreatorDetailServer({
    code: 'paper'
})
const title = computed(() => !error.value ? data.value?.result?.creator?.seoConfig?.title : "详情页")

</script>