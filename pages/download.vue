<template>
    <div class="h-full bg-slate-50 overflow-hidden flex flex-col">
        <!-- 页面标题区 -->
        <div class="bg-white/80 rounded-xl px-4 py-2 border border-blue-700/20 m-4">
            <div class="flex items-center justify-between gap-2">
                <!-- 标题部分 -->
                <div class="flex items-center">
                    <div
                        class="w-8 h-8 md:w-10 md:h-10 bg-blue-700 rounded-xl flex items-center justify-center mr-2 md:mr-4 shadow-inner">
                        <download theme="outline" size="20" md:size="24" fill="#FFFFFF" />
                    </div>
                    <div>
                        <h1
                            class="text-base md:text-lg font-medium text-transparent bg-gradient-to-r from-blue-700 to-indigo-700 bg-clip-text">
                            下载页面
                        </h1>
                        <p class="text-xs md:text-sm text-gray-500">我的AI外脑</p>
                    </div>
                </div>

                <!-- 用户信息区域 -->
                <div class="flex items-center">
                    <UserAvatar />
                </div>
            </div>
        </div>

        <div
            class="flex-1 overflow-auto p-4 bg-[url('https://static-1256600262.file.myqcloud.com/xiaoin-pc/images/seo/beijing.png')] bg-cover bg-center bg-no-repeat">
            <!-- 主要内容区域 -->
            <div class="text-center mt-8">
                <h1
                    class="text-[60px] md:text-[60px] text-[40px] font-bold text-[#2551B5] leading-[1.5] md:leading-[113px] tracking-[2px]">
                    万能小in</h1>
                <div
                    class="text-[30px] md:text-[45px] font-bold text-[#15264C] leading-[1.5] md:leading-[83px] tracking-[2px] mb-2">
                    基于个人知识库的AI生产力工具
                </div>
                <div
                    class="text-[20px] md:text-[30px] font-normal text-[#5A6170] leading-[1.5] md:leading-[53px] tracking-[2px]">
                    让每个人拥有自己的AI外脑</div>
            </div>

            <div class="flex justify-center gap-4 md:gap-8 my-8 md:my-12">
                <img src="https://static-1256600262.file.myqcloud.com/xiaoin-pc/images/seo/xdj.png" alt=""
                    class="w-[60px] md:w-[80px] h-[25px] md:h-[35px] object-contain">
                <img src="https://static-1256600262.file.myqcloud.com/xiaoin-pc/images/seo/cdz.png" alt=""
                    class="w-[60px] md:w-[80px] h-[25px] md:h-[35px] object-contain">
                <img src="https://static-1256600262.file.myqcloud.com/xiaoin-pc/images/seo/xdh.png" alt=""
                    class="w-[60px] md:w-[80px] h-[25px] md:h-[35px] object-contain">
            </div>

            <div class="flex justify-center items-center w-full mb-8 md:mb-12">
            </div>

            <!-- 下载选项区域 -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-0 justify-items-center max-w-4xl mx-auto px-4">
                <!-- iOS -->
                <div class="flex flex-col items-center w-full md:w-auto">
                    <!-- 桌面端显示二维码 -->
                    <div class="hidden md:block text-center w-full">
                        <div class="w-[160px] h-[160px] bg-white rounded-lg shadow-md relative mb-3 mx-auto">
                            <div class="absolute inset-0 flex items-center justify-center">
                                <img src="https://static-1256600262.file.myqcloud.com/xiaoin-pc/images/wp/ios-code.jpg"
                                    alt="iOS下载二维码" class="w-[140px] h-[140px] object-contain" />
                            </div>
                        </div>
                        <span class="text-[16px] font-normal text-[#333333] leading-[25px] text-center block">iOS</span>
                    </div>
                    <!-- 手机端显示按钮 -->
                    <a href="itms-apps://itunes.apple.com/cn/app/id6461309832"
                        class="md:hidden w-full h-[72px] flex items-center gap-3 bg-gradient-to-r from-blue-700 to-indigo-600 text-white px-4 rounded-xl transition-all duration-300">
                        <apple theme="filled" size="28" fill="#FFFFFF" />
                        <div class="flex flex-col items-start gap-0.5">
                            <span class="font-medium text-base leading-tight">iOS</span>
                            <span class="font-medium text-xs leading-tight opacity-90">版本下载</span>
                        </div>
                    </a>
                </div>

                <!-- Android -->
                <div class="flex flex-col items-center w-full md:w-auto">
                    <!-- 桌面端显示二维码 -->
                    <div class="hidden md:block text-center w-full">
                        <div class="w-[160px] h-[160px] bg-white rounded-lg shadow-md relative mb-3 mx-auto">
                            <div class="absolute inset-0 flex items-center justify-center">
                                <img src="https://static-1256600262.file.myqcloud.com/xiaoin-pc/images/wp/android-code.jpg"
                                    alt="Android下载二维码" class="w-[140px] h-[140px] object-contain" />
                            </div>
                        </div>
                        <span
                            class="text-[16px] font-normal text-[#333333] leading-[25px] text-center block">Android</span>
                    </div>
                    <!-- 手机端显示按钮 -->
                    <a href="https://file2.xiaoin.com.cn/download/app/0.2.6-xiaoin.apk"
                        class="md:hidden w-full h-[72px] flex items-center gap-3 bg-gradient-to-r from-blue-700 to-indigo-600 text-white px-4 rounded-xl transition-all duration-300">
                        <android theme="filled" size="28" fill="#FFFFFF" />
                        <div class="flex flex-col items-start gap-0.5">
                            <span class="font-medium text-base leading-tight">Android</span>
                            <span class="font-medium text-xs leading-tight opacity-90">版本下载</span>
                        </div>
                    </a>
                </div>

                <!-- 小程序 -->
                <div class="hidden md:flex flex-col items-center">
                    <div class="w-[160px] h-[160px] bg-white rounded-lg shadow-md relative mb-3">
                        <div class="absolute inset-0 flex items-center justify-center">
                            <img src="https://static-1256600262.file.myqcloud.com/xiaoin-pc/images/wp/miniapp-code.jpg"
                                alt="小程序二维码" class="w-[140px] h-[140px] object-contain" />
                        </div>
                    </div>
                    <span class="text-[16px] font-normal text-[#333333] leading-[25px]">小程序</span>
                </div>

                <!-- 公众号 -->
                <div class="hidden md:flex flex-col items-center">
                    <div class="w-[160px] h-[160px] bg-white rounded-lg shadow-md relative mb-3">
                        <div class="absolute inset-0 flex items-center justify-center">
                            <img src="https://static-1256600262.file.myqcloud.com/xiaoin-pc/images/wp/wechat-code.jpg"
                                alt="公众号二维码" class="w-[140px] h-[140px] object-contain" />
                        </div>
                    </div>
                    <span class="text-[16px] font-normal text-[#333333] leading-[25px]">公众号</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { useHead } from '#imports';
import { Android, Apple, Download } from '@icon-park/vue-next';


// const store = useUserStore()
// // 在页面加载完成后设置 channel
// const updateUserRecord = () => {
//     store.setChannel('comcn-download')
//     // store.setOpenRecordUrl(`${window.location.href}?channel=comcn-download`)
// }

// onMounted(() => {
//     updateUserRecord()
// })


useHead({
    title: '万能小in客户端下载-IOSAPP下载-Android下载',
    meta: [
        {
            name: 'keywords',
            content: '万能小in官网,万能小in下载,IOS下载,Android下载,客户端下载,APP下载',
            tagPriority: 'critical'
        },
        {
            name: 'description',
            content: '万能小in你的AI知识助手应用，万能小in官网下载，现支持客户端、IOS、Android、小程序及公众号等设备下载，下载万能小in拥有自己的AI外脑。',
            tagPriority: 'critical'
        }
    ]
})

</script>
