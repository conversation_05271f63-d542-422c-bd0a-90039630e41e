<template>
    <div>
        <!-- 操作按钮 -->
        <button @click="appendContent">插入复杂内容</button>

        <!-- 编辑器容器 -->
        <editor-content :editor="editor" ref="editorContent" class="editor-container" />
    </div>
</template>

<script>
import StarterKit from '@tiptap/starter-kit'
import { Editor, EditorContent } from '@tiptap/vue-3'

export default {
    components: { EditorContent },
    data() {
        return {
            editor: null
        }
    },
    mounted() {
        this.editor = new Editor({
            extensions: [StarterKit],
            content: '<p>初始内容</p>',
            onUpdate: () => {
                console.log('常规更新事件触发')
            }
        })
    },
    methods: {
        appendContent() {
            // 1. 静默插入内容
            this.editor.chain()
                .focus()  // 保持焦点
                .insertContent(this.generateComplexContent(), {
                    parseOptions: {
                        preserveWhitespace: 'full',
                    },
                    update: false  // 关键参数：禁止触发更新事件[1,7](@ref)
                })
                .run()

            // 2. 滚动到底部
            this.$nextTick(() => {
                const container = this.$refs.editorContent.$el
                const prosemirrorView = container.querySelector('.ProseMirror')
                if (prosemirrorView) {
                    prosemirrorView.scrollTo({
                        top: prosemirrorView.scrollHeight,
                        behavior: 'smooth'
                    })
                }
            })
        },
        generateComplexContent() {
            return `
          <h2>新增章节</h2>
          <ul>
            <li>项目1</li>
            <li>项目2</li>
          </ul>
          <blockquote>引用内容</blockquote>
        `
        }
    },
    beforeUnmount() {
        this.editor.destroy()
    }
}
</script>

<style>
.editor-container {
    border: 1px solid #ccc;
    padding: 1rem;
    min-height: 300px;
    max-height: 500px;
    overflow-y: auto;
}
</style>