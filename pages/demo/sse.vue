<template>
    <div class="container mx-auto p-6">
        <h1 class="text-2xl font-bold mb-6">SSE进度演示</h1>

        <div class="mb-8">
            <button @click="handleStartSSE"
                class="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-4 rounded"
                :disabled="isConnecting">
                {{ isConnecting ? '连接中...' : '启动SSE连接' }}
            </button>
        </div>

        <div v-if="isConnecting || hasStarted" class="mb-6">
            <div class="mb-2 flex justify-between">
                <span>进度: {{ percent }}%</span>
                <span>{{ statusMessage }}</span>
            </div>

            <div class="w-full bg-gray-200 rounded-full h-4">
                <div class="bg-blue-500 h-4 rounded-full transition-all duration-300" :style="{ width: `${percent}%` }">
                </div>
            </div>
        </div>

        <div v-if="isComplete" class="mt-4 text-green-600 font-bold">
            {{ completeMessage }}
        </div>

        <div v-if="error" class="mt-4 text-red-600">
            连接错误: {{ error }}
        </div>
    </div>
</template>

<script setup lang="ts">
import { onUnmounted, ref } from 'vue'

const isConnecting = ref(false)
const hasStarted = ref(false)
const percent = ref(0)
const statusMessage = ref('')
const completeMessage = ref('')
const isComplete = ref(false)
const error = ref('')
const eventSource = ref<EventSource | null>(null)

const handleStartSSE = () => {
    // 重置状态
    isConnecting.value = true
    hasStarted.value = true
    percent.value = 0
    statusMessage.value = '开始连接...'
    completeMessage.value = ''
    isComplete.value = false
    error.value = ''

    // 关闭可能存在的之前的连接
    if (eventSource.value) {
        eventSource.value.close()
    }

    // 创建新的SSE连接
    try {
        eventSource.value = new EventSource('http://127.0.0.1:8000/api/demo/sse/progress/')

        // 监听消息事件
        eventSource.value.onmessage = (event) => {
            const data = JSON.parse(event.data)

            // 处理进度更新
            if (data.percent !== undefined) {
                percent.value = data.percent
                statusMessage.value = data.message
            }

            // 处理完成信号
            if (data.status === 'complete') {
                completeMessage.value = data.message
                isComplete.value = true
            }
        }

        // 监听完成事件
        eventSource.value.addEventListener('done', () => {
            if (eventSource.value) {
                eventSource.value.close()
                eventSource.value = null
            }
            isConnecting.value = false
        })

        // 错误处理
        eventSource.value.onerror = (e) => {
            error.value = '连接出错，请重试'
            isConnecting.value = false
            if (eventSource.value) {
                eventSource.value.close()
                eventSource.value = null
            }
        }
    } catch (e) {
        error.value = '创建SSE连接失败'
        isConnecting.value = false
    }
}

// 组件卸载时关闭连接
onUnmounted(() => {
    if (eventSource.value) {
        eventSource.value.close()
        eventSource.value = null
    }
})
</script>
