<template>
    <div class="container mx-auto p-6">
        <h1 class="text-2xl font-bold mb-6">大模型问答演示</h1>

        <div class="mb-6">
            <div class="mb-4">
                <textarea v-model="question"
                    class="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows="4" placeholder="请输入您的问题..." :disabled="isLoading"></textarea>
            </div>
            <button @click="handleSubmit"
                class="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-4 rounded"
                :disabled="isLoading || !question.trim()">
                {{ isLoading ? '正在处理...' : '提交问题' }}
            </button>
        </div>

        <div v-if="response || isLoading" class="mt-6">
            <h2 class="text-xl font-semibold mb-3">回答结果：</h2>
            <div class="p-4 bg-gray-50 rounded-md min-h-32 whitespace-pre-wrap">
                <p v-if="isLoading && !response" class="text-gray-500">正在思考中...</p>
                <div v-else class="prose">{{ response }}</div>
            </div>
        </div>

        <div v-if="error" class="mt-4 text-red-600">
            出错了: {{ error }}
        </div>
    </div>
</template>

<script setup lang="ts">
import { fetchEventSource } from '@microsoft/fetch-event-source'
import { onUnmounted, ref } from 'vue'

const question = ref('')
const response = ref('')
const isLoading = ref(false)
const error = ref('')
let controller: AbortController | null = null

const handleSubmit = async () => {
    // if (!question.value.trim() || isLoading.value) return

    // 重置状态
    response.value = ''
    error.value = ''
    isLoading.value = true

    // 如果有正在进行的请求，取消它
    if (controller) {
        controller.abort()
    }

    // 创建新的 AbortController
    controller = new AbortController()

    try {
        await fetchEventSource('http://127.0.0.1:8000/api/ai/ask_sse/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                // 'Accept': 'text/event-stream',
            },
            body: JSON.stringify({
                messages: [
                    {
                        role: 'user',
                        content: question.value
                    }
                ]
            }),
            signal: controller.signal,
            credentials: 'include', // 包含 cookies 等认证信息
            openWhenHidden: true, // 即使页面不可见也保持连接
            onmessage: (event) => {
                try {
                    const data = JSON.parse(event.data)

                    if (data.content) {
                        // 拼接内容
                        response.value += data.content
                    } else if (data.status === 'completed') {
                        isLoading.value = false
                    } else if (data.status === 'error') {
                        error.value = data.error || '发生未知错误'
                        isLoading.value = false
                    }
                } catch (e: unknown) {
                    console.error('解析消息失败', e)
                }
            },
            onclose: () => {
                isLoading.value = false
            },
            onerror: (err) => {
                error.value = '连接错误，请重试'
                isLoading.value = false
                controller = null
                throw err // 这会导致连接重试
            }
        })
    } catch (e: unknown) {
        if (e instanceof Error && e.name !== 'AbortError') {
            error.value = '请求失败：' + (e.message || '未知错误')
        }
        isLoading.value = false
    }
}

// 组件卸载时取消请求
onUnmounted(() => {
    if (controller) {
        controller.abort()
        controller = null
    }
})
</script>