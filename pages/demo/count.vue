<template>
    <div class="flex justify-center items-center min-h-screen bg-gray-100">
        <div class="bg-white p-8 rounded-lg shadow-lg">
            <h2 class="text-2xl font-bold mb-6 text-center">距离午夜还有</h2>
            <div class="flex space-x-4">
                <div class="time-card">
                    <div class="text-4xl font-bold">{{ hours.toString().padStart(2, '0') }}</div>
                    <div class="text-sm text-gray-500">小时</div>
                </div>
                <div class="text-4xl font-bold">:</div>
                <div class="time-card">
                    <div class="text-4xl font-bold">{{ minutes.toString().padStart(2, '0') }}</div>
                    <div class="text-sm text-gray-500">分钟</div>
                </div>
                <div class="text-4xl font-bold">:</div>
                <div class="time-card">
                    <div class="text-4xl font-bold">{{ seconds.toString().padStart(2, '0') }}</div>
                    <div class="text-sm text-gray-500">秒</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { onMounted, onUnmounted } from 'vue'
import { useDemoStore } from '~/stores/demoStore'

const store = useDemoStore()
const { hours, minutes, seconds } = storeToRefs(store)

let timer: ReturnType<typeof setInterval>

onMounted(() => {
    // 立即更新一次
    store.updateCountdown()
    // 设置定时器，每秒更新一次
    timer = setInterval(() => {
        store.updateCountdown()
    }, 1000)
})

onUnmounted(() => {
    // 清理定时器
    if (timer) {
        clearInterval(timer)
    }
})
</script>

<style scoped>
.time-card {
    @apply bg-gray-50 px-6 py-3 rounded-lg text-center min-w-[100px];
}
</style>
