<template>
  <div class="m-3 bg-white space-y-6 p-6 border border-gray-200 rounded-lg">
    <!-- 邀请奖励说明 -->
    <div class="flex flex-row  justify-between items-center  bg-blue-50 rounded-lg p-6 border border-blue-100">
      <div class="flex items-center mb-4">
        <div class="flex items-center gap-3">
          <funds theme="outline" size="24" fill="#ff6600" />
          <div>
            <h3 class="text-base font-bold text-blue-800">邀请好友得奖励</h3>
            <p class="text-sm text-gray-600 mt-1">好友通过你的邀请海报完成新账号登录，你可以获得1万硬币，最多奖励10万！</p>
          </div>
        </div>
      </div>

      <!-- 生成邀请海报按钮 -->
      <button @click="handleGenerateInvitePoster"
        class="bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 p-2 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center gap-2">
        <add-user theme="outline" size="20" fill="#FFFFFF" />
        生成邀请海报
      </button>
    </div>

    <!-- 邀请记录列表 -->
    <div>
      <h3 class="text-base mb-4">我的邀请记录</h3>
      <div class="bg-white rounded-lg border border-gray-200">
        <EmptyState v-if="!isLoading && rechargeList.length === 0" />

        <!-- 表头 -->
        <div v-if="rechargeList.length > 0"
          class="grid grid-cols-3 text-gray-500 text-sm py-3 px-4 border-b border-gray-100">
          <div>邀请用户</div>
          <div>成功邀请时间</div>
          <div>硬币奖励</div>
        </div>
        <!-- 列表内容 -->
        <div class="divide-y divide-gray-100">
          <div v-if="isLoading" class="flex justify-center items-center h-32"><a-spin inning="isLoading"></a-spin></div>
          <div v-if="!isLoading" v-for="item in rechargeList" :key="item.id" class="grid grid-cols-3 py-4 px-4">
            <div class="text-sm text-[#333333]">{{ getRemarkText(item.remark) }}</div>
            <div class="text-sm text-[#333333]">{{ item.createTime }}</div>
            <div class="text-green-600 text-sm">10000硬币已发放！</div>
          </div>
        </div>
      </div>
    </div>

    <canvas canvas-id="canvas" id="canvas" class="canvas" width="320" height="440"
      style="width: 320px; height: 440px"></canvas>

    <Pagination v-if="pages != 0" v-model:current="reqParams.pageNo" :total-pages="pages" :total="reqParams.total"
      @change="handlePageChange">
    </Pagination>
  </div>
</template>

<script setup lang="ts">
import { getRecordList } from '@/api/recharge';
import { AddUser, Funds } from '@icon-park/vue-next';
import { message } from 'ant-design-vue';
import { onMounted, ref } from 'vue';
import { stroageKeyOfMiniappQrcode } from '~/utils/constants';
import { InviteUtil } from '~/utils/invite';

interface RechargeDetail {
  id: string
  amount: number
  coinId: string
  coinType: number
  createTime: string
  remark: string
  userId: string
}



const isLoading = ref<boolean>(true)
const rechargeList = ref<RechargeDetail[]>([])
const reqParams = reactive({
  pageNo: 1,
  total: 0,
  pageSize: 10,
  action: 4
})


const pages = ref(0)

const loadRecordsData = async () => {
  isLoading.value = true
  const params = {
    ...reqParams
  }
  let res = await getRecordList(params)

  if (!res.ok || !res.data) {
    isLoading.value = false
    message.error(res.message || '充值记录加载失败')
    return
  }
  if (res.data?.pages) {
    pages.value = parseInt(res.data?.pages) || 0
  }

  rechargeList.value.push(...(res.data.records || []))
  pages.value = res.data.pages || 0
  pages.value = res.data.total || 0

  isLoading.value = false
}


const handlePageChange = () => {
  loadRecordsData()
}




const getRemarkText = (text: any) => {
  if (!text) {
    return text
  }
  return text.replace(/邀请奖励/g, '***')
}


definePageMeta({
  layout: 'profile'
})

const qrcodeBase64Data = ref()


const getQRCode = async () => {
  const config = useRuntimeConfig()
  try {
    const qrCodeBase64 = await InviteUtil.getQRCode(config.public.apiBase)

    draw(qrCodeBase64)
  } catch (error) {
    console.error('Error fetching QR code:', error)
  }
}

const draw = (qrCodeBase64: unknown) => {
  const canvas = document.querySelector('canvas')

  if (!canvas) {
    return
  }
  InviteUtil.draw(canvas, qrCodeBase64, () => {
    setTimeout(() => {
      qrcodeBase64Data.value = storage.get(stroageKeyOfMiniappQrcode)
    }, 100)
  })
}


const handleGenerateInvitePoster = async () => {

  await getQRCode()


  setTimeout(() => {

    if (!StarloveUtil.isEmptyString(storage.get(stroageKeyOfMiniappQrcode))) {
      const url = storage.get(stroageKeyOfMiniappQrcode)
      InviteUtil.saveBas64Img(url, '万能小in邀请海报.png')
      return
    }
    const canvas = document.querySelector('canvas')
    if (!canvas) {
      message.error('下载失败')
      return
    }
    InviteUtil.readBlobAsBase64Data(canvas, () => {
      setTimeout(() => {
        const url = storage.get(stroageKeyOfMiniappQrcode)
        InviteUtil.saveBas64Img(url, '万能小in邀请海报.png')
      }, 100)
    })

  }, 200)
}

onMounted(() => {
  // getQRCode()
  loadRecordsData()
})

</script>

<style lang="css" scoped>
.canvas {
  width: 320px;
  height: 440px;
  position: fixed;
  visibility: hidden;
  pointer-events: none;
}
</style>
