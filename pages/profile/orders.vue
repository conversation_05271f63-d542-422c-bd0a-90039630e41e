<template>
  <div class="m-3 bg-white p-6 border border-gray-200 rounded-lg">
    <!-- 列表区域 -->
    <div class="space-y-4">
      <div v-if="isLoading" class="flex justify-center items-center h-32"><a-spin inning="isLoading"></a-spin></div>
      <EmptyState v-if="!isLoading && orderList.length === 0" />
      <div v-if="!isLoading" v-for="item in orderList" :key="item.id"
        class="flex flex-col sm:flex-row items-start sm:items-center justify-between p-4 sm:px-6 sm:py-4 space-y-4 sm:space-y-0 border border-blue-100 rounded-lg">



        <div class="flex items-center gap-4">
          <div>
            <div class="text-sm text-gray-800">{{ item.body }}</div>
            <div class="text-sm text-gray-500 mt-1">{{ item.createTime }}</div>
          </div>
        </div>

        <div v-if="item.status == RechargeOrderStatus.replenishAddress ||
          item.status == RechargeOrderStatus.waitConsignment ||
          item.status == RechargeOrderStatus.waitReceiving ||
          item.status == RechargeOrderStatus.received
        ">
          <div
            class="flex flex-col sm:flex-row items-start sm:items-center space-y-3 sm:space-y-0 sm:space-x-4 w-full sm:w-auto"
            v-if="knowledgeAssistantMemberInfo?.vipLevel == 3">
            <div v-if="item.status == RechargeOrderStatus.waitReceiving || item.status == RechargeOrderStatus.received">
              <span class="block sm:inline-block mb-2 sm:mb-0 text-gray-600">
                {{ item.status == RechargeOrderStatus.waitReceiving ? '待收货' : '已收货' }}
              </span>
              <button
                class="w-full sm:w-auto bg-cyan-50 hover:bg-cyan-100 text-cyan-800 font-semibold py-2 px-4 border border-cyan-300 rounded shadow"
                @click="handleShowMaterialFlowModal(item)">
                查看物流信息
              </button>
            </div>
            <div v-else class="space-x-3">
              <span class="block sm:inline-block mb-2 sm:mb-0 text-gray-600 text-sm">待发货</span>
              <button
                class="text-[12px] bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white p-2 rounded-lg"
                @click="handleShowReceivingModal(item)" v-if="addressList.length == 0">
                填写收货信息
              </button>
            </div>
          </div>
        </div>

        <div class="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-3">
          <div class="text-sm text-gray-500">
            订单金额：¥{{ moneyFormatCent(item.payment || item.totalAmount) || 0 }}
          </div>
          <div class="px-2.5 py-1 rounded-full text-xs font-medium" :class="{
            'bg-orange-50 text-orange-600 border border-orange-200': item.status === RechargeOrderStatus.waitPay,
            'bg-gray-50 text-gray-600 border border-gray-200': item.status !== RechargeOrderStatus.waitPay
          }">
            {{ orderStatusText(item.status) }}
          </div>
        </div>

      </div>
    </div>

    <div class="mt-4 sm:mt-6 pb-16 md:pb-0">
      <Pagination v-if="reqParams.pages != 0" v-model:current="reqParams.pageNo" :total-pages="reqParams.pages"
        :total="reqParams.total" @change="handlePageChange">
      </Pagination>
    </div>

    <MaterialFlowModal v-if="materialFlowVisible" v-model:open-visible="materialFlowVisible" :orderId="orderId">
    </MaterialFlowModal>

    <ReceivingModal v-if="receivingVisible" :isShowHint="false" :order-id="orderId"
      v-model:open-visible="receivingVisible" @add-success="onAddRessSuccess" :containerId="'order-container'">
    </ReceivingModal>
  </div>
</template>

<script setup lang="ts">
import { getRechargeOrderList } from '@/api/order';
import { type PageResult } from '@/api/typing';
import { getAddressList } from '@/api/user';
import EmptyState from '@/components/EmptyState.vue';
import MaterialFlowModal from '@/components/Profile/MaterialFlowModal.vue';
import ReceivingModal from '@/components/Profile/ReceivingModal.vue';
import { type RechargeOrderInfo } from '@/services/types/order';
import { UserService } from '@/services/user';
import { moneyFormatCent } from '@/utils/utils';
import { Spin as ASpin, message } from 'ant-design-vue';
import { computed, onMounted, reactive, ref } from 'vue';




import { RechargeOrderStatus } from '@/utils/constants';
const addressList = ref([])
const ordersPageResult = ref<PageResult<RechargeOrderInfo> | undefined>(undefined)
const isLoaded = ref<boolean>(false)
const orderId = ref()
const materialFlowVisible = ref(false)
const receivingVisible = ref(false)

const reqParams = reactive({
  pageNo: 1,
  pageSize: 10,
  pages: 0,
  total: 0
})

const knowledgeAssistantMemberInfo = computed(() => {
  return UserService.getKnowledgeAssistantMemberInfo()
})



const orderStatusText = (status: number) => {
  if (!status) {
    return '-'
  }
  switch (status) {
    case RechargeOrderStatus.waitPay:
      return '待支付'
    case RechargeOrderStatus.cancel:
      return '已取消'
    case RechargeOrderStatus.done:
      return '已完成'
    case RechargeOrderStatus.refund:
      return '已退款'
    case RechargeOrderStatus.replenishAddress:
      return addressList.value.length == 0 ? '补充收货地址' : '待发货'
    case RechargeOrderStatus.waitConsignment:
      return '待发货'
    case RechargeOrderStatus.waitReceiving:
      return '待收货'
    case RechargeOrderStatus.received:
      return '已收货'
    default:
      return '-'
  }
}


const isLoading = ref<boolean>(true)


const loadAddressData = async () => {
  isLoading.value = true
  const res = await getAddressList()
  if (!res.ok || !res.data) {
    // message.error(res.message || '地址列表加载失败')
    isLoading.value = false

    return
  }
  addressList.value = res.data
  isLoading.value = false

}


const loadData = async () => {
  isLoaded.value = true
  const params = {
    ...reqParams
  }
  const res = await getRechargeOrderList(params)
  isLoaded.value = false
  if (!res.ok || !res.data) {
    message.error(res.message || '订单记录加载失败')
    return
  }
  ordersPageResult.value = res.data
  reqParams.pages = res.data.pages || 0
  reqParams.total = res.data.total || 0


}


const orderList = computed(() => {
  if (!ordersPageResult.value) {
    return []
  }

  return [...ordersPageResult.value.records]
})


const handleShowMaterialFlowModal = async (item: any) => {
  orderId.value = item.id
  materialFlowVisible.value = true
}
const handleShowReceivingModal = (item: any) => {
  orderId.value = item.id
  receivingVisible.value = true
}

const onAddRessSuccess = async () => {
  await loadAddressData()
  await loadData()
}


const handlePageChange = () => {
  loadData()
}

onMounted(() => {
  loadAddressData()
  loadData()
})

definePageMeta({
  layout: 'profile'
})
</script>

<style scoped>
/* 移除了不必要的样式 */
</style>