<template>
  <div class="m-3 space-y-6">
    <!-- 客服信息卡片 -->
    <!-- <div class="bg-blue-50 rounded-lg p-6">
      <div class="flex items-center gap-3 mb-4">
        <div class="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center">
          <customer theme="outline" size="28" fill="#FFFFFF" />
        </div>
        <div>
          <h3 class="text-lg font-bold text-blue-700">联系客服</h3>
          <p class="text-blue-600 mt-1">工作时间：10:00-19:00</p>
        </div>
      </div>
    </div> -->

    <!-- 微信客服 -->
    <div class="bg-white rounded-lg p-6 border border-gray-200">
      <h3 class="text-base mb-4 flex items-center">
        <wechat theme="outline" size="24" class="mr-2" />
        微信客服
      </h3>
      <div class="flex items-center justify-center flex-col">
        <div class="w-48 h-48 mb-4">
          <img src="~/assets/images/qrcode.png" alt="微信客服二维码" class="w-full h-full object-contain">
        </div>
        <p class="text-gray-500 text-sm">扫描二维码添加客服微信（工作日10:00-19:00）</p>
      </div>
    </div>

    <!-- 用户ID信息 -->
    <div class="bg-white rounded-lg p-6 border border-gray-200">
      <h3 class="text-base mb-4">您的用户ID</h3>
      <div class="flex items-center justify-between bg-gray-50 p-4 rounded-lg">
        <span class="text-gray-600 text-sm">{{ userInfoId }}</span>
        <button class="text-blue-600 hover:text-blue-700 flex items-center text-sm" @click="copyUserId">
          <copy theme="outline" size="20" class="mr-1" />
          复制
        </button>
      </div>
      <p class="text-gray-500 text-sm mt-2">
        反馈问题时，请告知客服您的用户ID，以便更好地为您服务
      </p>
    </div>


  </div>
</template>

<script setup lang="ts">
import { Copy, Wechat } from '@icon-park/vue-next';
import { message } from 'ant-design-vue';
import { computed } from 'vue';
import { UserService } from '~/services/user';



const userInfoId = computed(() => {
  return UserService.getCurrentLoginInfo()?.id || ''
})

// 复制用户ID
const copyUserId = () => {
  navigator.clipboard.writeText(userInfoId.value)
    .then(() => {
      // TODO: 显示复制成功提示
      // console.log('用户ID已复制')
      // message.warning('用户ID已复制')
      message.success('复制成功')
    })
    .catch(err => {
      // console.error('复制失败:', err)
      message.error('复制失败')
    })
}

definePageMeta({
  layout: 'profile'
})



</script>