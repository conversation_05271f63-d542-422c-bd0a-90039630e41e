<template>
  <div v-if="isClientLoaded">
    <Basic />
  </div>
  <div v-else class="min-h-[200px]">
    <XLoading></XLoading>
  </div>
</template>

<script setup>
import Basic from '@/components/Profile/Basic.vue';
import XLoading from '@/components/XLoading.vue';

// 使用 ref 来追踪客户端加载状态
const isClientLoaded = ref(false)

definePageMeta({
  layout: 'profile',
})
onMounted(async () => {
  isClientLoaded.value = true
})
</script>

<style scoped>
.bg-gradient-to-r {
  background-size: 100% 100%;
}
</style>
