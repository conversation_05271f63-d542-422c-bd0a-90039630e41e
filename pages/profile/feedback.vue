<template>
  <div class="m-3 p-4 space-y-6 bg-white border border-gray-200/70">
    <!-- 反馈表单 -->
    <div class="space-y-6">
      <!-- 反馈类型 -->
      <div>
        <label class="block text-gray-700 mb-2 text-sm">反馈类型</label>
        <div class="flex flex-wrap gap-3">
          <button v-for="type in feedbackTypes" :key="type.value" class="px-3 py-2 rounded-full border" :class="[
            'text-xs',
            selectedType === type.value
              ? 'bg-blue-50 border-blue-500 text-blue-700'
              : 'border-gray-200 hover:border-blue-500'
          ]" @click="selectedType = type.value">
            {{ type.label }}
          </button>
        </div>
      </div>

      <div class="">
        <label class="block text-gray-700 mb-2 text-sm">反馈内容</label>
        <textarea v-model="content" rows="5" :maxlength="500"
          class="w-full text-sm px-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:border-blue-500"
          placeholder="请详细描述您的问题或建议..."></textarea>
        <p class="text-gray-500 text-sm mt-1">还可以输入 {{ 500 - content.length }} 字</p>

      </div>

      <!-- 联系方式 -->
      <div>
        <label class="block text-gray-700 mb-2 text-sm">联系方式</label>
        <input v-model="contact" type="text" maxlength="11"
          class="w-full text-sm px-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:border-blue-500"
          placeholder="请留下您的手机号，方便我们及时回复">
      </div>

      <!-- 提交按钮 -->
      <div class="flex justify-center pt-4">
        <button @click="submitFeedback"
          class="px-8 py-2 bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white rounded-full hover:bg-blue-800 transition-colors">
          提交反馈
        </button>
      </div>
    </div>


  </div>
</template>

<script setup lang="ts">
import { message } from 'ant-design-vue';
import { ref } from 'vue';

import { saveFeedback } from '@/api/user';


const feedbackTypes = [
  { label: '功能建议', value: 'feature' },
  { label: '体验问题', value: 'experience' },
  { label: 'Bug反馈', value: 'bug' },
  { label: '其他', value: 'other' }
]

const selectedType = ref('feature')
const content = ref('')
const contact = ref('')

// const feedbackRecords = ref([
//   {
//     type: '功能建议',
//     time: '2024-03-15 14:30',
//     content: '希望能增加批量导出功能',
//     status: '已回复',
//     reply: '感谢您的建议，我们会在下个版本中考虑添加该功能。'
//   },
//   {
//     type: 'Bug反馈',
//     time: '2024-03-14 09:15',
//     content: '页面加载偶尔出现卡顿现象',
//     status: '处理中'
//   }
// ])



const submitFeedback = async () => {


  if (!content.value || content.value.trim().length == 0) {
    message.warning('请填写反馈内容')

    return
  }

  if (!contact.value || contact.value.trim().length == 0) {
    message.warning('请填写手机号')
    return
  }


  if (!/^1[3456789]\d{9}$/.test(contact.value)) {
    message.warning('手机号格式错误')
    return
  }

  const params = {
    title: content.value,
    description: contact.value
  }
  const res = await saveFeedback(params)
  if (!res.ok) {
    message.error(res.message || '反馈失败')
    return
  }

  // 清空表单
  content.value = ''
  contact.value = ''
  selectedType.value = 'feature'
  message.success(res.message || '反馈成功')
}


definePageMeta({
  layout: 'profile'
})
</script>