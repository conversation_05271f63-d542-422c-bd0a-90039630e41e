<template>
    <div class="">
        <div class="max-w-3xl mx-auto p-4">
            <article class="prose prose-sm max-w-none mb-5">
                <p class="text-sm text-gray-600 mb-4">
                    通过分享链接邀请好友加入，您可以获得以下奖励：
                </p>

                <ul class="list-disc pl-6 mb-6 text-sm text-gray-600">
                    <li>每成功邀请1位新用户注册，获得100积分</li>
                    <li>新用户首次消费，您将获得消费金额的5%返现</li>
                    <li>累计邀请达到10人，升级为高级会员</li>
                </ul>

                <h2 class="text-xl text-gray-900 mb-4">
                    活动规则
                </h2>

                <p class="text-sm text-gray-600 mb-4">
                    1. 分享链接有效期为30天
                </p>
                <p class="text-sm text-gray-600 mb-4">
                    2. 每位新用户仅可被邀请一次
                </p>
                <p class="text-sm text-gray-600">
                    3. 最终解释权归平台所有
                </p>
            </article>
        </div>

        <div class="bg-white border-t border-gray-200">
            <div class="max-w-3xl mx-auto p-4">
                <form @submit.prevent="handleSubmit" class="space-y-4">
                    <div>
                        <label class="block text-sm text-gray-700 mb-2">
                            提交您的宣传链接
                        </label>
                        <div class="space-y-3">
                            <textarea v-model="promotionLink" rows="3" placeholder="请输入您在其他平台发布的宣传链接"
                                class="block w-full rounded-lg border border-gray-300 bg-white px-4 py-3 text-sm text-gray-900 placeholder-gray-500 shadow-sm transition duration-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:ring-opacity-20 resize-none"
                                style="min-height: 100px"></textarea>
                            <div class="flex items-center text-sm text-gray-500">
                                <info theme="outline" size="16" class="mr-1" />
                                <span>支持：小红书笔记、抖音视频、B站视频等平台的链接</span>
                            </div>
                            <div class="flex justify-center gap-3">
                                <button type="submit"
                                    class="inline-flex justify-center items-center px-4 py-2.5 border border-transparent text-sm rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    <send theme="outline" size="16" class="mr-1" />
                                    提交宣传链接
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</template>

<script setup>
import { Info, Send } from '@icon-park/vue-next'
import { ref } from 'vue'

definePageMeta({
    layout: 'profile'
})

const inviteLink = ref('https://example.com/invite/123456')
const promotionLink = ref('')

const copyInviteLink = () => {
    navigator.clipboard.writeText(inviteLink.value)
        .then(() => {
            alert('邀请链接已复制到剪贴板')
        })
        .catch(err => {
            console.error('复制失败:', err)
            alert('复制失败，请手动复制')
        })
}

const handleSubmit = () => {
    if (!promotionLink.value) {
        alert('请输入宣传链接')
        return
    }

    // TODO: 处理提交宣传链接的逻辑
    console.log('提交的宣传链接:', promotionLink.value)
}
</script>