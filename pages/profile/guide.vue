<template>
  <div class="space-y-8">
    <!-- 免费额度获取说明 -->
    <div class="bg-blue-50 rounded-lg p-6">
      <h3 class="text-lg font-bold text-blue-700 mb-4">免费额度获取方式</h3>
      <div class="space-y-3">
        <div v-for="(item, index) in coinRules" :key="index"
          class="flex items-center bg-white p-4 rounded-lg border border-blue-100">
          <div class="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mr-4">
            <component :is="item.icon" theme="outline" size="24" fill="#FFFFFF" />
          </div>
          <div>
            <h4 class="text-sm">{{ item.title }}</h4>
            <p class="text-gray-600 text-sm mt-1">{{ item.description }}</p>
          </div>
          <div class="ml-auto text-blue-600 text-sm">
            +{{ item.coins }}硬币
          </div>
        </div>
      </div>
    </div>

    <!-- 写作使用说明 -->
    <div class="bg-green-50 rounded-lg p-6">
      <h3 class="text-lg font-bold text-green-700 mb-4">写作使用说明</h3>
      <div class="space-y-4">
        <div v-for="(item, index) in creationGuides" :key="index"
          class="bg-white p-4 rounded-lg border border-green-100">
          <div class="flex items-center mb-2">
            <component :is="item.icon" theme="outline" size="20" class="text-green-600 mr-2" />
            <h4 class="text-sm">{{ item.title }}</h4>
          </div>
          <p class="text-gray-600 text-sm">{{ item.content }}</p>
        </div>
      </div>
    </div>

    <!-- AI提问使用说明 -->
    <div class="bg-purple-50 rounded-lg p-6">
      <h3 class="text-lg font-bold text-purple-700 mb-4">AI提问使用说明</h3>
      <div class="space-y-4">
        <div v-for="(item, index) in aiGuides" :key="index" class="bg-white p-4 rounded-lg border border-purple-100">
          <div class="flex items-center mb-2">
            <component :is="item.icon" theme="outline" size="20" class="text-purple-600 mr-2" />
            <h4 class="text-sm">{{ item.title }}</h4>
          </div>
          <p class="text-gray-600 text-sm">{{ item.content }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {
  Edit,
  Like,
  Message, Robot,
  Search,
  Share,
  Tips,
  User,
  UserPositioning
} from '@icon-park/vue-next';

const coinRules = [
  {
    icon: User,
    title: '新用户注册',
    description: '新用户注册即可获得5000硬币',
    coins: 5000
  },
  {
    icon: UserPositioning,
    title: '绑定手机号',
    description: '首次绑定手机号赠送5000硬币',
    coins: 5000
  },
  {
    icon: Like,
    title: '关注公众号',
    description: '关注公众号#万能小in 赠送2000硬币',
    coins: 2000
  },
  {
    icon: Share,
    title: '邀请新用户',
    description: '成功邀请一名新用户注册登录，即送10000硬币，最高可得100000硬币',
    coins: 10000
  }
]

const creationGuides = [
  {
    icon: Tips,
    title: '内容安全提示',
    content: '内容由AI生成，无版权风险，仅供参考'
  },
  {
    icon: Search,
    title: '选择写作模板',
    content: '写作前，建议选择最适合的写作应用/模板（可搜索）'
  },
  {
    icon: Edit,
    title: '输入写作信息',
    content: '建议依据提示输入写作辅助信息，描述越具体，写作质量越高'
  },
  {
    icon: Message,
    title: '内容优化建议',
    content: 'AI作品建议作为初稿，细节内容修改可通过其他写作应用，如"改写润色"等进行完善'
  }
]

const aiGuides = [
  {
    icon: Robot,
    title: 'AI回复说明',
    content: '提问回复内容由AI生成，非人工编辑；其内容准确性和完整性无法保证，不代表我们的态度和观点'
  },
  {
    icon: Message,
    title: '提问建议',
    content: '请准确输入提问问题，问题越详细，回答质量越高'
  },
  {
    icon: Tips,
    title: '多语言支持',
    content: '支持多语言聊天搜提问，原则上依据提问语言返回对应语言的回答'
  }
]


definePageMeta({
  layout: 'profile'
})
</script>