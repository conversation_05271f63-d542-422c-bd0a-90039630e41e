<template>
  <div class="flex-1 flex bg-slate-50">
  </div>
</template>

<script setup>
import { onMounted } from 'vue';
import { useRouter } from 'vue-router';


// 页面元信息
useHead({
  title: '个人中心 - 万能小in',
  meta: [
    {
      name: 'description',
      content: '管理您的个人信息和账户设置',
      tagPriority: 'critical', // 提高优先级
    }
  ]
})


// 重定向到个人资料页
const router = useRouter()
onMounted(() => {
  router.replace('/profile/basic')
})
</script>