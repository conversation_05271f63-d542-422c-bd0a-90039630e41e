<template>
  <div class="m-3 bg-white p-6 border border-gray-200 rounded-lg space-y-6">
    <!-- 实名认证 -->
    <div class="bg-white p-4 border border-blue-100 rounded-lg hover:border-blue-300 transition-colors cursor-pointer"
      @click="isAuthentication ? null : showVerificationModal = true">
      <div class="flex justify-between items-center">
        <div>
          <h3 class="text-base font-medium">实名认证</h3>
          <p class="text-gray-500 text-sm mt-1">完成实名认证，保障账号安全</p>
        </div>
        <div class="flex items-center text-gray-500">
          <span v-if="!isAuthentication" class="text-sm">未认证</span>
          <span v-if="isAuthentication" class="text-sm text-green-600">已认证</span>
          <right-one theme="outline" size="20" class="ml-2" />
        </div>
      </div>
    </div>

    <!-- 账号注销 -->
    <div class="bg-white p-4 border border-blue-100 rounded-lg hover:border-blue-300 transition-colors cursor-pointer"
      @click="handleDeleteAccount">
      <div class="flex justify-between items-center">
        <div>
          <h3 class="text-base font-medium">账号注销</h3>
          <p class="text-gray-500 text-sm mt-1">注销后账号将无法恢复</p>
        </div>
        <div class="flex items-center text-gray-500">
          <right-one theme="outline" size="20" class="ml-2" />
        </div>
      </div>
    </div>

    <!-- 使用 Teleport 将 Modal 传送到 body -->
    <Teleport to="body">
      <Modal v-model="showVerificationModal" :show-footer="false" title="实名认证" subtitle="请填写您的实名认证信息" :width="500"
        @confirm="handleVerificationSubmit">
        <VerificationModal ref="verificationModal" @close="showVerificationModal = false"
          @submit="handleVerificationSubmit" />
      </Modal>
    </Teleport>

    <!-- 注销确认弹窗 -->
    <Teleport to="body">
      <Modal v-model="showDeleteModal" title="账号注销" :width="480" :show-footer="false">
        <DeleteAccountModal @close="showDeleteModal = false" @confirm="handleConfirmDelete" />
      </Modal>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { UserService } from '@/services/user';
import { RightOne } from '@icon-park/vue-next';
import { computed, ref } from 'vue';
import Modal from '~/components/Common/Modal.vue';
import DeleteAccountModal from '~/components/Profile/DeleteAccountModal.vue';
import VerificationModal from '~/components/Profile/VerificationModal.vue';

// 响应式状态
const showVerificationModal = ref(false)
const showDeleteModal = ref(false)
const verificationModal = ref(null)

// 计算属性
const isAuthentication = computed(() => {
  return UserService.isAuthentication()
})

// 方法
const handleVerificationSubmit = () => {
  if (verificationModal.value) {
    showVerificationModal.value = false
  }
}

const handleDeleteAccount = () => {
  showDeleteModal.value = true
}

const handleConfirmDelete = async () => {
  try {
    // 这里添加实际的注销逻辑
    showDeleteModal.value = false
  } catch (error) {
    console.error('注销失败:', error)
  }
}

definePageMeta({
  layout: 'profile'
})
</script>