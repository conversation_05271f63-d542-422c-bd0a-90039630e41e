<template>
  <div class="m-3 bg-white p-6 border border-gray-200 rounded-lg">
    <!-- 列表区域 -->
    <div class="space-y-4">
      <div v-if="isLoading" class="flex justify-center items-center h-32"><a-spin inning="isLoading"></a-spin></div>
      <EmptyState v-if="!isLoading && rechargeList.length === 0" />
      <div v-if="!isLoading" v-for="item in rechargeList" :key="item.id"
        class="flex items-center justify-between p-3 sm:px-5 sm:py-3.5 border border-blue-100 rounded-lg">
        <div class="flex items-center gap-2 sm:gap-3">
          <div class="w-7 h-7 sm:w-8 sm:h-8  flex items-center justify-center">
            <funds theme="outline" size="20" fill="#F59E0B" />
          </div>
          <div>
            <div class="text-sm text-[#333333]">{{ formatTitle(item.remark) }}</div>
            <div class="text-sm text-gray-500 mt-0.5">{{ item.createTime }}</div>
          </div>
        </div>

        <div class="text-sm text-blue-600" v-if="item.amount > 0">+{{ item.amount || 0 }}硬币</div>
        <div class="text-sm text-gray-500" v-else>{{ item.amount || 0 }}硬币</div>
      </div>
    </div>

    <div class="mt-4 sm:mt-6 pb-16 md:pb-0">
      <Pagination v-model:current="reqParams.pageNo" :total-pages="reqParams.pages" :total="reqParams.total"
        @change="handlePageChange">
      </Pagination>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getRecordList } from '@/api/recharge';
import { type PageResult } from '@/api/typing';
import EmptyState from '@/components/EmptyState.vue';
import { Funds } from '@icon-park/vue-next';
import { Spin as ASpin, message } from 'ant-design-vue';
import { onMounted } from 'vue';
import Pagination from '~/components/Pagination.vue';

definePageMeta({
  layout: 'profile'
})




const reqParams = reactive({
  pageNo: 1,
  pageSize: 10,
  pages: 0,
  total: 0,
})

const ordersPageResult = ref<PageResult<RechargeDetail> | undefined>(undefined)


interface RechargeDetail {
  id: string
  amount: number
  coinId: string
  coinType: number
  createTime: string
  remark: string
  userId: string
}


const isLoading = ref<boolean>(true)

const loadRecordsData = async () => {
  isLoading.value = true
  const params = {
    ...reqParams
  }
  let res = await getRecordList(params)

  if (!res.ok || !res.data) {
    message.error(res.message || '充值记录加载失败')
    isLoading.value = false
    return
  }

  ordersPageResult.value = res.data
  reqParams.pages = res.data.pages || 0
  reqParams.total = res.data.total || 0
  isLoading.value = false

}


const formatTitle = (text: any) => {
  if (text == '图片识别') {
    return 'AI答题'
  }
  if (text == 'image') {
    return 'AI魔法头像'
  }
  if (text == 'ppt') {
    return 'PPT写作'
  }
  if (text == 'paper') {
    return '文章写作'
  }
  return text
}


const rechargeList = computed(() => {
  if (!ordersPageResult.value) {
    return []
  }
  const data = ordersPageResult.value.records || []
  const list: RechargeDetail[] = []
  // 相同的id两个值相加合成一条数据
  data.map((item) => {
    const index = list.findIndex((child) => child.id == item.id)
    if (index > -1) {
      list[index].amount += item.amount
    } else {
      list.push(item)
    }
  })
  return list //ordersPageResult.value.records
})

const handlePageChange = async () => {
  await loadRecordsData()
}


onMounted(() => {
  loadRecordsData()
})



</script>
