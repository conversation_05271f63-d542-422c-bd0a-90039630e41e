<template>
    <div class="m-3 bg-white p-6 border border-gray-200 rounded-lg">
        <!-- 列表区域 -->
        <div class="space-y-4">
            <div v-if="isLoading" class="flex justify-center items-center h-32">
                <a-spin :spinning="isLoading"></a-spin>
            </div>
            <EmptyState v-if="!isLoading && purchaseList.length === 0" />
            <div v-if="!isLoading" v-for="item in purchaseList" :key="item.id">
                <router-link :to="`/manual/detail/${item.id}`"
                    class="flex items-center justify-between p-3 sm:px-5 sm:py-3.5 border border-blue-100 rounded-lg">
                    <div class="flex items-center gap-2 sm:gap-3">
                        <div class="w-7 h-7 sm:w-8 sm:h-8 flex items-center justify-center">
                            <file-text theme="outline" size="20" fill="#3B82F6" />
                        </div>
                        <div>
                            <div class="text-sm text-gray-500 mt-0.5">{{ item.fileName || '未知文件' }}</div>
                        </div>
                    </div>

                    <div class="flex items-center gap-2">
                        <a-button type="link" size="small" @click.stop.prevent="handleDownload(item)">下载</a-button>
                        <div class="text-sm text-gray-500 mt-0.5">硬币：{{ item.coin }}</div>
                        <div @click.stop.prevent>
                            <a-popconfirm title="确认删除" content="确定要删除这条购买记录吗？" @confirm="handleDelete(item)"
                                ok-text="删除" cancel-text="取消">
                                <a-button type="link" status="danger" size="small">删除</a-button>
                            </a-popconfirm>
                        </div>
                    </div>
                </router-link>
            </div>
        </div>

        <div class="mt-4 sm:mt-6 pb-16 md:pb-0">
            <Pagination v-model:current="reqParams.pageNo" :total-pages="reqParams.pages" :total="reqParams.total"
                @change="handlePageChange">
            </Pagination>
        </div>
    </div>
</template>

<script setup lang="ts">
import { getDownloadInfo, userBuyFileDelete, userBuyFileList } from '@/api/seo';
import EmptyState from '@/components/EmptyState.vue';
import { FileText } from '@icon-park/vue-next';
import { Popconfirm as APopconfirm, Spin as ASpin, message } from 'ant-design-vue';
import { onMounted } from 'vue';
import Pagination from '~/components/Pagination.vue';

definePageMeta({
    layout: 'profile'
})

const reqParams = reactive({
    pageNo: 1,
    pageSize: 10,
    pages: 0,
    total: 0,
})

interface PurchaseDetail {
    id: string
    sourceUrl: string
    createTime: string
    site: string
    coin: number
    fileName: string
}

const purchaseList = ref<PurchaseDetail[]>([])
const isLoading = ref<boolean>(true)

const loadPurchaseData = async () => {

    isLoading.value = true
    try {
        const res = await userBuyFileList({ pageNo: reqParams.pageNo, pageSize: reqParams.pageSize })
        if (res.code === 200) {
            purchaseList.value = res.data.records || []
            reqParams.pages = res.data.pages || 0
            reqParams.total = res.data.total || 0
        } else {
            message.error(res.message || '记录加载失败')
        }
    } catch (error) {
        message.error('加载失败')
    } finally {
        isLoading.value = false
    }
}

const handleDownload = async (item: PurchaseDetail) => {
    try {
        const res = await getDownloadInfo(item.id)
        if (!res.ok) {
            message.error('获取下载链接失败')
            return
        }
        window.open(res.data?.fileUrl, '_blank')
    } catch (error) {
        message.error('下载失败')
    }
}

const handleDelete = async (item: PurchaseDetail) => {
    try {
        const res = await userBuyFileDelete(item.id)
        if (res.code === 200) {
            message.success('删除成功')
            await loadPurchaseData()
        } else {
            message.error(res.message || '删除失败')
        }
    } catch (error) {
        message.error('删除失败')
    }
}

const handlePageChange = async () => {
    await loadPurchaseData()
}

onMounted(() => {
    loadPurchaseData()
})
</script>
