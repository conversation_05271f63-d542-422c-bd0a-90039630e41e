<template>
    <div class="w-full flex justify-center bg-[#fafafa]">
        <div class="flex-1 h-full max-w-[1000px]">
            <ShareAnswerPage :is-web="true" :category-name="'知识助手'" :question-id="questionId" :robot-code="robotCode"
                :category="category" :mode="mode"></ShareAnswerPage>
        </div>
    </div>
</template>

<script setup lang="ts">
import { defineAsyncComponent } from 'vue';

const ShareAnswerPage = defineAsyncComponent(() =>
    import('@/components/Chat/ShareAnswer.vue')
)

import { StarloveConstants } from '@/utils/starloveConstants';
import { useRoute } from 'vue-router';

const route = useRoute()
const questionId = route.query.questionId ? route.query.questionId.toString() : '';
const robotCode = (route.query.robotCode ? route.query.robotCode.toString() || '' : '') || 'knowledge';
const category = (route.query.category ? route.query.category.toString() || '' : '') || 'knowledge';
const mode = (route.query.mode ? route.query.mode.toString() || '' : '') || StarloveConstants.sharePageMode.view;

definePageMeta({
    layout: 'empty'
})

</script>