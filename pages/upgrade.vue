<template>
    <div class="app-ie-tip">
        <h1>请更新您的浏览器</h1>
        <h2>IE浏览器及IE模式已不再受支持，推荐你使用以下浏览器来获得顺畅体验和最新功能，如果您使用的是下面提及的浏览器，请更新您的浏览器。</h2>
        <div class="app-ie-tip-browsers">
            <div class="ait-browser-wrapper"><a class="ait-browser" bname="chrome" href="https://www.google.cn/chrome/"
                    target="_blank">Chrome</a></div>
            <div class="ait-browser-wrapper"><a class="ait-browser" bname="firefox"
                    href="https://www.mozilla.org/firefox/new" target="_blank">Firefox</a></div>
            <div class="ait-browser-wrapper"><a class="ait-browser" bname="safari" href="https://www.apple.com/safari"
                    target="_blank">Safari</a></div>
            <div class="ait-browser-wrapper"><a class="ait-browser" bname="edge" href="https://www.microsoft.com/edge"
                    target="_blank">Edge</a></div>
        </div>
        <h2>如果您使用的是双核浏览器 (如搜狗、QQ、360 等浏览器) ，请切换到极速模式访问。</h2>
    </div>
</template>
<script setup lang="ts">
definePageMeta({
    layout: 'empty'
})
</script>
<style scoped>
.app-ie-tip {
    margin: 90px auto 0;
    width: 700px;
}

.ait-browser {
    text-align: center;
    display: inline-block;
    width: 170px;
    padding: 100px 0 20px;
    background-repeat: no-repeat;
    background-position: center 15px;
}

.ait-browser-wrapper {
    display: inline-block;
    border-radius: 5px;
}

.ait-browser-wrapper:hover {
    background-color: #EEEEEE;
}

.ait-browser[bname="chrome"] {
    background-image: url(//static-1256600262.file.myqcloud.com/xiaoin-pc/images/chrome.png);
}

.ait-browser[bname="firefox"] {
    background-image: url(//static-1256600262.file.myqcloud.com/xiaoin-pc/images/firefox.png);
}

.ait-browser[bname="safari"] {
    background-image: url(//static-1256600262.file.myqcloud.com/xiaoin-pc/images/safari.png);
}

.ait-browser[bname="edge"] {
    background-image: url(//static-1256600262.file.myqcloud.com/xiaoin-pc/images/edge.png);
}
</style>