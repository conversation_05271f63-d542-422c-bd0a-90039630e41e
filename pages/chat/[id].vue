<template>
  <Entrance v-if="!isLoading" :is-visible-top="true" :session-id="currentSessionId" :is-new-session-id="isNewSessionId"
    :is-agent="true" />
</template>
<script lang="ts" setup>
import { useChatStore } from '@/stores/chat';
import { onBeforeUnmount, onMounted } from 'vue';
import { useRoute } from 'vue-router';

import Entrance from '~/components/Chat/Entrance.vue';

const route = useRoute()
const routeId = route.params.id || ''
const currentSessionId = ref(`${routeId}`)
const isLoading = ref(true)

const chat = useChatStore()
const isNewSessionId = ref(chat.isNewSessionId || false)

onMounted(() => {
  isLoading.value = false
})
onBeforeUnmount(() => {
  const chat = useChatStore()
  chat.setIsNewSessionId(false)
});
</script>