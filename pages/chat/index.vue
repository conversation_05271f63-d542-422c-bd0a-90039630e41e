<template>
    <Entrance v-if="!isLoading" :is-visible-top="true" :session-id="currentSessionId"
        :is-new-session-id="isNewSessionId" :is-agent="true" />
</template>
<script lang="ts" setup>
import { useChatStore } from '@/stores/chat';
import { onBeforeUnmount, onMounted } from 'vue';
import { newSessionId } from '~/api/appMessage';

import Entrance from '~/components/Chat/Entrance.vue';
// const currentSessionId = ref(`${getCurrentTimestampString()}`) //不要生成新的 sessionId 有后端生成
const currentSessionId = ref('')
const isLoading = ref(true)
const isNewSessionId = ref(true)

onMounted(async () => {
    const res = await newSessionId()
    if (!res.success) {
        return
    }
    currentSessionId.value = res?.data || getCurrentTimestampString()
    isLoading.value = false
})
onBeforeUnmount(() => {
    const chat = useChatStore()
    chat.setIsNewSessionId(false)
});
</script>