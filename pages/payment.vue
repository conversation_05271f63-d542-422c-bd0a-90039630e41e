<template>
    <div>
        <client-only>
            <RechargeModal v-if="rechargeStore.rechargeModalVisible" v-model="rechargeStore.rechargeModalVisible"
                :showClose="false" :isEditor="true" />
        </client-only>
    </div>

</template>
<script setup lang="ts">
import { onMounted } from 'vue';
import RechargeModal from '~/components/Auth/RechargeModal.vue';
import { useRechargeStore } from '~/stores/recharge';
import { parentPostMessage } from '~/utils/utils';

const rechargeStore = useRechargeStore()

const onOperationCancel = () => {
    console.log('onOperationCancel')
    //关闭iframe
    parentPostMessage('closeIframe', {
        action: 'close'
    })
}

const onRechargeSuccess = () => {
    console.log('onOperationCancel')
    //关闭iframe
    parentPostMessage('closeIframe', {
        action: 'close',
        paySuccess: true
    })
}
watch(() => rechargeStore.rechargeStatus, async (_rechargeStatus) => {
    console.log('充值状态', rechargeStore.rechargeStatus)
    if (_rechargeStatus == RECHARGE_STATUS.SUCCESS) {
        console.log('充值成功')
        onRechargeSuccess()
    } else if (_rechargeStatus == RECHARGE_STATUS.CANCEL) {
        onOperationCancel()
    }
})

definePageMeta({
    layout: 'empty'
})
// 监听窗口尺寸变化
onMounted(() => {
    setTimeout(() => {
        rechargeStore.openRechargeModal(RechargeModalTab.coin)
    }, 1000)
})

// 在传递给 NavItem 之前先处理


</script>