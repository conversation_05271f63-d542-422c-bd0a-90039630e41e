{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build:prod": "DEPLOY_ENV=prod nuxt build", "build:pre": "DEPLOY_ENV=pre nuxt build", "build:test": "DEPLOY_ENV=test nuxt build", "build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "lint": "eslint --ext .js,.ts,.vue .", "lint:fix": "eslint --ext .js,.ts,.vue . --fix", "deploydocker": "pm2 start ecosystem-xiaoin-dev.config.cjs --no-daemon", "deploydocker:pre": "pm2 start ecosystem-xiaoin-dev.config.cjs --env pre --no-daemon", "deploydocker:prod": "pm2 start ecosystem-xiaoin-dev.config.cjs --env prod --no-daemon"}, "dependencies": {"@ant-design/icons-vue": "^6.1.0", "@headlessui/vue": "^1.7.23", "@icon-park/vue-next": "^1.4.2", "@microsoft/fetch-event-source": "^2.0.1", "@nuxt/ui": "^2.20.0", "@nuxtjs/robots": "^3.0.0", "@pinia/nuxt": "^0.9.0", "@svgdotjs/svg.js": "^3.2.4", "@svgdotjs/svg.topath.js": "^2.0.3", "@tiptap/core": "^2.11.7", "@tiptap/extension-bold": "^2.11.3", "@tiptap/extension-bubble-menu": "^2.11.7", "@tiptap/extension-code": "^2.11.7", "@tiptap/extension-code-block": "^2.11.7", "@tiptap/extension-code-block-lowlight": "^2.11.7", "@tiptap/extension-document": "^2.11.3", "@tiptap/extension-highlight": "^2.11.2", "@tiptap/extension-image": "^2.11.2", "@tiptap/extension-link": "^2.10.3", "@tiptap/extension-paragraph": "^2.11.3", "@tiptap/extension-table": "^2.11.2", "@tiptap/extension-table-cell": "^2.11.2", "@tiptap/extension-table-header": "^2.11.2", "@tiptap/extension-table-row": "^2.11.2", "@tiptap/extension-task-item": "^2.11.2", "@tiptap/extension-task-list": "^2.11.2", "@tiptap/extension-text": "^2.11.3", "@tiptap/extension-text-align": "^2.11.5", "@tiptap/extension-underline": "^2.10.4", "@tiptap/html": "^2.11.2", "@tiptap/pm": "^2.10.3", "@tiptap/starter-kit": "^2.11.7", "@tiptap/vue-3": "^2.10.3", "@types/katex": "^0.16.7", "@types/lodash": "^4.17.13", "@types/marked": "^5.0.2", "@vscode/markdown-it-katex": "^1.1.1", "@vueuse/core": "^12.0.0", "@vueuse/integrations": "^12.0.0", "ali-oss": "^6.22.0", "aliyun-sdk": "^1.12.10", "ant-design-vue": "^4.2.6", "axios": "^1.7.9", "chart.js": "^4.4.7", "cheerio": "^1.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "copy-text-to-clipboard": "^3.2.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "element-tiptap": "^2.2.1", "gsap": "^3.12.5", "highlight.js": "^11.11.0", "katex": "^0.16.15", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "lowlight": "^3.3.0", "lucide-vue-next": "^0.468.0", "markdown-it": "^14.1.0", "markdown-it-container": "^4.0.0", "markdown-it-highlightjs": "^4.2.0", "marked": "^15.0.7", "mitt": "^3.0.1", "nuxt": "^3.14", "nuxt-simple-sitemap": "^4.1.7", "pinia": "^2.3.0", "prosemirror-state": "^1.4.3", "qs": "^6.13.1", "radix-vue": "^1.9.11", "simple-mind-map": "^0.13.0", "simple-mind-map-plugin-themes": "^1.0.0", "tailwind-merge": "^2.5.5", "tiptap": "^1.32.2", "tiptap-extension-resize-image": "^1.2.1", "tiptap-markdown": "^0.8.10", "vite": "^6.2.2", "vue": "^3.5.13", "vue-chartjs": "^5.3.2", "vue-pdf-embed": "^2.1.1", "vue-router": "^4.3.0"}, "devDependencies": {"@nuxt/icon": "^1.1.2", "@nuxtjs/eslint-config-typescript": "^12.1.0", "@types/markdown-it": "^14.1.2", "@types/markdown-it-container": "^2.0.10", "autoprefixer": "^10.4.20", "postcss": "^8.4.49", "sass": "^1.83.0", "sass-loader": "^16.0.4", "shadcn-vue": "^0.11.3", "tailwindcss": "^3.4.16", "tailwindcss-animate": "^1.0.7", "unplugin-vue-components": "^0.27.5"}}