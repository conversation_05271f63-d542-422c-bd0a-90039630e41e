#!/bin/bash

# 获取环境参数，默认为 dev
DEPLOY_ENV=${1:-test}

# 验证环境参数
if [ "$DEPLOY_ENV" != "test" ] && [ "$DEPLOY_ENV" != "pre" ] && [ "$DEPLOY_ENV" != "prod" ]; then
    echo "错误: 环境参数必须是 'test', 'pre' 或 'prod'"
    echo "用法: ./local_deploy.sh [test|pre|prod]"
    exit 1
fi

# 设置基础路径 - 使用绝对路径
BASE_PATH="$(cd "$(dirname "${BASH_SOURCE[0]}")/../xiaoin_nuxt_deploy" && pwd)"
APP_NAME="xiaoin_nuxt"
BLUE_DIR="${BASE_PATH}/${APP_NAME}_blue"
GREEN_DIR="${BASE_PATH}/${APP_NAME}_green"
CURRENT_LINK="${BASE_PATH}/current"

echo "正在部署 ${DEPLOY_ENV} 环境..."

# 确保基础目录存在
mkdir -p ${BASE_PATH}

# 确定当前运行的是哪个目录
if [ -L "${CURRENT_LINK}" ] && [ "$(readlink ${CURRENT_LINK})" = "${BLUE_DIR}" ]; then
    DEPLOY_DIR="${GREEN_DIR}"
    CURRENT_DIR="${BLUE_DIR}"
    NEW_PORT="8013"
    OLD_PORT="8014"
else
    DEPLOY_DIR="${BLUE_DIR}"
    CURRENT_DIR="${GREEN_DIR}"
    NEW_PORT="8014"
    OLD_PORT="8013"
fi

echo "开始部署到目录: ${DEPLOY_DIR}"

# 清理目标目录
echo "清理目标目录..."
rm -rf ${DEPLOY_DIR}/*
mkdir -p ${DEPLOY_DIR}

# 复制所有项目文件到部署目录（包括隐藏文件）
rsync -av \
    --exclude='node_modules' \
    --exclude='.output' \
    --exclude='.nuxt' \
    --exclude='.git' \
    --include='.env*' \
    ./ ${DEPLOY_DIR}/

# 复制对应的 PM2 配置文件
if [ "${DEPLOY_DIR}" = "${BLUE_DIR}" ]; then
    cp ecosystem.config.blue.cjs ${DEPLOY_DIR}/ecosystem.config.cjs
    NEW_INSTANCE_NAME="xiaoin_nuxt_blue"
    OLD_INSTANCE_NAME="xiaoin_nuxt_green"
else
    cp ecosystem.config.green.cjs ${DEPLOY_DIR}/ecosystem.config.cjs
    NEW_INSTANCE_NAME="xiaoin_nuxt_green"
    OLD_INSTANCE_NAME="xiaoin_nuxt_blue"
fi

# 进入部署目录
cd ${DEPLOY_DIR}

# 在构建之前，根据环境复制对应的环境文件
if [ "$DEPLOY_ENV" = "prod" ]; then
    echo "使用生产环境配置..."
    cp .env.prod .env
elif [ "$DEPLOY_ENV" = "pre" ]; then
    echo "使用预发布环境配置..."
    cp .env.pre .env
else
    echo "使用测试环境配置..."
    cp .env.test .env
fi

# 安装依赖和构建
echo "安装依赖..."
# pnpm install --frozen-lockfile
pnpm install --no-frozen-lockfile

echo "构建应用..."
pnpm build:${DEPLOY_ENV}

# 上传静态资源到 CDN
echo "上传静态资源到 CDN..."

if [ "$DEPLOY_ENV" = "prod" ]; then
    ./upload-static-prod.sh
elif [ "$DEPLOY_ENV" = "pre" ]; then
    ./upload-static-pre.sh
else
    ./upload-static-test.sh 
fi
# 使用新的 PM2 配置启动应用
echo "启动新实例..."
pm2 start ecosystem.config.cjs --name "${NEW_INSTANCE_NAME}" --env ${DEPLOY_ENV}

# 等待新实例启动...
echo "等待新实例启动..."
sleep 10

# 如果存在旧实例，则关闭它
if pm2 list | grep -q "${OLD_INSTANCE_NAME}"; then
    echo "关闭旧实例..."
    pm2 delete "${OLD_INSTANCE_NAME}"
fi

# 更新符号链接
echo "更新符号链接..."
ln -sfn ${DEPLOY_DIR} ${CURRENT_LINK}

echo "部署完成! 新实例运行在端口 ${NEW_PORT}" 