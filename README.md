# Nuxt Minimal Starter

项目是基于nuxt3开发的，使用pnpm作为包管理工具。
名叫万能小in。主打个人知识库的个人写作。

Look at the [Nuxt documentation](https://nuxt.com/docs/getting-started/introduction) to learn more.

## Setup

Make sure to install dependencies:

```bash

# pnpm
pnpm install


```

确保严格按照 lock 文件安装依赖:

```bash
# 严格模式安装依赖
pnpm install --frozen-lockfile
```

## Development Server

Start the development server on `http://localhost:3000`:

```bash
# pnpm
pnpm dev

```

## Production

Build the application for production:

```bash

# pnpm
pnpm build

```

Locally preview production build:

```bash


# pnpm
pnpm preview

```

Check out the [deployment documentation](https://nuxt.com/docs/getting-started/deployment) for more information.

图标优先用icon-park的。

pnpm add @headlessui/vue

# 安装依赖

pnpm install

# 构建生产版本

pnpm build

# 安装 PM2

npm install -g pm2

# 启动应用

pm2 start ecosystem.config.cjs
重启
pm2 restart ecosystem.config.cjs

# 其他常用命令

根据配置，会在8013端口启动
查看日志 pm2 logs

电信服务路径 /data/opt/program/xiaoin_nuxt_preview
ui 库 地址 https://ui.nuxt.com/components/skeleton

# 发布生产

nginx 配置

```
upstream xiaoin_backend {
    server 127.0.0.1:8012;
    server 127.0.0.1:8013 backup;
    keepalive 32;
}

server {
    listen 80;
    server_name localhost;

    access_log /Users/<USER>/deploy/logs/nginx/access.log;
    error_log /Users/<USER>/deploy/logs/nginx/error.log;

    location / {
        proxy_pass http://xiaoin_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}
```

# 检查配置

sudo nginx -t

# 启动 Nginx

sudo nginx

# 停止 Nginx

sudo nginx -s stop

# 如果配置正确，重启 Nginx

sudo nginx -s reload

# 测试环境

chmod +x local_deploy.sh
./local_deploy.sh test

# 生产环境 阿里云服务器 http://***********:18966/8215bf828f 目录 /opt/program/xiaoin_nuxt_prod 访问地址 https://xiaoin.cn/

chmod +x local_deploy.sh
./local_deploy.sh prod

# 预发环境 电信服务器 http://**************:30781/hosts/terminal 目录 /data/opt/program/xiaoin_nuxt_preview 访问地址 https://preview.xiaoin.cn/

chmod +x local_deploy.sh
./local_deploy.sh pre

# 开发环境 电信服务器 http://**************:30781/hosts/terminal 目录 /data/opt/program/xiaoin-nuxt-dev 访问地址 http://xiaoin-test.inschool.top/

sh ./update_and_restart.sh

# 鼠标端开发环境 电信服务器 http://**************:30781/hosts/terminal 目录 /data/opt/program/xiaoin_nuxt_mouse_dev 访问地址 https://xiaoin-mouse-dev.xiaoin.cn

sh ./update_and_restart_mouse.sh

-----end-----

chmod +x upload-static-prod.sh
chmod +x upload-static-test.sh

# 阿里云5号 docker启动 dev 目录 /opt/program/xiaoin-nuxt-dev

```bash
docker build -t xiaoin-nuxt-dev-app:latest .
docker compose -f docker-compose-test.yml down -v
docker compose -f docker-compose-test.yml up -d
```

# 阿里云5号 docker启动 预发 目录 /opt/program/xiaoin-nuxt-pre

```bash
# docker build -t xiaoin-nuxt-pre-app:latest .
docker build -t xiaoin-nuxt-pre-app:latest -f Dockerfile.pre .
docker compose -f docker-compose-pre.yml down -v
docker compose -f docker-compose-pre.yml up -d
```

后台文档地址 https://lbm-helper-test.starringshop.com/doc.html#/default/AI-%E6%8F%90%E9%97%AE%E6%A8%A1%E5%9D%97/update_4
