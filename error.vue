<template>
    <div class="pt-[80px] flex justify-center text-center">
        <div v-if="error">
            <h1>发生错误</h1>
            <p class="py-10">{{ error.statusCode }} - {{ error.message }}</p>
            <button
                class="px-4 py-2 bg-blue-50 text-blue-700 rounded-lg hover:bg-blue-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors whitespace-nowrap"
                @click="handleError">返回首页</button>
        </div>
    </div>
</template>

<script setup lang="ts">
import type { NuxtError } from '#app';

const props = defineProps({
    error: Object as () => NuxtError
})

const handleError = () => clearError({ redirect: '/' })
</script>